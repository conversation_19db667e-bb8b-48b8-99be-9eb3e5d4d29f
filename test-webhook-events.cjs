// Simple test script to verify the structure of the processed_webhook_events table
const { createClient } = require('@supabase/supabase-js');

// Create a Supabase client
function createTestDbClient() {
  if (!process.env.SUPABASE_URL || !process.env.SUPABASE_DB_KEY) {
    throw new Error('SUPABASE_URL and SUPABASE_DB_KEY must be set for database tests');
  }

  return createClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_DB_KEY,
    {
      auth: {
        persistSession: false,
        autoRefreshToken: false,
        detectSessionInUrl: false
      }
    }
  );
}

async function testProcessedWebhookEventsTable() {
  try {
    console.log('Testing processed_webhook_events table structure...');

    // Create a Supabase client
    const supabase = createTestDbClient();

    // Try to insert a record into the processed_webhook_events table
    const { data, error } = await supabase
      .from('processed_webhook_events')
      .insert({
        event_id: 'test_event_id_' + Date.now(),
        event_type: 'test_event_type',
        processed_at: new Date().toISOString(),
        created_at: new Date().toISOString()
      })
      .select();

    console.log('Insert result:', data, error);

    if (error) {
      console.error('Error inserting record:', error);
      return;
    }

    // If we got here, the insert was successful, which means the created_at column exists
    console.log('Successfully inserted record with created_at field!');
    console.log('Test passed!');

    // Clean up the test record
    const { error: deleteError } = await supabase
      .from('processed_webhook_events')
      .delete()
      .eq('event_id', data[0].event_id);

    if (deleteError) {
      console.error('Error cleaning up test record:', deleteError);
    } else {
      console.log('Successfully cleaned up test record.');
    }
  } catch (error) {
    console.error('Error testing processed_webhook_events table:', error);
  }
}

// Load environment variables from .env file
require('dotenv').config();

// Run the test
testProcessedWebhookEventsTable();
