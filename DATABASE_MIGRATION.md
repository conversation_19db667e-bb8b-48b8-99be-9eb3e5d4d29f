# Database Migration for Clerk Authentication

## Issue

The current database schema uses UUID type for user IDs, but Clerk authentication uses a different format for user IDs (e.g., `user_2wGQABv0jGkkg4Sr5V9BhdKEiPA`). This causes an error when trying to create users or projects:

```
invalid input syntax for type uuid: "user_2wGQABv0jGkkg4Sr5V9BhdKEiPA"
```

## Solution

We need to modify the database schema to use `text` type instead of `uuid` for all user ID fields.

## Migration Steps

### Option 1: Using Supabase SQL Editor

1. Log in to your Supabase dashboard
2. Go to the SQL Editor
3. Copy and paste the contents of `scripts/update-schema.sql`
4. Run the SQL query

### Option 2: Using Supabase CLI

If you have the Supabase CLI installed:

```bash
cd supabase
npx supabase migration up
```

### Option 3: Using the Node.js Script

1. Make sure your environment variables are set up correctly in `.env`:
   ```
   SUPABASE_URL=your_supabase_url
   SUPABASE_DB_KEY=your_supabase_db_key
   ```

2. Run the script:
   ```bash
   node scripts/update-schema.js
   ```

## Verification

After applying the migration, restart your server and try creating a project again. The error should be resolved, and you should be able to create projects with Clerk authentication.

## Schema Changes

The following tables have been updated:

1. `users`: Changed `id` from UUID to text
2. `projects`: Changed `user_id` from UUID to text
3. `reference_categories`: Changed `user_id` from UUID to text
4. `reference_items`: Changed `user_id` from UUID to text
5. `renovation_presets`: Changed `created_by` from UUID to text

## Code Changes

The TypeScript types in `shared/types/supabase.ts` have been updated to reflect these changes.
