# Google Analytics 4 (GA4) Implementation Guide

This document describes the GA4 implementation for Renovision Studio, including setup, usage, and testing.

## Overview

The GA4 implementation provides comprehensive analytics tracking for the Renovision Studio application with:

- **Automatic page view tracking** for client-side routing (Wouter)
- **User identification** through Clerk authentication
- **Event tracking** for key user actions (auth, projects, payments, errors)
- **Privacy compliance** with production-only tracking
- **TypeScript support** with full type safety
- **Debug mode** for development testing

## Architecture

### Core Components

1. **`client/src/lib/ga4.ts`** - Core GA4 utility functions
2. **`client/src/hooks/use-ga4.tsx`** - React hook for GA4 integration
3. **`client/src/components/GA4Provider.tsx`** - Context provider for app-wide access
4. **`client/index.html`** - GA4 script loading

### Environment Configuration

```bash
# Required for GA4 tracking
VITE_GA4_MEASUREMENT_ID="G-SYV3MV2GT7"

# Optional debug mode
VITE_GA4_DEBUG_MODE="false"
```

## Usage

### Automatic Tracking

The following events are tracked automatically:

- **Page Views**: All route changes via Wouter routing
- **User Identification**: Automatic user ID setting with Clerk auth
- **Authentication Events**: Sign-in, sign-up, sign-out

### Manual Event Tracking

Use the GA4 context in any component:

```tsx
import { useGA4Context } from '@/components/GA4Provider';

function MyComponent() {
  const { trackEvent, trackProject, trackPayment } = useGA4Context();

  const handleButtonClick = () => {
    trackEvent({
      action: 'button_click',
      category: 'ui',
      label: 'hero_cta',
      custom_parameters: {
        button_text: 'Get Started',
      },
    });
  };

  const handleProjectCreated = (projectId: string) => {
    trackProject('project_created', projectId, {
      modification_type: 'kitchen_remodel',
    });
  };

  return (
    <button onClick={handleButtonClick}>
      Get Started
    </button>
  );
}
```

### Available Tracking Functions

```tsx
// Generic event tracking
trackEvent({
  action: string,
  category?: string,
  label?: string,
  value?: number,
  custom_parameters?: Record<string, any>
});

// Authentication events
trackAuth('sign_up' | 'sign_in' | 'sign_out');

// Project events
trackProject(
  'project_created' | 'project_completed' | 'image_generated' | 'image_generation_failed',
  projectId?: string,
  additionalParams?: Record<string, any>
);

// Payment events
trackPayment(
  'checkout_started' | 'checkout_completed' | 'subscription_created' | 'subscription_cancelled',
  additionalParams?: Record<string, any>
);

// Error tracking
trackError(
  errorType: string,
  errorMessage: string,
  additionalParams?: Record<string, any>
);
```

## Event Categories

### Standard Events

| Event | Category | Description |
|-------|----------|-------------|
| `page_view` | - | Automatic page view tracking |
| `sign_up` | `authentication` | User registration |
| `sign_in` | `authentication` | User login |
| `sign_out` | `authentication` | User logout |
| `project_created` | `project` | New project creation |
| `project_completed` | `project` | Project completion |
| `image_generated` | `project` | AI image generation |
| `image_generation_failed` | `project` | Failed image generation |
| `checkout_started` | `payment` | Stripe checkout initiated |
| `checkout_completed` | `payment` | Payment successful |
| `subscription_created` | `payment` | New subscription |
| `subscription_cancelled` | `payment` | Subscription cancellation |
| `error_occurred` | `error` | Application errors |

### Form Events

| Event | Category | Description |
|-------|----------|-------------|
| `form_start` | `form` | Form interaction started |
| `form_submit` | `form` | Form submitted successfully |
| `form_error` | `form` | Form submission error |
| `form_field_focus` | `form` | Field focused |
| `form_field_blur` | `form` | Field blurred |
| `form_field_change` | `form` | Field value changed |

## Testing

### Development Testing

1. **Enable Debug Mode**:
   ```bash
   VITE_GA4_DEBUG_MODE="true"
   VITE_DEBUG_MODE="analytics"
   ```

2. **Check Console Logs**:
   - Look for `[DEBUG] [ANALYTICS]` messages
   - Verify events are being tracked

3. **Use Test Utility**:
   ```javascript
   // In browser console
   import('/src/utils/ga4-test.js').then(m => m.testGA4Implementation());
   ```

### Production Testing

1. **GA4 Real-time Reports**:
   - Open GA4 dashboard
   - Go to Reports > Real-time
   - Verify events appear within 30 seconds

2. **Debug View** (if enabled):
   - Install GA4 Debug extension
   - Enable debug mode in GA4
   - View detailed event data

## Privacy and Compliance

### GDPR Compliance

- **Production Only**: Tracking only enabled in production
- **User Consent**: Consider implementing consent management
- **Data Minimization**: Only essential data is tracked
- **User Rights**: Support for data deletion requests

### Data Collected

- **Page Views**: URL, title, timestamp
- **User ID**: Clerk user ID (when authenticated)
- **Events**: Action, category, label, custom parameters
- **No PII**: Email addresses and personal data are not tracked

## Troubleshooting

### Common Issues

1. **GA4 Not Loading**:
   - Check `VITE_GA4_MEASUREMENT_ID` is set
   - Verify production environment
   - Check browser console for errors

2. **Events Not Appearing**:
   - Wait up to 24 hours for standard reports
   - Use Real-time reports for immediate verification
   - Check event parameters are valid

3. **TypeScript Errors**:
   - Ensure all GA4 types are imported correctly
   - Check `window.gtag` is properly declared

### Debug Commands

```bash
# Check GA4 configuration
console.log(window.gtag, window.dataLayer);

# Test event tracking
gtag('event', 'test_event', { test: true });

# View dataLayer
console.log(window.dataLayer);
```

## Performance Considerations

- **Async Loading**: GA4 script loads asynchronously
- **No Blocking**: Analytics never block page rendering
- **Minimal Impact**: Lightweight implementation with lazy loading
- **Error Handling**: Graceful fallback when GA4 fails

## Future Enhancements

- **Enhanced E-commerce**: Product and purchase tracking
- **Custom Dimensions**: User properties and custom metrics
- **Conversion Goals**: Goal and funnel tracking
- **A/B Testing**: Integration with Google Optimize
- **Server-Side Tracking**: Measurement Protocol for server events
