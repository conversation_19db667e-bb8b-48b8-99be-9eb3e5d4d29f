#!/bin/bash

echo "Setting up Tailwind CSS..."

# Remove any existing Tailwind CSS related packages
npm uninstall tailwindcss @tailwindcss/vite postcss autoprefixer

# Install the correct versions
npm install -D tailwindcss@3 postcss@8 autoprefixer@10

# Create Tailwind CSS config if it doesn't exist
if [ ! -f "tailwind.config.ts" ]; then
  echo "Creating Tailwind CSS config..."
  npx tailwindcss init -p --ts
fi

echo "Tailwind CSS setup complete!" 