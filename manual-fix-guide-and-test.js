#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const SUPABASE_URL = process.env.SUPABASE_URL;
const SUPABASE_DB_KEY = process.env.SUPABASE_DB_KEY;

console.log('📋 Manual Database Fix Guide and Test');
console.log('=====================================');

if (!SUPABASE_URL || !SUPABASE_DB_KEY) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(SUPABASE_URL, SUPABASE_DB_KEY, {
  auth: {
    persistSession: false,
    autoRefreshToken: false,
    detectSessionInUrl: false
  }
});

async function checkCurrentStatus() {
  console.log('\n🔍 Checking current database status...');
  
  try {
    // Test 1: Check if Clerk user IDs work
    const testUserId = 'user_clerk_test_123';
    const { data: testUser, error: userError } = await supabase
      .from('users')
      .upsert({
        id: testUserId,
        username: 'Test User',
        email: '<EMAIL>'
      })
      .select()
      .single();

    if (userError) {
      console.log('❌ User ID type issue:', userError.message);
      console.log('📋 STEP 1 NEEDED: Fix user ID types');
      return { userIdFixed: false, draftFieldsFixed: false };
    }

    console.log('✅ User ID types are compatible with Clerk');

    // Test 2: Check if basic projects work
    const { data: basicProject, error: basicError } = await supabase
      .from('projects')
      .insert({
        user_id: testUserId,
        title: 'Basic Test',
        description: 'Basic test',
        status: 'draft'
      })
      .select()
      .single();

    if (basicError) {
      console.log('❌ Basic project creation failed:', basicError.message);
      await supabase.from('users').delete().eq('id', testUserId);
      return { userIdFixed: false, draftFieldsFixed: false };
    }

    console.log('✅ Basic project creation works');

    // Test 3: Check if draft fields work
    const { data: draftProject, error: draftError } = await supabase
      .from('projects')
      .insert({
        user_id: testUserId,
        title: 'Draft Test',
        description: 'Draft test',
        status: 'draft',
        modification_description: 'Test modification',
        before_images: [{ url: 'test.jpg' }],
        reference_images: [{ url: 'ref.jpg' }],
        step: 1,
        modification_type: 'custom',
        modification_options: { test: true }
      })
      .select()
      .single();

    // Clean up test data
    if (basicProject) await supabase.from('projects').delete().eq('id', basicProject.id);
    if (draftProject) await supabase.from('projects').delete().eq('id', draftProject.id);
    await supabase.from('users').delete().eq('id', testUserId);

    if (draftError) {
      console.log('❌ Draft fields missing:', draftError.message);
      console.log('📋 STEP 2 NEEDED: Add draft fields');
      return { userIdFixed: true, draftFieldsFixed: false };
    }

    console.log('✅ Draft fields are working');
    return { userIdFixed: true, draftFieldsFixed: true };

  } catch (error) {
    console.error('❌ Error checking status:', error);
    return { userIdFixed: false, draftFieldsFixed: false };
  }
}

function showManualInstructions(status) {
  console.log('\n📋 MANUAL FIX INSTRUCTIONS');
  console.log('==========================');
  
  if (!status.userIdFixed) {
    console.log('\n🔧 STEP 1: Fix User ID Types');
    console.log('1. Go to your Supabase dashboard');
    console.log('2. Navigate to SQL Editor');
    console.log('3. Copy and paste this SQL:');
    console.log(`
-- Fix User ID Types for Clerk Compatibility
ALTER TABLE projects DROP CONSTRAINT IF EXISTS projects_user_id_fkey;
ALTER TABLE reference_categories DROP CONSTRAINT IF EXISTS reference_categories_user_id_fkey;
ALTER TABLE reference_items DROP CONSTRAINT IF EXISTS reference_items_user_id_fkey;
ALTER TABLE renovation_presets DROP CONSTRAINT IF EXISTS renovation_presets_created_by_fkey;
ALTER TABLE subscriptions DROP CONSTRAINT IF EXISTS subscriptions_user_id_fkey;
ALTER TABLE usage DROP CONSTRAINT IF EXISTS usage_user_id_fkey;

ALTER TABLE users ALTER COLUMN id TYPE text;
ALTER TABLE projects ALTER COLUMN user_id TYPE text;
ALTER TABLE reference_categories ALTER COLUMN user_id TYPE text;
ALTER TABLE reference_items ALTER COLUMN user_id TYPE text;
ALTER TABLE renovation_presets ALTER COLUMN created_by TYPE text;
ALTER TABLE subscriptions ALTER COLUMN user_id TYPE text;
ALTER TABLE usage ALTER COLUMN user_id TYPE text;

ALTER TABLE projects ADD CONSTRAINT projects_user_id_fkey 
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;
ALTER TABLE reference_categories ADD CONSTRAINT reference_categories_user_id_fkey 
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;
ALTER TABLE reference_items ADD CONSTRAINT reference_items_user_id_fkey 
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;
ALTER TABLE renovation_presets ADD CONSTRAINT renovation_presets_created_by_fkey 
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL;
ALTER TABLE subscriptions ADD CONSTRAINT subscriptions_user_id_fkey 
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;
ALTER TABLE usage ADD CONSTRAINT usage_user_id_fkey 
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;
    `);
    console.log('4. Click "Run" to execute');
    console.log('5. Wait for completion');
  }

  if (!status.draftFieldsFixed) {
    console.log('\n🔧 STEP 2: Add Draft Fields');
    console.log('1. In the same SQL Editor, copy and paste this SQL:');
    console.log(`
-- Add Draft Fields to Projects Table
ALTER TABLE public.projects
ADD COLUMN IF NOT EXISTS modification_description TEXT,
ADD COLUMN IF NOT EXISTS before_images JSONB,
ADD COLUMN IF NOT EXISTS reference_images JSONB,
ADD COLUMN IF NOT EXISTS step INTEGER,
ADD COLUMN IF NOT EXISTS modification_type TEXT,
ADD COLUMN IF NOT EXISTS modification_options JSONB,
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMPTZ DEFAULT NOW();

-- Refresh Schema Cache
NOTIFY pgrst, 'reload schema';
    `);
    console.log('2. Click "Run" to execute');
    console.log('3. Wait for completion');
  }

  console.log('\n🔄 STEP 3: Run Test');
  console.log('After applying the SQL changes:');
  console.log('1. Run this script again: node manual-fix-guide-and-test.js');
  console.log('2. If all tests pass, restart your server: npm run dev');
  console.log('3. Try creating a draft in your application');
}

async function runComprehensiveTest() {
  console.log('\n🧪 Running Comprehensive Test');
  console.log('==============================');
  
  try {
    const realUserId = 'user_2wGQABv0jGkkg4Sr5V9BhdKEiPA';
    
    // Test 1: User management
    console.log('\n1️⃣ Testing user management...');
    const { data: user, error: userError } = await supabase
      .from('users')
      .upsert({
        id: realUserId,
        username: 'Comprehensive Test User',
        email: '<EMAIL>',
        first_name: 'Test',
        last_name: 'User'
      })
      .select()
      .single();

    if (userError) {
      console.log('❌ User management failed:', userError.message);
      return false;
    }
    console.log('✅ User management works');

    // Test 2: Complete draft creation
    console.log('\n2️⃣ Testing complete draft creation...');
    const draftData = {
      user_id: realUserId,
      title: 'Comprehensive Test Draft',
      description: 'Testing all draft functionality after manual fix',
      status: 'draft',
      modification_description: 'Replace kitchen floor with premium hardwood flooring',
      before_images: [
        { url: 'kitchen-before-1.jpg', name: 'Kitchen Before View 1', size: 1024000 },
        { url: 'kitchen-before-2.jpg', name: 'Kitchen Before View 2', size: 856000 }
      ],
      reference_images: [
        { url: 'hardwood-ref-1.jpg', name: 'Oak Hardwood Reference', size: 512000 },
        { url: 'hardwood-ref-2.jpg', name: 'Installation Reference', size: 768000 }
      ],
      step: 2,
      modification_type: 'replace_floor',
      modification_options: {
        floorType: 'hardwood',
        wood: 'oak',
        finish: 'matte',
        plankWidth: '5-inch',
        installation: 'nail-down',
        estimatedCost: 3500,
        timeline: '3-4 days'
      }
    };

    const { data: draft, error: draftError } = await supabase
      .from('projects')
      .insert(draftData)
      .select()
      .single();

    if (draftError) {
      console.log('❌ Draft creation failed:', draftError.message);
      return false;
    }

    console.log('✅ Complete draft created:', {
      id: draft.id,
      title: draft.title,
      modification_type: draft.modification_type,
      step: draft.step,
      before_images_count: draft.before_images?.length || 0,
      reference_images_count: draft.reference_images?.length || 0
    });

    // Test 3: Draft updates
    console.log('\n3️⃣ Testing draft updates...');
    const { data: updatedDraft, error: updateError } = await supabase
      .from('projects')
      .update({
        step: 3,
        modification_description: 'Updated: Replace kitchen floor with premium oak hardwood flooring with matte finish',
        modification_options: {
          ...draft.modification_options,
          updated: true,
          updateTimestamp: new Date().toISOString(),
          notes: 'Updated after client consultation'
        }
      })
      .eq('id', draft.id)
      .select()
      .single();

    if (updateError) {
      console.log('❌ Draft update failed:', updateError.message);
      return false;
    }
    console.log('✅ Draft updated successfully');

    // Test 4: Status transitions
    console.log('\n4️⃣ Testing status transitions...');
    const { data: processingProject, error: statusError } = await supabase
      .from('projects')
      .update({ status: 'processing' })
      .eq('id', draft.id)
      .select()
      .single();

    if (statusError) {
      console.log('❌ Status change failed:', statusError.message);
      return false;
    }
    console.log('✅ Status changed to processing');

    // Test 5: Project retrieval and filtering
    console.log('\n5️⃣ Testing project retrieval...');
    const { data: userProjects, error: retrieveError } = await supabase
      .from('projects')
      .select('*')
      .eq('user_id', realUserId);

    if (retrieveError) {
      console.log('❌ Project retrieval failed:', retrieveError.message);
      return false;
    }
    console.log(`✅ Retrieved ${userProjects.length} projects for user`);

    // Test 6: Complex queries
    console.log('\n6️⃣ Testing complex queries...');
    const { data: draftProjects, error: draftQueryError } = await supabase
      .from('projects')
      .select('*')
      .eq('user_id', realUserId)
      .eq('modification_type', 'replace_floor')
      .not('modification_options', 'is', null);

    if (draftQueryError) {
      console.log('❌ Complex query failed:', draftQueryError.message);
      return false;
    }
    console.log(`✅ Complex query returned ${draftProjects.length} results`);

    // Clean up
    console.log('\n🧹 Cleaning up test data...');
    await supabase.from('projects').delete().eq('id', draft.id);
    console.log('✅ Test data cleaned up');

    return true;

  } catch (error) {
    console.error('❌ Comprehensive test failed:', error);
    return false;
  }
}

async function main() {
  const status = await checkCurrentStatus();
  
  if (status.userIdFixed && status.draftFieldsFixed) {
    console.log('\n🎉 All database fixes are already applied!');
    
    const testPassed = await runComprehensiveTest();
    
    if (testPassed) {
      console.log('\n🎉 COMPLETE SUCCESS!');
      console.log('=====================================');
      console.log('✅ Database schema is fully compatible with Clerk');
      console.log('✅ All draft fields are working correctly');
      console.log('✅ Draft creation, update, and status changes work');
      console.log('✅ Project retrieval and complex queries work');
      console.log('✅ User management works with Clerk user IDs');
      console.log('\n🚀 Your application should now work perfectly!');
      console.log('🔄 Restart your server with: npm run dev');
      console.log('📝 Try creating a draft project in your application');
    } else {
      console.log('\n⚠️  Some tests failed - check the logs above');
    }
    
    process.exit(testPassed ? 0 : 1);
  } else {
    showManualInstructions(status);
    console.log('\n⏳ Please apply the manual fixes above and run this script again');
    process.exit(1);
  }
}

main();
