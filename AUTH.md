# Authentication in Renovision.Studio

## Authentication Architecture

Renovision.Studio uses **Clerk** exclusively for authentication. Supabase is used only as a database and not for authentication.

## Key Points

1. **Clerk Authentication**: All user authentication is handled through <PERSON>, including:
   - User sign-up and sign-in
   - Session management
   - JWT token generation and validation
   - User profile management

2. **Supabase as Database Only**:
   - Supabase is configured to use its database capabilities only
   - Supabase Auth is explicitly disabled in the configuration
   - All Supabase client instances have auth features disabled

## Implementation Details

### Frontend Authentication

The frontend uses Clerk's React components and hooks:
- `Clerk<PERSON>rovider` wraps the application in `main.tsx`
- `useAuth` hook from `@/hooks/use-clerk-auth.tsx` provides authentication state and methods
- `SignIn` component from <PERSON> is used for the sign-in page

### Backend Authentication

The backend uses Clerk's Node.js SDK:
- `clerkAuth` middleware in `server/clerk.ts` validates authentication tokens
- `setupClerkRoutes` in `server/clerk.ts` sets up authentication endpoints
- User data is fetched from <PERSON>'s API

### Supabase Configuration

Supabase is configured to disable authentication:
- `auth.enabled = false` in `supabase/config.toml`
- All Supabase client instances have auth disabled with:
  ```js
  auth: {
    persistSession: false,
    autoRefreshToken: false,
    detectSessionInUrl: false
  }
  ```

## Development Notes

When working on this project, remember:
1. Do not enable Supabase Auth
2. Use Clerk for all authentication needs
3. Use Supabase only for database operations
