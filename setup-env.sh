#!/bin/bash

# Function to print usage
print_usage() {
  echo "Usage: ./setup-env.sh [mode]"
  echo "Modes:"
  echo "  --safe     Safe setup with compatible versions (default)"
  echo "  --latest   Setup with latest versions"
  echo "  --dev      Development setup with specific versions"
}

# Default mode
MODE="safe"

# Parse arguments
while [[ "$#" -gt 0 ]]; do
  case $1 in
    --safe) MODE="safe" ;;
    --latest) MODE="latest" ;;
    --dev) MODE="dev" ;;
    -h|--help) print_usage; exit 0 ;;
    *) echo "Unknown parameter: $1"; print_usage; exit 1 ;;
  esac
  shift
done

echo "Setting up environment in $MODE mode..."

# Check if nvm is installed
if [ ! -d "$HOME/.nvm" ]; then
  echo "nvm is not installed. Installing nvm..."
  curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.40.0/install.sh | bash

  # Source nvm
  export NVM_DIR="$HOME/.nvm"
  [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
else
  echo "nvm is already installed."
  # Source nvm
  export NVM_DIR="$HOME/.nvm"
  [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
fi

# Install Node.js based on mode
case $MODE in
  "safe")
    echo "Installing Node.js v18 LTS..."
    nvm install 18
    nvm use 18
    ;;
  "latest")
    echo "Installing latest Node.js LTS..."
    nvm install --lts
    nvm use --lts
    ;;
  "dev")
    echo "Installing Node.js v22.14.0..."
    nvm install 22.14.0
    nvm use 22.14.0
    ;;
esac

# Update .nvmrc with the current Node.js version
NODE_VERSION=$(node -v)
echo "${NODE_VERSION:1}" > .nvmrc
echo "Updated .nvmrc to use Node.js ${NODE_VERSION:1}"

# Fix npm cache permissions if needed
if [ ! -w "$HOME/.npm" ]; then
  echo "Fixing npm cache permissions..."
  sudo chown -R $(whoami) ~/.npm
fi

# Clean npm cache
echo "Cleaning npm cache..."
npm cache clean --force

# Install dependencies based on mode
case $MODE in
  "safe")
    echo "Installing dependencies with compatible versions..."
    npm install --legacy-peer-deps
    ;;
  "latest")
    echo "Installing latest dependencies..."
    npx npm-check-updates -u
    npm install
    ;;
  "dev")
    echo "Installing specific development versions..."
    npm install --legacy-peer-deps
    ;;
esac

# Run fix scripts
echo "Running fix scripts..."
./fix-path-to-regexp-combined.sh
./fix-clerk-combined.sh
./fix-tailwind-setup.sh

echo "Environment setup complete!"
echo "You are now using Node.js $(node -v) with npm $(npm -v)"
echo "To activate this environment in a new terminal, run:"
echo "  source ~/.nvm/nvm.sh && nvm use" 