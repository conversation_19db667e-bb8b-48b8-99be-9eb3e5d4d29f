#!/bin/bash

# Replit deployment script to handle canvas dependency conflicts and platform-specific Rollup issues
echo "🚀 Starting Replit deployment..."

# Clean any existing installations
echo "🧹 Cleaning previous installations..."
rm -rf node_modules
rm -f package-lock.json

# Clear npm cache to prevent corrupted binaries
echo "🧽 Clearing npm cache..."
npm cache clean --force

# Install with flags to prevent platform-specific dependencies
echo "📦 Installing dependencies with platform compatibility..."
npm install --legacy-peer-deps --no-optional --target_platform=linux --target_arch=x64

# Install correct Rollup native binary for Linux x64 (Replit environment)
echo "🔧 Installing Linux-compatible Rollup binary..."
npm install @rollup/rollup-linux-x64-gnu --legacy-peer-deps --save-optional

# Build the application
echo "🔨 Building application..."
npm run build

echo "✅ Replit deployment complete!"
echo "📝 Canvas 3.1.0 and jest-environment-jsdom coexist via --legacy-peer-deps"
echo "🔧 Platform-specific Rollup dependencies resolved for Linux x64"
echo "🚫 macOS-specific dependencies excluded via --no-optional flag"
