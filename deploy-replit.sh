#!/bin/bash

# Enhanced Replit deployment script to handle npm CLI bug #4828
# Fixes Rollup native binary issues with comprehensive error handling
echo "🚀 Starting Enhanced Replit deployment..."

# Function to check if a package is installed
check_package_installed() {
    local package_name="$1"
    if npm list "$package_name" >/dev/null 2>&1; then
        echo "✅ $package_name is installed"
        return 0
    else
        echo "❌ $package_name is NOT installed"
        return 1
    fi
}

# Function to verify Rollup binary
verify_rollup_binary() {
    echo "🔍 Verifying Rollup native binary..."
    if [ -f "node_modules/@rollup/rollup-linux-x64-gnu/rollup.linux-x64-gnu.node" ]; then
        echo "✅ Rollup Linux x64 binary found"
        return 0
    else
        echo "❌ Rollup Linux x64 binary missing"
        return 1
    fi
}

# Clean any existing installations
echo "🧹 Cleaning previous installations..."
rm -rf node_modules
rm -f package-lock.json

# Clear npm cache to prevent corrupted binaries
echo "🧽 Clearing npm cache..."
npm cache clean --force

# PRIMARY SOLUTION: Use npm ci with --include=optional to fix npm CLI bug #4828
echo "📦 Installing dependencies with optional dependencies (PRIMARY FIX)..."
if [ -f "package-lock.json" ]; then
    # If package-lock.json exists, use npm ci with --include=optional
    npm ci --legacy-peer-deps --include=optional
else
    # If no package-lock.json, use npm install with --include=optional
    npm install --legacy-peer-deps --include=optional
fi

# Verify the primary solution worked
if verify_rollup_binary; then
    echo "🎉 PRIMARY SOLUTION SUCCESSFUL - Rollup binary installed via --include=optional"
else
    echo "⚠️ Primary solution failed, attempting SECONDARY SOLUTION..."

    # SECONDARY SOLUTION: Manual binary installation
    echo "🔧 Manually installing Linux-compatible Rollup binary..."
    npm install @rollup/rollup-linux-x64-gnu --legacy-peer-deps --save-optional

    # Verify secondary solution
    if verify_rollup_binary; then
        echo "🎉 SECONDARY SOLUTION SUCCESSFUL - Rollup binary manually installed"
    else
        echo "❌ BOTH SOLUTIONS FAILED - Manual intervention required"
        echo "📋 Troubleshooting steps:"
        echo "   1. Check if you're on a Linux x64 platform"
        echo "   2. Verify npm version (should be 8.x or higher)"
        echo "   3. Try: npm install rollup --force"
        echo "   4. Check for network connectivity issues"
        exit 1
    fi
fi

# Final verification before build
echo "🔍 Final package verification..."
check_package_installed "rollup"
check_package_installed "@rollup/rollup-linux-x64-gnu"

# Build the application
echo "🔨 Building application..."
if npm run build; then
    echo "✅ BUILD SUCCESSFUL!"
else
    echo "❌ BUILD FAILED - Check error messages above"
    exit 1
fi

echo "🎉 Enhanced Replit deployment complete!"
echo "📝 Canvas 3.1.0 and jest-environment-jsdom coexist via --legacy-peer-deps"
echo "🔧 Platform-specific Rollup dependencies resolved for Linux x64"
echo "🛠️ npm CLI bug #4828 workaround applied successfully"
