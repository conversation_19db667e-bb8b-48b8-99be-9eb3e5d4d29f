export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string // Changed from UUID to text to support Clerk IDs
          username: string
          email: string | null
          first_name: string | null
          last_name: string | null
          bio: string | null
          profile_image_url: string | null
          created_at: Date
          updated_at: Date
        }
        Insert: {
          id: string // Changed from UUID to text to support Clerk IDs
          username: string
          email?: string | null
          first_name?: string | null
          last_name?: string | null
          bio?: string | null
          profile_image_url?: string | null
          created_at?: Date
          updated_at?: Date
        }
      }
      projects: {
        Row: {
          id: number
          user_id: string // Changed from UUID to text to support Clerk IDs
          title: string
          description: string | null
          created_at: Date
          updated_at: Date
          status: string
          modification_description: string | null
          before_images: Json | null
          reference_images: Json | null
          step: number | null
          modification_type: string | null
          modification_options: Json | null
        }
        Insert: {
          user_id: string // Changed from UUID to text to support Clerk IDs
          title: string
          description?: string | null
          status?: string
          modification_description?: string | null
          before_images?: Json | null
          reference_images?: Json | null
          step?: number | null
          modification_type?: string | null
          modification_options?: Json | null
        }
      }
      images: {
        Row: {
          id: number
          project_id: number
          type: string
          path: string
          original_filename: string | null
          uploaded_at: Date
        }
        Insert: {
          project_id: number
          type: string
          path: string
          original_filename?: string | null
        }
      }
      modifications: {
        Row: {
          id: number
          project_id: number
          description: string
          reference_image_ids: number[] | null
          created_at: Date
        }
        Insert: {
          project_id: number
          description: string
          reference_image_ids?: number[] | null
        }
      }
      reference_categories: {
        Row: {
          id: number
          user_id: string // Changed from UUID to text to support Clerk IDs
          name: string
          description: string | null
          created_at: Date
        }
        Insert: {
          user_id: string // Changed from UUID to text to support Clerk IDs
          name: string
          description?: string | null
        }
      }
      reference_items: {
        Row: {
          id: number
          user_id: string // Changed from UUID to text to support Clerk IDs
          category_id: number
          name: string
          description: string | null
          image_path: string
          tags: string[] | null
          created_at: Date
        }
        Insert: {
          user_id: string // Changed from UUID to text to support Clerk IDs
          category_id: number
          name: string
          description?: string | null
          image_path: string
          tags?: string[] | null
        }
      }
      renovation_presets: {
        Row: {
          id: number
          name: string
          description: string | null
          room_type: string
          prompt_template: string
          is_default: boolean
          created_by: string | null // Changed from UUID to text to support Clerk IDs
          is_public: boolean
          created_at: Date
          tags: string[] | null
          image_url: string | null
        }
        Insert: {
          name: string
          description?: string | null
          room_type: string
          prompt_template: string
          is_default?: boolean
          created_by?: string | null // Changed from UUID to text to support Clerk IDs
          is_public?: boolean
          tags?: string[] | null
          image_url?: string | null
        }
      }
      drafts: {
        Row: {
          id: number
          user_id: string
          title: string | null
          description: string | null
          modification_description: string | null
          project_id: number | null
          before_images: Json | null
          reference_images: Json | null
          step: number | null
          modification_type: string | null
          modification_options: Json | null
          created_at: Date
          updated_at: Date
        }
        Insert: {
          user_id: string
          title?: string | null
          description?: string | null
          modification_description?: string | null
          project_id?: number | null
          before_images?: Json | null
          reference_images?: Json | null
          step?: number | null
          modification_type?: string | null
          modification_options?: Json | null
        }
      }
      subscriptions: {
        Row: {
          id: number
          user_id: string
          stripe_customer_id: string | null
          stripe_subscription_id: string | null
          plan_id: string
          status: string
          current_period_start: Date | null
          current_period_end: Date | null
          cancel_at_period_end: boolean
          billing_cycle: string
          coupon_redemption_id: number | null
          created_at: Date
          updated_at: Date
        }
        Insert: {
          user_id: string
          stripe_customer_id?: string | null
          stripe_subscription_id?: string | null
          plan_id: string
          status: string
          current_period_start?: Date | null
          current_period_end?: Date | null
          cancel_at_period_end?: boolean
          billing_cycle: string
          coupon_redemption_id?: number | null
          created_at?: Date
          updated_at?: Date
        }
      }
      usage: {
        Row: {
          id: number
          user_id: string
          projects_count: number
          images_count: number
          period_start: Date
          period_end: Date
          created_at: Date
          updated_at: Date
        }
        Insert: {
          user_id: string
          projects_count?: number
          images_count?: number
          period_start: Date
          period_end: Date
          created_at?: Date
          updated_at?: Date
        }
      }
      coupons: {
        Row: {
          id: number
          code: string
          stripe_coupon_id: string
          description: string | null
          type: string
          amount: number | null
          duration: string
          duration_in_months: number | null
          max_redemptions: number | null
          redemption_count: number
          valid_from: Date
          valid_until: Date | null
          is_active: boolean
          created_at: Date
          updated_at: Date
        }
        Insert: {
          code: string
          stripe_coupon_id: string
          description?: string | null
          type: string
          amount?: number | null
          duration: string
          duration_in_months?: number | null
          max_redemptions?: number | null
          redemption_count?: number
          valid_from: Date
          valid_until?: Date | null
          is_active?: boolean
          created_at?: Date
          updated_at?: Date
        }
      }
      coupon_redemptions: {
        Row: {
          id: number
          coupon_id: number
          user_id: string
          subscription_id: number | null
          checkout_session_id: string | null
          redeemed_at: Date
        }
        Insert: {
          coupon_id: number
          user_id: string
          subscription_id?: number | null
          checkout_session_id?: string | null
          redeemed_at?: Date
        }
      }
      contact_submissions: {
        Row: {
          id: number
          name: string
          email: string
          subject: string
          message: string
          created_at: Date
          status: string
        }
        Insert: {
          name: string
          email: string
          subject: string
          message: string
          status?: string
        }
      }
      sessions: {
        Row: {
          sid: string
          sess: any
          expire: Date
        }
        Insert: {
          sid: string
          sess: any
          expire: Date
        }
      }
      processed_webhook_events: {
        Row: {
          id: number
          event_id: string
          event_type: string
          processed_at: Date
          created_at: Date
        }
        Insert: {
          event_id: string
          event_type: string
          processed_at?: Date // Optional in type but has default value in database
          created_at?: Date // Optional in type but has default value in database
        }
      }
    }
  }
}