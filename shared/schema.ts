import { z } from "zod";
import { Database } from './types/supabase';

// Type definitions for Supabase tables
export type Tables = Database['public']['Tables'];
export type TablesInsert = {
  [K in keyof Tables]: Tables[K]['Insert']
};
export type TablesRow = {
  [K in keyof Tables]: Tables[K]['Row']
};

// Types for TypeScript
export type User = TablesRow['users'];
export type Project = TablesRow['projects'];
export type Image = TablesRow['images'];
export type Modification = TablesRow['modifications'];
export type ReferenceCategory = TablesRow['reference_categories'];
export type ReferenceItem = TablesRow['reference_items'];
export type RenovationPreset = TablesRow['renovation_presets'];
export type Draft = TablesRow['drafts'];
export type Subscription = TablesRow['subscriptions'];
export type Usage = TablesRow['usage'];
export type Coupon = TablesRow['coupons'];
export type CouponRedemption = TablesRow['coupon_redemptions'];
export type ContactSubmission = {
  id: number;
  name: string;
  email: string;
  subject: string;
  message: string;
  created_at: Date;
  status: string;
};
export type Session = {
  sid: string;
  sess: any;
  expire: Date;
};
export type ProcessedWebhookEvent = {
  id: number;
  event_id: string;
  event_type: string;
  processed_at: Date;
  created_at: Date;
};

export type InsertUser = TablesInsert['users'];
export type InsertProject = TablesInsert['projects'];
export type InsertImage = TablesInsert['images'];
export type InsertModification = TablesInsert['modifications'];
export type InsertReferenceCategory = TablesInsert['reference_categories'];
export type InsertReferenceItem = TablesInsert['reference_items'];
export type InsertRenovationPreset = TablesInsert['renovation_presets'];
export type InsertDraft = TablesInsert['drafts'];
export type InsertSubscription = TablesInsert['subscriptions'];
export type InsertUsage = TablesInsert['usage'];
export type InsertCoupon = TablesInsert['coupons'];
export type InsertCouponRedemption = TablesInsert['coupon_redemptions'];
export type InsertContactSubmission = {
  name: string;
  email: string;
  subject: string;
  message: string;
  status?: string;
};
export type InsertSession = {
  sid: string;
  sess: any;
  expire: Date;
};
export type InsertProcessedWebhookEvent = {
  event_id: string;
  event_type: string;
  processed_at?: Date; // Optional in type but defaults to current date in schema
  created_at?: Date; // Optional in type but defaults to current date in schema
};

// Insertion schemas for validation
export const insertUserSchema = z.object({
  id: z.string(), // Accept any string format for ID to support Clerk IDs
  username: z.string(),
  email: z.string().optional(),
  first_name: z.string().optional(),
  last_name: z.string().optional(),
  bio: z.string().optional(),
  profile_image_url: z.string().optional(),
  created_at: z.date().optional(),
  updated_at: z.date().optional(),
});

// Define the modification types
export const modificationType = z.enum([
  'custom',
  'replace_floor',
  'change_wall_color',
  'update_cabinets',
  'change_countertops',
  'kitchen_remodel',
  'bathroom_remodel',
  'interior_painting',
  'lighting_upgrades',
  'exterior_repainting'
]);

export type ModificationType = z.infer<typeof modificationType>;

export const insertProjectSchema = z.object({
  user_id: z.string(), // Accept any string format for user_id to support Clerk IDs
  title: z.string(),
  description: z.string().optional(),
  status: z.string().default("draft"),
  modification_description: z.string().optional(),
  before_images: z.any().optional(),
  reference_images: z.any().optional(),
  step: z.number().optional(),
  modification_type: modificationType.optional(),
  modification_options: z.any().optional(),
  session_id: z.string().optional(), // For draft deduplication using advisory locks
  session_created_at: z.date().optional(), // Timestamp for session-based cleanup
});

export const insertImageSchema = z.object({
  project_id: z.number(),
  type: z.string(),
  path: z.string(),
  original_filename: z.string().optional(),
});

// Define the options schema for structured modifications
export const modificationOptionsSchema = z.object({
  material: z.string().optional(),
  color: z.string().optional(),
  style: z.string().optional(),
  useReferenceImage: z.boolean().optional(),
  referenceImageId: z.number().optional(),
  customDescription: z.string().optional(),
}).catchall(z.any());

export const insertModificationSchema = z.object({
  project_id: z.number(),
  description: z.string(), // Required in the database
  reference_image_ids: z.array(z.number()).optional(),
  // Extended fields for application logic - not stored directly in the database
  type: modificationType.optional().default('custom'),
  primary_reference_image_id: z.number().optional(), // Specific reference image to use for the modification
  options: modificationOptionsSchema.optional(),
});

export const insertReferenceCategorySchema = z.object({
  user_id: z.string(), // Accept any string format for user_id to support Clerk IDs
  name: z.string(),
  description: z.string().optional(),
});

export const insertReferenceItemSchema = z.object({
  user_id: z.string(), // Accept any string format for user_id to support Clerk IDs
  category_id: z.number(),
  name: z.string(),
  description: z.string().optional(),
  image_path: z.string(),
  tags: z.array(z.string()).optional(),
});

export const insertRenovationPresetSchema = z.object({
  room_type: z.string(),
  name: z.string(),
  description: z.string().optional(),
  prompt_template: z.string(),
  created_by: z.string().optional(), // Accept any string format for created_by to support Clerk IDs
  is_default: z.boolean().optional(),
  is_public: z.boolean().optional(),
  tags: z.array(z.string()).optional(),
  image_url: z.string().optional(),
});

export const insertDraftSchema = z.object({
  user_id: z.string(),
  title: z.string().optional(),
  description: z.string().optional(),
  modification_description: z.string().optional(),
  modification_type: modificationType.optional(),
  modification_options: z.any().optional(),
  project_id: z.number().optional(),
  before_images: z.any().optional(),
  reference_images: z.any().optional(),
  step: z.number().optional(),
});

export const insertSubscriptionSchema = z.object({
  user_id: z.string(),
  stripe_customer_id: z.string().optional(),
  stripe_subscription_id: z.string().optional(),
  plan_id: z.string(),
  status: z.string(),
  current_period_start: z.date().optional(),
  current_period_end: z.date().optional(),
  cancel_at_period_end: z.boolean().optional().default(false),
  billing_cycle: z.string(),
  coupon_redemption_id: z.number().optional(),
  created_at: z.date().optional(),
  updated_at: z.date().optional(),
});

export const insertUsageSchema = z.object({
  user_id: z.string(),
  projects_count: z.number().optional().default(0),
  images_count: z.number().optional().default(0),
  period_start: z.date(),
  period_end: z.date(),
  created_at: z.date().optional(),
  updated_at: z.date().optional(),
});

export const insertCouponSchema = z.object({
  code: z.string(),
  stripe_coupon_id: z.string(),
  description: z.string().optional(),
  type: z.enum(['percentage', 'fixed_amount', 'free_months']),
  amount: z.number(), // Required in the database (NOT NULL constraint)
  duration: z.enum(['once', 'repeating', 'forever']),
  duration_in_months: z.number().optional(),
  max_redemptions: z.number().optional(),
  redemption_count: z.number().optional().default(0),
  valid_from: z.date(),
  valid_until: z.date().optional(),
  is_active: z.boolean().optional().default(true),
  created_at: z.date().optional(),
  updated_at: z.date().optional(),
});

export const insertCouponRedemptionSchema = z.object({
  coupon_id: z.number(),
  user_id: z.string(),
  subscription_id: z.number().optional(),
  checkout_session_id: z.string().optional(),
  redeemed_at: z.date().optional(),
});

export const insertContactSubmissionSchema = z.object({
  name: z.string().min(2),
  email: z.string().email(),
  subject: z.string().min(5),
  message: z.string().min(10),
  status: z.string().optional().default('new'),
});

export const insertSessionSchema = z.object({
  sid: z.string(),
  sess: z.any(),
  expire: z.date(),
});

export const insertProcessedWebhookEventSchema = z.object({
  event_id: z.string(),
  event_type: z.string(),
  processed_at: z.date().default(() => new Date()), // Required in the database (NOT NULL constraint)
  created_at: z.date().default(() => new Date()), // Required in the database (NOT NULL constraint)
});

// Extended types for UI
export type ProjectWithImages = Project & {
  beforeImages: Image[];
  afterImages: Image[];
  referenceImages: Image[];
  modifications: Modification[];
};

export type ReferenceCategoryWithItems = ReferenceCategory & {
  items: ReferenceItem[];
};
