-- Add fields from drafts table to projects table
ALTER TABLE public.projects
ADD COLUMN IF NOT EXISTS modification_description TEXT,
ADD COLUMN IF NOT EXISTS before_images JSONB,
ADD COLUMN IF NOT EXISTS reference_images JSONB,
ADD COLUMN IF NOT EXISTS step INTEGER,
ADD COLUMN IF NOT EXISTS modification_type TEXT,
ADD COLUMN IF NOT EXISTS modification_options JSONB,
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMPTZ DEFAULT NOW();

-- Create a function to migrate existing drafts to projects
CREATE OR REPLACE FUNCTION migrate_drafts_to_projects()
RETURNS void AS $$
DECLARE
    draft_record RECORD;
BEGIN
    -- Loop through all drafts
    FOR draft_record IN SELECT * FROM public.drafts LOOP
        -- If the draft has a project_id, update that project
        IF draft_record.project_id IS NOT NULL THEN
            UPDATE public.projects
            SET
                title = COALESCE(draft_record.title, title),
                description = COALESCE(draft_record.description, description),
                modification_description = draft_record.modification_description,
                before_images = draft_record.before_images,
                reference_images = draft_record.reference_images,
                step = draft_record.step,
                modification_type = draft_record.modification_type,
                modification_options = draft_record.modification_options,
                status = 'draft',
                updated_at = NOW()
            WHERE id = draft_record.project_id;
        -- If the draft doesn't have a project_id, create a new project
        ELSE
            INSERT INTO public.projects (
                user_id,
                title,
                description,
                modification_description,
                before_images,
                reference_images,
                step,
                modification_type,
                modification_options,
                status,
                created_at,
                updated_at
            ) VALUES (
                draft_record.user_id,
                COALESCE(draft_record.title, 'Untitled Project'),
                draft_record.description,
                draft_record.modification_description,
                draft_record.before_images,
                draft_record.reference_images,
                draft_record.step,
                draft_record.modification_type,
                draft_record.modification_options,
                'draft',
                draft_record.created_at,
                draft_record.updated_at
            );
        END IF;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Execute the migration function
SELECT migrate_drafts_to_projects();

-- Drop the migration function after use
DROP FUNCTION migrate_drafts_to_projects();

-- Note: We're not dropping the drafts table yet to ensure a smooth transition
-- A separate migration will be created later to drop the drafts table after
-- confirming the migration was successful
