-- Create stored procedures for test setup

-- Create subscriptions table for tests
CREATE OR REPLACE FUNCTION public.create_subscriptions_table()
RETURNS void
LANGUAGE plpgsql
AS $$
BEGIN
  -- Create subscriptions table if it doesn't exist
  CREATE TABLE IF NOT EXISTS public.subscriptions (
    id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
    user_id TEXT NOT NULL,
    stripe_customer_id TEXT,
    stripe_subscription_id TEXT,
    plan_id TEXT NOT NULL, -- 'starter', 'professional', etc.
    status TEXT NOT NULL, -- 'active', 'canceled', 'past_due', etc.
    current_period_start TIMESTAMPTZ,
    current_period_end TIMESTAMPTZ,
    cancel_at_period_end BOOLEAN DEFAULT FALSE,
    billing_cycle TEXT NOT NULL, -- 'monthly', 'annual'
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id) -- One active subscription per user
  );

  -- Create RLS policies for subscriptions table
  ALTER TABLE public.subscriptions ENABLE ROW LEVEL SECURITY;

  -- Create policy to allow users to read their own subscriptions
  DROP POLICY IF EXISTS subscriptions_select_policy ON public.subscriptions;
  CREATE POLICY subscriptions_select_policy ON public.subscriptions
    FOR SELECT
    USING (user_id = auth.uid());

  -- Create policy to allow users to update their own subscriptions
  DROP POLICY IF EXISTS subscriptions_update_policy ON public.subscriptions;
  CREATE POLICY subscriptions_update_policy ON public.subscriptions
    FOR UPDATE
    USING (user_id = auth.uid());

  -- Create policy to allow users to delete their own subscriptions
  DROP POLICY IF EXISTS subscriptions_delete_policy ON public.subscriptions;
  CREATE POLICY subscriptions_delete_policy ON public.subscriptions
    FOR DELETE
    USING (user_id = auth.uid());

  -- Create policy to allow users to insert their own subscriptions
  DROP POLICY IF EXISTS subscriptions_insert_policy ON public.subscriptions;
  CREATE POLICY subscriptions_insert_policy ON public.subscriptions
    FOR INSERT
    WITH CHECK (user_id = auth.uid());
END;
$$;

-- Create usage table for tests
CREATE OR REPLACE FUNCTION public.create_usage_table()
RETURNS void
LANGUAGE plpgsql
AS $$
BEGIN
  -- Create usage table if it doesn't exist
  CREATE TABLE IF NOT EXISTS public.usage (
    id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
    user_id TEXT NOT NULL,
    projects_count INTEGER DEFAULT 0,
    images_count INTEGER DEFAULT 0,
    period_start TIMESTAMPTZ,
    period_end TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id, period_start, period_end) -- One usage record per user per period
  );

  -- Create RLS policies for usage table
  ALTER TABLE public.usage ENABLE ROW LEVEL SECURITY;

  -- Create policy to allow users to read their own usage
  DROP POLICY IF EXISTS usage_select_policy ON public.usage;
  CREATE POLICY usage_select_policy ON public.usage
    FOR SELECT
    USING (user_id = auth.uid());

  -- Create policy to allow users to update their own usage
  DROP POLICY IF EXISTS usage_update_policy ON public.usage;
  CREATE POLICY usage_update_policy ON public.usage
    FOR UPDATE
    USING (user_id = auth.uid());

  -- Create policy to allow users to delete their own usage
  DROP POLICY IF EXISTS usage_delete_policy ON public.usage;
  CREATE POLICY usage_delete_policy ON public.usage
    FOR DELETE
    USING (user_id = auth.uid());

  -- Create policy to allow users to insert their own usage
  DROP POLICY IF EXISTS usage_insert_policy ON public.usage;
  CREATE POLICY usage_insert_policy ON public.usage
    FOR INSERT
    WITH CHECK (user_id = auth.uid());
END;
$$;

-- Create processed_webhook_events table for tests
CREATE OR REPLACE FUNCTION public.create_processed_webhook_events_table()
RETURNS void
LANGUAGE plpgsql
AS $$
BEGIN
  -- Create processed_webhook_events table if it doesn't exist
  CREATE TABLE IF NOT EXISTS public.processed_webhook_events (
    id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
    event_id TEXT NOT NULL UNIQUE,
    event_type TEXT NOT NULL,
    processed_at TIMESTAMPTZ DEFAULT NOW(),
    created_at TIMESTAMPTZ DEFAULT NOW()
  );

  -- Create index on event_id for faster lookups
  CREATE INDEX IF NOT EXISTS processed_webhook_events_event_id_idx ON public.processed_webhook_events(event_id);
END;
$$;
