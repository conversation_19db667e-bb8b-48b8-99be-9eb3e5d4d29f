-- Create coupons table
CREATE TABLE IF NOT EXISTS public.coupons (
    id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
    code TEXT NOT NULL UNIQUE,
    stripe_coupon_id TEXT NOT NULL,
    description TEXT,
    type TEXT NOT NULL, -- 'percentage', 'fixed_amount', 'free_months'
    amount DECIMAL, -- Percentage or fixed amount
    duration TEXT NOT NULL, -- 'once', 'repeating', 'forever'
    duration_in_months INTEGER, -- For 'repeating' duration
    max_redemptions INTEGER, -- Maximum number of times this coupon can be redeemed
    redemption_count INTEGER DEFAULT 0, -- Number of times this coupon has been redeemed
    valid_from TIMESTAMPTZ NOT NULL,
    valid_until TIMESTAMPTZ, -- NULL means no expiration
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create coupon_redemptions table to track which users have used which coupons
CREATE TABLE IF NOT EXISTS public.coupon_redemptions (
    id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
    coupon_id BIGINT NOT NULL REFERENCES public.coupons(id) ON DELETE CASCADE,
    user_id TEXT NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    subscription_id BIGINT REFERENCES public.subscriptions(id) ON DELETE SET NULL,
    checkout_session_id TEXT, -- Stripe checkout session ID
    redeemed_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(coupon_id, user_id) -- Each user can only redeem a specific coupon once
);

-- Enable Row Level Security (RLS) on new tables
ALTER TABLE public.coupons ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.coupon_redemptions ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Coupons can be read by anyone but only managed by service role
CREATE POLICY "Coupons can be read by anyone" ON public.coupons
    FOR SELECT USING (true);

CREATE POLICY "Coupons can only be managed by service role" ON public.coupons
    FOR ALL USING (auth.role() = 'service_role');

-- Coupon redemptions can be read by the user who redeemed them
CREATE POLICY "Coupon redemptions can be read by the user who redeemed them" ON public.coupon_redemptions
    FOR SELECT USING (auth.uid() = user_id);

-- Coupon redemptions can only be managed by service role
CREATE POLICY "Coupon redemptions can only be managed by service role" ON public.coupon_redemptions
    FOR ALL USING (auth.role() = 'service_role');

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_coupons_code ON public.coupons(code);
CREATE INDEX IF NOT EXISTS idx_coupons_stripe_coupon_id ON public.coupons(stripe_coupon_id);
CREATE INDEX IF NOT EXISTS idx_coupons_valid_dates ON public.coupons(valid_from, valid_until);
CREATE INDEX IF NOT EXISTS idx_coupons_is_active ON public.coupons(is_active);
CREATE INDEX IF NOT EXISTS idx_coupon_redemptions_coupon_id ON public.coupon_redemptions(coupon_id);
CREATE INDEX IF NOT EXISTS idx_coupon_redemptions_user_id ON public.coupon_redemptions(user_id);
