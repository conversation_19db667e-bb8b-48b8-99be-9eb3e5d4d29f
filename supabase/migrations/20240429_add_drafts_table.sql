-- Create drafts table
CREATE TABLE IF NOT EXISTS public.drafts (
    id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
    user_id TEXT NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    title TEXT,
    description TEXT,
    modification_description TEXT,
    project_id BIGINT REFERENCES public.projects(id) ON DELETE CASCADE,
    before_images JSONB,
    reference_images JSONB,
    step INTEGER,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable Row Level Security (RLS) on drafts table
ALTER TABLE public.drafts ENABLE ROW LEVEL SECURITY;

-- Create RLS policy for drafts
CREATE POLICY "Drafts are managed by owner" ON public.drafts
    FOR ALL USING (auth.uid() = user_id);

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_drafts_user_id ON public.drafts(user_id);
