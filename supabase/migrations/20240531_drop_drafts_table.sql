-- This migration will drop the drafts table after confirming all data has been migrated
-- to the projects table. This should only be run after verifying the migration was successful.

-- First, check if there are any drafts that haven't been migrated
DO $$
DECLARE
    draft_count INTEGER;
BEGIN
    -- Count drafts that don't have a corresponding project
    SELECT COUNT(*) INTO draft_count
    FROM public.drafts d
    WHERE d.project_id IS NULL
    AND NOT EXISTS (
        SELECT 1 FROM public.projects p
        WHERE p.title = COALESCE(d.title, 'Untitled Project')
        AND p.user_id = d.user_id
        AND p.status = 'draft'
    );

    -- If there are still drafts that haven't been migrated, raise an exception
    IF draft_count > 0 THEN
        RAISE EXCEPTION 'There are still % drafts that have not been migrated to projects. Please run the migration script again.', draft_count;
    END IF;
END $$;

-- If we get here, all drafts have been migrated, so we can drop the table
DROP TABLE IF EXISTS public.drafts;
