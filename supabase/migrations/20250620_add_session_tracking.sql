-- Add session_id to projects table for duplicate prevention
-- and create index for efficient lookups

-- Add session_id column to projects table
ALTER TABLE projects ADD COLUMN session_id TEXT;

-- Create partial unique index to prevent duplicate drafts within same session
-- This allows multiple projects but prevents duplicate drafts from same client session
CREATE UNIQUE INDEX idx_projects_unique_session_draft 
ON projects (user_id, session_id, status) 
WHERE status = 'draft' AND session_id IS NOT NULL;

-- Create index for efficient session_id lookups
CREATE INDEX idx_projects_session_id ON projects (session_id) 
WHERE session_id IS NOT NULL;

-- Add timestamp for session-based cleanup
ALTER TABLE projects ADD COLUMN session_created_at TIMESTAMPTZ DEFAULT NOW();

-- Create index for efficient session cleanup
CREATE INDEX idx_projects_session_cleanup ON projects (session_created_at) 
WHERE status = 'draft';