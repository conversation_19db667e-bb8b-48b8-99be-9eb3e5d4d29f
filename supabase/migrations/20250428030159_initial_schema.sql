-- Create users table
CREATE TABLE IF NOT EXISTS public.users (
    id UUID PRIMARY KEY,
    username TEXT NOT NULL,
    email TEXT,
    first_name TEXT,
    last_name TEXT,
    bio TEXT,
    profile_image_url TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create projects table
CREATE TABLE IF NOT EXISTS public.projects (
    id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    description TEXT,
    status TEXT DEFAULT 'draft',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create images table
CREATE TABLE IF NOT EXISTS public.images (
    id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
    project_id BIGINT NOT NULL REFERENCES public.projects(id) ON DELETE CASCADE,
    type TEXT NOT NULL,
    path TEXT NOT NULL,
    original_filename TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create modifications table
CREATE TABLE IF NOT EXISTS public.modifications (
    id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
    project_id BIGINT NOT NULL REFERENCES public.projects(id) ON DELETE CASCADE,
    description TEXT NOT NULL,
    reference_image_ids BIGINT[],
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create reference_categories table
CREATE TABLE IF NOT EXISTS public.reference_categories (
    id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create reference_items table
CREATE TABLE IF NOT EXISTS public.reference_items (
    id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    category_id BIGINT NOT NULL REFERENCES public.reference_categories(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    image_path TEXT NOT NULL,
    tags TEXT[],
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create renovation_presets table
CREATE TABLE IF NOT EXISTS public.renovation_presets (
    id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    modifications TEXT[],
    reference_image_ids BIGINT[],
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable Row Level Security (RLS) on all tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.images ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.modifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.reference_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.reference_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.renovation_presets ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Users policies
CREATE POLICY "Users can read own data" ON public.users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own data" ON public.users
    FOR UPDATE USING (auth.uid() = id);

-- Projects policies
CREATE POLICY "Projects are managed by owner" ON public.projects
    FOR ALL USING (auth.uid() = user_id);

-- Images policies
CREATE POLICY "Images are managed by project owner" ON public.images
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.projects
            WHERE id = images.project_id
            AND user_id = auth.uid()
        )
    );

-- Modifications policies
CREATE POLICY "Modifications are managed by project owner" ON public.modifications
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.projects
            WHERE id = modifications.project_id
            AND user_id = auth.uid()
        )
    );

-- Reference categories policies
CREATE POLICY "Reference categories are managed by owner" ON public.reference_categories
    FOR ALL USING (auth.uid() = user_id);

-- Reference items policies
CREATE POLICY "Reference items are managed by owner" ON public.reference_items
    FOR ALL USING (auth.uid() = user_id);

-- Renovation presets policies
CREATE POLICY "Renovation presets are managed by owner" ON public.renovation_presets
    FOR ALL USING (auth.uid() = user_id);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_projects_user_id ON public.projects(user_id);
CREATE INDEX IF NOT EXISTS idx_images_project_id ON public.images(project_id);
CREATE INDEX IF NOT EXISTS idx_modifications_project_id ON public.modifications(project_id);
CREATE INDEX IF NOT EXISTS idx_reference_items_category_id ON public.reference_items(category_id);
CREATE INDEX IF NOT EXISTS idx_reference_items_user_id ON public.reference_items(user_id);
CREATE INDEX IF NOT EXISTS idx_reference_categories_user_id ON public.reference_categories(user_id);
CREATE INDEX IF NOT EXISTS idx_renovation_presets_user_id ON public.renovation_presets(user_id);
