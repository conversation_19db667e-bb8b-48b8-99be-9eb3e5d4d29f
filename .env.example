# Renovision Studio Environment Variables Template
# Copy this file to .env and fill in your actual values
# For Replit deployment, use Replit Secrets instead of this file

# Server Configuration
PORT=3000
NODE_ENV="production"
SESSION_SECRET="your-session-secret-here"

# Debug Configuration
# Set to 'true' to enable all debugging, 'false' to disable all debugging
# You can also set specific debug categories: 'api,auth,db,ui,all'
DEBUG_MODE="false"

# Email Configuration
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"
EMAIL_FROM="<EMAIL>"
CONTACT_EMAIL="<EMAIL>"

# Supabase Configuration
# SECURITY: Use Replit Secrets for production deployment
SUPABASE_URL="your-supabase-url"
SUPABASE_DB_KEY="your-supabase-service-role-key"
SUPABASE_KEY="your-supabase-service-role-key"

# Client Configuration (Vite)
VITE_SUPABASE_URL="your-supabase-url"
VITE_SUPABASE_ANON_KEY="your-supabase-anon-key"
VITE_ENABLE_AUTH_DEBUG="false"
VITE_STRIPE_PUBLISHABLE_KEY="your-stripe-publishable-key"

# AI Services
# SECURITY: Use Replit Secrets for production deployment
OPENAI_API_KEY="your-openai-api-key"
GEMINI_API_KEY=""

# Stripe Configuration
# SECURITY: Use Replit Secrets for production deployment
STRIPE_SECRET_KEY="your-stripe-secret-key"
STRIPE_PUBLISHABLE_KEY="your-stripe-publishable-key"
STRIPE_WEBHOOK_SECRET="your-stripe-webhook-secret"

# Clerk Configuration
# SECURITY: Use Replit Secrets for production deployment
VITE_CLERK_PUBLISHABLE_KEY="your-clerk-publishable-key"
CLERK_SECRET_KEY="your-clerk-secret-key"

# Google Analytics Configuration
# SECURITY: Use Replit Secrets for production deployment
VITE_GA4_MEASUREMENT_ID="your-ga4-measurement-id"
VITE_GA4_DEBUG_MODE="false"
