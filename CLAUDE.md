# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

**Setup and Installation:**
- `npm run setup` - Default safe setup with compatible versions
- `npm run setup:safe` - Safe setup with compatible versions  
- `npm run setup:latest` - Setup with latest versions
- `npm run setup:dev` - Development setup with specific versions

**Development:**
- `npm run dev` - Start development server (NODE_ENV=development)
- `npm run dev:prod` - Start development server with production environment
- `npm run build` - Build the application (client + server bundle)
- `npm run start` - Start production server
- `npm run check` - TypeScript type checking

**Testing:**
- `npm test` - Run comprehensive test suite (all tests)
- `npm run test:api` - Run API tests only
- `npm run test:e2e` - Run end-to-end tests with Playwright
- `npm run test:subscription` - Run subscription-related tests (API + E2E)
- `npm run test:stripe-webhooks` - Run Stripe webhook tests
- `npm run test:visual` - Run visual regression tests
- `npm run test:coupon` - Run coupon-related tests
- `npm run test:component` - Run React component tests

**Fixing Common Issues:**
- `npm run fix-path-to-regexp` - Fix path-to-regexp compatibility issues
- `npm run fix-clerk` - Fix Clerk SDK setup issues  
- `npm run fix-tailwind` - Fix Tailwind CSS configuration
- `npm run fix-jest-deps` - Fix Jest dependencies

## Architecture Overview

**Monorepo Structure:**
- `client/` - React frontend with Vite
- `server/` - Express.js backend with TypeScript
- `shared/` - Shared types and schemas (Zod)
- `tests/` - Comprehensive test suite
- `supabase/` - Database migrations and config

**Frontend Architecture:**
- **Framework:** React 18 with TypeScript, Vite for bundling
- **Routing:** Wouter for client-side routing
- **UI:** Radix UI components with Tailwind CSS and shadcn/ui patterns
- **State:** React Query for server state, React Context for app state
- **Auth:** Clerk for authentication with custom auth wrapper
- **Analytics:** Google Analytics 4 integration
- **Payments:** Stripe integration with React Stripe.js

**Backend Architecture:**
- **Framework:** Express.js with TypeScript (ES modules)
- **Database:** Supabase (PostgreSQL) with typed client
- **Auth Middleware:** Clerk Express middleware
- **APIs:** OpenAI for image generation, Stripe for payments
- **File Storage:** Multer for image uploads, local storage in uploads/
- **Business Logic:** Subscription management, usage tracking, coupon system

**Key Patterns:**
- **Schema Validation:** Zod schemas in `shared/schema.ts` used across frontend/backend
- **Error Handling:** Centralized error handler with typed error responses
- **Authentication:** Clerk tokens stored in localStorage, middleware validates requests
- **Subscription System:** Plan limits enforced via middleware, usage tracking per user
- **Type Safety:** Generated Supabase types, shared schemas ensure consistency
- **Draft Deduplication:** PostgreSQL advisory locks prevent duplicate draft creation
- **Atomic Operations:** Database-level atomicity ensures data consistency

**Database Design:**
- Users, projects, images, modifications, drafts
- Subscription system with plans, usage tracking, coupons
- Reference library with categories and items
- Renovation presets for quick project setup
- Draft deduplication via session tracking and atomic operations

**File Structure Patterns:**
- `client/src/components/` - Reusable React components
- `client/src/pages/` - Page-level components
- `client/src/hooks/` - Custom React hooks
- `client/src/lib/` - Client utilities (API client, Stripe, etc.)
- `server/routes/` - API route handlers  
- `server/services/` - Business logic services
- `server/middleware/` - Express middleware
- `server/utils/` - Backend utilities

**Testing Strategy:**
- **Unit Tests:** Jest for API endpoints and services
- **Integration Tests:** Database operations with test fixtures
- **E2E Tests:** Playwright for user workflows
- **Visual Regression:** Screenshot comparison tests
- **Mocking:** Comprehensive mocks for external APIs (OpenAI, Stripe)

## Required Environment Variables

The following environment variables are required:
- `SUPABASE_URL`, `SUPABASE_KEY` - Database connection
- `CLERK_SECRET_KEY`, `VITE_CLERK_PUBLISHABLE_KEY` - Authentication
- `STRIPE_SECRET_KEY`, `STRIPE_PUBLISHABLE_KEY` - Payments
- `OPENAI_API_KEY` - AI image generation
- `VITE_GA4_MEASUREMENT_ID` - Google Analytics (optional)

## Development Notes

**Path Aliases:**
- `@/` maps to `client/src/`
- `@shared/` maps to `shared/`
- `@assets/` maps to `attached_assets/`

**Common Fixes:**
The project includes several fix scripts for known compatibility issues with npm packages and SDK configurations. Run the appropriate fix script if you encounter build issues.

**Test Environment:**
Tests require proper environment setup. The test suite automatically sets up fixtures and validates environment variables before running.

## Draft Deduplication System

The application implements a robust draft deduplication system to prevent users from accidentally creating duplicate drafts when rapidly clicking or due to network issues.

**Implementation:**
- **PostgreSQL Advisory Locks:** Uses database-level advisory locks for atomic "find or create" operations
- **Session-Based Tracking:** Each client session has a unique ID that prevents duplicate drafts
- **Multiple Fallback Layers:** Graceful degradation if advisory locks are unavailable
- **Zero Race Conditions:** Mathematically impossible to create duplicate drafts

**Key Features:**
- **Atomic Operations:** Only one draft can be created per session across all server instances
- **Cross-Instance Safe:** Works correctly with horizontal scaling
- **Performance Optimized:** Locks held for microseconds only
- **Error Resilient:** Comprehensive fallback mechanisms ensure service continuity

**Technical Details:**
- Lock ID generated from: `MD5(draft_${userId}_${sessionId})`
- Advisory lock functions: `pg_try_advisory_lock` and `pg_advisory_unlock`
- Fallback: Time-based deduplication (10-second window)
- Returns existing draft with 200 status instead of creating duplicates

**Location:** `server/routes.ts` - POST `/api/drafts` endpoint