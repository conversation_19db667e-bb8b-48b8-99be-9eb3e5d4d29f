# System Patterns

## System Architecture
- **Monorepo structure:** Separate `client/` (React frontend), `server/` (Express backend), and `shared/` (types, schemas)
- **API-first:** All client-server communication via RESTful API endpoints
- **Authentication middleware:** Clerk for authentication, Supabase used only as a database
- **AI as a service:** Image generation handled by external APIs (Gemini, OpenAI)
- **File uploads:** Handled via multer, stored locally, paths referenced in DB
- **Payments:** Stripe endpoints and webhooks for checkout and payment status
- **Reference Library:** CRUD for categories/items, linked to user and projects
- **Presets:** Renovation presets for common scenarios, customizable by users

## Key Technical Decisions
- **Type safety:** Shared types and validation schemas (Zod, Drizzle ORM) across client and server
- **React Query:** Used for all data fetching and mutation on the frontend
- **Radix UI:** For accessible, composable UI primitives
- **Wouter:** Lightweight routing for SPA navigation
- **Detailed logging:** Required for all external and internal operations, especially API calls and file uploads
- **Structured logging:** All logs use structured loggers with context and tags (e.g., [API], [UPLOAD], [SCHEMA])
- **Error handling:** All async actions are wrapped in try/catch, errors are logged with context and surfaced to users via toasts or structured responses
- **User feedback:** All major user actions and errors trigger clear, actionable feedback (toasts, loading indicators, error messages)
- **Polling:** Project status is polled for updates (e.g., image generation progress)

## Design Patterns
- **Separation of concerns:** Clear split between UI, API, business logic, and data models
- **Hooks:** Custom React hooks for auth, dialogs, toasts, etc.
- **Component composition:** UI built from reusable, composable components (Radix, custom UI)
- **Mutation/Query separation:** All data mutations and queries handled via React Query
- **Schema-driven validation:** Zod schemas for all user input and API payloads

## Component Relationships
- **Dashboard:** Entry point, lists projects, links to creation and gallery
- **Project:** Shows project details, images, status, and results
- **CreateVisualization:** Multi-step workflow for new projects (details, images, references, submit)
- **ReferenceLibrary:** Manages categories/items for style/material references
- **RenovationPresets:** Manages and applies preset templates
- **Gallery:** Displays completed projects
- **Settings/Profile:** User account management