# Tech Context

## Technologies Used
- **Frontend:** <PERSON><PERSON> (TypeScript), Vite, Tai<PERSON>wind CSS, Radix UI, TanStack React Query, Wouter (routing)
- **Backend:** Node.js, Express, TypeScript
- **Database:** PostgreSQL (via Drizzle ORM)
- **Authentication:** <PERSON> (Supabase used only as database)
- **AI Integration:** Gemini API, OpenAI API
- **Payments:** Stripe
- **File Storage:** Local uploads directory (multer), with image management
- **Other:** <PERSON><PERSON> (validation), Drizzle-Zod, <PERSON>lter (file uploads), Framer Motion, Lucide React (icons)

## Development Setup
- Uses Vite for frontend dev/build
- Uses tsx for backend dev
- Scripts for DB migration (drizzle-kit), auth checks, and build
- All dependencies managed via package.json
- TypeScript throughout (client, server, shared)
- Shared types and schemas in `shared/schema.ts`

## Technical Constraints
- Image uploads limited to 10MB per file, JPEG/PNG/WEBP only
- AI image generation depends on external APIs (Gemini, OpenAI)
- Payments require valid Stripe configuration
- Auth requires Clerk setup
- Logging required for all external and internal operations

## Dependencies
- See package.json for full list
- Key libraries: React, Express, Drizzle ORM, Clerk, Supabase, Stripe, OpenAI, Gemini, Zod, Multer, Tailwind, Radix UI, React Query