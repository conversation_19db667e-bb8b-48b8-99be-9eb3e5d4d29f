# .cursorrules

## Critical Implementation Paths
- All new features and changes must be reflected in the memory bank
- Logging is required for all external and internal operations (API, uploads, payments, auth)
- Shared types and validation schemas must be used for all data boundaries
- clerkAuth middleware must be applied to all sensitive (POST, PATCH, DELETE) endpoints
- All endpoints must accept and return JSON, with Content-Type: application/json
- All errors must be logged and returned in a consistent JSON format
- Plan for API versioning to support future changes
- CORS and rate limiting must be documented and configured as needed
- Automated tests required for all API endpoints

## User Workflow Preferences
- Simple, step-by-step project creation and visualization
- Easy access to reference materials and presets
- Clear feedback and progress indicators for long-running operations

## Project-Specific Patterns
- Use React Query for all data fetching/mutation in the frontend
- Use Zod and Drizzle ORM for schema validation and type safety
- Use Clerk and Supabase for authentication and session management
- Use Stripe for payment flows
- Use polling for project status updates (AI image generation)
- Use structured loggers (logger, backendLogger, scriptLogger) for all logging with context and tags
- All async actions must be wrapped in try/catch, with errors logged and surfaced to users via toasts or structured responses
- All major user actions and errors must trigger clear, actionable feedback (toasts, loading indicators, error messages)

## Known Challenges
- Ensuring logging is present and detailed for all operations
- Keeping documentation and memory bank in sync with codebase changes
- Managing async AI operations and user feedback
- Ensuring CORS and rate limiting are properly configured
- Planning and implementing API versioning
- Maintaining automated test coverage for endpoints

## Evolution of Project Decisions
- Adopted memory bank for all documentation and context
- Prioritized logging and traceability for all features
- Committed to type safety and schema-driven development
- Adopted REST API best practices and security improvements

## Tool Usage Patterns
- Vite for frontend dev/build
- tsx for backend dev
- drizzle-kit for DB migrations
- React Query Devtools for debugging 