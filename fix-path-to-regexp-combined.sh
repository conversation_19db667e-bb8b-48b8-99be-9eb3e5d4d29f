#!/bin/bash

echo "Fixing path-to-regexp issues..."

# Clean npm cache and remove node_modules
echo "Cleaning npm cache and removing node_modules..."
npm cache clean --force
rm -rf node_modules package-lock.json

# Install specific version of path-to-regexp
echo "Installing specific version of path-to-regexp..."
npm install path-to-regexp@6.2.1 --save

# Fix express version to ensure compatibility
echo "Installing compatible express version..."
npm install express@4.18.2 --save

# Clean install dependencies
echo "Performing clean install..."
npm install --legacy-peer-deps

echo "path-to-regexp fixes completed!" 