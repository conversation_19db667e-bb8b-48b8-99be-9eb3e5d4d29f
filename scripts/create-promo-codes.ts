#!/usr/bin/env tsx

import 'dotenv/config';
import { createClient } from '@supabase/supabase-js';
import Stripe from 'stripe';

// Initialize Stripe
if (!process.env.STRIPE_SECRET_KEY) {
  console.error('❌ Missing STRIPE_SECRET_KEY environment variable');
  process.exit(1);
}

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: '2024-06-20',
});

// Initialize Supabase
const supabaseUrl = process.env.SUPABASE_URL as string;
const supabaseKey = process.env.SUPABASE_DB_KEY as string;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase configuration. Check environment variables.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    persistSession: false,
    autoRefreshToken: false,
    detectSessionInUrl: false
  }
});

// Generate unique promo codes
function generatePromoCodes(count: number): string[] {
  const codes: string[] = [];
  const prefixes = ['WELCOME', 'TRYIT', 'FREE', 'START', 'EXPLORE'];
  const suffixes = ['2025', '100', 'NOW', 'FREE', 'MONTH'];
  
  for (let i = 0; i < count; i++) {
    const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];
    const suffix = suffixes[Math.floor(Math.random() * suffixes.length)];
    const randomNum = Math.floor(Math.random() * 999) + 1;
    const code = `${prefix}${randomNum}${suffix}`;
    
    // Ensure uniqueness
    if (!codes.includes(code)) {
      codes.push(code);
    } else {
      i--; // Try again if duplicate
    }
  }
  
  return codes;
}

async function createPromoCode(code: string): Promise<boolean> {
  try {
    console.log(`🎫 Creating promo code: ${code}`);

    // Create coupon in Stripe first - using fixed amount for exactly one month's discount
    // Using $50 AUD which covers most of Professional monthly ($58.80) and more than covers Starter ($17.99)
    const discountAmount = 50.00; // $50 AUD

    const stripeCoupon = await stripe.coupons.create({
      id: code.toLowerCase(), // Use the code as the Stripe coupon ID
      name: `$${discountAmount} Off First Month - ${code}`,
      amount_off: Math.round(discountAmount * 100), // Convert to cents (5000 cents = $50)
      currency: 'aud',
      duration: 'once', // Apply only once to the subscription
      max_redemptions: 1, // Each code can only be used once
      metadata: {
        description: `$${discountAmount} AUD off promotional code`,
        created_by: 'promo-script',
        type: 'first_month_discount'
      }
    });

    console.log(`   ✅ Created Stripe coupon: ${stripeCoupon.id}`);

    // Create coupon in database
    const now = new Date();
    const validUntil = new Date();
    validUntil.setMonth(validUntil.getMonth() + 6); // Valid for 6 months

    const { data, error } = await supabase
      .from('coupons')
      .insert({
        code: code.toUpperCase(),
        stripe_coupon_id: stripeCoupon.id,
        description: `$${discountAmount} Off First Month - Promotional Code`,
        type: 'fixed_amount',
        amount: discountAmount, // $50 AUD
        duration: 'once',
        duration_in_months: null,
        max_redemptions: 1,
        redemption_count: 0,
        valid_from: now.toISOString(),
        valid_until: validUntil.toISOString(),
        is_active: true
      })
      .select();

    if (error) {
      console.error(`   ❌ Error creating coupon in database:`, error);
      
      // Clean up Stripe coupon if database creation failed
      try {
        await stripe.coupons.del(stripeCoupon.id);
        console.log(`   🧹 Cleaned up Stripe coupon: ${stripeCoupon.id}`);
      } catch (cleanupError) {
        console.error(`   ⚠️  Failed to cleanup Stripe coupon: ${stripeCoupon.id}`, cleanupError);
      }
      
      return false;
    }

    console.log(`   ✅ Created database entry for: ${code}`);
    return true;

  } catch (error) {
    console.error(`   ❌ Error creating promo code ${code}:`, error);
    return false;
  }
}

async function createPromoCodes() {
  try {
    console.log('🚀 Creating 10 unique promo codes for $50 AUD off (fixed amount)...\n');

    // Generate 10 unique codes
    const codes = generatePromoCodes(10);
    
    console.log('📝 Generated codes:');
    codes.forEach((code, index) => {
      console.log(`   ${index + 1}. ${code}`);
    });
    console.log('');

    // Create each promo code
    const results = [];
    for (const code of codes) {
      const success = await createPromoCode(code);
      results.push({ code, success });
      
      // Small delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    // Summary
    console.log('\n📊 Summary:');
    const successful = results.filter(r => r.success);
    const failed = results.filter(r => !r.success);

    console.log(`✅ Successfully created: ${successful.length} promo codes`);
    if (failed.length > 0) {
      console.log(`❌ Failed to create: ${failed.length} promo codes`);
      failed.forEach(f => console.log(`   - ${f.code}`));
    }

    if (successful.length > 0) {
      console.log('\n🎉 Ready to use promo codes:');
      successful.forEach((s, index) => {
        console.log(`   ${index + 1}. ${s.code} - $50 AUD off (one-time use)`);
      });

      console.log('\n📋 Usage Instructions:');
      console.log('   • Each code gives $50 AUD off the first payment');
      console.log('   • Starter Monthly ($17.99): Completely free first month + $32.01 credit');
      console.log('   • Starter Annual ($179.88): $50 off total = $129.88 for first year');
      console.log('   • Professional Monthly ($58.80): $8.80 for first month');
      console.log('   • Professional Annual ($588.00): $50 off total = $538.00 for first year');
      console.log('   • Each code can only be used once');
      console.log('   • Codes are valid for 6 months from creation');
      console.log('   • Users can enter these codes during checkout');
    }

  } catch (error) {
    console.error('❌ Error creating promo codes:', error);
  }
}

// Run the script
createPromoCodes().then(() => {
  console.log('\n✅ Promo code creation completed.');
  process.exit(0);
}).catch((error) => {
  console.error('❌ Script failed:', error);
  process.exit(1);
});
