// Manual test script for usage tracking
// Run this script with Node.js directly

console.log('Starting manual test for usage tracking...');

// Define test steps
const steps = [
  {
    name: 'Create a new project',
    instructions: `
    1. Go to http://localhost:3000/
    2. Log in with your account
    3. Click "Start New Project"
    4. Fill in the project details and create it
    5. Check the usage count in http://localhost:3000/account under the "Usage" tab
    6. Verify that the project count has increased by 1
    `
  },
  {
    name: 'Upload before images',
    instructions: `
    1. In your project, upload 1-2 before images
    2. Check the usage count in http://localhost:3000/account under the "Usage" tab
    3. Verify that the image count has increased by the number of images you uploaded
    `
  },
  {
    name: 'Upload reference images',
    instructions: `
    1. In your project, upload 1-2 reference images
    2. Check the usage count in http://localhost:3000/account under the "Usage" tab
    3. Verify that the image count has increased by the number of images you uploaded
    `
  },
  {
    name: 'Generate after images',
    instructions: `
    1. In your project, add a modification description
    2. Click "Visualise Job" to generate after images
    3. Wait for the generation to complete
    4. Check the usage count in http://localhost:3000/account under the "Usage" tab
    5. Verify that the image count has increased by the number of after images generated
    `
  }
];

// Display the test steps
console.log('\nManual Test Steps:');
steps.forEach((step, index) => {
  console.log(`\nStep ${index + 1}: ${step.name}`);
  console.log(step.instructions);
});

console.log('\nAfter completing these steps, verify that:');
console.log('1. The project count shows the correct number of projects you created');
console.log('2. The image count shows the correct total number of images (before + reference + after)');
console.log('3. The usage bars reflect the correct percentages based on your subscription plan limits');
