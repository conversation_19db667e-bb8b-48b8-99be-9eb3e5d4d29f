#!/usr/bin/env tsx

/**
 * Demo Data Population Script for Renovision Studio
 * 
 * This script populates demo accounts with sample projects, reference categories,
 * and other data to make them more realistic for testing and demonstrations.
 */

import 'dotenv/config';
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL as string;
const supabaseKey = process.env.SUPABASE_DB_KEY as string;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase configuration. Check environment variables.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    persistSession: false,
    autoRefreshToken: false,
    detectSessionInUrl: false
  }
});

// Configure logging
const logger = {
  info: (message: string, ...args: any[]) => console.log(`[INFO] ${message}`, ...args),
  warn: (message: string, ...args: any[]) => console.warn(`[WARN] ${message}`, ...args),
  error: (message: string, ...args: any[]) => console.error(`[ERROR] ${message}`, ...args),
  success: (message: string, ...args: any[]) => console.log(`[SUCCESS] ✅ ${message}`, ...args),
};

// Sample project data for different user types
const SAMPLE_PROJECTS = [
  {
    title: "Modern Kitchen Renovation",
    description: "Complete kitchen makeover with contemporary design elements, quartz countertops, and smart appliances.",
    status: "completed"
  },
  {
    title: "Living Room Refresh",
    description: "Open concept living space with neutral color palette and natural lighting optimization.",
    status: "in_progress"
  },
  {
    title: "Master Bathroom Upgrade",
    description: "Luxury bathroom renovation featuring walk-in shower, double vanity, and heated floors.",
    status: "draft"
  },
  {
    title: "Home Office Design",
    description: "Productive workspace design with built-in storage and ergonomic considerations.",
    status: "completed"
  },
  {
    title: "Outdoor Patio Transformation",
    description: "Outdoor living space with weather-resistant materials and entertainment features.",
    status: "in_progress"
  }
];

// Sample reference categories
const SAMPLE_CATEGORIES = [
  {
    name: "Kitchen Inspiration",
    description: "Modern kitchen designs and layout ideas"
  },
  {
    name: "Color Palettes",
    description: "Curated color schemes for different room types"
  },
  {
    name: "Lighting Solutions",
    description: "Innovative lighting designs and fixtures"
  },
  {
    name: "Storage Ideas",
    description: "Creative storage solutions for small spaces"
  }
];

/**
 * Get demo account user IDs from the database
 */
async function getDemoAccounts(): Promise<string[]> {
  const { data: users, error } = await supabase
    .from('users')
    .select('id, username')
    .like('username', 'acquireuser%')
    .order('username');

  if (error) {
    logger.error('Failed to fetch demo accounts:', error.message);
    return [];
  }

  return users?.map(user => user.id) || [];
}

/**
 * Create sample projects for a user
 */
async function createSampleProjects(userId: string, count: number = 2): Promise<number> {
  let createdCount = 0;
  
  for (let i = 0; i < count && i < SAMPLE_PROJECTS.length; i++) {
    const project = SAMPLE_PROJECTS[i];
    
    try {
      const { data, error } = await supabase
        .from('projects')
        .insert({
          user_id: userId,
          title: project.title,
          description: project.description,
          status: project.status,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) {
        logger.warn(`Failed to create project "${project.title}":`, error.message);
      } else {
        createdCount++;
      }
    } catch (err: any) {
      logger.warn(`Error creating project "${project.title}":`, err.message);
    }
  }
  
  return createdCount;
}

/**
 * Create sample reference categories for a user
 */
async function createSampleCategories(userId: string, count: number = 2): Promise<number> {
  let createdCount = 0;
  
  for (let i = 0; i < count && i < SAMPLE_CATEGORIES.length; i++) {
    const category = SAMPLE_CATEGORIES[i];
    
    try {
      const { data, error } = await supabase
        .from('reference_categories')
        .insert({
          user_id: userId,
          name: category.name,
          description: category.description,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) {
        logger.warn(`Failed to create category "${category.name}":`, error.message);
      } else {
        createdCount++;
      }
    } catch (err: any) {
      logger.warn(`Error creating category "${category.name}":`, err.message);
    }
  }
  
  return createdCount;
}

/**
 * Create usage records for demo accounts
 */
async function createUsageRecord(userId: string): Promise<boolean> {
  try {
    const now = new Date();
    const periodStart = new Date(now.getFullYear(), now.getMonth(), 1);
    const periodEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    
    const { data, error } = await supabase
      .from('usage')
      .insert({
        user_id: userId,
        projects_count: Math.floor(Math.random() * 5) + 1, // 1-5 projects
        images_count: Math.floor(Math.random() * 20) + 5, // 5-25 images
        period_start: periodStart.toISOString(),
        period_end: periodEnd.toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      logger.warn(`Failed to create usage record:`, error.message);
      return false;
    }
    
    return true;
  } catch (err: any) {
    logger.warn(`Error creating usage record:`, err.message);
    return false;
  }
}

/**
 * Populate demo data for all demo accounts
 */
async function populateDemoData(): Promise<void> {
  logger.info('🎨 Populating Demo Data for Renovision Studio');
  logger.info('============================================');
  
  // Get demo accounts
  const demoUserIds = await getDemoAccounts();
  
  if (demoUserIds.length === 0) {
    logger.error('No demo accounts found. Run "npm run create-demo-accounts" first.');
    process.exit(1);
  }
  
  logger.info(`Found ${demoUserIds.length} demo accounts to populate`);
  
  let totalProjects = 0;
  let totalCategories = 0;
  let totalUsageRecords = 0;
  
  for (const userId of demoUserIds) {
    logger.info(`\nPopulating data for user: ${userId}`);
    
    // Create sample projects (1-3 per user)
    const projectCount = Math.floor(Math.random() * 3) + 1;
    const createdProjects = await createSampleProjects(userId, projectCount);
    totalProjects += createdProjects;
    logger.info(`  Created ${createdProjects} sample projects`);
    
    // Create sample reference categories (1-2 per user)
    const categoryCount = Math.floor(Math.random() * 2) + 1;
    const createdCategories = await createSampleCategories(userId, categoryCount);
    totalCategories += createdCategories;
    logger.info(`  Created ${createdCategories} reference categories`);
    
    // Create usage record
    const usageCreated = await createUsageRecord(userId);
    if (usageCreated) {
      totalUsageRecords++;
      logger.info(`  Created usage tracking record`);
    }
    
    // Small delay between users
    await new Promise(resolve => setTimeout(resolve, 200));
  }
  
  // Summary
  logger.info('\n📊 Demo Data Population Summary:');
  logger.info(`✅ Total Projects Created: ${totalProjects}`);
  logger.info(`✅ Total Categories Created: ${totalCategories}`);
  logger.info(`✅ Total Usage Records Created: ${totalUsageRecords}`);
  logger.info(`📝 Demo Accounts Populated: ${demoUserIds.length}`);
  
  logger.success('\n🎉 Demo data population completed!');
  logger.info('\n📋 What was created:');
  logger.info('• Sample renovation projects with different statuses');
  logger.info('• Reference categories for inspiration and organization');
  logger.info('• Usage tracking records for realistic account activity');
  logger.info('• Varied data across accounts for diverse testing scenarios');
}

// Run the script
populateDemoData()
  .then(() => {
    logger.success('Demo data population completed!');
    process.exit(0);
  })
  .catch((error) => {
    logger.error('Fatal error during demo data population:', error);
    process.exit(1);
  });
