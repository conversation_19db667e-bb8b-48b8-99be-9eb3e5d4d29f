import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';

// Load environment variables
dotenv.config();

// Initialize Supabase client with database key for full database access
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_DB_KEY,
  {
    auth: {
      persistSession: false,
      autoRefreshToken: false,
      detectSessionInUrl: false
    }
  }
);

async function updateSchema() {
  try {
    console.log('Updating database schema to support Clerk IDs...');
    
    // Read the migration SQL
    const migrationSQL = fs.readFileSync(
      path.join(process.cwd(), 'supabase/migrations/20240428_update_user_id_type.sql'),
      'utf8'
    );
    
    // Execute the SQL
    const { error } = await supabase.rpc('pgexec', { sql: migrationSQL });
    
    if (error) {
      console.error('Error updating schema:', error);
      process.exit(1);
    }
    
    console.log('Schema updated successfully!');
    process.exit(0);
  } catch (err) {
    console.error('Error:', err);
    process.exit(1);
  }
}

updateSchema();
