#!/usr/bin/env tsx

/**
 * Demo Account Subscription Upgrade Script
 * 
 * This script upgrades a demo account to a professional plan for demonstration purposes.
 * It creates a subscription without going through Stripe payment processing.
 */

import 'dotenv/config';
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL as string;
const supabaseKey = process.env.SUPABASE_DB_KEY as string;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase configuration. Check environment variables.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    persistSession: false,
    autoRefreshToken: false,
    detectSessionInUrl: false
  }
});

// Configure logging
const logger = {
  info: (message: string, ...args: any[]) => console.log(`[INFO] ${message}`, ...args),
  warn: (message: string, ...args: any[]) => console.warn(`[WARN] ${message}`, ...args),
  error: (message: string, ...args: any[]) => console.error(`[ERROR] ${message}`, ...args),
  success: (message: string, ...args: any[]) => console.log(`[SUCCESS] ✅ ${message}`, ...args),
};

/**
 * Get user ID by username
 */
async function getUserByUsername(username: string): Promise<string | null> {
  try {
    const { data: user, error } = await supabase
      .from('users')
      .select('id, username, email, first_name, last_name')
      .eq('username', username)
      .single();

    if (error || !user) {
      logger.error(`User ${username} not found:`, error?.message);
      return null;
    }

    logger.info(`Found user: ${user.first_name} ${user.last_name} (${user.email})`);
    return user.id;
  } catch (error: any) {
    logger.error(`Error finding user ${username}:`, error.message);
    return null;
  }
}

/**
 * Check if user already has an active subscription
 */
async function checkExistingSubscription(userId: string): Promise<boolean> {
  try {
    const { data: subscription, error } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (subscription) {
      logger.warn(`User already has an active subscription:`, {
        plan: subscription.plan_id,
        status: subscription.status,
        billingCycle: subscription.billing_cycle,
        periodEnd: subscription.current_period_end
      });
      return true;
    }

    return false;
  } catch (error) {
    // No subscription found, which is fine
    return false;
  }
}

/**
 * Create a demo subscription for the user
 */
async function createDemoSubscription(userId: string, planId: string, durationMonths: number = 1): Promise<boolean> {
  try {
    const now = new Date();
    const periodEnd = new Date(now);
    periodEnd.setMonth(periodEnd.getMonth() + durationMonths);

    // Create subscription record
    const subscriptionData = {
      user_id: userId,
      stripe_customer_id: `demo_customer_${userId}`, // Demo customer ID
      stripe_subscription_id: `demo_sub_${userId}_${Date.now()}`, // Demo subscription ID
      plan_id: planId,
      status: 'active',
      current_period_start: now.toISOString(),
      current_period_end: periodEnd.toISOString(),
      cancel_at_period_end: false,
      billing_cycle: 'monthly',
      created_at: now.toISOString(),
      updated_at: now.toISOString()
    };

    logger.info(`Creating ${planId} subscription for ${durationMonths} month(s)...`);
    logger.info(`Period: ${now.toISOString()} to ${periodEnd.toISOString()}`);

    const { data: subscription, error: subError } = await supabase
      .from('subscriptions')
      .insert(subscriptionData)
      .select()
      .single();

    if (subError) {
      logger.error('Failed to create subscription:', subError.message);
      return false;
    }

    logger.success(`Created subscription: ${subscription.id}`);

    // Create usage record for the subscription period
    const usageData = {
      user_id: userId,
      projects_count: 0,
      images_count: 0,
      period_start: now.toISOString(),
      period_end: periodEnd.toISOString(),
      created_at: now.toISOString(),
      updated_at: now.toISOString()
    };

    const { data: usage, error: usageError } = await supabase
      .from('usage')
      .upsert(usageData)
      .select()
      .single();

    if (usageError) {
      logger.warn('Failed to create usage record:', usageError.message);
    } else {
      logger.success(`Created usage record: ${usage.id}`);
    }

    return true;
  } catch (error: any) {
    logger.error('Error creating demo subscription:', error.message);
    return false;
  }
}

/**
 * Main function to upgrade demo account subscription
 */
async function upgradeDemoSubscription(): Promise<void> {
  const username = 'acquireuser7';
  const planId = 'professional';
  const durationMonths = 1;

  logger.info('🚀 Upgrading Demo Account Subscription');
  logger.info('=====================================');
  logger.info(`Target Account: ${username}`);
  logger.info(`Plan: ${planId}`);
  logger.info(`Duration: ${durationMonths} month(s)`);
  logger.info('');

  // Get user ID
  const userId = await getUserByUsername(username);
  if (!userId) {
    logger.error(`Demo account ${username} not found. Please run "npm run create-demo-accounts" first.`);
    process.exit(1);
  }

  // Check for existing subscription
  const hasSubscription = await checkExistingSubscription(userId);
  if (hasSubscription) {
    logger.warn('User already has an active subscription. Skipping upgrade.');
    logger.info('To replace the existing subscription, manually delete it first.');
    process.exit(0);
  }

  // Create the demo subscription
  const success = await createDemoSubscription(userId, planId, durationMonths);
  
  if (success) {
    logger.success('\n🎉 Demo subscription upgrade completed!');
    logger.info('\n📋 Subscription Details:');
    logger.info(`• Account: ${username} (Alex Johnson)`);
    logger.info(`• Plan: Professional`);
    logger.info(`• Duration: ${durationMonths} month from today`);
    logger.info(`• Status: Active`);
    logger.info(`• Billing: Demo (no actual charges)`);
    logger.info('\n✨ Benefits:');
    logger.info('• 10 projects per month');
    logger.info('• 5 images per project');
    logger.info('• Access to all renovation styles');
    logger.info('• High quality image generation');
    logger.info('• Priority support features');
    logger.info('\n🔗 Test the account:');
    logger.info('1. Sign in with username: acquireuser7');
    logger.info('2. Verification code: 424242');
    logger.info('3. Check subscription status in the app');
  } else {
    logger.error('\n❌ Failed to upgrade demo subscription');
    logger.info('Check the logs above for error details.');
    process.exit(1);
  }
}

// Run the script
upgradeDemoSubscription()
  .then(() => {
    logger.success('Demo subscription upgrade script completed!');
    process.exit(0);
  })
  .catch((error) => {
    logger.error('Fatal error during demo subscription upgrade:', error);
    process.exit(1);
  });
