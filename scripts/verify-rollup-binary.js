#!/usr/bin/env node

/**
 * Rollup Binary Verification Script
 * Verifies that the correct Rollup native binary is installed for the current platform
 * Helps diagnose npm CLI bug #4828 issues
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { existsSync, statSync } from 'fs';
import { platform, arch } from 'os';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');

// Platform-specific binary mapping
const ROLLUP_BINARIES = {
  'linux-x64': '@rollup/rollup-linux-x64-gnu',
  'linux-arm64': '@rollup/rollup-linux-arm64-gnu',
  'darwin-x64': '@rollup/rollup-darwin-x64',
  'darwin-arm64': '@rollup/rollup-darwin-arm64',
  'win32-x64': '@rollup/rollup-win32-x64-msvc',
  'win32-ia32': '@rollup/rollup-win32-ia32-msvc',
  'win32-arm64': '@rollup/rollup-win32-arm64-msvc'
};

function getCurrentPlatform() {
  const currentPlatform = platform();
  const currentArch = arch();
  
  // Normalize architecture names
  const normalizedArch = currentArch === 'x64' ? 'x64' : 
                        currentArch === 'arm64' ? 'arm64' : 
                        currentArch === 'ia32' ? 'ia32' : currentArch;
  
  return `${currentPlatform}-${normalizedArch}`;
}

function checkBinaryExists(binaryPackage) {
  const binaryPath = join(projectRoot, 'node_modules', binaryPackage);
  
  if (!existsSync(binaryPath)) {
    return { exists: false, path: binaryPath };
  }
  
  const stats = statSync(binaryPath);
  return { 
    exists: true, 
    path: binaryPath,
    isDirectory: stats.isDirectory(),
    size: stats.size,
    modified: stats.mtime
  };
}

function checkRollupMainPackage() {
  const rollupPath = join(projectRoot, 'node_modules', 'rollup');
  const packageJsonPath = join(rollupPath, 'package.json');
  
  if (!existsSync(packageJsonPath)) {
    return { exists: false, path: rollupPath };
  }
  
  try {
    const packageJson = JSON.parse(require('fs').readFileSync(packageJsonPath, 'utf8'));
    return {
      exists: true,
      path: rollupPath,
      version: packageJson.version,
      optionalDependencies: packageJson.optionalDependencies || {}
    };
  } catch (error) {
    return { exists: false, error: error.message };
  }
}

function main() {
  console.log('🔍 Rollup Binary Verification Report');
  console.log('=====================================\n');
  
  // Current platform info
  const currentPlatform = getCurrentPlatform();
  console.log(`📋 Platform Information:`);
  console.log(`   OS: ${platform()}`);
  console.log(`   Architecture: ${arch()}`);
  console.log(`   Platform Key: ${currentPlatform}\n`);
  
  // Expected binary for current platform
  const expectedBinary = ROLLUP_BINARIES[currentPlatform];
  if (!expectedBinary) {
    console.log(`❌ Unsupported platform: ${currentPlatform}`);
    console.log(`   Supported platforms: ${Object.keys(ROLLUP_BINARIES).join(', ')}`);
    process.exit(1);
  }
  
  console.log(`🎯 Expected Binary: ${expectedBinary}\n`);
  
  // Check main Rollup package
  console.log(`📦 Rollup Main Package:`);
  const rollupMain = checkRollupMainPackage();
  if (rollupMain.exists) {
    console.log(`   ✅ Installed: ${rollupMain.version}`);
    console.log(`   📁 Path: ${rollupMain.path}`);
    
    const optionalDeps = Object.keys(rollupMain.optionalDependencies);
    if (optionalDeps.length > 0) {
      console.log(`   🔧 Optional Dependencies: ${optionalDeps.length} declared`);
      if (optionalDeps.includes(expectedBinary.replace('@rollup/', ''))) {
        console.log(`   ✅ Expected binary declared in optionalDependencies`);
      } else {
        console.log(`   ⚠️ Expected binary NOT declared in optionalDependencies`);
      }
    } else {
      console.log(`   ❌ No optional dependencies declared`);
    }
  } else {
    console.log(`   ❌ Not installed`);
    console.log(`   📁 Expected path: ${rollupMain.path}`);
  }
  
  console.log();
  
  // Check platform-specific binary
  console.log(`🔧 Platform-Specific Binary:`);
  const binaryCheck = checkBinaryExists(expectedBinary);
  if (binaryCheck.exists) {
    console.log(`   ✅ Installed: ${expectedBinary}`);
    console.log(`   📁 Path: ${binaryCheck.path}`);
    console.log(`   📊 Directory: ${binaryCheck.isDirectory ? 'Yes' : 'No'}`);
    console.log(`   📅 Modified: ${binaryCheck.modified}`);
  } else {
    console.log(`   ❌ Missing: ${expectedBinary}`);
    console.log(`   📁 Expected path: ${binaryCheck.path}`);
  }
  
  console.log();
  
  // Check all available Rollup binaries
  console.log(`🌍 All Rollup Binaries:`);
  let foundBinaries = 0;
  for (const [platformKey, binaryName] of Object.entries(ROLLUP_BINARIES)) {
    const check = checkBinaryExists(binaryName);
    if (check.exists) {
      console.log(`   ✅ ${platformKey}: ${binaryName}`);
      foundBinaries++;
    } else {
      console.log(`   ❌ ${platformKey}: ${binaryName}`);
    }
  }
  
  console.log(`\n📊 Summary: ${foundBinaries}/${Object.keys(ROLLUP_BINARIES).length} binaries found`);
  
  // Final verdict
  console.log('\n🎯 Verdict:');
  if (rollupMain.exists && binaryCheck.exists) {
    console.log('   ✅ ROLLUP CONFIGURATION IS CORRECT');
    console.log('   🚀 Build should work successfully');
    process.exit(0);
  } else if (!rollupMain.exists) {
    console.log('   ❌ ROLLUP MAIN PACKAGE MISSING');
    console.log('   💡 Run: npm install rollup --legacy-peer-deps');
    process.exit(1);
  } else if (!binaryCheck.exists) {
    console.log('   ❌ PLATFORM-SPECIFIC BINARY MISSING');
    console.log('   💡 This is likely npm CLI bug #4828');
    console.log('   🔧 Solutions:');
    console.log(`      1. npm install ${expectedBinary} --legacy-peer-deps --save-optional`);
    console.log('      2. npm ci --include=optional --legacy-peer-deps');
    console.log('      3. rm -rf node_modules package-lock.json && npm install --include=optional');
    process.exit(1);
  }
}

// Run the verification
main();
