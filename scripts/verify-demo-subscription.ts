#!/usr/bin/env tsx

/**
 * Demo Account Subscription Verification Script
 * 
 * This script verifies that the demo account subscription is working correctly
 * by checking the database and testing the subscription API.
 */

import 'dotenv/config';
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL as string;
const supabaseKey = process.env.SUPABASE_DB_KEY as string;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase configuration. Check environment variables.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    persistSession: false,
    autoRefreshToken: false,
    detectSessionInUrl: false
  }
});

// Configure logging
const logger = {
  info: (message: string, ...args: any[]) => console.log(`[INFO] ${message}`, ...args),
  warn: (message: string, ...args: any[]) => console.warn(`[WARN] ${message}`, ...args),
  error: (message: string, ...args: any[]) => console.error(`[ERROR] ${message}`, ...args),
  success: (message: string, ...args: any[]) => console.log(`[SUCCESS] ✅ ${message}`, ...args),
};

/**
 * Get detailed subscription information for a user
 */
async function getSubscriptionDetails(username: string): Promise<any> {
  try {
    // Get user
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id, username, email, first_name, last_name')
      .eq('username', username)
      .single();

    if (userError || !user) {
      logger.error(`User ${username} not found:`, userError?.message);
      return null;
    }

    // Get subscription
    const { data: subscription, error: subError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', user.id)
      .single();

    if (subError) {
      logger.warn(`No subscription found for ${username}:`, subError.message);
      return { user, subscription: null };
    }

    // Get usage
    const { data: usage, error: usageError } = await supabase
      .from('usage')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    if (usageError) {
      logger.warn(`No usage data found for ${username}:`, usageError.message);
    }

    return { user, subscription, usage };
  } catch (error: any) {
    logger.error(`Error getting subscription details for ${username}:`, error.message);
    return null;
  }
}

/**
 * Format date for display
 */
function formatDate(dateString: string): string {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
}

/**
 * Calculate days remaining in subscription
 */
function getDaysRemaining(endDate: string): number {
  const end = new Date(endDate);
  const now = new Date();
  const diffTime = end.getTime() - now.getTime();
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
}

/**
 * Main verification function
 */
async function verifyDemoSubscription(): Promise<void> {
  const username = 'acquireuser7';

  logger.info('🔍 Verifying Demo Account Subscription');
  logger.info('====================================');
  logger.info(`Checking account: ${username}`);
  logger.info('');

  const details = await getSubscriptionDetails(username);
  
  if (!details) {
    logger.error('Failed to retrieve account details');
    process.exit(1);
  }

  const { user, subscription, usage } = details;

  // Display user information
  logger.info('👤 User Information:');
  logger.info(`   Name: ${user.first_name} ${user.last_name}`);
  logger.info(`   Email: ${user.email}`);
  logger.info(`   Username: ${user.username}`);
  logger.info(`   User ID: ${user.id}`);
  logger.info('');

  // Display subscription information
  if (subscription) {
    const daysRemaining = getDaysRemaining(subscription.current_period_end);
    const isActive = subscription.status === 'active' && daysRemaining > 0;

    logger.info('💳 Subscription Information:');
    logger.info(`   Plan: ${subscription.plan_id}`);
    logger.info(`   Status: ${subscription.status}`);
    logger.info(`   Billing Cycle: ${subscription.billing_cycle}`);
    logger.info(`   Period Start: ${formatDate(subscription.current_period_start)}`);
    logger.info(`   Period End: ${formatDate(subscription.current_period_end)}`);
    logger.info(`   Days Remaining: ${daysRemaining}`);
    logger.info(`   Customer ID: ${subscription.stripe_customer_id}`);
    logger.info(`   Subscription ID: ${subscription.stripe_subscription_id}`);
    logger.info('');

    if (isActive) {
      logger.success('✅ Subscription is ACTIVE and valid');
    } else {
      logger.warn('⚠️  Subscription may be expired or inactive');
    }

    // Display plan benefits
    if (subscription.plan_id === 'professional') {
      logger.info('🎯 Professional Plan Benefits:');
      logger.info('   • 10 projects per month');
      logger.info('   • 5 images per project');
      logger.info('   • Access to all renovation styles');
      logger.info('   • High quality image generation');
      logger.info('   • Priority support features');
      logger.info('');
    }
  } else {
    logger.warn('💳 No active subscription found');
    logger.info('   This account is on the free tier');
    logger.info('');
  }

  // Display usage information
  if (usage) {
    logger.info('📊 Current Usage:');
    logger.info(`   Projects: ${usage.projects_count}`);
    logger.info(`   Images: ${usage.images_count}`);
    logger.info(`   Period: ${formatDate(usage.period_start)} to ${formatDate(usage.period_end)}`);
    logger.info('');
  } else {
    logger.info('📊 No usage data found');
    logger.info('');
  }

  // Authentication instructions
  logger.info('🔐 Authentication Instructions:');
  logger.info('1. Go to http://localhost:3000/sign-in');
  logger.info(`2. Enter username: ${username}`);
  logger.info('3. Enter verification code: 424242');
  logger.info('4. Check subscription status in the app');
  logger.info('');

  logger.success('🎉 Demo account verification completed!');
}

// Run the script
verifyDemoSubscription()
  .then(() => {
    logger.success('Demo subscription verification completed!');
    process.exit(0);
  })
  .catch((error) => {
    logger.error('Fatal error during demo subscription verification:', error);
    process.exit(1);
  });
