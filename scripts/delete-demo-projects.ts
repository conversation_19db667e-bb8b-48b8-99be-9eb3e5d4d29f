#!/usr/bin/env tsx

/**
 * Delete Demo Projects Script
 * 
 * This script deletes all projects for a specific demo account.
 * Use with caution - this action cannot be undone.
 */

import 'dotenv/config';
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL as string;
const supabaseKey = process.env.SUPABASE_DB_KEY as string;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase configuration. Check environment variables.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    persistSession: false,
    autoRefreshToken: false,
    detectSessionInUrl: false
  }
});

// Configure logging
const logger = {
  info: (message: string, ...args: any[]) => console.log(`[INFO] ${message}`, ...args),
  warn: (message: string, ...args: any[]) => console.warn(`[WARN] ${message}`, ...args),
  error: (message: string, ...args: any[]) => console.error(`[ERROR] ${message}`, ...args),
  success: (message: string, ...args: any[]) => console.log(`[SUCCESS] ✅ ${message}`, ...args),
};

/**
 * Delete all projects for a specific demo account
 */
async function deleteDemoProjects(username: string): Promise<void> {
  logger.info('🗑️  Deleting Demo Account Projects');
  logger.info('===================================');
  logger.info(`Target account: ${username}`);
  logger.info('');

  try {
    // Get user
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id, username, email, first_name, last_name')
      .eq('username', username)
      .single();

    if (userError || !user) {
      logger.error(`User ${username} not found:`, userError?.message);
      process.exit(1);
    }

    logger.info('👤 User Information:');
    logger.info(`   Name: ${user.first_name} ${user.last_name}`);
    logger.info(`   Email: ${user.email}`);
    logger.info(`   Username: ${user.username}`);
    logger.info(`   User ID: ${user.id}`);
    logger.info('');

    // Get all projects for this user
    const { data: projects, error: projectsError } = await supabase
      .from('projects')
      .select('id, title, created_at')
      .eq('user_id', user.id);

    if (projectsError) {
      logger.error('Error fetching projects:', projectsError.message);
      process.exit(1);
    }

    if (!projects || projects.length === 0) {
      logger.info('ℹ️  No projects found for this user.');
      return;
    }

    logger.info(`📋 Found ${projects.length} projects to delete:`);
    projects.forEach((project, index) => {
      logger.info(`   ${index + 1}. "${project.title}" (ID: ${project.id})`);
    });
    logger.info('');

    // Delete all drafts associated with these projects first
    logger.info('🗑️  Deleting associated drafts...');
    const projectIds = projects.map(p => p.id);
    
    const { error: draftsError } = await supabase
      .from('drafts')
      .delete()
      .in('project_id', projectIds);

    if (draftsError) {
      logger.error('Error deleting drafts:', draftsError.message);
      process.exit(1);
    }

    logger.success('Drafts deleted successfully');

    // Delete all projects
    logger.info('🗑️  Deleting projects...');
    const { error: deleteError } = await supabase
      .from('projects')
      .delete()
      .eq('user_id', user.id);

    if (deleteError) {
      logger.error('Error deleting projects:', deleteError.message);
      process.exit(1);
    }

    logger.success(`Successfully deleted ${projects.length} projects`);
    logger.info('');

    // Update usage statistics
    logger.info('📊 Updating usage statistics...');
    const { error: usageError } = await supabase
      .from('usage')
      .update({
        projects_count: 0,
        images_count: 0,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', user.id);

    if (usageError) {
      logger.warn('Warning: Could not update usage statistics:', usageError.message);
    } else {
      logger.success('Usage statistics updated');
    }

    logger.info('');
    logger.success('🎉 Project deletion completed!');
    logger.info('');
    logger.info('📋 Summary:');
    logger.info(`   User: ${user.username} (${user.first_name} ${user.last_name})`);
    logger.info(`   Projects deleted: ${projects.length}`);
    logger.info(`   Drafts deleted: Associated drafts removed`);
    logger.info(`   Usage reset: Projects and images count reset to 0`);
    logger.info('');
    logger.info('✨ The account is now clean and ready for fresh demo data.');

  } catch (error: any) {
    logger.error('Error during project deletion:', error.message);
    process.exit(1);
  }
}

// Get username from command line arguments or default to acquireuser7
const username = process.argv[2] || 'acquireuser7';

// Validate username (must be a demo account)
if (!username.startsWith('acquireuser')) {
  logger.error('❌ This script only works with demo accounts (acquireuser7-16)');
  logger.info('Usage: npm run delete-demo-projects [username]');
  logger.info('Example: npm run delete-demo-projects acquireuser7');
  process.exit(1);
}

// Run the deletion
deleteDemoProjects(username)
  .then(() => {
    logger.success('Project deletion completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    logger.error('Fatal error during project deletion:', error);
    process.exit(1);
  });
