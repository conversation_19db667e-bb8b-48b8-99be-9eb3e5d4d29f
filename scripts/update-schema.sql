-- Update the users table to use text instead of uuid for id
ALTER TABLE users ALTER COLUMN id TYPE text;

-- Update the projects table to use text instead of uuid for user_id
ALTER TABLE projects ALTER COLUMN user_id TYPE text;

-- Update the reference_categories table to use text instead of uuid for user_id
ALTER TABLE reference_categories ALTER COLUMN user_id TYPE text;

-- Update the reference_items table to use text instead of uuid for user_id
ALTER TABLE reference_items ALTER COLUMN user_id TYPE text;

-- Update the renovation_presets table to use text instead of uuid for created_by
ALTER TABLE renovation_presets ALTER COLUMN created_by TYPE text;
