#!/usr/bin/env tsx

/**
 * Demo Account Authentication Test Script
 * 
 * This script tests that demo accounts can be retrieved from <PERSON>
 * and verifies their authentication setup.
 */

import 'dotenv/config';
import { clerkClient } from '@clerk/express';

// Configure logging
const logger = {
  info: (message: string, ...args: any[]) => console.log(`[INFO] ${message}`, ...args),
  warn: (message: string, ...args: any[]) => console.warn(`[WARN] ${message}`, ...args),
  error: (message: string, ...args: any[]) => console.error(`[ERROR] ${message}`, ...args),
  success: (message: string, ...args: any[]) => console.log(`[SUCCESS] ✅ ${message}`, ...args),
};

// Demo account usernames to test
const DEMO_USERNAMES = [
  'acquireuser7', 'acquireuser8', 'acquireuser9', 'acquireuser10',
  'acquireuser11', 'acquireuser12', 'acquireuser13', 'acquireuser14',
  'acquireuser15', 'acquireuser16'
];

/**
 * Test if a demo account exists and can be retrieved from Clerk
 */
async function testDemoAccount(username: string): Promise<boolean> {
  try {
    logger.info(`Testing demo account: ${username}`);
    
    // Get user list and find our demo user
    const userList = await clerkClient.users.getUserList({
      username: [username],
      limit: 1
    });
    
    if (userList.data.length === 0) {
      logger.error(`Demo account ${username} not found in Clerk`);
      return false;
    }
    
    const user = userList.data[0];
    
    // Verify user details
    logger.info(`Found user: ${user.id}`);
    logger.info(`  Username: ${user.username}`);
    logger.info(`  Email: ${user.emailAddresses[0]?.emailAddress || 'None'}`);
    logger.info(`  Name: ${user.firstName} ${user.lastName}`);
    logger.info(`  Created: ${user.createdAt}`);
    
    // Check if it's a demo account
    const isDemoAccount = user.publicMetadata?.isDemoAccount;
    if (isDemoAccount) {
      logger.success(`✅ Confirmed demo account: ${username}`);
    } else {
      logger.warn(`⚠️  Account ${username} exists but not marked as demo`);
    }
    
    // Verify email format
    const email = user.emailAddresses[0]?.emailAddress;
    if (email && email.includes('+clerk_test@')) {
      logger.success(`✅ Test email format verified: ${email}`);
    } else {
      logger.warn(`⚠️  Email format not as expected: ${email}`);
    }
    
    return true;
    
  } catch (error: any) {
    logger.error(`Failed to test demo account ${username}:`, error.message);
    return false;
  }
}

/**
 * Test Clerk configuration and demo account setup
 */
async function testDemoAuthentication(): Promise<void> {
  logger.info('🧪 Testing Demo Account Authentication Setup');
  logger.info('==========================================');
  
  // Validate environment
  if (!process.env.CLERK_SECRET_KEY) {
    logger.error('CLERK_SECRET_KEY not found in environment');
    process.exit(1);
  }
  
  logger.success('✅ Clerk configuration found');
  
  let successCount = 0;
  let totalCount = DEMO_USERNAMES.length;
  
  // Test each demo account
  for (const username of DEMO_USERNAMES) {
    const success = await testDemoAccount(username);
    if (success) {
      successCount++;
    }
    
    // Add small delay between requests
    await new Promise(resolve => setTimeout(resolve, 200));
  }
  
  // Summary
  logger.info('\n📊 Demo Account Test Summary:');
  logger.info(`✅ Successfully verified: ${successCount}/${totalCount} accounts`);
  
  if (successCount === totalCount) {
    logger.success('\n🎉 All demo accounts are properly configured!');
    logger.info('\n📋 Authentication Test Instructions:');
    logger.info('1. Open http://localhost:3000 in your browser');
    logger.info('2. Click "Sign In" or go to the sign-in page');
    logger.info('3. Enter any demo username (e.g., acquireuser7)');
    logger.info('4. When prompted for verification, enter: 424242');
    logger.info('5. You should be successfully logged in');
  } else {
    logger.error('\n❌ Some demo accounts are not properly configured');
    logger.info('Run "npm run create-demo-accounts" to recreate them');
    process.exit(1);
  }
}

// Run the test
testDemoAuthentication()
  .then(() => {
    logger.success('Demo authentication test completed!');
    process.exit(0);
  })
  .catch((error) => {
    logger.error('Fatal error during demo authentication test:', error);
    process.exit(1);
  });
