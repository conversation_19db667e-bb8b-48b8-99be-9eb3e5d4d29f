#!/usr/bin/env tsx

/**
 * Test Subscription Fix Script
 * 
 * This script tests that the demo subscription fix is working correctly
 * by simulating the subscription API call.
 */

import 'dotenv/config';
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL as string;
const supabaseKey = process.env.SUPABASE_DB_KEY as string;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase configuration. Check environment variables.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    persistSession: false,
    autoRefreshToken: false,
    detectSessionInUrl: false
  }
});

// Configure logging
const logger = {
  info: (message: string, ...args: any[]) => console.log(`[INFO] ${message}`, ...args),
  warn: (message: string, ...args: any[]) => console.warn(`[WARN] ${message}`, ...args),
  error: (message: string, ...args: any[]) => console.error(`[ERROR] ${message}`, ...args),
  success: (message: string, ...args: any[]) => console.log(`[SUCCESS] ✅ ${message}`, ...args),
};

/**
 * Test the subscription fix by checking the demo subscription
 */
async function testSubscriptionFix(): Promise<void> {
  const username = 'acquireuser7';

  logger.info('🧪 Testing Subscription Fix for Demo Account');
  logger.info('===========================================');
  logger.info(`Testing account: ${username}`);
  logger.info('');

  try {
    // Get user
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id, username, email, first_name, last_name')
      .eq('username', username)
      .single();

    if (userError || !user) {
      logger.error(`User ${username} not found:`, userError?.message);
      process.exit(1);
    }

    logger.info('👤 User Information:');
    logger.info(`   Name: ${user.first_name} ${user.last_name}`);
    logger.info(`   Email: ${user.email}`);
    logger.info(`   Username: ${user.username}`);
    logger.info(`   User ID: ${user.id}`);
    logger.info('');

    // Get subscription
    const { data: subscription, error: subError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', user.id)
      .single();

    if (subError) {
      logger.error(`No subscription found for ${username}:`, subError.message);
      process.exit(1);
    }

    logger.info('💳 Subscription Information:');
    logger.info(`   Plan: ${subscription.plan_id}`);
    logger.info(`   Status: ${subscription.status}`);
    logger.info(`   Billing Cycle: ${subscription.billing_cycle}`);
    logger.info(`   Stripe Customer ID: ${subscription.stripe_customer_id}`);
    logger.info(`   Stripe Subscription ID: ${subscription.stripe_subscription_id}`);
    logger.info(`   Period End: ${subscription.current_period_end}`);
    logger.info('');

    // Check if this is a demo subscription
    const isDemoSubscription = subscription.stripe_subscription_id?.startsWith('demo_sub_');
    const isDemoCustomer = subscription.stripe_customer_id?.startsWith('demo_customer_');

    if (isDemoSubscription && isDemoCustomer) {
      logger.success('✅ Demo subscription detected correctly');
      logger.info('   The API should skip Stripe calls for this subscription');
    } else {
      logger.warn('⚠️  This does not appear to be a demo subscription');
      logger.info(`   Subscription ID: ${subscription.stripe_subscription_id}`);
      logger.info(`   Customer ID: ${subscription.stripe_customer_id}`);
    }

    // Test the logic that would be used in the API
    logger.info('');
    logger.info('🔍 Testing API Logic:');
    
    if (subscription.stripe_subscription_id && !subscription.stripe_subscription_id.startsWith('demo_sub_')) {
      logger.info('   ❌ Would call Stripe API (this should NOT happen for demo accounts)');
    } else if (subscription.stripe_subscription_id && subscription.stripe_subscription_id.startsWith('demo_sub_')) {
      logger.success('   ✅ Would skip Stripe API call (correct for demo accounts)');
    } else {
      logger.info('   ℹ️  No Stripe subscription ID found');
    }

    logger.info('');
    logger.success('🎉 Subscription fix test completed!');
    logger.info('');
    logger.info('📋 Expected Behavior:');
    logger.info('1. Demo subscriptions should not call Stripe API');
    logger.info('2. Subscription page should show active status without "syncing"');
    logger.info('3. All subscription features should work normally');
    logger.info('');
    logger.info('🔗 Test the fix:');
    logger.info('1. Start the development server: npm run dev');
    logger.info('2. Sign in with username: acquireuser7');
    logger.info('3. Go to http://localhost:3000/account');
    logger.info('4. Check that subscription shows as active (no "syncing" message)');

  } catch (error: any) {
    logger.error('Error during subscription fix test:', error.message);
    process.exit(1);
  }
}

// Run the test
testSubscriptionFix()
  .then(() => {
    logger.success('Subscription fix test completed!');
    process.exit(0);
  })
  .catch((error) => {
    logger.error('Fatal error during subscription fix test:', error);
    process.exit(1);
  });
