#!/usr/bin/env tsx

import 'dotenv/config';
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL as string;
const supabaseKey = process.env.SUPABASE_DB_KEY as string;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase configuration. Check environment variables.');
  console.error('Required: SUPABASE_URL, SUPABASE_DB_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    persistSession: false,
    autoRefreshToken: false,
    detectSessionInUrl: false
  }
});

interface UserWithSubscription {
  id: string;
  username: string;
  email: string | null;
  first_name: string | null;
  last_name: string | null;
  created_at: string;
  subscription?: {
    plan_id: string;
    status: string;
    billing_cycle: string;
    current_period_end: string;
    stripe_customer_id: string | null;
  };
  usage?: {
    projects_count: number;
    images_count: number;
    period_start: string;
    period_end: string;
  };
}

async function checkUsers() {
  try {
    console.log('🔍 Checking users in the Renovision Studio database...\n');

    // Get all users
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('*')
      .order('created_at', { ascending: false });

    if (usersError) {
      console.error('❌ Error fetching users:', usersError);
      return;
    }

    if (!users || users.length === 0) {
      console.log('📭 No users found in the database.');
      return;
    }

    console.log(`👥 Found ${users.length} user(s) in the system:\n`);

    // Get subscription and usage data for each user
    const usersWithDetails: UserWithSubscription[] = [];

    for (const user of users) {
      const userWithDetails: UserWithSubscription = { ...user };

      // Get subscription info
      const { data: subscription } = await supabase
        .from('subscriptions')
        .select('plan_id, status, billing_cycle, current_period_end, stripe_customer_id')
        .eq('user_id', user.id)
        .single();

      if (subscription) {
        userWithDetails.subscription = subscription;
      }

      // Get current usage info
      const now = new Date();
      const { data: usage } = await supabase
        .from('usage')
        .select('projects_count, images_count, period_start, period_end')
        .eq('user_id', user.id)
        .lte('period_start', now.toISOString())
        .gte('period_end', now.toISOString())
        .single();

      if (usage) {
        userWithDetails.usage = usage;
      }

      usersWithDetails.push(userWithDetails);
    }

    // Display user information
    usersWithDetails.forEach((user, index) => {
      console.log(`${index + 1}. User Details:`);
      console.log(`   📧 ID: ${user.id}`);
      console.log(`   👤 Username: ${user.username}`);
      console.log(`   📧 Email: ${user.email || 'Not provided'}`);
      console.log(`   🏷️  Name: ${user.first_name || ''} ${user.last_name || ''}`.trim() || 'Not provided');
      console.log(`   📅 Joined: ${new Date(user.created_at).toLocaleDateString()}`);
      
      if (user.subscription) {
        console.log(`   💳 Subscription:`);
        console.log(`      Plan: ${user.subscription.plan_id}`);
        console.log(`      Status: ${user.subscription.status}`);
        console.log(`      Billing: ${user.subscription.billing_cycle}`);
        console.log(`      Period End: ${new Date(user.subscription.current_period_end).toLocaleDateString()}`);
        console.log(`      Stripe Customer: ${user.subscription.stripe_customer_id || 'Not set'}`);
      } else {
        console.log(`   💳 Subscription: Free plan (no active subscription)`);
      }

      if (user.usage) {
        console.log(`   📊 Current Usage:`);
        console.log(`      Projects: ${user.usage.projects_count}`);
        console.log(`      Images: ${user.usage.images_count}`);
        console.log(`      Period: ${new Date(user.usage.period_start).toLocaleDateString()} - ${new Date(user.usage.period_end).toLocaleDateString()}`);
      } else {
        console.log(`   📊 Current Usage: No usage data found`);
      }

      console.log(''); // Empty line for spacing
    });

    // Summary statistics
    console.log('📈 Summary Statistics:');
    console.log(`   Total Users: ${users.length}`);
    
    const subscribedUsers = usersWithDetails.filter(u => u.subscription);
    console.log(`   Subscribed Users: ${subscribedUsers.length}`);
    console.log(`   Free Users: ${users.length - subscribedUsers.length}`);

    if (subscribedUsers.length > 0) {
      const planCounts = subscribedUsers.reduce((acc, user) => {
        const plan = user.subscription!.plan_id;
        acc[plan] = (acc[plan] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      console.log(`   Plan Distribution:`);
      Object.entries(planCounts).forEach(([plan, count]) => {
        console.log(`      ${plan}: ${count} user(s)`);
      });
    }

    // Get total projects and images across all users
    const { data: projectStats } = await supabase
      .from('projects')
      .select('id, user_id, status');

    const { data: imageStats } = await supabase
      .from('images')
      .select('id, type');

    if (projectStats) {
      console.log(`   Total Projects: ${projectStats.length}`);
      const completedProjects = projectStats.filter(p => p.status === 'completed').length;
      console.log(`   Completed Projects: ${completedProjects}`);
    }

    if (imageStats) {
      console.log(`   Total Images: ${imageStats.length}`);
      const generatedImages = imageStats.filter(i => i.type === 'generated').length;
      console.log(`   Generated Images: ${generatedImages}`);
    }

  } catch (error) {
    console.error('❌ Error checking users:', error);
  }
}

// Run the script
checkUsers().then(() => {
  console.log('✅ User check completed.');
  process.exit(0);
}).catch((error) => {
  console.error('❌ Script failed:', error);
  process.exit(1);
});
