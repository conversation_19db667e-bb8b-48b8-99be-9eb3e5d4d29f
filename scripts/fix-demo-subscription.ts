#!/usr/bin/env tsx

/**
 * Fix Demo Account Subscription Status
 * 
 * This script fixes the subscription status for the demo account.
 */

import 'dotenv/config';
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL as string;
const supabaseKey = process.env.SUPABASE_DB_KEY as string;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase configuration. Check environment variables.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    persistSession: false,
    autoRefreshToken: false,
    detectSessionInUrl: false
  }
});

// Configure logging
const logger = {
  info: (message: string, ...args: any[]) => console.log(`[INFO] ${message}`, ...args),
  warn: (message: string, ...args: any[]) => console.warn(`[WARN] ${message}`, ...args),
  error: (message: string, ...args: any[]) => console.error(`[ERROR] ${message}`, ...args),
  success: (message: string, ...args: any[]) => console.log(`[SUCCESS] ✅ ${message}`, ...args),
};

/**
 * Fix subscription status for acquireuser7
 */
async function fixDemoSubscription(): Promise<void> {
  const username = 'acquireuser7';

  logger.info('🔧 Fixing Demo Account Subscription Status');
  logger.info('==========================================');

  try {
    // Get user ID
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id, username')
      .eq('username', username)
      .single();

    if (userError || !user) {
      logger.error(`User ${username} not found:`, userError?.message);
      process.exit(1);
    }

    logger.info(`Found user: ${user.username} (${user.id})`);

    // Update subscription status to active
    const { data: subscription, error: updateError } = await supabase
      .from('subscriptions')
      .update({
        status: 'active',
        updated_at: new Date().toISOString()
      })
      .eq('user_id', user.id)
      .select()
      .single();

    if (updateError) {
      logger.error('Failed to update subscription status:', updateError.message);
      process.exit(1);
    }

    logger.success('Updated subscription status to "active"');
    logger.info('Subscription details:', {
      plan: subscription.plan_id,
      status: subscription.status,
      billingCycle: subscription.billing_cycle,
      periodEnd: subscription.current_period_end
    });

    logger.success('\n🎉 Demo subscription status fixed!');
    logger.info('\n📋 The account is now ready for demonstration:');
    logger.info('• Professional plan active for 1 month');
    logger.info('• 10 projects per month limit');
    logger.info('• 5 images per project limit');
    logger.info('• All premium features enabled');

  } catch (error: any) {
    logger.error('Error fixing demo subscription:', error.message);
    process.exit(1);
  }
}

// Run the script
fixDemoSubscription()
  .then(() => {
    logger.success('Demo subscription fix completed!');
    process.exit(0);
  })
  .catch((error) => {
    logger.error('Fatal error during demo subscription fix:', error);
    process.exit(1);
  });
