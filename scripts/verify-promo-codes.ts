#!/usr/bin/env tsx

import 'dotenv/config';
import { createClient } from '@supabase/supabase-js';
import Stripe from 'stripe';

// Initialize Stripe
if (!process.env.STRIPE_SECRET_KEY) {
  console.error('❌ Missing STRIPE_SECRET_KEY environment variable');
  process.exit(1);
}

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: '2024-06-20',
});

// Initialize Supabase
const supabaseUrl = process.env.SUPABASE_URL as string;
const supabaseKey = process.env.SUPABASE_DB_KEY as string;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase configuration. Check environment variables.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    persistSession: false,
    autoRefreshToken: false,
    detectSessionInUrl: false
  }
});

async function verifyPromoCodes() {
  try {
    console.log('🔍 Verifying promo codes configuration...\n');

    // Get all active promo codes from database
    const { data: coupons, error } = await supabase
      .from('coupons')
      .select('*')
      .eq('type', 'fixed_amount')
      .eq('is_active', true)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('❌ Error fetching coupons:', error);
      return;
    }

    if (!coupons || coupons.length === 0) {
      console.log('📭 No fixed_amount coupons found in the database.');
      return;
    }

    console.log(`✅ Found ${coupons.length} active fixed_amount promo codes:\n`);

    // Verify each coupon
    for (const coupon of coupons) {
      console.log(`🎫 ${coupon.code}:`);
      console.log(`   Database Type: ${coupon.type}`);
      console.log(`   Amount: ${coupon.amount} month(s)`);
      console.log(`   Duration: ${coupon.duration}`);
      console.log(`   Duration in Months: ${coupon.duration_in_months}`);
      console.log(`   Max Redemptions: ${coupon.max_redemptions}`);
      console.log(`   Current Redemptions: ${coupon.redemption_count}`);
      console.log(`   Valid Until: ${new Date(coupon.valid_until).toLocaleDateString()}`);

      // Verify in Stripe
      try {
        const stripeCoupon = await stripe.coupons.retrieve(coupon.stripe_coupon_id);
        console.log(`   ✅ Stripe Verification:`);
        console.log(`      Percent Off: ${stripeCoupon.percent_off}%`);
        console.log(`      Duration: ${stripeCoupon.duration}`);
        console.log(`      Duration in Months: ${stripeCoupon.duration_in_months}`);
        console.log(`      Max Redemptions: ${stripeCoupon.max_redemptions}`);
        console.log(`      Times Redeemed: ${stripeCoupon.times_redeemed}`);
        console.log(`      Valid: ${stripeCoupon.valid}`);

        // Verify configuration is correct
        const isCorrect =
          stripeCoupon.amount_off === 5000 && // $50 AUD = 5000 cents
          stripeCoupon.currency === 'aud' &&
          stripeCoupon.duration === 'once' &&
          stripeCoupon.max_redemptions === 1;

        if (isCorrect) {
          console.log(`   ✅ Configuration: CORRECT - Gives $50 AUD off first payment`);
        } else {
          console.log(`   ❌ Configuration: INCORRECT - Check settings`);
        }

      } catch (stripeError) {
        console.log(`   ❌ Stripe Error: ${stripeError}`);
      }

      console.log(''); // Empty line for spacing
    }

    // Summary
    console.log('📋 Summary:');
    console.log(`   • These codes give $50 AUD off the first payment`);
    console.log(`   • Starter Monthly ($17.99): Free first month + $32.01 credit`);
    console.log(`   • Starter Annual ($179.88): $129.88 for first year`);
    console.log(`   • Professional Monthly ($58.80): $8.80 for first month`);
    console.log(`   • Professional Annual ($588.00): $538.00 for first year`);
    console.log(`   • Each code can only be used once`);
    console.log(`   • Codes are valid until December 2025`);

    console.log('\n💡 How it works:');
    console.log(`   1. User selects any plan (monthly or annual)`);
    console.log(`   2. User enters promo code during checkout`);
    console.log(`   3. Stripe applies $50 AUD discount to the first payment`);
    console.log(`   4. After first payment, normal billing resumes`);
    console.log(`   5. Fixed amount prevents issues with annual vs monthly billing`);

  } catch (error) {
    console.error('❌ Error verifying promo codes:', error);
  }
}

// Run the script
verifyPromoCodes().then(() => {
  console.log('\n✅ Verification completed.');
  process.exit(0);
}).catch((error) => {
  console.error('❌ Script failed:', error);
  process.exit(1);
});
