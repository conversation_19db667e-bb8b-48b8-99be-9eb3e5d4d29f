#!/usr/bin/env tsx

/**
 * Demo Account Creation Script for Renovision Studio
 * 
 * This script creates 10 demo user accounts (acquireuser7-16) using Clerk's Backend API
 * with test email addresses that work with <PERSON>'s test mode.
 * 
 * Features:
 * - Creates users with test email format (+clerk_test)
 * - Uses sequential usernames (acquireuser7 through acquireuser16)
 * - Populates realistic profile data
 * - Handles errors gracefully
 * - Validates environment setup
 * 
 * Usage:
 *   npm run create-demo-accounts
 *   or
 *   npx tsx scripts/create-demo-accounts.ts
 */

import 'dotenv/config';
import { clerkClient } from '@clerk/express';
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL as string;
const supabaseKey = process.env.SUPABASE_DB_KEY as string;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase configuration. Check environment variables.');
  console.error('Required: SUPABASE_URL, SUPABASE_DB_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    persistSession: false,
    autoRefreshToken: false,
    detectSessionInUrl: false
  }
});

// Configure logging
const logger = {
  info: (message: string, ...args: any[]) => console.log(`[INFO] ${message}`, ...args),
  warn: (message: string, ...args: any[]) => console.warn(`[WARN] ${message}`, ...args),
  error: (message: string, ...args: any[]) => console.error(`[ERROR] ${message}`, ...args),
  success: (message: string, ...args: any[]) => console.log(`[SUCCESS] ✅ ${message}`, ...args),
};

// Demo account configuration
const DEMO_ACCOUNTS = [
  { username: 'acquireuser7', firstName: 'Alex', lastName: 'Johnson', bio: 'Interior design enthusiast with a passion for modern aesthetics.' },
  { username: 'acquireuser8', firstName: 'Sarah', lastName: 'Chen', bio: 'Home renovation expert specializing in sustainable design solutions.' },
  { username: 'acquireuser9', firstName: 'Michael', lastName: 'Rodriguez', bio: 'Architect and designer focused on creating functional living spaces.' },
  { username: 'acquireuser10', firstName: 'Emma', lastName: 'Thompson', bio: 'DIY home improvement blogger and renovation project manager.' },
  { username: 'acquireuser11', firstName: 'David', lastName: 'Kim', bio: 'Real estate developer with expertise in property transformation.' },
  { username: 'acquireuser12', firstName: 'Jessica', lastName: 'Williams', bio: 'Professional contractor specializing in kitchen and bathroom renovations.' },
  { username: 'acquireuser13', firstName: 'Ryan', lastName: 'Davis', bio: 'Home staging consultant helping clients maximize property value.' },
  { username: 'acquireuser14', firstName: 'Lisa', lastName: 'Anderson', bio: 'Interior decorator with 10+ years of residential design experience.' },
  { username: 'acquireuser15', firstName: 'James', lastName: 'Wilson', bio: 'Construction project manager and home renovation specialist.' },
  { username: 'acquireuser16', firstName: 'Maria', lastName: 'Garcia', bio: 'Sustainable design advocate promoting eco-friendly renovation practices.' },
];

/**
 * Validates that required environment variables are set
 */
function validateEnvironment(): boolean {
  const requiredVars = ['CLERK_SECRET_KEY', 'SUPABASE_URL', 'SUPABASE_DB_KEY'];
  const missing = requiredVars.filter(varName => !process.env[varName]);

  if (missing.length > 0) {
    logger.error(`Missing required environment variables: ${missing.join(', ')}`);
    logger.error('Please ensure your .env file is properly configured.');
    return false;
  }

  return true;
}

/**
 * Creates a single demo account using Clerk's Backend API
 */
async function createDemoAccount(accountData: typeof DEMO_ACCOUNTS[0]): Promise<boolean> {
  try {
    const { username, firstName, lastName, bio } = accountData;
    const email = `${username}+<EMAIL>`;
    
    logger.info(`Creating demo account: ${username} (${email})`);
    
    // Create user in Clerk
    const clerkUser = await clerkClient.users.createUser({
      username,
      firstName,
      lastName,
      emailAddress: [email],
      skipPasswordRequirement: true, // No password needed for demo accounts
      publicMetadata: {
        isDemoAccount: true,
        createdBy: 'demo-script',
        createdAt: new Date().toISOString(),
      },
      privateMetadata: {
        accountType: 'demo',
        bio,
      },
    });
    
    logger.success(`Created Clerk user: ${clerkUser.id} (${username})`);
    
    // Create corresponding database record
    const userData = {
      id: clerkUser.id,
      username,
      email,
      first_name: firstName,
      last_name: lastName,
      bio,
      profile_image_url: null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
    
    const { data: dbUser, error: dbError } = await supabase
      .from('users')
      .upsert(userData)
      .select()
      .single();

    if (dbError) {
      logger.warn(`Database upsert warning for ${username}:`, dbError.message);
    } else {
      logger.success(`Created database record for: ${username}`);
    }
    
    return true;
  } catch (error: any) {
    logger.error(`Failed to create demo account ${accountData.username}:`, error.message);
    
    // Handle specific Clerk errors
    if (error.status === 422) {
      logger.warn(`Account ${accountData.username} may already exist or validation failed`);
    } else if (error.status === 429) {
      logger.warn('Rate limit exceeded. Please wait before retrying.');
    }
    
    return false;
  }
}

/**
 * Checks if a demo account already exists
 */
async function checkExistingAccount(username: string): Promise<boolean> {
  try {
    // Check if user exists in database
    const { data: existingUser, error } = await supabase
      .from('users')
      .select('id, username')
      .eq('username', username)
      .single();

    if (existingUser) {
      logger.warn(`Demo account ${username} already exists in database`);
      return true;
    }

    return false;
  } catch (error) {
    // If user doesn't exist, that's fine
    return false;
  }
}

/**
 * Main function to create all demo accounts
 */
async function createDemoAccounts(): Promise<void> {
  logger.info('🚀 Starting demo account creation for Renovision Studio');
  logger.info(`Creating ${DEMO_ACCOUNTS.length} demo accounts...`);
  
  // Validate environment
  if (!validateEnvironment()) {
    process.exit(1);
  }
  
  let successCount = 0;
  let skipCount = 0;
  let errorCount = 0;
  
  for (const accountData of DEMO_ACCOUNTS) {
    try {
      // Check if account already exists
      const exists = await checkExistingAccount(accountData.username);
      if (exists) {
        skipCount++;
        continue;
      }
      
      // Create the account
      const success = await createDemoAccount(accountData);
      if (success) {
        successCount++;
      } else {
        errorCount++;
      }
      
      // Add a small delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 500));
      
    } catch (error: any) {
      logger.error(`Unexpected error processing ${accountData.username}:`, error.message);
      errorCount++;
    }
  }
  
  // Summary
  logger.info('\n📊 Demo Account Creation Summary:');
  logger.info(`✅ Successfully created: ${successCount} accounts`);
  logger.info(`⏭️  Skipped (already exist): ${skipCount} accounts`);
  logger.info(`❌ Failed: ${errorCount} accounts`);
  logger.info(`📝 Total processed: ${DEMO_ACCOUNTS.length} accounts`);
  
  if (successCount > 0) {
    logger.success('\n🎉 Demo accounts created successfully!');
    logger.info('\n📋 Authentication Instructions:');
    logger.info('1. Go to the sign-in page of Renovision Studio');
    logger.info('2. Use any of these usernames: acquireuser7, acquireuser8, ..., acquireuser16');
    logger.info('3. Or use the corresponding email: acquireuser{N}+<EMAIL>');
    logger.info('4. When prompted for verification code, use: 424242');
    logger.info('5. The accounts will be automatically verified and logged in');
  }
  
  if (errorCount > 0) {
    logger.warn('\n⚠️  Some accounts failed to create. Check the logs above for details.');
    process.exit(1);
  }
}

// Run the script
createDemoAccounts()
  .then(() => {
    logger.success('Demo account creation completed!');
    process.exit(0);
  })
  .catch((error) => {
    logger.error('Fatal error during demo account creation:', error);
    process.exit(1);
  });

export { createDemoAccounts, DEMO_ACCOUNTS };
