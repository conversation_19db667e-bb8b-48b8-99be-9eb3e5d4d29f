#!/usr/bin/env tsx

/**
 * Direct Database Subscription Check
 */

import 'dotenv/config';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.SUPABASE_URL as string;
const supabaseKey = process.env.SUPABASE_DB_KEY as string;

const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    persistSession: false,
    autoRefreshToken: false,
    detectSessionInUrl: false
  }
});

async function checkSubscription() {
  console.log('🔍 Direct Database Subscription Check');
  console.log('====================================');
  
  const { data: subscriptions, error } = await supabase
    .from('subscriptions')
    .select('*')
    .eq('user_id', 'user_2yoSBuAhZUgxucMofVWF7PvPgHN');

  if (error) {
    console.error('Error:', error);
    return;
  }

  console.log('Subscription records found:', subscriptions?.length || 0);
  
  if (subscriptions && subscriptions.length > 0) {
    subscriptions.forEach((sub, index) => {
      console.log(`\nSubscription ${index + 1}:`);
      console.log(`  ID: ${sub.id}`);
      console.log(`  Plan: ${sub.plan_id}`);
      console.log(`  Status: ${sub.status}`);
      console.log(`  Billing: ${sub.billing_cycle}`);
      console.log(`  Period End: ${sub.current_period_end}`);
      console.log(`  Created: ${sub.created_at}`);
      console.log(`  Updated: ${sub.updated_at}`);
    });
  }
}

checkSubscription();
