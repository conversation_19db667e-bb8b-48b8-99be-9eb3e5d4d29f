// TypeScript script to test usage tracking
import { storage } from '../server/storage';
import { v4 as uuidv4 } from 'uuid';

// Create a test user ID
const testUserId = `test_user_${uuidv4()}`;
console.log(`Testing usage tracking with test user ID: ${testUserId}`);

async function runTest() {
  try {
    // Create a test user
    console.log('Creating test user...');
    await storage.upsertUser({
      id: testUserId,
      username: 'test_user',
      email: '<EMAIL>',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    });
    console.log('Test user created successfully');

    // Test incrementing project count
    console.log('\nTesting incrementProjectCount...');
    const projectUsage = await storage.incrementProjectCount(testUserId);
    console.log('Project usage after first increment:', projectUsage);

    // Test incrementing project count again
    console.log('\nTesting incrementProjectCount again...');
    const projectUsage2 = await storage.incrementProjectCount(testUserId);
    console.log('Project usage after second increment:', projectUsage2);

    // Test incrementing image count
    console.log('\nTesting incrementImageCount...');
    const imageUsage = await storage.incrementImageCount(testUserId, 3);
    console.log('Usage after adding 3 images:', imageUsage);

    // Test incrementing image count again
    console.log('\nTesting incrementImageCount again...');
    const imageUsage2 = await storage.incrementImageCount(testUserId, 2);
    console.log('Usage after adding 2 more images:', imageUsage2);

    // Get current usage
    console.log('\nGetting current usage...');
    const currentUsage = await storage.getCurrentUsage(testUserId);
    console.log('Current usage:', currentUsage);

    // Verify the counts
    console.log('\nVerifying counts...');
    if (currentUsage?.projects_count === 2) {
      console.log('✅ Project count is correct: 2');
    } else {
      console.log(`❌ Project count is incorrect: ${currentUsage?.projects_count}, expected: 2`);
    }

    if (currentUsage?.images_count === 5) {
      console.log('✅ Image count is correct: 5');
    } else {
      console.log(`❌ Image count is incorrect: ${currentUsage?.images_count}, expected: 5`);
    }

    // Clean up test data
    console.log('\nCleaning up test data...');
    const { db } = await import('../server/db');
    await db.client.from('usage').delete().eq('user_id', testUserId);
    await db.client.from('users').delete().eq('id', testUserId);
    console.log('Test data cleaned up successfully');

    console.log('\n✅ Usage tracking test completed successfully!');
  } catch (error) {
    console.error('Error during test:', error);
    process.exit(1);
  }
}

runTest();
