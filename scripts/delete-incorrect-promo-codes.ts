#!/usr/bin/env tsx

import 'dotenv/config';
import { createClient } from '@supabase/supabase-js';
import Stripe from 'stripe';

// Initialize Stripe
if (!process.env.STRIPE_SECRET_KEY) {
  console.error('❌ Missing STRIPE_SECRET_KEY environment variable');
  process.exit(1);
}

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: '2024-06-20',
});

// Initialize Supabase
const supabaseUrl = process.env.SUPABASE_URL as string;
const supabaseKey = process.env.SUPABASE_DB_KEY as string;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase configuration. Check environment variables.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    persistSession: false,
    autoRefreshToken: false,
    detectSessionInUrl: false
  }
});

// The incorrect codes we need to delete (current free_months codes)
const incorrectCodes = [
  'WELCOME6692025',
  'WELCOME358NOW',
  'EXPLORE434NOW',
  'EXPLORE327FREE',
  'WELCOME713MONTH',
  'START2432025',
  'TRYIT514NOW',
  'FREE86100',
  'FREE351100',
  'EXPLORE624100'
];

async function deleteIncorrectCode(code: string): Promise<boolean> {
  try {
    console.log(`🗑️  Deleting incorrect promo code: ${code}`);

    // Get the coupon from database first to get Stripe ID
    const { data: coupon } = await supabase
      .from('coupons')
      .select('stripe_coupon_id')
      .eq('code', code.toUpperCase())
      .single();

    if (coupon?.stripe_coupon_id) {
      // Delete from Stripe first
      try {
        await stripe.coupons.del(coupon.stripe_coupon_id);
        console.log(`   ✅ Deleted from Stripe: ${coupon.stripe_coupon_id}`);
      } catch (stripeError) {
        console.log(`   ⚠️  Stripe deletion failed (may not exist): ${coupon.stripe_coupon_id}`);
      }
    }

    // Delete from database
    const { error } = await supabase
      .from('coupons')
      .delete()
      .eq('code', code.toUpperCase());

    if (error) {
      console.error(`   ❌ Error deleting from database:`, error);
      return false;
    }

    console.log(`   ✅ Deleted from database: ${code}`);
    return true;

  } catch (error) {
    console.error(`   ❌ Error deleting promo code ${code}:`, error);
    return false;
  }
}

async function deleteIncorrectCodes() {
  try {
    console.log('🧹 Deleting incorrect promo codes that give 100% off entire subscription...\n');

    const results = [];
    for (const code of incorrectCodes) {
      const success = await deleteIncorrectCode(code);
      results.push({ code, success });
      
      // Small delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    // Summary
    console.log('\n📊 Deletion Summary:');
    const successful = results.filter(r => r.success);
    const failed = results.filter(r => !r.success);

    console.log(`✅ Successfully deleted: ${successful.length} promo codes`);
    if (failed.length > 0) {
      console.log(`❌ Failed to delete: ${failed.length} promo codes`);
      failed.forEach(f => console.log(`   - ${f.code}`));
    }

    console.log('\n✅ Cleanup completed. Ready to create correct promo codes.');

  } catch (error) {
    console.error('❌ Error deleting incorrect promo codes:', error);
  }
}

// Run the script
deleteIncorrectCodes().then(() => {
  console.log('\n✅ Deletion completed.');
  process.exit(0);
}).catch((error) => {
  console.error('❌ Script failed:', error);
  process.exit(1);
});
