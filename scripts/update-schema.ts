import { sql } from 'drizzle-orm';
import { db, pool } from '../server/db';

// Add a simple structured logger for scripts
const scriptLogger = {
  info: (...args: any[]) => console.info('[SCHEMA]', ...args),
  warn: (...args: any[]) => console.warn('[SCHEMA]', ...args),
  error: (...args: any[]) => console.error('[SCHEMA]', ...args),
  debug: (...args: any[]) => {
    if (process.env.NODE_ENV === 'development') {
      console.debug('[SCHEMA]', ...args);
    }
  },
};

async function updateSchema() {
  scriptLogger.info('Beginning database schema update...');

  try {
    // Start a transaction
    await db.execute(sql`BEGIN`);

    // Drop existing users table and recreate it with new schema
    scriptLogger.info('Updating users table...');
    await db.execute(sql`
      -- Drop related tables with foreign key constraints first
      DROP TABLE IF EXISTS projects CASCADE;
      DROP TABLE IF EXISTS images CASCADE;
      DROP TABLE IF EXISTS modifications CASCADE;
      DROP TABLE IF EXISTS reference_categories CASCADE;
      DROP TABLE IF EXISTS reference_items CASCADE;
      DROP TABLE IF EXISTS renovation_presets CASCADE;

      -- Drop the users table
      DROP TABLE IF EXISTS users CASCADE;

      -- Create new users table
      CREATE TABLE users (
        id TEXT PRIMARY KEY NOT NULL,
        username TEXT UNIQUE NOT NULL,
        email TEXT UNIQUE,
        first_name TEXT,
        last_name TEXT,
        bio TEXT,
        profile_image_url TEXT,
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW()
      );
    `);

    // Create sessions table for Replit Auth
    scriptLogger.info('Creating sessions table...');
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS sessions (
        sid TEXT PRIMARY KEY,
        sess JSONB NOT NULL,
        expire TIMESTAMP NOT NULL
      );
      CREATE INDEX IF NOT EXISTS IDX_session_expire ON sessions (expire);
    `);

    // Recreate other tables that depend on users
    scriptLogger.info('Recreating projects table...');
    await db.execute(sql`
      CREATE TABLE projects (
        id SERIAL PRIMARY KEY,
        user_id TEXT REFERENCES users(id),
        title TEXT NOT NULL,
        description TEXT,
        created_at TIMESTAMP DEFAULT NOW() NOT NULL,
        status TEXT NOT NULL DEFAULT 'draft'
      );
    `);

    scriptLogger.info('Recreating images table...');
    await db.execute(sql`
      CREATE TABLE images (
        id SERIAL PRIMARY KEY,
        project_id INTEGER REFERENCES projects(id),
        type TEXT NOT NULL,
        path TEXT NOT NULL,
        original_filename TEXT,
        uploaded_at TIMESTAMP DEFAULT NOW() NOT NULL
      );
    `);

    scriptLogger.info('Recreating modifications table...');
    await db.execute(sql`
      CREATE TABLE modifications (
        id SERIAL PRIMARY KEY,
        project_id INTEGER REFERENCES projects(id),
        description TEXT NOT NULL,
        reference_image_ids JSONB,
        created_at TIMESTAMP DEFAULT NOW() NOT NULL
      );
    `);

    scriptLogger.info('Recreating reference_categories table...');
    await db.execute(sql`
      CREATE TABLE reference_categories (
        id SERIAL PRIMARY KEY,
        user_id TEXT REFERENCES users(id) NOT NULL,
        name TEXT NOT NULL,
        description TEXT,
        created_at TIMESTAMP DEFAULT NOW() NOT NULL
      );
    `);

    scriptLogger.info('Recreating reference_items table...');
    await db.execute(sql`
      CREATE TABLE reference_items (
        id SERIAL PRIMARY KEY,
        user_id TEXT REFERENCES users(id) NOT NULL,
        category_id INTEGER REFERENCES reference_categories(id),
        name TEXT NOT NULL,
        description TEXT,
        image_path TEXT NOT NULL,
        tags JSONB,
        created_at TIMESTAMP DEFAULT NOW() NOT NULL
      );
    `);

    scriptLogger.info('Recreating renovation_presets table...');
    await db.execute(sql`
      CREATE TABLE renovation_presets (
        id SERIAL PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        room_type TEXT NOT NULL,
        prompt_template TEXT NOT NULL,
        is_default BOOLEAN DEFAULT FALSE,
        created_by TEXT REFERENCES users(id),
        is_public BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT NOW() NOT NULL,
        tags JSONB,
        image_url TEXT
      );
    `);

    // Commit the transaction
    await db.execute(sql`COMMIT`);
    scriptLogger.info('Database schema updated successfully!');
  } catch (error) {
    // Rollback in case of error
    await db.execute(sql`ROLLBACK`);
    scriptLogger.error('Error updating database schema:', error);
    throw error;
  } finally {
    // Close the pool
    await pool.end();
  }
}

updateSchema().catch((err) => scriptLogger.error('Unhandled error in updateSchema:', err));