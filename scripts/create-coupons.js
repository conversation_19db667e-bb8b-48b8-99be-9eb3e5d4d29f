#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to create various types of coupons in Stripe and the database
 *
 * Usage:
 * node scripts/create-coupons.js
 *
 * This script will create the following coupons:
 * 1. WELCOME25 - 25% off first payment
 * 2. FLAT20 - $20 off first payment
 * 3. MONTHLY10 - 10% off recurring monthly payments
 * 4. ANNUAL15 - 15% off recurring annual payments
 * 5. FREE1MONTH - First month free (100% off for 1 month)
 * 6. FREE2MONTHS - First 2 months free (100% off for 2 months)
 */

import 'dotenv/config';
import Stripe from 'stripe';
import { createClient } from '@supabase/supabase-js';

// Initialize Stripe
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: "2025-03-31.basil",
});

// Initialize Supabase
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_DB_KEY
);

// Helper function to create a coupon in both Stripe and the database
async function createCoupon(couponData) {
  try {
    console.log(`Creating coupon ${couponData.code} in Stripe...`);

    // Create the coupon in Stripe
    const stripeParams = {
      name: couponData.description,
      metadata: {
        code: couponData.code,
        type: couponData.type,
      }
    };

    // Set up the coupon parameters based on the type
    if (couponData.type === 'percentage') {
      stripeParams.percent_off = couponData.amount;
    } else if (couponData.type === 'fixed_amount') {
      stripeParams.amount_off = Math.round(couponData.amount * 100); // Convert to cents
      stripeParams.currency = 'aud';
    } else if (couponData.type === 'free_months') {
      stripeParams.percent_off = 100;
      stripeParams.duration = 'repeating';
      stripeParams.duration_in_months = couponData.amount;
    }

    // Set the duration
    if (couponData.type !== 'free_months') {
      stripeParams.duration = couponData.duration;
      if (couponData.duration === 'repeating' && couponData.duration_in_months) {
        stripeParams.duration_in_months = couponData.duration_in_months;
      }
    }

    // Set expiration if valid_until is provided
    if (couponData.valid_until) {
      stripeParams.redeem_by = Math.floor(couponData.valid_until.getTime() / 1000);
    }

    // Create the coupon in Stripe
    const stripeCoupon = await stripe.coupons.create(stripeParams);

    console.log(`Created Stripe coupon: ${stripeCoupon.id}`);

    // Create the coupon in the database
    const { data, error } = await supabase
      .from('coupons')
      .insert({
        ...couponData,
        stripe_coupon_id: stripeCoupon.id,
        valid_from: couponData.valid_from.toISOString(),
        valid_until: couponData.valid_until ? couponData.valid_until.toISOString() : null,
      })
      .select();

    if (error) {
      console.error(`Error creating coupon ${couponData.code} in database:`, error);
      return null;
    }

    console.log(`Successfully created coupon ${couponData.code} in database:`, data[0]);
    return data[0];

  } catch (error) {
    console.error(`Error creating coupon ${couponData.code}:`, error);
    return null;
  }
}

async function createAllCoupons() {
  const now = new Date();
  const oneYearFromNow = new Date();
  oneYearFromNow.setFullYear(oneYearFromNow.getFullYear() + 1);

  const coupons = [
    {
      code: 'WELCOME25',
      description: 'Welcome Offer - 25% off your first payment',
      type: 'percentage',
      amount: 25,
      duration: 'once',
      valid_from: now,
      valid_until: oneYearFromNow,
      is_active: true,
      redemption_count: 0
    },
    {
      code: 'FLAT20',
      description: '$20 off your first payment',
      type: 'fixed_amount',
      amount: 20,
      duration: 'once',
      valid_from: now,
      valid_until: oneYearFromNow,
      is_active: true,
      redemption_count: 0
    },
    {
      code: 'MONTHLY10',
      description: '10% off recurring monthly payments',
      type: 'percentage',
      amount: 10,
      duration: 'forever',
      valid_from: now,
      valid_until: oneYearFromNow,
      is_active: true,
      redemption_count: 0
    },
    {
      code: 'ANNUAL15',
      description: '15% off recurring annual payments',
      type: 'percentage',
      amount: 15,
      duration: 'forever',
      valid_from: now,
      valid_until: oneYearFromNow,
      is_active: true,
      redemption_count: 0
    },
    {
      code: 'FREE1MONTH',
      description: 'First month free',
      type: 'free_months',
      amount: 1,
      duration: 'repeating',
      duration_in_months: 1,
      valid_from: now,
      valid_until: oneYearFromNow,
      is_active: true,
      redemption_count: 0
    },
    {
      code: 'FREE2MONTHS',
      description: 'First 2 months free',
      type: 'free_months',
      amount: 2,
      duration: 'repeating',
      duration_in_months: 2,
      valid_from: now,
      valid_until: oneYearFromNow,
      is_active: true,
      redemption_count: 0
    }
  ];

  console.log('Creating coupons...');

  for (const couponData of coupons) {
    await createCoupon(couponData);
    console.log('-----------------------------------');
  }

  console.log('\nAll coupons created successfully!');
  console.log('\nAvailable coupon codes:');
  coupons.forEach(coupon => {
    console.log(`- ${coupon.code}: ${coupon.description}`);
  });
}

createAllCoupons();
