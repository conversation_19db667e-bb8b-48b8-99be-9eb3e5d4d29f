#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to create a test coupon in Stripe and the database
 *
 * Usage:
 * node scripts/create-test-coupon.js
 *
 * This script will create a test coupon with the following details:
 * - Code: TEST25 (25% off)
 * - Duration: once
 * - Valid for 30 days
 */

import 'dotenv/config';
import Strip<PERSON> from 'stripe';
import { createClient } from '@supabase/supabase-js';

// Initialize Stripe
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: "2025-03-31.basil",
});

// Initialize Supabase
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_DB_KEY
);

async function createTestCoupon() {
  try {
    console.log('Creating test coupon in Stripe...');

    // Create the coupon in Stripe
    const stripeCoupon = await stripe.coupons.create({
      name: 'Test Coupon - 25% Off',
      percent_off: 25,
      duration: 'once',
      metadata: {
        code: 'TEST25',
        type: 'percentage',
      }
    });

    console.log(`Created Stripe coupon: ${stripeCoupon.id}`);

    // Create the coupon in the database
    const now = new Date();
    const validUntil = new Date();
    validUntil.setDate(validUntil.getDate() + 30); // Valid for 30 days

    const { data, error } = await supabase
      .from('coupons')
      .insert({
        code: 'TEST25',
        stripe_coupon_id: stripeCoupon.id,
        description: 'Test Coupon - 25% Off',
        type: 'percentage',
        amount: 25,
        duration: 'once',
        valid_from: now.toISOString(),
        valid_until: validUntil.toISOString(),
        is_active: true,
        redemption_count: 0
      })
      .select();

    if (error) {
      console.error('Error creating coupon in database:', error);
      return;
    }

    console.log('Successfully created test coupon in database:', data[0]);
    console.log('\nTest coupon details:');
    console.log('- Code: TEST25');
    console.log('- Discount: 25% off');
    console.log('- Duration: One-time use');
    console.log(`- Valid until: ${validUntil.toLocaleDateString()}`);
    console.log('\nYou can use this coupon for testing the checkout process.');

  } catch (error) {
    console.error('Error creating test coupon:', error);
  }
}

createTestCoupon();
