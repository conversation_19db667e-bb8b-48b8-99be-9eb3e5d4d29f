#!/usr/bin/env node

/**
 * Script to import an existing Stripe coupon into our database
 * 
 * Usage:
 * node scripts/import-stripe-coupon.js COUPON_ID
 * 
 * Where COUPON_ID is the ID of the coupon in Stripe (not the code)
 */

import 'dotenv/config';
import Stripe from 'stripe';
import { createClient } from '@supabase/supabase-js';

// Initialize Stripe
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: "2025-03-31.basil",
});

// Initialize Supabase
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_DB_KEY
);

async function importStripeCoupon(couponId) {
  try {
    console.log(`Fetching coupon ${couponId} from Stripe...`);
    
    // Get the coupon from Stripe
    const stripeCoupon = await stripe.coupons.retrieve(couponId);
    
    if (!stripeCoupon) {
      console.error(`Coupon ${couponId} not found in Stripe`);
      return;
    }
    
    console.log('Found coupon in Stripe:', stripeCoupon);
    
    // Extract coupon details
    const now = new Date();
    const validUntil = stripeCoupon.redeem_by ? new Date(stripeCoupon.redeem_by * 1000) : null;
    
    // Determine coupon type and amount
    let type, amount;
    if (stripeCoupon.percent_off) {
      type = 'percentage';
      amount = stripeCoupon.percent_off;
    } else if (stripeCoupon.amount_off) {
      type = 'fixed_amount';
      amount = stripeCoupon.amount_off / 100; // Convert from cents to dollars
    } else {
      console.error('Coupon has neither percent_off nor amount_off');
      return;
    }
    
    // Get coupon code from metadata or use ID
    const code = stripeCoupon.metadata?.code || stripeCoupon.id;
    
    // Check if coupon already exists in our database
    const { data: existingCoupon } = await supabase
      .from('coupons')
      .select('*')
      .eq('stripe_coupon_id', couponId)
      .single();
    
    if (existingCoupon) {
      console.log(`Coupon already exists in database with code ${existingCoupon.code}`);
      return;
    }
    
    // Create the coupon in our database
    const { data, error } = await supabase
      .from('coupons')
      .insert({
        code: code.toUpperCase(),
        stripe_coupon_id: couponId,
        description: stripeCoupon.name || `${type === 'percentage' ? amount + '%' : '$' + amount} off`,
        type,
        amount,
        duration: stripeCoupon.duration,
        duration_in_months: stripeCoupon.duration_in_months || null,
        valid_from: now.toISOString(),
        valid_until: validUntil ? validUntil.toISOString() : null,
        is_active: true,
        redemption_count: 0
      })
      .select();
    
    if (error) {
      console.error('Error creating coupon in database:', error);
      return;
    }
    
    console.log('Successfully imported coupon into database:', data[0]);
    console.log('\nCoupon details:');
    console.log(`- Code: ${code.toUpperCase()}`);
    console.log(`- Type: ${type}`);
    console.log(`- Amount: ${amount}${type === 'percentage' ? '%' : ' AUD'}`);
    console.log(`- Duration: ${stripeCoupon.duration}${stripeCoupon.duration_in_months ? ' (' + stripeCoupon.duration_in_months + ' months)' : ''}`);
    if (validUntil) {
      console.log(`- Valid until: ${validUntil.toLocaleDateString()}`);
    }
    
  } catch (error) {
    console.error('Error importing coupon:', error);
  }
}

// Get coupon ID from command line arguments
const couponId = process.argv[2];

if (!couponId) {
  console.error('Please provide a Stripe coupon ID');
  process.exit(1);
}

importStripeCoupon(couponId);
