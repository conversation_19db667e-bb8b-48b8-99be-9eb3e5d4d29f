import { Page } from '@playwright/test';

/**
 * Helper function to get a test auth token from Clerk
 */
export async function getTestAuthToken(): Promise<string | null> {
  // For testing purposes, we'll use the Clerk API to get a token
  // In a real environment, you would use a test user account
  
  if (!process.env.CLERK_SECRET_KEY) {
    console.error('CLERK_SECRET_KEY is not set. Cannot get test auth token.');
    return null;
  }
  
  try {
    // This is a simplified example - in a real test, you would:
    // 1. Create a test user if it doesn't exist
    // 2. Get a session token for that user
    // For now, we'll return a placeholder
    return process.env.TEST_AUTH_TOKEN || null;
  } catch (error) {
    console.error('Error getting test auth token:', error);
    return null;
  }
}

/**
 * Helper function to authenticate a page for E2E tests
 */
export async function authenticatePage(page: Page): Promise<void> {
  // Store the auth token in localStorage
  await page.evaluate(() => {
    localStorage.setItem('clerk-auth-token', 'test-token');
    
    // Also set up the auth cache
    const authCache = {
      isAuthenticated: true,
      authToken: 'test-token',
      isInitialized: true,
      lastUpdated: Date.now()
    };
    sessionStorage.setItem('renovate-ai-auth-cache', JSON.stringify(authCache));
  });
  
  // Reload the page to apply the auth
  await page.reload();
}
