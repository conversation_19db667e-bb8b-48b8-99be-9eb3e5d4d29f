/**
 * Subscription E2E test utilities
 * 
 * This module provides utilities for subscription E2E testing.
 */

import { createTestSubscriptionData } from '../fixtures/subscriptions';

/**
 * Set up a subscription in localStorage
 * @param {Page} page Playwright page
 * @param {Object} params Subscription parameters
 */
export async function setupSubscription(page, params = {}) {
  const subscriptionData = createTestSubscriptionData(params);
  
  await page.evaluate((data) => {
    localStorage.setItem('subscription-data', JSON.stringify(data));
  }, subscriptionData);
}

/**
 * Clear subscription data from localStorage
 * @param {Page} page Playwright page
 */
export async function clearSubscription(page) {
  await page.evaluate(() => {
    localStorage.removeItem('subscription-data');
  });
}

/**
 * Set up authentication for testing
 * @param {Page} page Playwright page
 * @param {Object} params Auth parameters
 */
export async function setupAuth(page, params = {}) {
  const userId = params.userId || 'test_user_123';
  const email = params.email || '<EMAIL>';
  const firstName = params.firstName || 'Test';
  const lastName = params.lastName || 'User';
  const username = params.username || 'testuser';

  await page.evaluate(({ userId, email, firstName, lastName, username }) => {
    // Set up auth data in localStorage
    localStorage.setItem('clerk-auth-token', 'test-token');

    // Set up auth cache
    const authCache = {
      isAuthenticated: true,
      authToken: 'test-token',
      isInitialized: true,
      lastUpdated: Date.now(),
      user: {
        id: userId,
        email,
        firstName,
        lastName,
        username
      }
    };
    sessionStorage.setItem('renovate-ai-auth-cache', JSON.stringify(authCache));
  }, { userId, email, firstName, lastName, username });
}

/**
 * Mock API responses for subscription operations
 * @param {Page} page Playwright page
 */
export async function mockSubscriptionApis(page) {
  // Mock subscription API
  await page.route('**/api/subscription', async (route) => {
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify(createTestSubscriptionData({
        plan_id: 'professional',
        billing_cycle: 'annual'
      }))
    });
  });

  // Mock subscription cancel API
  await page.route('**/api/subscription/cancel', async (route) => {
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify({ 
        success: true, 
        message: 'Subscription cancelled successfully',
        subscription: {
          id: 'sub_test123',
          cancel_at_period_end: true
        }
      })
    });
  });

  // Mock subscription reactivate API
  await page.route('**/api/subscription/reactivate', async (route) => {
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify({ 
        success: true, 
        message: 'Subscription reactivated successfully',
        subscription: {
          id: 'sub_test123',
          cancel_at_period_end: false
        }
      })
    });
  });

  // Mock checkout session API
  await page.route('**/api/create-checkout-session', async (route) => {
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify({ 
        url: 'https://checkout.stripe.com/c/pay/test_checkout_session'
      })
    });
  });

  // Mock manual subscription creation API
  await page.route('**/api/manual-subscription-create', async (route) => {
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify({ 
        success: true, 
        message: 'Subscription created successfully',
        subscription: {
          id: 1,
          user_id: 'test_user_123',
          plan_id: 'professional',
          billing_cycle: 'annual',
          status: 'active',
          stripe_customer_id: 'cus_test123',
          stripe_subscription_id: 'sub_test123',
          current_period_start: new Date().toISOString(),
          current_period_end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
          cancel_at_period_end: false
        }
      })
    });
  });
}

/**
 * Test subscription cancellation flow
 * @param {Page} page Playwright page
 */
export async function testCancellationFlow(page) {
  // Navigate to the account page
  await page.goto('/account');

  // Wait for the subscription details to load
  await page.waitForSelector('text=Subscription Details');

  // Click the cancel subscription button
  await page.locator('button:text("Cancel Subscription")').click();

  // Confirm the cancellation in the dialog
  await page.locator('button:text("Yes, cancel")').click();

  // Wait for the success message
  await page.waitForSelector('text=Subscription cancelled successfully');

  // Check that the subscription is now marked as ending
  await page.locator('text=Subscription Ending').isVisible();
}

/**
 * Test subscription reactivation flow
 * @param {Page} page Playwright page
 */
export async function testReactivationFlow(page) {
  // Navigate to the account page
  await page.goto('/account');

  // Wait for the subscription details to load
  await page.waitForSelector('text=Subscription Details');

  // Check that the subscription is marked as ending
  await page.locator('text=Subscription Ending').isVisible();

  // Click the reactivate subscription button
  await page.locator('button:text("Reactivate Subscription")').click();

  // Wait for the success message
  await page.waitForSelector('text=Subscription reactivated successfully');

  // Check that the subscription is no longer marked as ending
  await page.locator('text=Subscription Ending').isHidden();
}

/**
 * Test checkout flow
 * @param {Page} page Playwright page
 * @param {Object} params Checkout parameters
 */
export async function testCheckoutFlow(page, params = {}) {
  const planId = params.planId || 'professional';
  const billingCycle = params.billingCycle || 'annual';

  // Navigate to the pricing page
  await page.goto('/pricing');

  // Wait for the pricing plans to load
  await page.waitForSelector('text=Pricing Plans');

  // Click the subscribe button for the specified plan
  await page.locator(`text=${planId} >> .. >> button:text("Subscribe")`).click();

  // Select billing cycle if available
  if (billingCycle === 'annual') {
    const annualBillingOption = page.locator('text=Annual (20% discount)');
    if (await annualBillingOption.isVisible()) {
      await annualBillingOption.click();
    }
  } else {
    const monthlyBillingOption = page.locator('text=Monthly');
    if (await monthlyBillingOption.isVisible()) {
      await monthlyBillingOption.click();
    }
  }

  // Click the confirm button
  await page.locator('button:text("Confirm")').click();

  // Check that we're redirected to Stripe checkout
  // Since we're mocking, we'll just check that the redirect was attempted
  await page.waitForRequest(request => 
    request.url().includes('checkout.stripe.com') || 
    request.url().includes('api/create-checkout-session')
  );
}

export default {
  setupSubscription,
  clearSubscription,
  setupAuth,
  mockSubscriptionApis,
  testCancellationFlow,
  testReactivationFlow,
  testCheckoutFlow
};
