/**
 * Stripe testing utilities
 *
 * This module provides utilities for mocking Stripe API responses in tests.
 * It uses <PERSON><PERSON>'s mocking capabilities to intercept Stripe API calls and return
 * predefined responses.
 */

const Stripe = require('stripe');

/**
 * Create Stripe mocks for testing
 * @returns {Object} Stripe mock utilities
 */
function createStripeMocks() {
  // Store original implementation
  const originalStripe = jest.requireActual('stripe');

  // Mock Stripe constructor
  jest.mock('stripe', () => {
    return jest.fn().mockImplementation(() => {
      return {
        customers: {
          create: jest.fn(),
          retrieve: jest.fn(),
          update: jest.fn(),
        },
        subscriptions: {
          create: jest.fn(),
          retrieve: jest.fn(),
          update: jest.fn(),
          del: jest.fn(),
        },
        checkout: {
          sessions: {
            create: jest.fn(),
            retrieve: jest.fn(),
          },
        },
        webhooks: {
          constructEvent: jest.fn(),
        },
      };
    });
  });

  // Get the mocked Stripe instance
  const stripe = new Stripe('sk_test_mock');

  return {
    /**
     * Mock customer creation
     * @param {Object} customer Customer data to return
     */
    mockCustomerCreate: (customer) => {
      stripe.customers.create.mockResolvedValue(customer);
    },

    /**
     * Mock customer retrieval
     * @param {Object} customer Customer data to return
     */
    mockCustomerRetrieve: (customer) => {
      stripe.customers.retrieve.mockResolvedValue(customer);
    },

    /**
     * Mock customer update
     * @param {Object} customer Updated customer data to return
     */
    mockCustomerUpdate: (customer) => {
      stripe.customers.update.mockResolvedValue(customer);
    },

    /**
     * Mock subscription creation
     * @param {Object} subscription Subscription data to return
     */
    mockSubscriptionCreate: (subscription) => {
      stripe.subscriptions.create.mockResolvedValue(subscription);
    },

    /**
     * Mock subscription retrieval
     * @param {Object} subscription Subscription data to return
     */
    mockSubscriptionRetrieve: (subscription) => {
      stripe.subscriptions.retrieve.mockResolvedValue(subscription);
    },

    /**
     * Mock subscription update
     * @param {Object} subscription Updated subscription data to return
     */
    mockSubscriptionUpdate: (subscription) => {
      stripe.subscriptions.update.mockResolvedValue(subscription);
    },

    /**
     * Mock subscription deletion
     * @param {Object} subscription Deleted subscription data to return
     */
    mockSubscriptionDelete: (subscription) => {
      stripe.subscriptions.del.mockResolvedValue(subscription);
    },

    /**
     * Mock subscription update error
     * @param {Object} params Parameters including error to throw
     */
    mockSubscriptionUpdateError: (params) => {
      const error = new Error(params.error.message);
      error.type = params.error.type;
      error.statusCode = params.statusCode || 400;
      stripe.subscriptions.update.mockRejectedValue(error);
    },

    /**
     * Mock subscription update with delay
     * @param {Object} params Parameters including delay in ms
     */
    mockSubscriptionUpdateWithDelay: (params) => {
      stripe.subscriptions.update.mockImplementation(() => {
        return new Promise((resolve, reject) => {
          setTimeout(() => {
            if (params.error) {
              reject(new Error(params.error.message));
            } else {
              resolve({
                id: params.id,
                cancel_at_period_end: params.cancel_at_period_end || false
              });
            }
          }, params.delay || 5000);
        });
      });
    },

    /**
     * Mock checkout session creation
     * @param {Object} session Checkout session data to return
     */
    mockCheckoutSessionCreate: (session) => {
      stripe.checkout.sessions.create.mockResolvedValue(session);
    },

    /**
     * Mock checkout session retrieval
     * @param {Object} session Checkout session data to return
     */
    mockCheckoutSessionRetrieve: (session) => {
      stripe.checkout.sessions.retrieve.mockResolvedValue(session);
    },

    /**
     * Mock webhook event construction
     * @param {Object} event Webhook event data to return
     */
    mockWebhookConstructEvent: (event) => {
      stripe.webhooks.constructEvent.mockReturnValue(event);
    },

    /**
     * Reset all mocks
     */
    resetMocks: () => {
      jest.resetAllMocks();
    },

    /**
     * Get the mocked Stripe instance
     * @returns {Object} Mocked Stripe instance
     */
    getStripe: () => stripe,
  };
}

/**
 * Create a mock Stripe subscription object
 * @param {Object} params Subscription parameters
 * @returns {Object} Mock subscription object
 */
function mockStripeSubscription(params = {}) {
  const now = Math.floor(Date.now() / 1000);
  const oneMonthLater = now + 30 * 24 * 60 * 60;

  return {
    id: params.id || 'sub_mock' + Math.random().toString(36).substring(2, 10),
    object: 'subscription',
    application: null,
    application_fee_percent: null,
    automatic_tax: { enabled: false },
    billing_cycle_anchor: now,
    billing_thresholds: null,
    cancel_at: null,
    cancel_at_period_end: params.cancel_at_period_end || false,
    canceled_at: null,
    collection_method: 'charge_automatically',
    created: now,
    currency: 'aud',
    current_period_end: params.current_period_end || oneMonthLater,
    current_period_start: params.current_period_start || now,
    customer: params.customer || 'cus_mock' + Math.random().toString(36).substring(2, 10),
    days_until_due: null,
    default_payment_method: null,
    default_source: null,
    default_tax_rates: [],
    description: null,
    discount: null,
    ended_at: null,
    items: {
      object: 'list',
      data: [
        {
          id: params.item_id || 'si_mock' + Math.random().toString(36).substring(2, 10),
          object: 'subscription_item',
          billing_thresholds: null,
          created: now,
          metadata: {},
          price: {
            id: params.price_id || 'price_mock' + Math.random().toString(36).substring(2, 10),
            object: 'price',
            active: true,
            billing_scheme: 'per_unit',
            created: now,
            currency: 'aud',
            custom_unit_amount: null,
            livemode: false,
            lookup_key: null,
            metadata: {},
            nickname: null,
            product: 'prod_mock' + Math.random().toString(36).substring(2, 10),
            recurring: {
              aggregate_usage: null,
              interval: params.interval || 'month',
              interval_count: 1,
              trial_period_days: null,
              usage_type: 'licensed'
            },
            tax_behavior: 'unspecified',
            tiers_mode: null,
            transform_quantity: null,
            type: 'recurring',
            unit_amount: params.unit_amount || 4900,
            unit_amount_decimal: params.unit_amount ? params.unit_amount.toString() : '4900'
          },
          quantity: 1,
          subscription: params.id || 'sub_mock' + Math.random().toString(36).substring(2, 10),
          tax_rates: []
        }
      ],
      has_more: false,
      total_count: 1,
      url: '/v1/subscription_items?subscription=' + (params.id || 'sub_mock' + Math.random().toString(36).substring(2, 10))
    },
    latest_invoice: 'in_mock' + Math.random().toString(36).substring(2, 10),
    livemode: false,
    metadata: params.metadata || {},
    next_pending_invoice_item_invoice: null,
    on_behalf_of: null,
    pause_collection: null,
    payment_settings: {
      payment_method_options: null,
      payment_method_types: null,
      save_default_payment_method: 'off'
    },
    pending_invoice_item_interval: null,
    pending_setup_intent: null,
    pending_update: null,
    plan: {
      id: params.plan_id || 'plan_mock' + Math.random().toString(36).substring(2, 10),
      object: 'plan',
      active: true,
      aggregate_usage: null,
      amount: params.unit_amount || 4900,
      amount_decimal: params.unit_amount ? params.unit_amount.toString() : '4900',
      billing_scheme: 'per_unit',
      created: now,
      currency: 'aud',
      interval: params.interval || 'month',
      interval_count: 1,
      livemode: false,
      metadata: {},
      nickname: null,
      product: 'prod_mock' + Math.random().toString(36).substring(2, 10),
      tiers: null,
      tiers_mode: null,
      transform_usage: null,
      trial_period_days: null,
      usage_type: 'licensed'
    },
    quantity: 1,
    schedule: null,
    start_date: now,
    status: params.status || 'active',
    test_clock: null,
    transfer_data: null,
    trial_end: null,
    trial_start: null
  };
}

/**
 * Create a mock Stripe checkout session object
 * @param {Object} params Checkout session parameters
 * @returns {Object} Mock checkout session object
 */
function mockStripeCheckoutSession(params = {}) {
  const now = Math.floor(Date.now() / 1000);

  return {
    id: params.id || 'cs_test_' + Math.random().toString(36).substring(2, 10),
    object: 'checkout.session',
    after_expiration: null,
    allow_promotion_codes: null,
    amount_subtotal: params.amount_subtotal || 4900,
    amount_total: params.amount_total || 4900,
    automatic_tax: { enabled: false, status: null },
    billing_address_collection: null,
    cancel_url: params.cancel_url || 'http://localhost:3000/pricing',
    client_reference_id: null,
    consent: null,
    consent_collection: null,
    created: now,
    currency: 'aud',
    custom_fields: [],
    custom_text: { shipping_address: null, submit: null },
    customer: params.customer || 'cus_mock' + Math.random().toString(36).substring(2, 10),
    customer_creation: 'always',
    customer_details: {
      address: { city: null, country: null, line1: null, line2: null, postal_code: null, state: null },
      email: params.email || '<EMAIL>',
      name: params.name || 'Test User',
      phone: null,
      tax_exempt: 'none',
      tax_ids: []
    },
    customer_email: params.email || '<EMAIL>',
    expires_at: now + 24 * 60 * 60,
    invoice: null,
    invoice_creation: { enabled: false, invoice_data: { account_tax_ids: null, custom_fields: null, description: null, footer: null, metadata: {}, rendering_options: null } },
    livemode: false,
    locale: null,
    metadata: params.metadata || {},
    mode: 'subscription',
    payment_intent: null,
    payment_link: null,
    payment_method_collection: 'always',
    payment_method_options: {},
    payment_method_types: ['card'],
    payment_status: 'paid',
    phone_number_collection: { enabled: false },
    recovered_from: null,
    setup_intent: null,
    shipping_address_collection: null,
    shipping_cost: null,
    shipping_details: null,
    shipping_options: [],
    status: 'complete',
    submit_type: null,
    subscription: params.subscription || 'sub_mock' + Math.random().toString(36).substring(2, 10),
    success_url: params.success_url || 'http://localhost:3000/payment-success?session_id={CHECKOUT_SESSION_ID}',
    total_details: { amount_discount: 0, amount_shipping: 0, amount_tax: 0 },
    url: params.url || null
  };
}

/**
 * Create a mock Stripe webhook event
 * @param {string} type Event type
 * @param {Object} data Event data
 * @returns {Object} Mock webhook event
 */
function mockStripeWebhookEvent(type, data) {
  const now = Math.floor(Date.now() / 1000);

  return {
    id: 'evt_' + Math.random().toString(36).substring(2, 10),
    object: 'event',
    api_version: '2025-03-31.basil',
    created: now,
    data: {
      object: data
    },
    livemode: false,
    pending_webhooks: 1,
    request: {
      id: 'req_' + Math.random().toString(36).substring(2, 10),
      idempotency_key: 'idempotency_' + Math.random().toString(36).substring(2, 10)
    },
    type
  };
}

module.exports = {
  createStripeMocks,
  mockStripeSubscription,
  mockStripeCheckoutSession,
  mockStripeWebhookEvent
};
