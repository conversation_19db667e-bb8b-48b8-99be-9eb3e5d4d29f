// Database utilities for testing
import { createClient } from '@supabase/supabase-js';

// Test database configuration
const TEST_SUPABASE_URL = process.env.SUPABASE_URL || 'https://test.supabase.co';
const TEST_SUPABASE_ANON_KEY = process.env.SUPABASE_ANON_KEY || 'test-key';

// Create test database client
export const testDb = createClient(TEST_SUPABASE_URL, TEST_SUPABASE_ANON_KEY);

// Test user ID for consistent testing
export const TEST_USER_ID = 'test-user-123';

// Database cleanup utilities
export async function cleanupDatabase() {
  try {
    // Clean up test data in reverse dependency order
    await testDb.from('project_images').delete().neq('id', 0);
    await testDb.from('projects').delete().neq('id', 0);
    await testDb.from('user_subscriptions').delete().neq('id', 0);
    await testDb.from('coupons').delete().neq('id', 0);
    await testDb.from('reference_images').delete().neq('id', 0);
    await testDb.from('processed_webhook_events').delete().neq('id', 0);
    
    console.log('Database cleanup completed');
  } catch (error) {
    console.warn('Database cleanup failed:', error);
    // Don't throw error to avoid breaking tests
  }
}

// Setup test data
export async function setupTestData() {
  try {
    // Insert test reference images
    const { data: referenceImages } = await testDb
      .from('reference_images')
      .insert([
        {
          id: 1,
          name: 'Modern Hardwood',
          category: 'flooring',
          image_url: '/uploads/reference/hardwood1.jpg',
          description: 'Modern hardwood flooring'
        },
        {
          id: 2,
          name: 'Classic Tile',
          category: 'flooring',
          image_url: '/uploads/reference/tile1.jpg',
          description: 'Classic tile flooring'
        }
      ])
      .select();

    // Insert test user subscription
    const { data: subscription } = await testDb
      .from('user_subscriptions')
      .insert([
        {
          user_id: TEST_USER_ID,
          subscription_id: 'sub_test123',
          plan_id: 'professional',
          billing_cycle: 'monthly',
          status: 'active',
          current_period_start: new Date().toISOString(),
          current_period_end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
        }
      ])
      .select();

    console.log('Test data setup completed');
    return { referenceImages, subscription };
  } catch (error) {
    console.warn('Test data setup failed:', error);
    // Return empty data to avoid breaking tests
    return { referenceImages: [], subscription: [] };
  }
}

// Mock database responses for tests that don't need real database
export const mockDatabaseResponses = {
  projects: {
    select: jest.fn().mockResolvedValue({
      data: [
        {
          id: 1,
          user_id: TEST_USER_ID,
          title: 'Test Project',
          status: 'completed',
          created_at: new Date().toISOString()
        }
      ],
      error: null
    }),
    insert: jest.fn().mockResolvedValue({
      data: [
        {
          id: 1,
          user_id: TEST_USER_ID,
          title: 'Test Project',
          status: 'draft',
          created_at: new Date().toISOString()
        }
      ],
      error: null
    }),
    update: jest.fn().mockResolvedValue({
      data: [
        {
          id: 1,
          user_id: TEST_USER_ID,
          title: 'Updated Test Project',
          status: 'completed',
          updated_at: new Date().toISOString()
        }
      ],
      error: null
    }),
    delete: jest.fn().mockResolvedValue({
      data: [],
      error: null
    })
  },
  
  user_subscriptions: {
    select: jest.fn().mockResolvedValue({
      data: [
        {
          user_id: TEST_USER_ID,
          subscription_id: 'sub_test123',
          plan_id: 'professional',
          billing_cycle: 'monthly',
          status: 'active'
        }
      ],
      error: null
    }),
    insert: jest.fn().mockResolvedValue({
      data: [
        {
          user_id: TEST_USER_ID,
          subscription_id: 'sub_test123',
          plan_id: 'professional',
          billing_cycle: 'monthly',
          status: 'active'
        }
      ],
      error: null
    }),
    update: jest.fn().mockResolvedValue({
      data: [
        {
          user_id: TEST_USER_ID,
          subscription_id: 'sub_test123',
          plan_id: 'professional',
          billing_cycle: 'monthly',
          status: 'cancelled'
        }
      ],
      error: null
    })
  },

  reference_images: {
    select: jest.fn().mockResolvedValue({
      data: [
        {
          id: 1,
          name: 'Modern Hardwood',
          category: 'flooring',
          image_url: '/uploads/reference/hardwood1.jpg'
        },
        {
          id: 2,
          name: 'Classic Tile',
          category: 'flooring',
          image_url: '/uploads/reference/tile1.jpg'
        }
      ],
      error: null
    })
  }
};

// Database connection test
export async function testDatabaseConnection() {
  try {
    const { data, error } = await testDb.from('reference_images').select('id').limit(1);
    if (error) {
      console.warn('Database connection test failed:', error);
      return false;
    }
    console.log('Database connection test passed');
    return true;
  } catch (error) {
    console.warn('Database connection test error:', error);
    return false;
  }
}

// Setup and teardown for tests
export async function setupTestDatabase() {
  const isConnected = await testDatabaseConnection();
  if (isConnected) {
    await cleanupDatabase();
    await setupTestData();
  } else {
    console.warn('Using mock database responses due to connection issues');
  }
  return isConnected;
}

export async function teardownTestDatabase() {
  const isConnected = await testDatabaseConnection();
  if (isConnected) {
    await cleanupDatabase();
  }
}
