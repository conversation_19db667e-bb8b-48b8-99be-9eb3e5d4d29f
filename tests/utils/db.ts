const { createClient } = require('@supabase/supabase-js');

/**
 * Create a Supabase client for test operations
 */
function createTestDbClient() {
  if (!process.env.SUPABASE_URL || !process.env.SUPABASE_DB_KEY) {
    throw new Error('SUPABASE_URL and SUPABASE_DB_KEY must be set for database tests');
  }

  return createClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_DB_KEY,
    {
      auth: {
        persistSession: false,
        autoRefreshToken: false,
        detectSessionInUrl: false
      }
    }
  );
}

/**
 * Clean up test data from the database
 */
async function cleanupTestData() {
  const supabase = createTestDbClient();

  // Delete test projects
  await supabase
    .from('projects')
    .delete()
    .like('title', 'Test Project%');

  // Delete test reference categories
  await supabase
    .from('reference_categories')
    .delete()
    .like('name', 'Test Category%');

  // Delete test renovation presets
  await supabase
    .from('renovation_presets')
    .delete()
    .like('name', 'Test Preset%');

  // Delete test drafts
  await supabase
    .from('drafts')
    .delete()
    .like('title', 'Test Draft%');

  // Delete test subscriptions
  await supabase
    .from('subscriptions')
    .delete()
    .eq('stripe_subscription_id', 'sub_test123');
}

/**
 * Create a test user in the database
 */
async function createTestUser(userId) {
  const supabase = createTestDbClient();

  const { data, error } = await supabase
    .from('users')
    .upsert({
      id: userId,
      username: 'testuser',
      email: '<EMAIL>',
      first_name: 'Test',
      last_name: 'User',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    })
    .select()
    .single();

  if (error) {
    throw new Error(`Failed to create test user: ${error.message}`);
  }

  return data;
}

module.exports = {
  createTestDbClient,
  cleanupTestData,
  createTestUser
};
