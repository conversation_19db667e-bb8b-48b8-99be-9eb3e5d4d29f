// Use CommonJS require for compatibility with Jest
const dotenv = require('dotenv');
const path = require('path');

// Load environment variables from .env file
dotenv.config({ path: path.join(__dirname, '..', '.env') });

// Import database utilities
const { setupTestDatabase, teardownTestDatabase } = require('./utils/database.ts');

// Set up global test environment
beforeAll(async () => {
  console.log('Setting up test environment...');

  // Set test environment variables with defaults
  process.env.NODE_ENV = 'test';
  process.env.SUPABASE_URL = process.env.SUPABASE_URL || 'https://test.supabase.co';
  process.env.SUPABASE_ANON_KEY = process.env.SUPABASE_ANON_KEY || 'test-key';
  process.env.SUPABASE_DB_KEY = process.env.SUPABASE_DB_KEY || 'test-key';
  process.env.STRIPE_SECRET_KEY = process.env.STRIPE_SECRET_KEY || 'sk_test_mock';
  process.env.OPENAI_API_KEY = process.env.OPENAI_API_KEY || 'sk-test-mock';
  process.env.CLERK_SECRET_KEY = process.env.CLERK_SECRET_KEY || 'sk_test_mock';

  // Set longer timeout for tests that might need it
  jest.setTimeout(30000);

  // Setup test database
  try {
    await setupTestDatabase();
  } catch (error) {
    console.warn('Database setup failed, using mocks:', error.message);
  }
});

afterAll(async () => {
  console.log('Cleaning up test environment...');

  // Cleanup test database
  try {
    await teardownTestDatabase();
  } catch (error) {
    console.warn('Database cleanup failed:', error.message);
  }
});

// Export empty object for TypeScript compatibility
// This is needed for TypeScript but causes issues with Jest in CommonJS mode
// export {};
