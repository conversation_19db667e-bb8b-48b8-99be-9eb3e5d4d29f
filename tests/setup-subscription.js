const dotenv = require('dotenv');
const path = require('path');
const { createTestDbClient } = require('./utils/db');

// Load environment variables from .env file
dotenv.config({ path: path.join(__dirname, '..', '.env') });

// Set up global test timeout (30 seconds)
jest.setTimeout(30000);

// Set up global test environment
beforeAll(async () => {
  console.log('Setting up subscription test environment...');

  // Ensure required environment variables are set for tests
  if (!process.env.CLERK_SECRET_KEY) {
    console.warn('CLERK_SECRET_KEY is not set. Authentication tests will fail.');
  }

  if (!process.env.SUPABASE_URL || !process.env.SUPABASE_DB_KEY) {
    console.warn('SUPABASE_URL or SUPABASE_DB_KEY is not set. Database tests will fail.');
  }

  if (!process.env.STRIPE_SECRET_KEY) {
    console.warn('STRIPE_SECRET_KEY is not set. Subscription tests will fail.');
  }

  // Create test database tables if they don't exist
  try {
    const supabase = createTestDbClient();

    // Check if the subscriptions table exists
    const { data: subscriptionsTable } = await supabase
      .from('information_schema.tables')
      .select('*')
      .eq('table_name', 'subscriptions')
      .eq('table_schema', 'public');

    if (!subscriptionsTable || subscriptionsTable.length === 0) {
      console.log('Creating subscriptions table for tests...');

      // Create the subscriptions table
      await supabase.rpc('create_subscriptions_table');
    }

    // Check if the usage table exists
    const { data: usageTable } = await supabase
      .from('information_schema.tables')
      .select('*')
      .eq('table_name', 'usage')
      .eq('table_schema', 'public');

    if (!usageTable || usageTable.length === 0) {
      console.log('Creating usage table for tests...');

      // Create the usage table
      await supabase.rpc('create_usage_table');
    }

    // Check if the processed_webhook_events table exists
    const { data: webhookEventsTable } = await supabase
      .from('information_schema.tables')
      .select('*')
      .eq('table_name', 'processed_webhook_events')
      .eq('table_schema', 'public');

    if (!webhookEventsTable || webhookEventsTable.length === 0) {
      console.log('Creating processed_webhook_events table for tests...');

      // Create the processed_webhook_events table
      await supabase.rpc('create_processed_webhook_events_table');
    }
  } catch (error) {
    console.error('Error setting up test database tables:', error);
  }
});

afterAll(async () => {
  console.log('Cleaning up subscription test environment...');

  try {
    const supabase = createTestDbClient();

    // Clean up test data
    await supabase
      .from('subscriptions')
      .delete()
      .eq('user_id', 'test_user_123');

    await supabase
      .from('subscriptions')
      .delete()
      .eq('user_id', 'test_user_subscription_123');

    await supabase
      .from('subscriptions')
      .delete()
      .eq('user_id', 'test_user_webhook_123');

    await supabase
      .from('processed_webhook_events')
      .delete()
      .like('event_id', 'evt_test_%');
  } catch (error) {
    console.error('Error cleaning up test data:', error);
  }
});
