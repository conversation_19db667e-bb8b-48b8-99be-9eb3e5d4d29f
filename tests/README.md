# Renovision.Studio Testing Framework

This directory contains the automated testing framework for the Renovision.Studio application.

## Test Structure

### E2E Tests
Located in `tests/e2e/` directory:
- `auth.spec.ts` - Authentication tests
- `dashboard.spec.ts` - Dashboard functionality tests
- `gallery.spec.ts` - Gallery page tests
- `image-comparison.spec.ts` - Image comparison slider tests
- `landing.spec.ts` - Landing page tests
- `pricing.spec.ts` - Pricing page tests
- `project-creation.spec.ts` - Project creation flow tests
- `reference-library.spec.ts` - Reference library tests
- `contact-newsletter.spec.ts` - Contact and newsletter functionality tests
- `coupon.spec.ts` - Coupon code functionality tests
- `navigation.spec.ts` - Navigation and scrolling behavior tests
- `subscription-comprehensive.spec.js` - Comprehensive subscription tests

### API Tests
Located in `tests/api/` directory:
- `projects.test.ts` - Project API endpoint tests
- `reference-library.test.ts` - Reference library API endpoint tests
- `drafts.test.ts` - Drafts API endpoint tests
- `renovation-presets.test.ts` - Renovation presets API endpoint tests
- `openai.test.ts` - OpenAI integration tests
- `coupon.test.ts` - Coupon code functionality tests
- `subscription.test.js` - Subscription API tests
- `stripe-webhooks.test.js` - Stripe webhook tests
- `usage-tracking.test.ts` - Usage tracking tests

### Utilities
Located in `tests/utils/` directory:
- `auth.ts` - Authentication utilities for API tests
- `auth.e2e.ts` - Authentication utilities for E2E tests
- `db.ts` - Database utilities for tests

### Test Fixtures
Located in `tests/fixtures/` directory:
- Test images for image upload tests
- `coupons.ts` - Test fixtures for coupon tests
- `subscriptions.ts` - Test fixtures for subscription tests
- Other test data

### Component Tests
Located in `client/src/__tests__/` directory:
- `ScrollToTop.test.tsx` - Tests for the ScrollToTop component
- `CreateVisualizationFlow.test.tsx` - Tests for the CreateVisualizationFlow component

### Visual Regression Tests
Located in `tests/e2e/visual-regression/` directory:
- `subscription-ui.spec.js` - Visual tests for subscription UI
- `coupon-ui.spec.js` - Visual tests for coupon UI
- `navigation.spec.js` - Visual tests for navigation components

## Running Tests

### Running All Tests
```bash
npm run test
```

### Running E2E Tests Only
```bash
npm run test:e2e
```

For a specific E2E test file:
```bash
npm run test:e2e -- tests/e2e/landing.spec.ts
```

To run tests in headed mode (with browser visible):
```bash
npm run test:e2e -- --headed
```

### Running API Tests Only
```bash
npm run test:api
```

For a specific API test file:
```bash
npm run test:api tests/api/projects.test.ts
```

### Running Component Tests
```bash
npm run test:component
```

### Running Visual Regression Tests
```bash
npm run test:visual
```

To update visual regression snapshots:
```bash
npm run test:visual:update
```

### Running Specific Feature Tests
```bash
# Coupon tests
npm run test:coupon         # API tests
npm run test:coupon:e2e     # E2E tests
npm run test:visual:coupon  # Visual tests

# Navigation tests
npm run test:navigation
npm run test:visual:navigation

# Subscription tests
npm run test:subscription
npm run test:subscription:api
npm run test:subscription:e2e
npm run test:subscription:visual
```

## Adding New Tests

### Adding a New E2E Test
1. Create a new file in `tests/e2e/` directory
2. Import the necessary Playwright utilities:
   ```typescript
   import { test, expect } from '@playwright/test';
   ```
3. Write your test using the Playwright API
4. Run the test to verify it works

### Adding a New API Test
1. Create a new file in `tests/api/` directory
2. Import the necessary Jest utilities and test helpers
3. Write your test using the Jest API
4. Run the test to verify it works

## Best Practices

1. **Test Isolation**: Each test should be independent and not rely on the state from other tests
2. **Use Test Utilities**: Leverage the auth and db utilities for common operations
3. **Descriptive Test Names**: Use clear, descriptive names for your tests
4. **Test Data Management**: Use the fixtures directory for test data
5. **Error Handling**: Include proper assertions and error handling in your tests
6. **Skip Flaky Tests**: Use `test.skip()` for tests that are not yet stable
7. **Test Coverage**: Aim for comprehensive test coverage of all features
8. **Add Tests for New Features**: Always add tests for new functionality added to the application

## Continuous Integration

The test framework is designed to work with CI/CD pipelines. When setting up CI:
1. Install dependencies: `npm install`
2. Install Playwright browsers: `npx playwright install`
3. Run tests: `npm run test`
4. Publish test reports
