// Use CommonJS require for compatibility with Jest
const dotenv = require('dotenv');
const path = require('path');

// Import testing library setup
require('@testing-library/jest-dom');

// Load environment variables from .env file
dotenv.config({ path: path.join(__dirname, '..', '.env') });

// Set up global test environment for component tests
beforeAll(() => {
  console.log('Setting up component test environment...');

  // Mock DOM environment for React components
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: jest.fn().mockImplementation(query => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: jest.fn(), // deprecated
      removeListener: jest.fn(), // deprecated
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      dispatchEvent: jest.fn(),
    })),
  });

  // Mock IntersectionObserver
  global.IntersectionObserver = jest.fn().mockImplementation(() => ({
    observe: jest.fn(),
    unobserve: jest.fn(),
    disconnect: jest.fn(),
  }));

  // Mock ResizeObserver
  global.ResizeObserver = jest.fn().mockImplementation(() => ({
    observe: jest.fn(),
    unobserve: jest.fn(),
    disconnect: jest.fn(),
  }));
});

afterAll(() => {
  console.log('Cleaning up component test environment...');
});
