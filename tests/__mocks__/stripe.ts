// Mock implementation of <PERSON>e for testing

// Mock Stripe responses
const mockSubscription = {
  id: 'sub_test123',
  object: 'subscription',
  status: 'active',
  cancel_at_period_end: false,
  current_period_start: Math.floor(Date.now() / 1000),
  current_period_end: Math.floor(Date.now() / 1000) + 30 * 24 * 60 * 60,
  customer: 'cus_test123',
  plan: {
    id: 'professional',
    interval: 'month',
    amount: 4900
  },
  items: {
    data: [{
      price: {
        id: 'price_professional_monthly',
        recurring: { interval: 'month' }
      }
    }]
  }
};

const mockCheckoutSession = {
  id: 'cs_test_123',
  object: 'checkout.session',
  status: 'complete',
  payment_status: 'paid',
  customer: 'cus_test123',
  subscription: 'sub_test123',
  url: 'https://checkout.stripe.com/pay/cs_test_123'
};

const mockCustomer = {
  id: 'cus_test123',
  object: 'customer',
  email: '<EMAIL>',
  name: 'Test User'
};

const mockCoupon = {
  id: 'TEST_COUPON',
  object: 'coupon',
  percent_off: 20,
  duration: 'once',
  valid: true
};

// Mock Stripe class
class MockStripe {
  subscriptions: {
    retrieve: jest.Mock;
    update: jest.Mock;
    cancel: jest.Mock;
    create: jest.Mock;
    list: jest.Mock;
  };

  checkout: {
    sessions: {
      create: jest.Mock;
      retrieve: jest.Mock;
    };
  };

  customers: {
    retrieve: jest.Mock;
    update: jest.Mock;
    create: jest.Mock;
  };

  coupons: {
    retrieve: jest.Mock;
    list: jest.Mock;
  };

  webhooks: {
    constructEvent: jest.Mock;
  };

  constructor() {
    this.subscriptions = {
      retrieve: jest.fn().mockResolvedValue(mockSubscription),
      update: jest.fn().mockResolvedValue({ ...mockSubscription, cancel_at_period_end: true }),
      cancel: jest.fn().mockResolvedValue({ ...mockSubscription, status: 'canceled' }),
      create: jest.fn().mockResolvedValue(mockSubscription),
      list: jest.fn().mockResolvedValue({ data: [mockSubscription] })
    };

    this.checkout = {
      sessions: {
        create: jest.fn().mockResolvedValue(mockCheckoutSession),
        retrieve: jest.fn().mockResolvedValue(mockCheckoutSession)
      }
    };

    this.customers = {
      retrieve: jest.fn().mockResolvedValue(mockCustomer),
      update: jest.fn().mockResolvedValue(mockCustomer),
      create: jest.fn().mockResolvedValue(mockCustomer)
    };

    this.coupons = {
      retrieve: jest.fn().mockResolvedValue(mockCoupon),
      list: jest.fn().mockResolvedValue({ data: [mockCoupon] })
    };

    this.webhooks = {
      constructEvent: jest.fn().mockReturnValue({
        id: 'evt_test123',
        type: 'customer.subscription.updated',
        data: { object: mockSubscription }
      })
    };
  }
}

// Mock the Stripe constructor
const mockStripe = jest.fn().mockImplementation(() => new MockStripe());

// Export for ES modules
export default mockStripe;
export { MockStripe, mockSubscription, mockCheckoutSession, mockCustomer, mockCoupon };

// Export for CommonJS
module.exports = mockStripe;
module.exports.default = mockStripe;
module.exports.MockStripe = MockStripe;
module.exports.mockSubscription = mockSubscription;
module.exports.mockCheckoutSession = mockCheckoutSession;
module.exports.mockCustomer = mockCustomer;
module.exports.mockCoupon = mockCoupon;
