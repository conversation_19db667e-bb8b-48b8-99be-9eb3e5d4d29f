// Mock implementation of node-fetch for testing
const mockFetch = jest.fn();

// Mock Response class
class MockResponse {
  ok: boolean;
  status: number;
  statusText: string;
  headers: Map<string, string>;
  private _body: any;

  constructor(body: any, init: { status?: number; statusText?: string; headers?: Record<string, string> } = {}) {
    this._body = body;
    this.status = init.status || 200;
    this.statusText = init.statusText || 'OK';
    this.ok = this.status >= 200 && this.status < 300;
    this.headers = new Map(Object.entries(init.headers || {}));
  }

  async json() {
    return typeof this._body === 'string' ? JSON.parse(this._body) : this._body;
  }

  async text() {
    return typeof this._body === 'string' ? this._body : JSON.stringify(this._body);
  }

  async arrayBuffer() {
    const text = await this.text();
    return new TextEncoder().encode(text).buffer;
  }
}

// Default mock implementation
mockFetch.mockImplementation(async (url: string, options: any = {}) => {
  // Default successful response
  return new MockResponse({ success: true, url, method: options.method || 'GET' });
});

// Export the mock
export default mockFetch;
export { MockResponse };

// For CommonJS compatibility
module.exports = mockFetch;
module.exports.default = mockFetch;
module.exports.MockResponse = MockResponse;
