// Mock implementation of OpenAI for testing
import fs from 'fs';
import path from 'path';

// Mock OpenAI response
const mockOpenAIResponse = {
  created: Date.now(),
  data: [
    {
      b64_json: 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==', // 1x1 transparent PNG
      revised_prompt: 'A test image generated for testing purposes'
    }
  ],
  usage: {
    prompt_tokens: 50,
    completion_tokens: 1000,
    total_tokens: 1050
  }
};

// Mock OpenAI class
class MockOpenAI {
  images: {
    edit: jest.Mock;
    generate: jest.Mock;
  };

  constructor() {
    this.images = {
      edit: jest.fn().mockResolvedValue(mockOpenAIResponse),
      generate: jest.fn().mockResolvedValue(mockOpenAIResponse)
    };
  }
}

// Mock the OpenAI module
const mockOpenAI = jest.fn().mockImplementation(() => new MockOpenAI());

// Export for ES modules
export default mockOpenAI;
export { MockOpenAI };

// Export for CommonJS
module.exports = mockOpenAI;
module.exports.default = mockOpenAI;
module.exports.MockOpenAI = MockOpenAI;
