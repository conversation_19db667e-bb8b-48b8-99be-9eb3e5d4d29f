import { test, expect } from '@playwright/test';
import { authenticatePage } from '../utils/auth.e2e';
import path from 'path';

// Test the reference library functionality
test.describe('Reference Library', () => {
  test.beforeEach(async ({ page }) => {
    // Authenticate the page before each test
    await page.goto('/');
    await authenticatePage(page);
    await page.goto('/reference-library');
  });

  test('should display reference library', async ({ page }) => {
    // Check if reference library page is visible
    await expect(page.locator('text=Reference Library')).toBeVisible();
  });

  test('should create new category', async ({ page }) => {
    // Click on create new category button
    await page.click('text=New Category, text=Add Category');

    // Fill out category details
    await page.fill('input[name="name"]', 'Test Category');
    await page.fill('textarea[name="description"]', 'This is a test category');

    // Save category
    await page.click('text=Create Category, text=Save, text=Add');

    // Check that new category appears
    await expect(page.locator('text=Test Category')).toBeVisible();
  });

  test('should navigate to category and add reference item', async ({ page }) => {
    // First create a category if none exists
    if (await page.locator('text=No categories found, text=No categories').isVisible()) {
      await page.click('text=New Category, text=Add Category');
      await page.fill('input[name="name"]', 'Test Items Category');
      await page.fill('textarea[name="description"]', 'Category for testing items');
      await page.click('text=Create Category, text=Save, text=Add');
    }

    // Click on a category
    await page.click('text=Test Items Category, text=Test Category');

    // Check that we're on the category page
    await expect(page.locator('text=Add Reference Item, text=Add Item')).toBeVisible();

    // Click to add new reference item
    await page.click('text=Add Reference Item, text=Add Item');

    // Fill out item details
    await page.fill('input[name="name"]', 'Test Reference Item');
    await page.fill('textarea[name="description"]', 'This is a test reference item');

    // Upload test image
    const testImagePath = path.join(__dirname, '../fixtures/reference-image.jpg');
    await page.setInputFiles('input[type="file"]', testImagePath);

    // Save item
    await page.click('text=Add Item, text=Save');

    // Check that new item appears
    await expect(page.locator('text=Test Reference Item')).toBeVisible();

    // Check that the image is loaded properly (not showing broken image)
    const imageElement = page.locator('img[alt="Test Reference Item"]');
    await expect(imageElement).toBeVisible();

    // Check that the image has a valid src attribute
    const imageSrc = await imageElement.getAttribute('src');
    expect(imageSrc).toBeTruthy();
    expect(imageSrc).toContain('/uploads/');

    // Check that the image is actually loaded (no error event)
    const hasError = await page.evaluate(async (selector) => {
      const img = document.querySelector(selector);
      return img && img.naturalWidth === 0;
    }, 'img[alt="Test Reference Item"]');

    expect(hasError).toBeFalsy();
  });

  test('should delete reference item', async ({ page }) => {
    // Navigate to a category
    await page.click('text=Test Items Category, text=Test Category');

    // Find a test item to delete
    const itemExists = await page.locator('text=Test Reference Item').isVisible();

    if (itemExists) {
      // Click on the item to open details
      await page.click('text=Test Reference Item');

      // Click delete button
      await page.click('text=Delete, text=Remove');

      // Confirm deletion
      await page.click('text=Confirm, text=Yes');

      // Check that item is gone
      await expect(page.locator('text=Test Reference Item')).not.toBeVisible();
    } else {
      // Skip test if no item exists
      test.skip();
    }
  });
});
