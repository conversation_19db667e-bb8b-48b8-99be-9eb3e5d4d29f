import { test, expect } from '@playwright/test';
import { authenticatePage } from '../utils/auth.e2e';
import path from 'path';

// Skip project creation tests for now until authentication is properly set up
test.describe.skip('Project Creation', () => {
  test.beforeEach(async ({ page }) => {
    // Authenticate the page before each test
    await page.goto('/');
    await authenticatePage(page);
    await page.goto('/create');
  });

  test('should display project creation form', async ({ page }) => {
    // Check if project creation form is visible
    await expect(page.locator('text=Create New Project, text=New Project')).toBeVisible();
    await expect(page.locator('input[name="title"]')).toBeVisible();
    await expect(page.locator('textarea[name="description"]')).toBeVisible();
  });

  test('should save project as draft', async ({ page }) => {
    // Fill out project details
    await page.fill('input[name="title"]', 'Test Draft Project');
    await page.fill('textarea[name="description"]', 'This is a test draft project');

    // Click save as draft
    await page.click('text=Save as Draft, text=Save Draft');

    // Check for success message
    await expect(page.locator('text=Draft saved, text=Saved')).toBeVisible();
  });

  test('should upload before images', async ({ page }) => {
    // Fill out project details
    await page.fill('input[name="title"]', 'Test Image Upload Project');
    await page.fill('textarea[name="description"]', 'Testing image uploads');

    // Create project
    await page.click('text=Create Project, text=Next');

    // Wait for navigation to upload page
    await expect(page.locator('text=Upload Before Images, text=Before Images')).toBeVisible();

    // Upload test image
    const testImagePath = path.join(__dirname, '../fixtures/test-image.jpg');
    await page.setInputFiles('input[type="file"]', testImagePath);

    // Check that image preview appears
    await expect(page.locator('.image-preview, img')).toBeVisible();

    // Continue to next step
    await page.click('text=Continue, text=Next');

    // Check that we moved to reference images step
    await expect(page.locator('text=Upload Reference Images, text=Reference Images')).toBeVisible();
  });

  test('should upload reference images', async ({ page }) => {
    // Fill out project details and create project
    await page.fill('input[name="title"]', 'Test Reference Upload');
    await page.fill('textarea[name="description"]', 'Testing reference uploads');
    await page.click('text=Create Project, text=Next');

    // Skip before images for this test
    await page.click('text=Skip');

    // Wait for navigation to reference upload page
    await expect(page.locator('text=Upload Reference Images, text=Reference Images')).toBeVisible();

    // Upload test image
    const testImagePath = path.join(__dirname, '../fixtures/reference-image.jpg');
    await page.setInputFiles('input[type="file"]', testImagePath);

    // Check that image preview appears
    await expect(page.locator('.image-preview, img')).toBeVisible();

    // Continue to next step
    await page.click('text=Continue, text=Next');

    // Check that we moved to modification step
    await expect(page.locator('text=Describe Your Renovation, text=Renovation Details')).toBeVisible();
  });

  test('should create modification and visualize', async ({ page }) => {
    // Fill out project details and create project
    await page.fill('input[name="title"]', 'Test Visualization');
    await page.fill('textarea[name="description"]', 'Testing visualization');
    await page.click('text=Create Project, text=Next');

    // Upload a test image
    const testImagePath = path.join(__dirname, '../fixtures/test-image.jpg');
    await page.setInputFiles('input[type="file"]', testImagePath);

    // Wait for the image to be processed
    await expect(page.locator('.image-preview, img')).toBeVisible();

    // Select "Custom Modification" option
    await page.click('text=Custom Modification');

    // Move to next step
    await page.click('text=Next');

    // Fill out custom modification details
    await page.fill('textarea[placeholder*="Describe in detail the modifications"]', 'Change the wall color to light blue and make the floor darker');

    // Move to review step
    await page.click('text=Next');

    // Check that we're on the review step
    await expect(page.locator('text=Review Your Visualization Request')).toBeVisible();

    // Complete the flow
    await page.click('text=Create Visualization');

    // Check that we moved to processing page
    await expect(page.locator('text=Your images are being processed')).toBeVisible();
  });

  test('should create floor replacement with reference image', async ({ page }) => {
    // Fill out project details and create project
    await page.fill('input[name="title"]', 'Floor Replacement Test');
    await page.fill('textarea[name="description"]', 'Testing floor replacement');
    await page.click('text=Create Project, text=Next');

    // Upload a test image for before
    const testImagePath = path.join(__dirname, '../fixtures/test-image.jpg');
    await page.setInputFiles('input[type="file"]', testImagePath);

    // Wait for the image to be processed
    await expect(page.locator('.image-preview, img')).toBeVisible();

    // Select "Replace Floor" option
    await page.click('text=Replace Floor');

    // Move to next step
    await page.click('text=Next');

    // Upload a reference image
    const referenceImagePath = path.join(__dirname, '../fixtures/reference-image.jpg');
    await page.setInputFiles('input[type="file"]', referenceImagePath);

    // Wait for the reference image to be processed
    await expect(page.locator('.image-preview, img')).toHaveCount(2);

    // Move to next step
    await page.click('text=Next');

    // Ensure "Use Reference Image" is toggled on
    await expect(page.locator('text=Use Reference Image')).toBeVisible();

    // Select the reference image
    await page.click('.aspect-square >> nth=0');

    // Move to review step
    await page.click('text=Next');

    // Check that we're on the review step
    await expect(page.locator('text=Review Your Visualization Request')).toBeVisible();

    // Complete the flow
    await page.click('text=Create Visualization');

    // Check that we moved to processing page
    await expect(page.locator('text=Your images are being processed')).toBeVisible();
  });
});
