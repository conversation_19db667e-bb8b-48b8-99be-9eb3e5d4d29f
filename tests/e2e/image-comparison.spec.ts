import { test, expect } from '@playwright/test';
import { authenticatePage } from '../utils/auth.e2e';

// Skip image comparison tests for now until authentication is properly set up
test.describe.skip('Image Comparison Slider', () => {
  test.beforeEach(async ({ page }) => {
    // Authenticate the page before each test
    await page.goto('/');
    await authenticatePage(page);

    // Navigate to gallery where we expect to find image comparison sliders
    await page.goto('/gallery');
  });

  test('should display before image on left and after image on right', async ({ page }) => {
    // Check if any image comparison sliders exist
    const hasSliders = await page.locator('.image-comparison-slider, .comparison-slider').count() > 0;

    if (hasSliders) {
      // Get the first slider
      const slider = page.locator('.image-comparison-slider, .comparison-slider').first();

      // Check that before image is on the left
      const beforeImage = slider.locator('.before-image, .before');
      await expect(beforeImage).toBeVisible();

      // Check that after image is on the right
      const afterImage = slider.locator('.after-image, .after');
      await expect(afterImage).toBeVisible();

      // Check slider position (should be in the middle by default)
      const sliderHandle = slider.locator('.slider-handle, .handle');
      const sliderBounds = await slider.boundingBox();
      const handleBounds = await sliderHandle.boundingBox();

      if (sliderBounds && handleBounds) {
        const sliderMiddle = sliderBounds.x + sliderBounds.width / 2;
        const handleMiddle = handleBounds.x + handleBounds.width / 2;

        // Handle should be approximately in the middle
        expect(Math.abs(sliderMiddle - handleMiddle)).toBeLessThan(20);
      }
    } else {
      // Skip test if no sliders exist
      test.skip();
    }
  });

  test('should allow dragging slider to compare images', async ({ page }) => {
    // Check if any image comparison sliders exist
    const hasSliders = await page.locator('.image-comparison-slider, .comparison-slider').count() > 0;

    if (hasSliders) {
      // Get the first slider
      const slider = page.locator('.image-comparison-slider, .comparison-slider').first();
      const sliderHandle = slider.locator('.slider-handle, .handle');

      // Get initial position
      const sliderBounds = await slider.boundingBox();
      const initialHandleBounds = await sliderHandle.boundingBox();

      if (sliderBounds && initialHandleBounds) {
        // Drag slider to 25% position
        await sliderHandle.dragTo(slider, {
          targetPosition: {
            x: sliderBounds.width * 0.25,
            y: sliderBounds.height / 2
          }
        });

        // Check new position
        const newHandleBounds = await sliderHandle.boundingBox();

        if (newHandleBounds) {
          const expectedX = sliderBounds.x + sliderBounds.width * 0.25;
          const actualX = newHandleBounds.x + newHandleBounds.width / 2;

          // Handle should be approximately at 25% position
          expect(Math.abs(expectedX - actualX)).toBeLessThan(30);
        }
      }
    } else {
      // Skip test if no sliders exist
      test.skip();
    }
  });

  test('should implement slider consistently across different pages', async ({ page }) => {
    // First check gallery page
    const gallerySliders = await page.locator('.image-comparison-slider, .comparison-slider').count();

    // Then check project page if any projects exist
    await page.goto('/dashboard');
    const projectsExist = await page.locator('.project-card, .project-item').count() > 0;

    if (projectsExist) {
      // Click on a project to view details
      await page.click('.project-card, .project-item');

      // Check for image comparison slider
      const projectSliders = await page.locator('.image-comparison-slider, .comparison-slider').count();
      expect(projectSliders).toBeGreaterThan(0);

      // Check that sliders have the same structure
      const sliderClasses = await page.$$eval('.image-comparison-slider, .comparison-slider',
        sliders => sliders.map(s => s.className));

      // All sliders should have at least one common class
      if (sliderClasses.length > 1) {
        const commonClass = sliderClasses[0].split(' ').find(cls =>
          sliderClasses.every(classes => classes.includes(cls)));

        expect(commonClass).toBeTruthy();
      }
    } else {
      // Skip test if no projects exist
      test.skip();
    }
  });
});
