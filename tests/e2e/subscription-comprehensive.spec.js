/**
 * Comprehensive E2E tests for subscription functionality
 *
 * This test suite covers all aspects of subscription management from the UI:
 * - Viewing subscription details
 * - Subscribing to a plan (both monthly and annual)
 * - Canceling a subscription
 * - Reactivating a subscription
 * - Changing subscription plans
 * - Handling subscription errors
 */

import { test, expect } from '@playwright/test';
import { createTestSubscriptionData } from '../fixtures/subscriptions';

test.describe('Subscription Management - Comprehensive Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Go to the home page
    await page.goto('/');

    // Simulate being logged in
    await page.evaluate(() => {
      // Set up auth data in localStorage
      localStorage.setItem('clerk-auth-token', 'test-token');

      // Set up auth cache
      const authCache = {
        isAuthenticated: true,
        authToken: 'test-token',
        isInitialized: true,
        lastUpdated: Date.now(),
        user: {
          id: 'test_user_123',
          email: '<EMAIL>',
          firstName: 'Test',
          lastName: 'User',
          username: 'testuser'
        }
      };
      sessionStorage.setItem('renovate-ai-auth-cache', JSON.stringify(authCache));
    });

    // Reload the page to apply the auth
    await page.reload();
  });

  test('should display free tier when no subscription exists', async ({ page }) => {
    // Set up free tier data
    await page.evaluate(() => {
      // Clear any existing subscription data
      localStorage.removeItem('subscription-data');
    });

    // Navigate to the account page
    await page.goto('/account');

    // Wait for the subscription details to load
    await page.waitForSelector('text=Subscription Details');

    // Check that the free tier is displayed
    await expect(page.locator('text=Free Tier')).toBeVisible();
    await expect(page.locator('text=Upgrade Now')).toBeVisible();
  });

  test('should display subscription details correctly for professional plan', async ({ page }) => {
    // Set up professional plan data
    await page.evaluate(() => {
      const subscriptionData = {
        subscription: {
          subscription: {
            id: 1,
            user_id: 'test_user_123',
            plan_id: 'professional',
            billing_cycle: 'annual',
            status: 'active',
            stripe_customer_id: 'cus_test123',
            stripe_subscription_id: 'sub_test123',
            current_period_start: new Date().toISOString(),
            current_period_end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
            cancel_at_period_end: false
          },
          usage: {
            projects: 2,
            images: 5
          },
          limits: {
            projects: 10,
            imagesPerProject: 5
          }
        }
      };
      localStorage.setItem('subscription-data', JSON.stringify(subscriptionData));
    });

    // Navigate to the account page
    await page.goto('/account');

    // Wait for the subscription details to load
    await page.waitForSelector('text=Subscription Details');

    // Check that the professional plan is displayed
    await expect(page.locator('text=Professional Plan')).toBeVisible();

    // Check that the status is active
    await expect(page.locator('text=Active')).toBeVisible();

    // Check that the billing cycle is annual
    await expect(page.locator('text=Annual')).toBeVisible();
    await expect(page.locator('text=20% discount')).toBeVisible();

    // Check that the cancel button is visible
    await expect(page.locator('button:text("Cancel Subscription")')).toBeVisible();
  });

  test('should display subscription details correctly for starter plan', async ({ page }) => {
    // Set up starter plan data
    await page.evaluate(() => {
      const subscriptionData = {
        subscription: {
          subscription: {
            id: 1,
            user_id: 'test_user_123',
            plan_id: 'starter',
            billing_cycle: 'monthly',
            status: 'active',
            stripe_customer_id: 'cus_test123',
            stripe_subscription_id: 'sub_test123',
            current_period_start: new Date().toISOString(),
            current_period_end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
            cancel_at_period_end: false
          },
          usage: {
            projects: 1,
            images: 2
          },
          limits: {
            projects: 3,
            imagesPerProject: 3
          }
        }
      };
      localStorage.setItem('subscription-data', JSON.stringify(subscriptionData));
    });

    // Navigate to the account page
    await page.goto('/account');

    // Wait for the subscription details to load
    await page.waitForSelector('text=Subscription Details');

    // Check that the starter plan is displayed
    await expect(page.locator('text=Starter Plan')).toBeVisible();

    // Check that the status is active
    await expect(page.locator('text=Active')).toBeVisible();

    // Check that the billing cycle is monthly
    await expect(page.locator('text=Monthly')).toBeVisible();

    // Check that the cancel button is visible
    await expect(page.locator('button:text("Cancel Subscription")')).toBeVisible();
  });

  test('should handle subscription cancellation', async ({ page }) => {
    // Set up professional plan data
    await page.evaluate(() => {
      const subscriptionData = {
        subscription: {
          subscription: {
            id: 1,
            user_id: 'test_user_123',
            plan_id: 'professional',
            billing_cycle: 'annual',
            status: 'active',
            stripe_customer_id: 'cus_test123',
            stripe_subscription_id: 'sub_test123',
            current_period_start: new Date().toISOString(),
            current_period_end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
            cancel_at_period_end: false
          },
          usage: {
            projects: 2,
            images: 5
          },
          limits: {
            projects: 10,
            imagesPerProject: 5
          }
        }
      };
      localStorage.setItem('subscription-data', JSON.stringify(subscriptionData));
    });

    // Mock the API response for cancellation
    await page.route('**/api/subscription/cancel', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          message: 'Subscription cancelled successfully',
          subscription: {
            id: 'sub_test123',
            cancel_at_period_end: true
          }
        })
      });
    });

    // Navigate to the account page
    await page.goto('/account');

    // Wait for the subscription details to load
    await page.waitForSelector('text=Subscription Details');

    // Click the cancel subscription button
    await page.locator('button:text("Cancel Subscription")').click();

    // Confirm the cancellation in the dialog
    await page.locator('button:text("Yes, cancel")').click();

    // Wait for the success message
    await page.waitForSelector('text=Subscription cancelled successfully');

    // Check that the subscription is now marked as ending
    await expect(page.locator('text=Subscription Ending')).toBeVisible();
  });

  test('should handle subscription reactivation', async ({ page }) => {
    // Set up a cancelled subscription
    await page.evaluate(() => {
      const subscriptionData = {
        subscription: {
          subscription: {
            id: 1,
            user_id: 'test_user_123',
            plan_id: 'professional',
            billing_cycle: 'annual',
            status: 'active',
            stripe_customer_id: 'cus_test123',
            stripe_subscription_id: 'sub_test123',
            current_period_start: new Date().toISOString(),
            current_period_end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
            cancel_at_period_end: true
          },
          usage: {
            projects: 2,
            images: 5
          },
          limits: {
            projects: 10,
            imagesPerProject: 5
          }
        }
      };
      localStorage.setItem('subscription-data', JSON.stringify(subscriptionData));
    });

    // Mock the API response for reactivation
    await page.route('**/api/subscription/reactivate', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          message: 'Subscription reactivated successfully',
          subscription: {
            id: 'sub_test123',
            cancel_at_period_end: false
          }
        })
      });
    });

    // Navigate to the account page
    await page.goto('/account');

    // Wait for the subscription details to load
    await page.waitForSelector('text=Subscription Details');

    // Check that the subscription is marked as ending
    await expect(page.locator('text=Subscription Ending')).toBeVisible();

    // Click the reactivate subscription button
    await page.locator('button:text("Reactivate Subscription")').click();

    // Wait for the success message
    await page.waitForSelector('text=Subscription reactivated successfully');

    // Check that the subscription is no longer marked as ending
    await expect(page.locator('text=Subscription Ending')).not.toBeVisible();
  });

  test('should handle subscription checkout flow', async ({ page }) => {
    // Navigate to the pricing page
    await page.goto('/pricing');

    // Wait for the pricing plans to load
    await page.waitForSelector('text=Pricing Plans');

    // Mock the API response for checkout session creation
    await page.route('**/api/create-checkout-session', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          url: 'https://checkout.stripe.com/c/pay/test_checkout_session'
        })
      });
    });

    // Click the subscribe button for the professional plan
    await page.locator('text=Professional >> .. >> button:text("Subscribe")').click();

    // Select annual billing if available
    const annualBillingOption = page.locator('text=Annual (20% discount)');
    if (await annualBillingOption.isVisible()) {
      await annualBillingOption.click();
    }

    // Click the confirm button
    await page.locator('button:text("Confirm")').click();

    // Check that we're redirected to Stripe checkout
    // Since we're mocking, we'll just check that the redirect was attempted
    await page.waitForRequest(request =>
      request.url().includes('checkout.stripe.com') ||
      request.url().includes('api/create-checkout-session')
    );
  });

  test('should display usage statistics correctly', async ({ page }) => {
    // Set up professional plan data with usage
    await page.evaluate(() => {
      const subscriptionData = {
        subscription: {
          subscription: {
            id: 1,
            user_id: 'test_user_123',
            plan_id: 'professional',
            billing_cycle: 'annual',
            status: 'active',
            stripe_customer_id: 'cus_test123',
            stripe_subscription_id: 'sub_test123',
            current_period_start: new Date().toISOString(),
            current_period_end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
            cancel_at_period_end: false
          },
          usage: {
            projects: 5,
            images: 15
          },
          limits: {
            projects: 10,
            imagesPerProject: 5
          }
        }
      };
      localStorage.setItem('subscription-data', JSON.stringify(subscriptionData));
    });

    // Navigate to the account page
    await page.goto('/account');

    // Wait for the subscription details to load
    await page.waitForSelector('text=Subscription Details');

    // Switch to the usage tab
    await page.locator('button:text("Usage")').click();

    // Check that the usage statistics are displayed correctly
    await expect(page.locator('text=Projects')).toBeVisible();
    await expect(page.locator('text=5 / 10')).toBeVisible();

    // Check that the progress bar is displayed
    await expect(page.locator('div[role="progressbar"]')).toBeVisible();
  });

  test('should handle both nested and flat subscription structures', async ({ page }) => {
    // Test with nested structure
    await page.evaluate(() => {
      const subscriptionData = {
        subscription: {
          subscription: {
            id: 1,
            user_id: 'test_user_123',
            plan_id: 'professional',
            billing_cycle: 'annual',
            status: 'active',
            stripe_customer_id: 'cus_test123',
            stripe_subscription_id: 'sub_test123',
            current_period_start: new Date().toISOString(),
            current_period_end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
            cancel_at_period_end: false
          },
          usage: {
            projects: 2,
            images: 5
          },
          limits: {
            projects: 10,
            imagesPerProject: 5
          }
        }
      };
      localStorage.setItem('subscription-data', JSON.stringify(subscriptionData));
    });

    // Navigate to the account page
    await page.goto('/account');
    await page.waitForSelector('text=Subscription Details');
    await expect(page.locator('text=Professional Plan')).toBeVisible();

    // Now test with flat structure
    await page.evaluate(() => {
      // Mock flat subscription data
      const subscriptionData = {
        subscription: {
          id: 1,
          user_id: 'test_user_123',
          plan_id: 'professional',
          billing_cycle: 'annual',
          status: 'active',
          stripe_customer_id: 'cus_test123',
          stripe_subscription_id: 'sub_test123',
          current_period_start: new Date().toISOString(),
          current_period_end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
          cancel_at_period_end: false
        },
        usage: {
          projects: 2,
          images: 5
        },
        limits: {
          projects: 10,
          imagesPerProject: 5
        }
      };
      localStorage.setItem('subscription-data', JSON.stringify(subscriptionData));
    });

    // Reload the page to apply the new data
    await page.reload();

    // Check that the professional plan is still displayed correctly
    await page.waitForSelector('text=Subscription Details');
    await expect(page.locator('text=Professional Plan')).toBeVisible();
    await expect(page.locator('text=Active')).toBeVisible();
  });

  test('should handle subscription errors gracefully', async ({ page }) => {
    // Set up professional plan data
    await page.evaluate(() => {
      const subscriptionData = {
        subscription: {
          subscription: {
            id: 1,
            user_id: 'test_user_123',
            plan_id: 'professional',
            billing_cycle: 'annual',
            status: 'active',
            stripe_customer_id: 'cus_test123',
            stripe_subscription_id: 'sub_test123',
            current_period_start: new Date().toISOString(),
            current_period_end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
            cancel_at_period_end: false
          },
          usage: {
            projects: 2,
            images: 5
          },
          limits: {
            projects: 10,
            imagesPerProject: 5
          }
        }
      };
      localStorage.setItem('subscription-data', JSON.stringify(subscriptionData));
    });

    // Mock the API response for cancellation with an error
    await page.route('**/api/subscription/cancel', async (route) => {
      await route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({
          success: false,
          error: 'Failed to cancel subscription',
          message: 'An error occurred while cancelling your subscription'
        })
      });
    });

    // Navigate to the account page
    await page.goto('/account');

    // Wait for the subscription details to load
    await page.waitForSelector('text=Subscription Details');

    // Click the cancel subscription button
    await page.locator('button:text("Cancel Subscription")').click();

    // Confirm the cancellation in the dialog
    await page.locator('button:text("Yes, cancel")').click();

    // Wait for the error message
    await page.waitForSelector('text=Failed to cancel subscription');

    // Check that the subscription is still active
    await expect(page.locator('text=Active')).toBeVisible();
    await expect(page.locator('text=Subscription Ending')).not.toBeVisible();
  });

  test('should handle network timeouts gracefully', async ({ page }) => {
    // Set up professional plan data
    await page.evaluate(() => {
      const subscriptionData = {
        subscription: {
          subscription: {
            id: 1,
            user_id: 'test_user_123',
            plan_id: 'professional',
            billing_cycle: 'annual',
            status: 'active',
            stripe_customer_id: 'cus_test123',
            stripe_subscription_id: 'sub_test123',
            current_period_start: new Date().toISOString(),
            current_period_end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
            cancel_at_period_end: false
          },
          usage: {
            projects: 2,
            images: 5
          },
          limits: {
            projects: 10,
            imagesPerProject: 5
          }
        }
      };
      localStorage.setItem('subscription-data', JSON.stringify(subscriptionData));
    });

    // Mock a network timeout
    await page.route('**/api/subscription/cancel', async (route) => {
      // Wait for a long time before responding (simulating timeout)
      await new Promise(resolve => setTimeout(resolve, 30000));
      await route.fulfill({
        status: 408,
        contentType: 'application/json',
        body: JSON.stringify({
          success: false,
          error: 'Request timeout',
          message: 'The request timed out'
        })
      });
    });

    // Navigate to the account page
    await page.goto('/account');

    // Wait for the subscription details to load
    await page.waitForSelector('text=Subscription Details');

    // Click the cancel subscription button
    await page.locator('button:text("Cancel Subscription")').click();

    // Confirm the cancellation in the dialog
    await page.locator('button:text("Yes, cancel")').click();

    // Wait for the error message (timeout or network error)
    await page.waitForSelector('text=error', { timeout: 10000 });

    // Check that the subscription is still active
    await expect(page.locator('text=Active')).toBeVisible();
  });

  test('should handle rate limiting errors gracefully', async ({ page }) => {
    // Set up professional plan data
    await page.evaluate(() => {
      const subscriptionData = {
        subscription: {
          subscription: {
            id: 1,
            user_id: 'test_user_123',
            plan_id: 'professional',
            billing_cycle: 'annual',
            status: 'active',
            stripe_customer_id: 'cus_test123',
            stripe_subscription_id: 'sub_test123',
            current_period_start: new Date().toISOString(),
            current_period_end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
            cancel_at_period_end: false
          },
          usage: {
            projects: 2,
            images: 5
          },
          limits: {
            projects: 10,
            imagesPerProject: 5
          }
        }
      };
      localStorage.setItem('subscription-data', JSON.stringify(subscriptionData));
    });

    // Mock a rate limit error
    await page.route('**/api/subscription/cancel', async (route) => {
      await route.fulfill({
        status: 429,
        contentType: 'application/json',
        body: JSON.stringify({
          success: false,
          error: 'Too many requests',
          message: 'Rate limit exceeded. Please try again later.'
        })
      });
    });

    // Navigate to the account page
    await page.goto('/account');

    // Wait for the subscription details to load
    await page.waitForSelector('text=Subscription Details');

    // Click the cancel subscription button
    await page.locator('button:text("Cancel Subscription")').click();

    // Confirm the cancellation in the dialog
    await page.locator('button:text("Yes, cancel")').click();

    // Wait for the rate limit error message
    await page.waitForSelector('text=Rate limit exceeded');

    // Check that the subscription is still active
    await expect(page.locator('text=Active')).toBeVisible();
  });

  test('should handle authentication errors gracefully', async ({ page }) => {
    // Set up professional plan data but with invalid auth
    await page.evaluate(() => {
      const subscriptionData = {
        subscription: {
          subscription: {
            id: 1,
            user_id: 'test_user_123',
            plan_id: 'professional',
            billing_cycle: 'annual',
            status: 'active',
            stripe_customer_id: 'cus_test123',
            stripe_subscription_id: 'sub_test123',
            current_period_start: new Date().toISOString(),
            current_period_end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
            cancel_at_period_end: false
          },
          usage: {
            projects: 2,
            images: 5
          },
          limits: {
            projects: 10,
            imagesPerProject: 5
          }
        }
      };
      localStorage.setItem('subscription-data', JSON.stringify(subscriptionData));

      // Set invalid auth token
      localStorage.setItem('clerk-auth-token', 'invalid_token');
    });

    // Mock an authentication error
    await page.route('**/api/subscription/cancel', async (route) => {
      await route.fulfill({
        status: 401,
        contentType: 'application/json',
        body: JSON.stringify({
          success: false,
          error: 'Unauthorized',
          message: 'Authentication required'
        })
      });
    });

    // Navigate to the account page
    await page.goto('/account');

    // Wait for the subscription details to load
    await page.waitForSelector('text=Subscription Details');

    // Click the cancel subscription button
    await page.locator('button:text("Cancel Subscription")').click();

    // Confirm the cancellation in the dialog
    await page.locator('button:text("Yes, cancel")').click();

    // Wait for the authentication error message
    await page.waitForSelector('text=Authentication required');
  });

  test('should handle malformed subscription data gracefully', async ({ page }) => {
    // Set up malformed subscription data
    await page.evaluate(() => {
      const malformedData = {
        subscription: {
          // Missing required fields
          user_id: 'test_user_123',
          // plan_id is missing
          billing_cycle: 'annual',
          // status is missing
          stripe_customer_id: 'cus_test123',
          stripe_subscription_id: 'sub_test123',
          // Dates are invalid
          current_period_start: 'invalid-date',
          current_period_end: 'invalid-date',
          cancel_at_period_end: 'not-a-boolean' // Should be boolean
        },
        // Usage is missing
        // Limits are missing
      };
      localStorage.setItem('subscription-data', JSON.stringify(malformedData));
    });

    // Navigate to the account page
    await page.goto('/account');

    // The page should still load without crashing
    await page.waitForSelector('text=Account Settings');

    // There should be a fallback to free tier or an error message
    const hasFreeText = await page.locator('text=Free').isVisible();
    const hasErrorText = await page.locator('text=error').isVisible();

    expect(hasFreeText || hasErrorText).toBeTruthy();
  });

  test('should handle browser refresh during subscription operations', async ({ page }) => {
    // Set up professional plan data
    await page.evaluate(() => {
      const subscriptionData = {
        subscription: {
          subscription: {
            id: 1,
            user_id: 'test_user_123',
            plan_id: 'professional',
            billing_cycle: 'annual',
            status: 'active',
            stripe_customer_id: 'cus_test123',
            stripe_subscription_id: 'sub_test123',
            current_period_start: new Date().toISOString(),
            current_period_end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
            cancel_at_period_end: false
          },
          usage: {
            projects: 2,
            images: 5
          },
          limits: {
            projects: 10,
            imagesPerProject: 5
          }
        }
      };
      localStorage.setItem('subscription-data', JSON.stringify(subscriptionData));
    });

    // Navigate to the account page
    await page.goto('/account');

    // Wait for the subscription details to load
    await page.waitForSelector('text=Subscription Details');

    // Click the cancel subscription button
    await page.locator('button:text("Cancel Subscription")').click();

    // Confirm the cancellation in the dialog
    await page.locator('button:text("Yes, cancel")').click();

    // Immediately refresh the page (simulating user refreshing during operation)
    await page.reload();

    // The page should still load without crashing
    await page.waitForSelector('text=Subscription Details');

    // The subscription should still be in a valid state
    await expect(page.locator('text=Professional Plan')).toBeVisible();
  });

  test('should handle payment success page correctly', async ({ page }) => {
    // Navigate to the payment success page with a session ID
    await page.goto('/payment-success?session_id=cs_test_123');

    // Mock the API response for manual subscription creation
    await page.route('**/api/manual-subscription-create', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          message: 'Subscription created successfully',
          subscription: {
            id: 1,
            user_id: 'test_user_123',
            plan_id: 'professional',
            billing_cycle: 'annual',
            status: 'active',
            stripe_customer_id: 'cus_test123',
            stripe_subscription_id: 'sub_test123',
            current_period_start: new Date().toISOString(),
            current_period_end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
            cancel_at_period_end: false
          }
        })
      });
    });

    // Wait for the success message
    await page.waitForSelector('text=Payment Successful');

    // Check that the success message is displayed
    await expect(page.locator('text=Thank you for your subscription')).toBeVisible();

    // Check that there's a button to go to the dashboard
    await expect(page.locator('text=Go to Dashboard')).toBeVisible();
  });
});
