import { test, expect } from '@playwright/test';

// Skip contact and newsletter tests for now until the UI is implemented
test.describe.skip('Contact and Newsletter', () => {
  test('should display contact form', async ({ page }) => {
    await page.goto('/contact');

    // Check if contact form is visible
    await expect(page.locator('h1:has-text("Contact Us")')).toBeVisible();
    await expect(page.locator('input[name="name"]')).toBeVisible();
    await expect(page.locator('input[name="email"]')).toBeVisible();
    await expect(page.locator('textarea[name="message"]')).toBeVisible();
    await expect(page.locator('button:has-text("Send Message")')).toBeVisible();
  });

  test('should validate contact form fields', async ({ page }) => {
    await page.goto('/contact');

    // Try to submit empty form
    await page.click('button:has-text("Send Message")');

    // Check for validation errors
    await expect(page.locator('text=Name is required')).toBeVisible();
    await expect(page.locator('text=Email is required')).toBeVisible();
    await expect(page.locator('text=Message is required')).toBeVisible();

    // Fill out only email with invalid format
    await page.fill('input[name="email"]', 'invalid-email');
    await page.click('button:has-text("Send Message")');

    // Check for email validation error
    await expect(page.locator('text=Invalid email address')).toBeVisible();
  });

  test('should submit contact form successfully', async ({ page }) => {
    await page.goto('/contact');

    // Fill out form
    await page.fill('input[name="name"]', 'Test User');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('textarea[name="message"]', 'This is a test message');

    // Submit form
    await page.click('button:has-text("Send Message")');

    // Check for success message
    await expect(page.locator('text=Message sent successfully')).toBeVisible();
  });

  test('should display newsletter subscription form', async ({ page }) => {
    await page.goto('/');

    // Check if newsletter form is visible
    await expect(page.locator('text=Subscribe to our newsletter')).toBeVisible();
    await expect(page.locator('input[type="email"]')).toBeVisible();
    await expect(page.locator('button:has-text("Subscribe")')).toBeVisible();
  });

  test('should validate newsletter email', async ({ page }) => {
    await page.goto('/');

    // Try to submit empty email
    const newsletterForm = page.locator('form:has(input[type="email"])');
    await newsletterForm.locator('button:has-text("Subscribe")').click();

    // Check for validation error
    await expect(page.locator('text=Email is required')).toBeVisible();

    // Try invalid email
    await newsletterForm.locator('input[type="email"]').fill('invalid-email');
    await newsletterForm.locator('button:has-text("Subscribe")').click();

    // Check for email validation error
    await expect(page.locator('text=Invalid email address')).toBeVisible();
  });

  test('should subscribe to newsletter successfully', async ({ page }) => {
    await page.goto('/');

    // Fill out newsletter form
    const newsletterForm = page.locator('form:has(input[type="email"])');
    await newsletterForm.locator('input[type="email"]').fill('<EMAIL>');

    // Submit form
    await newsletterForm.locator('button:has-text("Subscribe")').click();

    // Check for success message
    await expect(page.locator('text=Thank you for subscribing')).toBeVisible();
  });

  test('contact us buttons should navigate to contact page', async ({ page }) => {
    // Start on pricing page
    await page.goto('/pricing');

    // Click on Contact Us button
    await page.click('text=Contact Us');

    // Check that we navigated to contact page
    await expect(page).toHaveURL(/contact/);

    // Check that we're at the top of the contact section
    const contactSection = page.locator('h1:has-text("Contact Us")').first();
    const contactBounds = await contactSection.boundingBox();

    if (contactBounds) {
      // The contact section should be at or near the top of the viewport
      expect(contactBounds.y).toBeLessThan(200);
    }
  });
});
