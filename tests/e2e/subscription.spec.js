// @ts-check
import { test, expect } from '@playwright/test';

test.describe('Subscription Functionality', () => {
  test.beforeEach(async ({ page }) => {
    // Go to the home page
    await page.goto('/');

    // Simulate being logged in
    await page.evaluate(() => {
      // Set up auth data in localStorage
      localStorage.setItem('clerk-auth-token', 'test-token');

      // Set up auth cache
      const authCache = {
        isAuthenticated: true,
        authToken: 'test-token',
        isInitialized: true,
        lastUpdated: Date.now()
      };
      sessionStorage.setItem('renovate-ai-auth-cache', JSON.stringify(authCache));

      // Mock subscription data
      const subscriptionData = {
        subscription: {
          subscription: {
            id: 1,
            user_id: 'test_user_123',
            plan_id: 'professional',
            billing_cycle: 'annual',
            status: 'active',
            stripe_customer_id: 'cus_test123',
            stripe_subscription_id: 'sub_test123',
            current_period_start: new Date().toISOString(),
            current_period_end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
            cancel_at_period_end: false
          },
          usage: {
            projects: 2,
            images: 5
          },
          limits: {
            projects: 10,
            imagesPerProject: 5
          }
        }
      };
      localStorage.setItem('subscription-data', JSON.stringify(subscriptionData));
    });

    // Reload the page to apply the auth
    await page.reload();
  });

  test('should display subscription details correctly', async ({ page }) => {
    // Navigate to the account page
    await page.goto('/account');

    // Wait for the subscription details to load
    await page.waitForSelector('text=Subscription Details');

    // Check that the professional plan is displayed
    await expect(page.locator('text=professional Plan')).toBeVisible();

    // Check that the status is active
    await expect(page.locator('text=Active')).toBeVisible();

    // Check that the billing cycle is annual
    await expect(page.locator('text=annual (20% discount)')).toBeVisible();
  });

  test('should handle subscription cancellation', async ({ page }) => {
    // Mock the API response for cancellation
    await page.route('**/api/subscription/cancel', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ success: true, message: 'Subscription cancelled successfully' })
      });
    });

    // Navigate to the account page
    await page.goto('/account');

    // Wait for the subscription details to load
    await page.waitForSelector('text=Subscription Details');

    // Click the cancel subscription button
    await page.locator('button:text("Cancel Subscription")').click();

    // Confirm the cancellation in the dialog
    page.on('dialog', dialog => dialog.accept());

    // Wait for the success message
    await page.waitForSelector('text=Subscription cancelled successfully');

    // Check that the subscription is now marked as ending
    await expect(page.locator('text=Subscription Ending')).toBeVisible();
  });

  test('should handle subscription reactivation', async ({ page }) => {
    // Set up a cancelled subscription
    await page.evaluate(() => {
      const subscriptionData = JSON.parse(localStorage.getItem('subscription-data') || '{}');
      if (subscriptionData.subscription && subscriptionData.subscription.subscription) {
        subscriptionData.subscription.subscription.cancel_at_period_end = true;
        localStorage.setItem('subscription-data', JSON.stringify(subscriptionData));
      }
    });

    // Mock the API response for reactivation
    await page.route('**/api/subscription/reactivate', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ success: true, message: 'Subscription reactivated successfully' })
      });
    });

    // Navigate to the account page
    await page.goto('/account');

    // Wait for the subscription details to load
    await page.waitForSelector('text=Subscription Details');

    // Check that the subscription is marked as ending
    await expect(page.locator('text=Subscription Ending')).toBeVisible();

    // Click the reactivate subscription button
    await page.locator('button:text("Reactivate Subscription")').click();

    // Wait for the success message
    await page.waitForSelector('text=Subscription reactivated successfully');

    // Check that the subscription is no longer marked as ending
    await expect(page.locator('text=Subscription Ending')).not.toBeVisible();
  });

  test('should handle both nested and flat subscription structures', async ({ page }) => {
    // Test with nested structure (already set up in beforeEach)
    await page.goto('/account');
    await page.waitForSelector('text=Subscription Details');
    await expect(page.locator('text=professional Plan')).toBeVisible();

    // Now test with flat structure
    await page.evaluate(() => {
      // Mock flat subscription data
      const subscriptionData = {
        subscription: {
          id: 1,
          user_id: 'test_user_123',
          plan_id: 'professional',
          billing_cycle: 'annual',
          status: 'active',
          stripe_customer_id: 'cus_test123',
          stripe_subscription_id: 'sub_test123',
          current_period_start: new Date().toISOString(),
          current_period_end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
          cancel_at_period_end: false
        },
        usage: {
          projects: 2,
          images: 5
        },
        limits: {
          projects: 10,
          imagesPerProject: 5
        }
      };
      localStorage.setItem('subscription-data', JSON.stringify(subscriptionData));
    });

    // Reload the page to apply the new data
    await page.reload();

    // Check that the professional plan is still displayed correctly
    await page.waitForSelector('text=Subscription Details');
    await expect(page.locator('text=professional Plan')).toBeVisible();
    await expect(page.locator('text=Active')).toBeVisible();
  });
});
