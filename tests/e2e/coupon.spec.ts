/**
 * E2E tests for coupon functionality
 */
import { test, expect } from '@playwright/test';

test.describe('Coupon Functionality', () => {
  test.beforeEach(async ({ page }) => {
    // Go to the home page first to set localStorage
    await page.goto('/');

    // Now we can safely access localStorage
    await page.evaluate(() => {
      try {
        // Set up auth data in localStorage
        localStorage.setItem('clerk-auth-token', 'test-token');

        // Set up auth cache
        const authCache = {
          isAuthenticated: true,
          authToken: 'test-token',
          isInitialized: true,
          lastUpdated: Date.now(),
          user: {
            id: 'test_user_123',
            email: '<EMAIL>',
            firstName: 'Test',
            lastName: 'User',
            username: 'testuser'
          }
        };
        sessionStorage.setItem('renovate-ai-auth-cache', JSON.stringify(authCache));
      } catch (e) {
        console.error('Error setting up auth data:', e);
      }
    });
  });

  test('should display coupon input on checkout page', async ({ page }) => {
    // Navigate to the checkout page
    await page.goto('/checkout?plan=professional&billing=monthly');

    // Wait for the checkout page to load
    await page.waitForSelector('text=Redirecting to Checkout');

    // Check if the coupon input is visible
    const couponInput = page.locator('input[placeholder="Enter coupon code"]');
    await expect(couponInput).toBeVisible();

    // Check if the apply button is visible but disabled (since no code is entered)
    const applyButton = page.locator('button:has-text("Apply")');
    await expect(applyButton).toBeVisible();
    await expect(applyButton).toBeDisabled();
  });

  test('should enable apply button when coupon code is entered', async ({ page }) => {
    // Navigate to the checkout page
    await page.goto('/checkout?plan=professional&billing=monthly');

    // Wait for the checkout page to load
    await page.waitForSelector('text=Redirecting to Checkout');

    // Enter a coupon code
    const couponInput = page.locator('input[placeholder="Enter coupon code"]');
    await couponInput.fill('TEST25');

    // Check if the apply button is enabled
    const applyButton = page.locator('button:has-text("Apply")');
    await expect(applyButton).toBeEnabled();
  });

  test('should show error for invalid coupon code', async ({ page }) => {
    // Mock the API response for invalid coupon
    await page.route('/api/validate-coupon', async (route) => {
      await route.fulfill({
        status: 400,
        contentType: 'application/json',
        body: JSON.stringify({
          success: false,
          error: 'Invalid or expired coupon code'
        })
      });
    });

    // Navigate to the checkout page
    await page.goto('/checkout?plan=professional&billing=monthly');

    // Wait for the checkout page to load
    await page.waitForSelector('text=Redirecting to Checkout');

    // Enter an invalid coupon code
    const couponInput = page.locator('input[placeholder="Enter coupon code"]');
    await couponInput.fill('INVALID');

    // Click the apply button
    const applyButton = page.locator('button:has-text("Apply")');
    await applyButton.click();

    // Check if the error message is displayed
    const errorMessage = page.locator('text=Invalid or expired coupon code');
    await expect(errorMessage).toBeVisible();
  });

  test('should show success for valid coupon code', async ({ page }) => {
    // Mock the API response for valid coupon
    await page.route('/api/validate-coupon', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          coupon: {
            code: 'TEST25',
            description: 'Test Coupon - 25% Off',
            type: 'percentage',
            amount: 25,
            duration: 'once'
          }
        })
      });
    });

    // Navigate to the checkout page
    await page.goto('/checkout?plan=professional&billing=monthly');

    // Wait for the checkout page to load
    await page.waitForSelector('text=Redirecting to Checkout');

    // Enter a valid coupon code
    const couponInput = page.locator('input[placeholder="Enter coupon code"]');
    await couponInput.fill('TEST25');

    // Click the apply button
    const applyButton = page.locator('button:has-text("Apply")');
    await applyButton.click();

    // Check if the success message is displayed
    const successMessage = page.locator('text=25% discount');
    await expect(successMessage).toBeVisible();

    // Check if the remove button is displayed
    const removeButton = page.locator('button svg[class*="XCircle"]').first();
    await expect(removeButton).toBeVisible();
  });

  test('should remove applied coupon when remove button is clicked', async ({ page }) => {
    // Mock the API response for valid coupon
    await page.route('/api/validate-coupon', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          coupon: {
            code: 'TEST25',
            description: 'Test Coupon - 25% Off',
            type: 'percentage',
            amount: 25,
            duration: 'once'
          }
        })
      });
    });

    // Navigate to the checkout page
    await page.goto('/checkout?plan=professional&billing=monthly');

    // Wait for the checkout page to load
    await page.waitForSelector('text=Redirecting to Checkout');

    // Enter a valid coupon code
    const couponInput = page.locator('input[placeholder="Enter coupon code"]');
    await couponInput.fill('TEST25');

    // Click the apply button
    const applyButton = page.locator('button:has-text("Apply")');
    await applyButton.click();

    // Wait for the success message
    await page.waitForSelector('text=25% discount');

    // Click the remove button
    const removeButton = page.locator('button svg[class*="XCircle"]').first();
    await removeButton.click();

    // Check if the coupon input is displayed again
    const newCouponInput = page.locator('input[placeholder="Enter coupon code"]');
    await expect(newCouponInput).toBeVisible();
  });
});
