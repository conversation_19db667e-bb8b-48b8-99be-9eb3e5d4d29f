/**
 * E2E tests for navigation and scrolling behavior
 */
import { test, expect } from '@playwright/test';

test.describe('Navigation and Scrolling', () => {
  test('should scroll to top when navigating to a new page', async ({ page }) => {
    // Start on the home page
    await page.goto('/');

    // Scroll down
    await page.evaluate(() => {
      window.scrollTo(0, 500);
    });

    // Get the current scroll position
    const scrollPositionBefore = await page.evaluate(() => window.scrollY);
    expect(scrollPositionBefore).toBeGreaterThan(0);

    // Click a navigation link
    await page.click('a:has-text("Pricing")');

    // Check that we navigated to the pricing page
    await expect(page).toHaveURL(/pricing/);

    // Check that we're at the top of the page
    const scrollPositionAfter = await page.evaluate(() => window.scrollY);
    expect(scrollPositionAfter).toBe(0);
  });

  test('should scroll to top when clicking a link to the current page', async ({ page }) => {
    // Start on the pricing page
    await page.goto('/pricing');

    // Scroll down
    await page.evaluate(() => {
      window.scrollTo(0, 500);
    });

    // Get the current scroll position
    const scrollPositionBefore = await page.evaluate(() => window.scrollY);
    expect(scrollPositionBefore).toBeGreaterThan(0);

    // Click a link to the same page (pricing)
    // This assumes there's a link in the footer that points to the pricing page
    await page.click('footer a:has-text("Pricing")');

    // Check that we're still on the pricing page
    await expect(page).toHaveURL(/pricing/);

    // Check that we're at the top of the page
    const scrollPositionAfter = await page.evaluate(() => window.scrollY);
    expect(scrollPositionAfter).toBe(0);
  });

  test('footer links should scroll to top of the page', async ({ page }) => {
    // Start on the home page
    await page.goto('/');

    // Scroll to the footer
    await page.evaluate(() => {
      window.scrollTo(0, document.body.scrollHeight);
    });

    // Get the current scroll position
    const scrollPositionBefore = await page.evaluate(() => window.scrollY);
    expect(scrollPositionBefore).toBeGreaterThan(0);

    // Click a footer link
    await page.click('footer a:has-text("Home")');

    // Check that we're at the top of the page
    const scrollPositionAfter = await page.evaluate(() => window.scrollY);
    expect(scrollPositionAfter).toBe(0);
  });

  test('logo should navigate to home and scroll to top', async ({ page }) => {
    // Start on a different page
    await page.goto('/pricing');

    // Scroll down
    await page.evaluate(() => {
      window.scrollTo(0, 500);
    });

    // Get the current scroll position
    const scrollPositionBefore = await page.evaluate(() => window.scrollY);
    expect(scrollPositionBefore).toBeGreaterThan(0);

    // Click the logo
    await page.click('a:has-text("Renovision.Studio")');

    // Check that we navigated to the home page
    await expect(page).toHaveURL(/^\/$|^\/$/);

    // Check that we're at the top of the page
    const scrollPositionAfter = await page.evaluate(() => window.scrollY);
    expect(scrollPositionAfter).toBe(0);
  });
});
