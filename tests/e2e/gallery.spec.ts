import { test, expect } from '@playwright/test';
import { authenticatePage } from '../utils/auth.e2e';

// Skip gallery tests for now until authentication is properly set up
test.describe.skip('Gallery', () => {
  test.beforeEach(async ({ page }) => {
    // Authenticate the page before each test
    await page.goto('/');
    await authenticatePage(page);
    await page.goto('/gallery');
  });

  test('should display gallery page', async ({ page }) => {
    // Check if gallery page is visible
    await expect(page.locator('text=Gallery')).toBeVisible();
  });

  test('should display image comparison sliders', async ({ page }) => {
    // Check if any image comparison sliders are visible
    const hasSliders = await page.locator('.image-comparison-slider').count() > 0;

    if (hasSliders) {
      // Check that sliders have before and after images
      await expect(page.locator('.before-image')).toBeVisible();
      await expect(page.locator('.after-image')).toBeVisible();
    } else {
      // If no projects exist, check for empty state
      await expect(page.locator('text=No projects found, text=No results')).toBeVisible();
    }
  });

  test('should filter projects by room type', async ({ page }) => {
    // Check if filter controls exist
    const hasFilters = await page.locator('text=Filter by Room, text=Filter').isVisible();

    if (hasFilters) {
      // Click on a room type filter
      await page.click('text=Kitchen, text=Bathroom, text=Living Room');

      // Check that filter is applied
      await expect(page.locator('.active-filter, .selected')).toBeVisible();
    } else {
      // Skip test if no filters exist
      test.skip();
    }
  });

  test('should navigate to project details', async ({ page }) => {
    // Check if any projects exist
    const hasProjects = await page.locator('.project-card, .project-item').count() > 0;

    if (hasProjects) {
      // Click on a project card
      await page.click('.project-card, .project-item');

      // Check that we navigated to project details
      await expect(page.locator('text=Project Details, text=Project')).toBeVisible();

      // Check for image comparison slider
      await expect(page.locator('.image-comparison-slider, .comparison-slider')).toBeVisible();
    } else {
      // Skip test if no projects exist
      test.skip();
    }
  });
});
