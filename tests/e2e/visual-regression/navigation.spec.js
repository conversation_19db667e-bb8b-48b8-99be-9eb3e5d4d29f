/**
 * Visual regression tests for navigation components
 * 
 * This test suite captures screenshots of navigation components and compares them
 * with baseline screenshots to detect visual regressions.
 */

import { test, expect } from '@playwright/test';

test.describe('Navigation Visual Regression', () => {
  test('header should match snapshot', async ({ page }) => {
    // Navigate to the home page
    await page.goto('/');

    // Wait for the header to load
    await page.waitForSelector('header');

    // Find the header
    const header = page.locator('header').first();
    await expect(header).toBeVisible();
    
    // Take a screenshot and compare with baseline
    await expect(header).toHaveScreenshot('header.png');
  });

  test('footer should match snapshot', async ({ page }) => {
    // Navigate to the home page
    await page.goto('/');

    // Wait for the footer to load
    await page.waitForSelector('footer');

    // Find the footer
    const footer = page.locator('footer').first();
    await expect(footer).toBeVisible();
    
    // Take a screenshot and compare with baseline
    await expect(footer).toHaveScreenshot('footer.png');
  });

  test('mobile header should match snapshot', async ({ page }) => {
    // Set viewport to mobile size
    await page.setViewportSize({ width: 375, height: 667 });

    // Navigate to the home page
    await page.goto('/');

    // Wait for the header to load
    await page.waitForSelector('header');

    // Find the header
    const header = page.locator('header').first();
    await expect(header).toBeVisible();
    
    // Take a screenshot and compare with baseline
    await expect(header).toHaveScreenshot('mobile-header.png');
  });

  test('mobile footer should match snapshot', async ({ page }) => {
    // Set viewport to mobile size
    await page.setViewportSize({ width: 375, height: 667 });

    // Navigate to the home page
    await page.goto('/');

    // Wait for the footer to load
    await page.waitForSelector('footer');

    // Find the footer
    const footer = page.locator('footer').first();
    await expect(footer).toBeVisible();
    
    // Take a screenshot and compare with baseline
    await expect(footer).toHaveScreenshot('mobile-footer.png');
  });

  test('user dropdown should match snapshot', async ({ page }) => {
    // Simulate being logged in
    await page.evaluate(() => {
      // Set up auth data in localStorage
      localStorage.setItem('clerk-auth-token', 'test-token');

      // Set up auth cache
      const authCache = {
        isAuthenticated: true,
        authToken: 'test-token',
        isInitialized: true,
        lastUpdated: Date.now(),
        user: {
          id: 'test_user_123',
          email: '<EMAIL>',
          firstName: 'Test',
          lastName: 'User',
          username: 'testuser'
        }
      };
      sessionStorage.setItem('renovate-ai-auth-cache', JSON.stringify(authCache));
    });

    // Navigate to the home page
    await page.goto('/');

    // Wait for the user avatar to load
    await page.waitForSelector('header button:has(svg)');

    // Click the user avatar to open the dropdown
    await page.click('header button:has(svg)');

    // Wait for the dropdown to open
    await page.waitForSelector('text=Sign Out');

    // Find the dropdown
    const dropdown = page.locator('div[role="menu"]').first();
    await expect(dropdown).toBeVisible();
    
    // Take a screenshot and compare with baseline
    await expect(dropdown).toHaveScreenshot('user-dropdown.png');
  });
});
