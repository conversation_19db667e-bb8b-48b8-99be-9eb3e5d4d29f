/**
 * Visual regression tests for coupon UI
 * 
 * This test suite captures screenshots of coupon UI components and compares them
 * with baseline screenshots to detect visual regressions.
 */

import { test, expect } from '@playwright/test';

test.describe('Coupon UI Visual Regression', () => {
  test.beforeEach(async ({ page }) => {
    // Simulate being logged in
    await page.evaluate(() => {
      // Set up auth data in localStorage
      localStorage.setItem('clerk-auth-token', 'test-token');

      // Set up auth cache
      const authCache = {
        isAuthenticated: true,
        authToken: 'test-token',
        isInitialized: true,
        lastUpdated: Date.now(),
        user: {
          id: 'test_user_123',
          email: '<EMAIL>',
          firstName: 'Test',
          lastName: 'User',
          username: 'testuser'
        }
      };
      sessionStorage.setItem('renovate-ai-auth-cache', JSON.stringify(authCache));
    });
  });

  test('empty coupon input should match snapshot', async ({ page }) => {
    // Navigate to the checkout page
    await page.goto('/checkout?plan=professional&billing=monthly');

    // Wait for the checkout page to load
    await page.waitForSelector('text=Redirecting to Checkout');

    // Find the coupon input section
    const couponSection = page.locator('div').filter({ has: page.locator('input[placeholder="Enter coupon code"]') }).first();
    await expect(couponSection).toBeVisible();
    
    // Take a screenshot and compare with baseline
    await expect(couponSection).toHaveScreenshot('empty-coupon-input.png');
  });

  test('coupon input with code entered should match snapshot', async ({ page }) => {
    // Navigate to the checkout page
    await page.goto('/checkout?plan=professional&billing=monthly');

    // Wait for the checkout page to load
    await page.waitForSelector('text=Redirecting to Checkout');

    // Find the coupon input and enter a code
    const couponInput = page.locator('input[placeholder="Enter coupon code"]');
    await couponInput.fill('TEST25');

    // Find the coupon input section
    const couponSection = page.locator('div').filter({ has: page.locator('input[placeholder="Enter coupon code"]') }).first();
    await expect(couponSection).toBeVisible();
    
    // Take a screenshot and compare with baseline
    await expect(couponSection).toHaveScreenshot('coupon-input-with-code.png');
  });

  test('coupon input with error should match snapshot', async ({ page }) => {
    // Mock the API response for invalid coupon
    await page.route('/api/validate-coupon', async (route) => {
      await route.fulfill({
        status: 400,
        contentType: 'application/json',
        body: JSON.stringify({
          success: false,
          error: 'Invalid or expired coupon code'
        })
      });
    });

    // Navigate to the checkout page
    await page.goto('/checkout?plan=professional&billing=monthly');

    // Wait for the checkout page to load
    await page.waitForSelector('text=Redirecting to Checkout');

    // Find the coupon input and enter a code
    const couponInput = page.locator('input[placeholder="Enter coupon code"]');
    await couponInput.fill('INVALID');

    // Click the apply button
    const applyButton = page.locator('button:has-text("Apply")');
    await applyButton.click();

    // Wait for the error message
    await page.waitForSelector('text=Invalid or expired coupon code');

    // Find the coupon input section
    const couponSection = page.locator('div').filter({ has: page.locator('text=Invalid or expired coupon code') }).first();
    await expect(couponSection).toBeVisible();
    
    // Take a screenshot and compare with baseline
    await expect(couponSection).toHaveScreenshot('coupon-input-with-error.png');
  });

  test('applied coupon should match snapshot', async ({ page }) => {
    // Mock the API response for valid coupon
    await page.route('/api/validate-coupon', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          coupon: {
            code: 'TEST25',
            description: 'Test Coupon - 25% Off',
            type: 'percentage',
            amount: 25,
            duration: 'once'
          }
        })
      });
    });

    // Navigate to the checkout page
    await page.goto('/checkout?plan=professional&billing=monthly');

    // Wait for the checkout page to load
    await page.waitForSelector('text=Redirecting to Checkout');

    // Find the coupon input and enter a code
    const couponInput = page.locator('input[placeholder="Enter coupon code"]');
    await couponInput.fill('TEST25');

    // Click the apply button
    const applyButton = page.locator('button:has-text("Apply")');
    await applyButton.click();

    // Wait for the success message
    await page.waitForSelector('text=25% discount');

    // Find the coupon section
    const couponSection = page.locator('div').filter({ has: page.locator('text=25% discount') }).first();
    await expect(couponSection).toBeVisible();
    
    // Take a screenshot and compare with baseline
    await expect(couponSection).toHaveScreenshot('applied-coupon.png');
  });
});
