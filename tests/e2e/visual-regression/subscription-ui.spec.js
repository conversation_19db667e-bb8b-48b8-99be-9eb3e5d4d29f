/**
 * Visual regression tests for subscription UI
 * 
 * This test suite captures screenshots of subscription UI components and compares them
 * with baseline screenshots to detect visual regressions.
 */

import { test, expect } from '@playwright/test';
import { createTestSubscriptionData } from '../../fixtures/subscriptions';

test.describe('Subscription UI Visual Regression', () => {
  test.beforeEach(async ({ page }) => {
    // Go to the home page
    await page.goto('/');

    // Simulate being logged in
    await page.evaluate(() => {
      // Set up auth data in localStorage
      localStorage.setItem('clerk-auth-token', 'test-token');

      // Set up auth cache
      const authCache = {
        isAuthenticated: true,
        authToken: 'test-token',
        isInitialized: true,
        lastUpdated: Date.now(),
        user: {
          id: 'test_user_123',
          email: '<EMAIL>',
          firstName: 'Test',
          lastName: 'User',
          username: 'testuser'
        }
      };
      sessionStorage.setItem('renovate-ai-auth-cache', JSON.stringify(authCache));
    });

    // Reload the page to apply the auth
    await page.reload();
  });

  test('account page with professional subscription should match snapshot', async ({ page }) => {
    // Set up professional plan data
    await page.evaluate(() => {
      const subscriptionData = {
        subscription: {
          subscription: {
            id: 1,
            user_id: 'test_user_123',
            plan_id: 'professional',
            billing_cycle: 'annual',
            status: 'active',
            stripe_customer_id: 'cus_test123',
            stripe_subscription_id: 'sub_test123',
            current_period_start: new Date().toISOString(),
            current_period_end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
            cancel_at_period_end: false
          },
          usage: {
            projects: 2,
            images: 5
          },
          limits: {
            projects: 10,
            imagesPerProject: 5
          }
        }
      };
      localStorage.setItem('subscription-data', JSON.stringify(subscriptionData));
    });

    // Navigate to the account page
    await page.goto('/account');

    // Wait for the subscription details to load
    await page.waitForSelector('text=Subscription Details');

    // Take a screenshot of the subscription details section
    const subscriptionCard = page.locator('h2:text("Subscription Details")').locator('xpath=ancestor::div[contains(@class, "card")]');
    await expect(subscriptionCard).toBeVisible();
    
    // Take a screenshot and compare with baseline
    await expect(subscriptionCard).toHaveScreenshot('professional-subscription-details.png');
  });

  test('account page with starter subscription should match snapshot', async ({ page }) => {
    // Set up starter plan data
    await page.evaluate(() => {
      const subscriptionData = {
        subscription: {
          subscription: {
            id: 1,
            user_id: 'test_user_123',
            plan_id: 'starter',
            billing_cycle: 'monthly',
            status: 'active',
            stripe_customer_id: 'cus_test123',
            stripe_subscription_id: 'sub_test123',
            current_period_start: new Date().toISOString(),
            current_period_end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
            cancel_at_period_end: false
          },
          usage: {
            projects: 1,
            images: 2
          },
          limits: {
            projects: 3,
            imagesPerProject: 3
          }
        }
      };
      localStorage.setItem('subscription-data', JSON.stringify(subscriptionData));
    });

    // Navigate to the account page
    await page.goto('/account');

    // Wait for the subscription details to load
    await page.waitForSelector('text=Subscription Details');

    // Take a screenshot of the subscription details section
    const subscriptionCard = page.locator('h2:text("Subscription Details")').locator('xpath=ancestor::div[contains(@class, "card")]');
    await expect(subscriptionCard).toBeVisible();
    
    // Take a screenshot and compare with baseline
    await expect(subscriptionCard).toHaveScreenshot('starter-subscription-details.png');
  });

  test('account page with free tier should match snapshot', async ({ page }) => {
    // Set up free tier (no subscription data)
    await page.evaluate(() => {
      localStorage.removeItem('subscription-data');
    });

    // Navigate to the account page
    await page.goto('/account');

    // Wait for the subscription details to load
    await page.waitForSelector('text=Subscription Details');

    // Take a screenshot of the subscription details section
    const subscriptionCard = page.locator('h2:text("Subscription Details")').locator('xpath=ancestor::div[contains(@class, "card")]');
    await expect(subscriptionCard).toBeVisible();
    
    // Take a screenshot and compare with baseline
    await expect(subscriptionCard).toHaveScreenshot('free-tier-subscription-details.png');
  });

  test('account page with cancelled subscription should match snapshot', async ({ page }) => {
    // Set up cancelled subscription data
    await page.evaluate(() => {
      const subscriptionData = {
        subscription: {
          subscription: {
            id: 1,
            user_id: 'test_user_123',
            plan_id: 'professional',
            billing_cycle: 'annual',
            status: 'active',
            stripe_customer_id: 'cus_test123',
            stripe_subscription_id: 'sub_test123',
            current_period_start: new Date().toISOString(),
            current_period_end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
            cancel_at_period_end: true
          },
          usage: {
            projects: 2,
            images: 5
          },
          limits: {
            projects: 10,
            imagesPerProject: 5
          }
        }
      };
      localStorage.setItem('subscription-data', JSON.stringify(subscriptionData));
    });

    // Navigate to the account page
    await page.goto('/account');

    // Wait for the subscription details to load
    await page.waitForSelector('text=Subscription Details');

    // Take a screenshot of the subscription details section
    const subscriptionCard = page.locator('h2:text("Subscription Details")').locator('xpath=ancestor::div[contains(@class, "card")]');
    await expect(subscriptionCard).toBeVisible();
    
    // Take a screenshot and compare with baseline
    await expect(subscriptionCard).toHaveScreenshot('cancelled-subscription-details.png');
  });

  test('usage tab should match snapshot', async ({ page }) => {
    // Set up professional plan data with usage
    await page.evaluate(() => {
      const subscriptionData = {
        subscription: {
          subscription: {
            id: 1,
            user_id: 'test_user_123',
            plan_id: 'professional',
            billing_cycle: 'annual',
            status: 'active',
            stripe_customer_id: 'cus_test123',
            stripe_subscription_id: 'sub_test123',
            current_period_start: new Date().toISOString(),
            current_period_end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
            cancel_at_period_end: false
          },
          usage: {
            projects: 5,
            images: 15
          },
          limits: {
            projects: 10,
            imagesPerProject: 5
          }
        }
      };
      localStorage.setItem('subscription-data', JSON.stringify(subscriptionData));
    });

    // Navigate to the account page
    await page.goto('/account');

    // Wait for the subscription details to load
    await page.waitForSelector('text=Subscription Details');

    // Switch to the usage tab
    await page.locator('button:text("Usage")').click();

    // Wait for the usage statistics to load
    await page.waitForSelector('text=Usage Statistics');

    // Take a screenshot of the usage tab
    const usageCard = page.locator('h2:text("Usage Statistics")').locator('xpath=ancestor::div[contains(@class, "card")]');
    await expect(usageCard).toBeVisible();
    
    // Take a screenshot and compare with baseline
    await expect(usageCard).toHaveScreenshot('usage-statistics.png');
  });

  test('pricing page should match snapshot', async ({ page }) => {
    // Navigate to the pricing page
    await page.goto('/pricing');

    // Wait for the pricing plans to load
    await page.waitForSelector('text=Pricing Plans');

    // Take a screenshot of the pricing page
    const pricingSection = page.locator('h1:text("Pricing Plans")').locator('xpath=ancestor::section');
    await expect(pricingSection).toBeVisible();
    
    // Take a screenshot and compare with baseline
    await expect(pricingSection).toHaveScreenshot('pricing-plans.png');
  });

  test('checkout page should match snapshot', async ({ page }) => {
    // Mock the API response for checkout session creation
    await page.route('**/api/create-checkout-session', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ 
          url: 'https://checkout.stripe.com/c/pay/test_checkout_session'
        })
      });
    });

    // Navigate to the checkout page
    await page.goto('/checkout?plan=professional&billing=annual');

    // Wait for the checkout page to load
    await page.waitForSelector('text=Redirecting to Checkout');

    // Take a screenshot of the checkout page
    const checkoutContainer = page.locator('div.container').first();
    await expect(checkoutContainer).toBeVisible();
    
    // Take a screenshot and compare with baseline
    await expect(checkoutContainer).toHaveScreenshot('checkout-page.png');
  });

  test('payment success page should match snapshot', async ({ page }) => {
    // Navigate to the payment success page
    await page.goto('/payment-success?session_id=cs_test_123');

    // Wait for the success page to load
    await page.waitForSelector('text=Payment Successful');

    // Take a screenshot of the success page
    const successContainer = page.locator('div.container').first();
    await expect(successContainer).toBeVisible();
    
    // Take a screenshot and compare with baseline
    await expect(successContainer).toHaveScreenshot('payment-success-page.png');
  });

  test('subscription cancellation dialog should match snapshot', async ({ page }) => {
    // Set up professional plan data
    await page.evaluate(() => {
      const subscriptionData = {
        subscription: {
          subscription: {
            id: 1,
            user_id: 'test_user_123',
            plan_id: 'professional',
            billing_cycle: 'annual',
            status: 'active',
            stripe_customer_id: 'cus_test123',
            stripe_subscription_id: 'sub_test123',
            current_period_start: new Date().toISOString(),
            current_period_end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
            cancel_at_period_end: false
          },
          usage: {
            projects: 2,
            images: 5
          },
          limits: {
            projects: 10,
            imagesPerProject: 5
          }
        }
      };
      localStorage.setItem('subscription-data', JSON.stringify(subscriptionData));
    });

    // Navigate to the account page
    await page.goto('/account');

    // Wait for the subscription details to load
    await page.waitForSelector('text=Subscription Details');

    // Click the cancel subscription button
    await page.locator('button:text("Cancel Subscription")').click();

    // Wait for the confirmation dialog to appear
    await page.waitForSelector('text=Cancel Subscription?');

    // Take a screenshot of the confirmation dialog
    const dialog = page.locator('h2:text("Cancel Subscription?")').locator('xpath=ancestor::div[contains(@role, "dialog")]');
    await expect(dialog).toBeVisible();
    
    // Take a screenshot and compare with baseline
    await expect(dialog).toHaveScreenshot('cancel-subscription-dialog.png');
  });
});
