import { test, expect } from '@playwright/test';

// Skip pricing tests for now until the pricing page is implemented
test.describe.skip('Pricing', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/pricing');
  });

  test('should display pricing plans', async ({ page }) => {
    // Check if pricing page is visible
    await expect(page.locator('text=Pricing Plans, text=Pricing')).toBeVisible();

    // Check for all three pricing tiers
    await expect(page.locator('text=Starter, text=Basic')).toBeVisible();
    await expect(page.locator('text=Professional, text=Pro')).toBeVisible();
    await expect(page.locator('text=Enterprise, text=Business')).toBeVisible();
  });

  test('should display correct pricing for Starter plan', async ({ page }) => {
    // Check for annual pricing
    await expect(page.locator('text=$14.99/month, text=$14.99')).toBeVisible();
    await expect(page.locator('text=billed annually, text=per year')).toBeVisible();

    // Switch to monthly pricing if toggle exists
    const hasPricingToggle = await page.locator('.pricing-toggle, .toggle, [role="switch"]').isVisible();
    if (hasPricingToggle) {
      await page.click('.pricing-toggle, .toggle, [role="switch"]');

      // Check for monthly pricing (20% more)
      await expect(page.locator('text=$17.99/month, text=$17.99')).toBeVisible();
      await expect(page.locator('text=billed monthly, text=per month')).toBeVisible();
    }
  });

  test('should display correct pricing for Professional plan', async ({ page }) => {
    // Check for annual pricing
    await expect(page.locator('text=$49/month, text=$49')).toBeVisible();
    await expect(page.locator('text=billed annually, text=per year')).toBeVisible();

    // Switch to monthly pricing if toggle exists
    const hasPricingToggle = await page.locator('.pricing-toggle, .toggle, [role="switch"]').isVisible();
    if (hasPricingToggle) {
      await page.click('.pricing-toggle, .toggle, [role="switch"]');

      // Check for monthly pricing (20% more)
      await expect(page.locator('text=$58.80/month, text=$58.80')).toBeVisible();
      await expect(page.locator('text=billed monthly, text=per month')).toBeVisible();
    }
  });

  test('should have consistent sign-in buttons', async ({ page }) => {
    // Check that all pricing plans have sign-in buttons
    const signInButtons = await page.locator('text=Sign Up, text=Get Started, text=Subscribe').count();

    // Should have at least 3 buttons (one for each plan)
    expect(signInButtons).toBeGreaterThanOrEqual(3);

    // All buttons should have the same class for consistency
    const buttonClasses = await page.$$eval('button:has-text("Sign Up"), button:has-text("Get Started"), button:has-text("Subscribe")',
      buttons => buttons.map(b => b.className));

    if (buttonClasses.length > 0) {
      // Check that all buttons have at least one common class
      const commonClass = buttonClasses[0].split(' ').find(cls =>
        buttonClasses.every(classes => classes.includes(cls)));

      expect(commonClass).toBeTruthy();
    }
  });
});
