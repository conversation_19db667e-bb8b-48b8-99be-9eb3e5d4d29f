import { test, expect } from '@playwright/test';

test.describe('Landing Page', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
  });

  test('should load the page', async ({ page }) => {
    // Take a screenshot of the landing page for visual inspection
    await page.screenshot({ path: 'tests/screenshots/landing-page.png' });

    // Check if page has loaded by verifying that the body element exists
    const bodyExists = await page.locator('body').isVisible();
    expect(bodyExists).toBeTruthy();

    // Check if the page has a title
    const title = await page.title();
    console.log(`Page title: ${title}`);

    // This test should always pass as long as the page loads
    expect(true).toBeTruthy();
  });
});
