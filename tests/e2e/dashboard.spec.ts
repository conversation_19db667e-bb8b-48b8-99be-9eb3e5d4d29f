import { test, expect } from '@playwright/test';
import { authenticatePage } from '../utils/auth.e2e';

// Skip dashboard tests for now until authentication is properly set up
test.describe.skip('Dashboard', () => {
  test.beforeEach(async ({ page }) => {
    // Authenticate the page before each test
    await page.goto('/');
    await authenticatePage(page);
    await page.goto('/dashboard');
  });

  test('should display user projects', async ({ page }) => {
    // Check if dashboard loads
    await expect(page.locator('h1:has-text("Dashboard")')).toBeVisible();

    // Check for projects section
    await expect(page.locator('text=Your Projects')).toBeVisible();
  });

  test('should navigate to create new project', async ({ page }) => {
    // Click on Start New Project button
    await page.click('text=Start New Project');

    // Check if we're on the create visualization page
    await expect(page).toHaveURL(/create/);

    // Check for saved drafts section
    await expect(page.locator('text=Saved Drafts')).toBeVisible();
  });

  test('should navigate to gallery', async ({ page }) => {
    // Click on Gallery link
    await page.click('text=Gallery');

    // Check if we're on the gallery page
    await expect(page).toHaveURL(/gallery/);
  });

  test('should navigate to reference library', async ({ page }) => {
    // Click on Reference Library link
    await page.click('text=Reference Library');

    // Check if we're on the reference library page
    await expect(page).toHaveURL(/reference-library/);
  });

  test('logo should navigate back to dashboard', async ({ page }) => {
    // First navigate away from dashboard
    await page.click('text=Gallery');
    await expect(page).toHaveURL(/gallery/);

    // Click on logo to return to dashboard
    await page.click('text=Renovision.Studio');

    // Check if we're back on the dashboard
    await expect(page).toHaveURL(/dashboard/);
  });
});
