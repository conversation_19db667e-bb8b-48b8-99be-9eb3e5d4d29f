import { storage } from '../../server/storage';
import { db } from '../../server/db';
import { v4 as uuidv4 } from 'uuid';

// Mock user data
const testUserId = `test_user_${uuidv4()}`;

// Clean up test data before and after tests
beforeAll(async () => {
  await cleanupTestData();
});

afterAll(async () => {
  await cleanupTestData();
});

async function cleanupTestData() {
  try {
    // Delete test usage records
    await db.client
      .from('usage')
      .delete()
      .eq('user_id', testUserId);
    
    // Delete test user if exists
    await db.client
      .from('users')
      .delete()
      .eq('id', testUserId);
  } catch (error) {
    console.error('Error cleaning up test data:', error);
  }
}

describe('Usage Tracking', () => {
  // Create a test user
  beforeAll(async () => {
    await storage.upsertUser({
      id: testUserId,
      username: 'test_user',
      email: '<EMAIL>',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    });
  });

  it('should create a new usage record when incrementing project count for a new user', async () => {
    const usage = await storage.incrementProjectCount(testUserId);

    expect(usage).toBeDefined();
    expect(usage?.user_id).toBe(testUserId);
    expect(usage?.projects_count).toBe(1);
    expect(usage?.images_count).toBe(0);
  });

  it('should validate input parameters for incrementImageCount', async () => {
    await expect(storage.incrementImageCount('', 1)).rejects.toThrow('Invalid userId provided');
    await expect(storage.incrementImageCount(testUserId, 0)).rejects.toThrow('Count must be positive');
    await expect(storage.incrementImageCount(testUserId, -1)).rejects.toThrow('Count must be positive');
  });

  it('should validate input parameters for decrementImageCount', async () => {
    await expect(storage.decrementImageCount('', 1)).rejects.toThrow('Invalid userId provided');
    await expect(storage.decrementImageCount(testUserId, 0)).rejects.toThrow('Count must be positive');
    await expect(storage.decrementImageCount(testUserId, -1)).rejects.toThrow('Count must be positive');
  });

  it('should increment project count for an existing usage record', async () => {
    // First increment should have created a record, now increment again
    const usage = await storage.incrementProjectCount(testUserId);
    
    expect(usage).toBeDefined();
    expect(usage?.projects_count).toBe(2); // Should be 2 now
    expect(usage?.images_count).toBe(0);
  });

  it('should create a new usage record when incrementing image count for a new user', async () => {
    // Create a new test user ID for this test
    const newTestUserId = `test_user_${uuidv4()}`;

    // Create the user
    await storage.upsertUser({
      id: newTestUserId,
      username: 'test_user_2',
      email: '<EMAIL>',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    });

    const usage = await storage.incrementImageCount(newTestUserId, 3);

    expect(usage).toBeDefined();
    expect(usage?.user_id).toBe(newTestUserId);
    expect(usage?.projects_count).toBe(0);
    expect(usage?.images_count).toBe(3);

    // Clean up
    await db.client
      .from('usage')
      .delete()
      .eq('user_id', newTestUserId);

    await db.client
      .from('users')
      .delete()
      .eq('id', newTestUserId);
  });

  it('should decrement image count correctly', async () => {
    // Create a new test user ID for this test
    const newTestUserId = `test_user_${uuidv4()}`;

    // Create the user
    await storage.upsertUser({
      id: newTestUserId,
      username: 'test_user_3',
      email: '<EMAIL>',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    });

    // First increment to 5
    await storage.incrementImageCount(newTestUserId, 5);

    // Then decrement by 2
    const usage = await storage.decrementImageCount(newTestUserId, 2);

    expect(usage).toBeDefined();
    expect(usage?.user_id).toBe(newTestUserId);
    expect(usage?.images_count).toBe(3);

    // Clean up
    await db.client
      .from('usage')
      .delete()
      .eq('user_id', newTestUserId);

    await db.client
      .from('users')
      .delete()
      .eq('id', newTestUserId);
  });

  it('should not allow image count to go below zero', async () => {
    // Create a new test user ID for this test
    const newTestUserId = `test_user_${uuidv4()}`;

    // Create the user
    await storage.upsertUser({
      id: newTestUserId,
      username: 'test_user_4',
      email: '<EMAIL>',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    });

    // First increment to 2
    await storage.incrementImageCount(newTestUserId, 2);

    // Then try to decrement by 5 (should only go to 0)
    const usage = await storage.decrementImageCount(newTestUserId, 5);

    expect(usage).toBeDefined();
    expect(usage?.user_id).toBe(newTestUserId);
    expect(usage?.images_count).toBe(0);

    // Clean up
    await db.client
      .from('usage')
      .delete()
      .eq('user_id', newTestUserId);

    await db.client
      .from('users')
      .delete()
      .eq('id', newTestUserId);
  });

  it('should handle decrementing when no usage record exists', async () => {
    // Create a new test user ID for this test
    const newTestUserId = `test_user_${uuidv4()}`;

    // Create the user
    await storage.upsertUser({
      id: newTestUserId,
      username: 'test_user_5',
      email: '<EMAIL>',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    });

    // Try to decrement without any existing usage record
    const usage = await storage.decrementImageCount(newTestUserId, 1);

    expect(usage).toBeUndefined();

    // Clean up
    await db.client
      .from('users')
      .delete()
      .eq('id', newTestUserId);
  });

  it('should increment image count for an existing usage record', async () => {
    // First increment images
    await storage.incrementImageCount(testUserId, 2);
    
    // Then increment again and check
    const usage = await storage.incrementImageCount(testUserId, 3);
    
    expect(usage).toBeDefined();
    expect(usage?.projects_count).toBe(2); // From previous tests
    expect(usage?.images_count).toBe(5); // 2 + 3 = 5
  });

  it('should correctly retrieve current usage', async () => {
    const usage = await storage.getCurrentUsage(testUserId);
    
    expect(usage).toBeDefined();
    expect(usage?.user_id).toBe(testUserId);
    expect(usage?.projects_count).toBe(2);
    expect(usage?.images_count).toBe(5);
  });
});
