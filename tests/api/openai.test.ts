const fs = require('fs');
const path = require('path');
const { generateRenovationImage } = require('../../server/openai');

describe('OpenAI Image Generation', () => {
  // Skip all tests if OpenAI API key is not set
  beforeAll(() => {
    if (!process.env.OPENAI_API_KEY) {
      console.warn('Skipping OpenAI tests: OPENAI_API_KEY is not set');
      jest.setTimeout(1);
    } else {
      // Set a longer timeout for OpenAI API calls
      jest.setTimeout(30000);
    }
  });

  it('should generate an image with OpenAI gpt-image-1', async () => {
    // Skip test if OpenAI API key is not set
    if (!process.env.OPENAI_API_KEY) {
      return;
    }

    // Create a test image path
    const testImagePath = path.join(__dirname, '../fixtures/test-room.jpg');

    // Ensure the test image exists
    if (!fs.existsSync(testImagePath)) {
      throw new Error(`Test image not found at ${testImagePath}`);
    }

    // Generate an image
    const result = await generateRenovationImage(
      testImagePath,
      'Change the wall color to light blue'
    );

    // Check that the result is a valid file path
    expect(result).toBeTruthy();
    expect(fs.existsSync(result)).toBe(true);

    // Check that the result is an image file
    const fileStats = fs.statSync(result);
    expect(fileStats.size).toBeGreaterThan(0);
    expect(path.extname(result)).toBe('.png');
  });

  it('should handle reference images in generation', async () => {
    // Skip test if OpenAI API key is not set
    if (!process.env.OPENAI_API_KEY) {
      return;
    }

    // Create test image paths
    const testImagePath = path.join(__dirname, '../fixtures/test-room.jpg');
    const referenceImagePath = path.join(__dirname, '../fixtures/reference-image.jpg');

    // Ensure the test images exist
    if (!fs.existsSync(testImagePath) || !fs.existsSync(referenceImagePath)) {
      throw new Error('Test images not found');
    }

    // Generate an image with reference
    const result = await generateRenovationImage(
      testImagePath,
      'Renovate this room in the style of the reference image',
      [referenceImagePath]
    );

    // Check that the result is a valid file path
    expect(result).toBeTruthy();
    expect(fs.existsSync(result)).toBe(true);

    // Check that the result is an image file
    const fileStats = fs.statSync(result);
    expect(fileStats.size).toBeGreaterThan(0);
    expect(path.extname(result)).toBe('.png');
  });

  it('should handle errors gracefully', async () => {
    // Skip test if OpenAI API key is not set
    if (!process.env.OPENAI_API_KEY) {
      return;
    }

    // Try to generate with a non-existent image
    const result = await generateRenovationImage(
      'non-existent-image.jpg',
      'Change the wall color to light blue'
    );

    // Should return an error image path
    expect(result).toBeTruthy();
    expect(fs.existsSync(result)).toBe(true);

    // The error image should be a valid file
    const fileStats = fs.statSync(result);
    expect(fileStats.size).toBeGreaterThan(0);
  });

  it('should handle structured modification data with floor replacement', async () => {
    // Skip test if OpenAI API key is not set
    if (!process.env.OPENAI_API_KEY) {
      return;
    }

    // Create test image path
    const testImagePath = path.join(__dirname, '../fixtures/test-room.jpg');

    // Ensure the test image exists
    if (!fs.existsSync(testImagePath)) {
      throw new Error('Test image not found');
    }

    // Generate an image with structured data
    const result = await generateRenovationImage(
      testImagePath,
      {
        type: 'replace_floor',
        description: 'Replace the floor with dark hardwood',
        options: {
          material: 'hardwood',
          color: 'dark',
          customDescription: 'With wide planks',
          useReferenceImage: false
        }
      }
    );

    // Check that the result is a valid file path
    expect(result).toBeTruthy();
    expect(fs.existsSync(result)).toBe(true);

    // Check that the result is an image file
    const fileStats = fs.statSync(result);
    expect(fileStats.size).toBeGreaterThan(0);
    expect(path.extname(result)).toBe('.png');
  });

  it('should handle structured modification with reference image', async () => {
    // Skip test if OpenAI API key is not set
    if (!process.env.OPENAI_API_KEY) {
      return;
    }

    // Create test image paths
    const testImagePath = path.join(__dirname, '../fixtures/test-room.jpg');
    const referenceImagePath = path.join(__dirname, '../fixtures/reference-image.jpg');

    // Ensure the test images exist
    if (!fs.existsSync(testImagePath) || !fs.existsSync(referenceImagePath)) {
      throw new Error('Test images not found');
    }

    // Generate an image with structured data and reference image
    const result = await generateRenovationImage(
      testImagePath,
      {
        type: 'replace_floor',
        description: 'Replace the floor using reference image',
        options: {
          useReferenceImage: true,
          customDescription: 'Make it look modern'
        },
        primaryReferenceImageId: 1 // This won't be used in the test but simulates the structure
      },
      [referenceImagePath]
    );

    // Check that the result is a valid file path
    expect(result).toBeTruthy();
    expect(fs.existsSync(result)).toBe(true);

    // Check that the result is an image file
    const fileStats = fs.statSync(result);
    expect(fileStats.size).toBeGreaterThan(0);
    expect(path.extname(result)).toBe('.png');
  });
});
