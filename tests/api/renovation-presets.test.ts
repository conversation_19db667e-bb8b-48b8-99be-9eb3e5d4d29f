const { createAuthenticatedApiClient } = require('../utils/auth');
const { createTestUser, cleanupTestData } = require('../utils/db');

const API_BASE_URL = 'http://localhost:3000';
const TEST_USER_ID = 'test_user_123';

describe('Renovation Presets API', () => {
  let apiClient: ReturnType<typeof createAuthenticatedApiClient>;
  let testPresetId: number;

  beforeAll(async () => {
    // Create a test user in the database
    await createTestUser(TEST_USER_ID);

    // Get a test auth token
    const authToken = process.env.TEST_AUTH_TOKEN || 'test-token';

    // Create an authenticated API client
    apiClient = createAuthenticatedApiClient(API_BASE_URL, authToken);
  });

  afterAll(async () => {
    // Clean up test data
    await cleanupTestData();
  });

  it('should create a new renovation preset', async () => {
    const presetData = {
      name: 'Test Preset API',
      description: 'Created via API test',
      room_type: 'kitchen',
      style: 'modern',
      color_scheme: 'blue',
      materials: ['wood', 'metal'],
      is_public: false,
      is_default: false
    };

    const response = await apiClient.post('/api/renovation-presets', presetData);

    expect(response).toBeDefined();
    expect(response.id).toBeDefined();
    expect(response.name).toBe(presetData.name);
    expect(response.description).toBe(presetData.description);
    expect(response.created_by).toBe(TEST_USER_ID);

    // Save the preset ID for later tests
    testPresetId = response.id;
  });

  it('should get all renovation presets', async () => {
    const response = await apiClient.get('/api/renovation-presets');

    expect(Array.isArray(response)).toBe(true);

    // Check if our test preset is in the list
    const testPreset = response.find((p: any) => p.name === 'Test Preset API');
    expect(testPreset).toBeDefined();
  });

  it('should filter renovation presets by room type', async () => {
    const response = await apiClient.get('/api/renovation-presets?roomType=kitchen');

    expect(Array.isArray(response)).toBe(true);

    // All presets should be for kitchens
    response.forEach((preset: any) => {
      expect(preset.room_type).toBe('kitchen');
    });

    // Check if our test preset is in the list
    const testPreset = response.find((p: any) => p.name === 'Test Preset API');
    expect(testPreset).toBeDefined();
  });

  it('should get a single renovation preset by ID', async () => {
    const response = await apiClient.get(`/api/renovation-presets/${testPresetId}`);

    expect(response).toBeDefined();
    expect(response.id).toBe(testPresetId);
    expect(response.name).toBe('Test Preset API');
    expect(response.description).toBe('Created via API test');
  });

  it('should update a renovation preset', async () => {
    const updateData = {
      name: 'Updated Test Preset',
      description: 'Updated via API test',
      style: 'contemporary'
    };

    const response = await apiClient.put(`/api/renovation-presets/${testPresetId}`, updateData);

    expect(response).toBeDefined();
    expect(response.id).toBe(testPresetId);
    expect(response.name).toBe(updateData.name);
    expect(response.description).toBe(updateData.description);
    expect(response.style).toBe(updateData.style);
  });

  it('should delete a renovation preset', async () => {
    const response = await apiClient.delete(`/api/renovation-presets/${testPresetId}`);

    // Check that the preset is gone
    try {
      await apiClient.get(`/api/renovation-presets/${testPresetId}`);
      fail('Preset should have been deleted');
    } catch (error) {
      expect(error).toBeDefined();
    }
  });
});
