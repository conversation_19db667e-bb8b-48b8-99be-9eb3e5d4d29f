// A simple test file to verify the subscription functionality

describe('Subscription Functionality', () => {
  it('should handle both nested and flat subscription structures', () => {
    // Test with nested structure
    const nestedSubscription = {
      subscription: {
        plan_id: 'professional',
        status: 'active',
        stripe_subscription_id: 'sub_123'
      }
    };
    
    // Test with flat structure
    const flatSubscription = {
      plan_id: 'professional',
      status: 'active',
      stripe_subscription_id: 'sub_123'
    };
    
    // Function that should work with both structures
    function getPlanId(subscription) {
      return subscription?.subscription?.plan_id || subscription?.plan_id;
    }
    
    function getSubscriptionId(subscription) {
      return subscription?.subscription?.stripe_subscription_id || subscription?.stripe_subscription_id;
    }
    
    // Test the functions
    expect(getPlanId(nestedSubscription)).toBe('professional');
    expect(getPlanId(flatSubscription)).toBe('professional');
    
    expect(getSubscriptionId(nestedSubscription)).toBe('sub_123');
    expect(getSubscriptionId(flatSubscription)).toBe('sub_123');
  });
});
