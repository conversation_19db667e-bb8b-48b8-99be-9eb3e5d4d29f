const { createAuthenticatedApiClient } = require('../utils/auth');
const { createTestUser, cleanupTestData } = require('../utils/db');

const API_BASE_URL = 'http://localhost:3000';
const TEST_USER_ID = 'test_user_123';

describe('Drafts API', () => {
  let apiClient: ReturnType<typeof createAuthenticatedApiClient>;
  let testDraftId: number;

  beforeAll(async () => {
    // Create a test user in the database
    await createTestUser(TEST_USER_ID);

    // Get a test auth token
    const authToken = process.env.TEST_AUTH_TOKEN || 'test-token';

    // Create an authenticated API client
    apiClient = createAuthenticatedApiClient(API_BASE_URL, authToken);
  });

  afterAll(async () => {
    // Clean up test data
    await cleanupTestData();
  });

  it('should create a new draft', async () => {
    const draftData = {
      title: 'Test Draft API',
      description: 'Created via API test',
      room_type: 'kitchen',
      content: JSON.stringify({
        step: 1,
        formData: {
          title: 'Test Draft API',
          description: 'Created via API test',
          room_type: 'kitchen'
        }
      })
    };

    const response = await apiClient.post('/api/drafts', draftData);

    expect(response).toBeDefined();
    expect(response.id).toBeDefined();
    expect(response.title).toBe(draftData.title);
    expect(response.description).toBe(draftData.description);
    expect(response.user_id).toBe(TEST_USER_ID);

    // Save the draft ID for later tests
    testDraftId = response.id;
  });

  it('should get all drafts for user', async () => {
    const response = await apiClient.get('/api/drafts');

    expect(Array.isArray(response)).toBe(true);

    // Check if our test draft is in the list
    const testDraft = response.find((d: any) => d.title === 'Test Draft API');
    expect(testDraft).toBeDefined();
  });

  it('should get a single draft by ID', async () => {
    const response = await apiClient.get(`/api/drafts/${testDraftId}`);

    expect(response).toBeDefined();
    expect(response.id).toBe(testDraftId);
    expect(response.title).toBe('Test Draft API');
    expect(response.description).toBe('Created via API test');
  });

  it('should update a draft', async () => {
    const updateData = {
      title: 'Updated Test Draft',
      description: 'Updated via API test',
      content: JSON.stringify({
        step: 2,
        formData: {
          title: 'Updated Test Draft',
          description: 'Updated via API test',
          room_type: 'kitchen'
        }
      })
    };

    const response = await apiClient.put(`/api/drafts/${testDraftId}`, updateData);

    expect(response).toBeDefined();
    expect(response.id).toBe(testDraftId);
    expect(response.title).toBe(updateData.title);
    expect(response.description).toBe(updateData.description);
  });

  it('should delete a draft', async () => {
    const response = await apiClient.delete(`/api/drafts/${testDraftId}`);

    // Check that the draft is gone
    try {
      await apiClient.get(`/api/drafts/${testDraftId}`);
      fail('Draft should have been deleted');
    } catch (error) {
      expect(error).toBeDefined();
    }
  });
});
