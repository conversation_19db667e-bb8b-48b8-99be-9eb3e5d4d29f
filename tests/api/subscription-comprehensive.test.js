/**
 * Comprehensive API tests for subscription functionality
 *
 * This test suite covers all aspects of subscription management:
 * - Creating subscriptions
 * - Retrieving subscription details
 * - Canceling subscriptions
 * - Reactivating subscriptions
 * - Changing subscription plans
 * - Handling subscription limits
 * - Testing Stripe webhook integration
 */

const { createAuthenticatedApiClient } = require('../utils/auth');
const { createTestUser, cleanupTestData, createTestDbClient } = require('../utils/db');
const { createStripeMocks, mockStripeSubscription } = require('../utils/stripe');

// Constants
const API_BASE_URL = 'http://localhost:3000';
const TEST_USER_ID = 'test_user_subscription_123';
const TEST_EMAIL = '<EMAIL>';
const TEST_NAME = 'Test Subscription User';

describe('Subscription API - Comprehensive Tests', () => {
  let apiClient;
  let supabase;
  let stripeMocks;

  beforeAll(async () => {
    // Create a test user in the database
    await createTestUser(TEST_USER_ID, TEST_EMAIL, TEST_NAME);

    // Get a test auth token
    const authToken = process.env.TEST_AUTH_TOKEN || 'test-token';

    // Create an authenticated API client
    apiClient = createAuthenticatedApiClient(API_BASE_URL, authToken);

    // Create a Supabase client for direct DB operations
    supabase = createTestDbClient();

    // Set up Stripe mocks
    stripeMocks = createStripeMocks();
  });

  afterAll(async () => {
    // Clean up test data
    await cleanupTestData();

    // Clean up test subscriptions
    await supabase
      .from('subscriptions')
      .delete()
      .eq('user_id', TEST_USER_ID);
  });

  // Helper function to create a test subscription
  async function createTestSubscription(planId = 'professional', billingCycle = 'annual') {
    const { data, error } = await supabase
      .from('subscriptions')
      .upsert({
        user_id: TEST_USER_ID,
        plan_id: planId,
        billing_cycle: billingCycle,
        status: 'active',
        stripe_customer_id: 'cus_test123',
        stripe_subscription_id: 'sub_test123',
        current_period_start: new Date().toISOString(),
        current_period_end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        cancel_at_period_end: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create test subscription: ${error.message}`);
    }

    return data;
  }

  // Helper function to clean up test subscriptions
  async function cleanupTestSubscriptions() {
    await supabase
      .from('subscriptions')
      .delete()
      .eq('user_id', TEST_USER_ID);
  }

  beforeEach(async () => {
    // Clean up any existing test subscriptions before each test
    await cleanupTestSubscriptions();
  });

  describe('Subscription Retrieval', () => {
    it('should return free tier when no subscription exists', async () => {
      const response = await apiClient.get('/api/subscription');

      expect(response).toBeDefined();
      expect(response.subscription).toBeNull();
      expect(response.limits).toBeDefined();
      expect(response.limits.projects).toBe(1);
      expect(response.limits.imagesPerProject).toBe(1);
    });

    it('should return subscription details when subscription exists', async () => {
      // Create a test subscription
      const testSubscription = await createTestSubscription('professional', 'annual');

      // Get subscription details
      const response = await apiClient.get('/api/subscription');

      expect(response).toBeDefined();
      expect(response.subscription).toBeDefined();
      expect(response.subscription.subscription).toBeDefined();
      expect(response.subscription.subscription.plan_id).toBe('professional');
      expect(response.subscription.subscription.billing_cycle).toBe('annual');
      expect(response.subscription.subscription.status).toBe('active');
      expect(response.limits).toBeDefined();
      expect(response.limits.projects).toBe(10); // Professional plan limit
    });

    it('should handle both nested and flat subscription structures', async () => {
      // Create a test subscription
      const testSubscription = await createTestSubscription();

      // Test the debug endpoint to get raw subscription data
      const debugResponse = await apiClient.get('/api/debug/subscription');

      expect(debugResponse).toBeDefined();
      expect(debugResponse.rawSubscription).toBeDefined();
      expect(debugResponse.rawSubscription.plan_id).toBe('professional');

      // Get the regular subscription endpoint
      const response = await apiClient.get('/api/subscription');

      expect(response).toBeDefined();
      expect(response.subscription).toBeDefined();

      // Check that we can access the subscription data in both formats
      // Nested format (subscription.subscription.plan_id)
      expect(response.subscription.subscription).toBeDefined();
      expect(response.subscription.subscription.plan_id).toBe('professional');

      // The client should be able to handle both formats
      const planId = response.subscription.subscription?.plan_id || response.subscription.plan_id;
      expect(planId).toBe('professional');
    });
  });

  describe('Subscription Management', () => {
    it('should cancel a subscription', async () => {
      // Create a test subscription
      const testSubscription = await createTestSubscription();

      // Mock Stripe API response for cancellation
      stripeMocks.mockSubscriptionUpdate({
        id: testSubscription.stripe_subscription_id,
        cancel_at_period_end: true
      });

      // Cancel the subscription
      const response = await apiClient.post('/api/subscription/cancel', {
        subscriptionId: testSubscription.stripe_subscription_id
      });

      expect(response).toBeDefined();
      expect(response.success).toBe(true);

      // Verify the subscription was updated in the database
      const { data: updatedSubscription } = await supabase
        .from('subscriptions')
        .select('*')
        .eq('user_id', TEST_USER_ID)
        .single();

      expect(updatedSubscription).toBeDefined();
      expect(updatedSubscription.cancel_at_period_end).toBe(true);
    });

    it('should reactivate a cancelled subscription', async () => {
      // Create a test subscription that's cancelled
      const testSubscription = await createTestSubscription();
      await supabase
        .from('subscriptions')
        .update({ cancel_at_period_end: true })
        .eq('id', testSubscription.id);

      // Mock Stripe API response for reactivation
      stripeMocks.mockSubscriptionUpdate({
        id: testSubscription.stripe_subscription_id,
        cancel_at_period_end: false
      });

      // Reactivate the subscription
      const response = await apiClient.post('/api/subscription/reactivate', {
        subscriptionId: testSubscription.stripe_subscription_id
      });

      expect(response).toBeDefined();
      expect(response.success).toBe(true);

      // Verify the subscription was updated in the database
      const { data: updatedSubscription } = await supabase
        .from('subscriptions')
        .select('*')
        .eq('user_id', TEST_USER_ID)
        .single();

      expect(updatedSubscription).toBeDefined();
      expect(updatedSubscription.cancel_at_period_end).toBe(false);
    });

    it('should create a checkout session for a new subscription', async () => {
      // Mock Stripe API response for checkout session creation
      const mockSessionId = 'cs_test_' + Math.random().toString(36).substring(2, 15);
      const mockSessionUrl = 'https://checkout.stripe.com/c/pay/' + mockSessionId;

      stripeMocks.mockCheckoutSessionCreate({
        id: mockSessionId,
        url: mockSessionUrl,
        customer: 'cus_test123',
        subscription: 'sub_test123',
        metadata: {
          userId: TEST_USER_ID,
          planId: 'professional',
          billingCycle: 'annual'
        }
      });

      // Create a checkout session
      const response = await apiClient.post('/api/create-checkout-session', {
        planId: 'professional',
        billingCycle: 'annual',
        requestId: 'test-request-id'
      });

      expect(response).toBeDefined();
      expect(response.url).toBe(mockSessionUrl);
    });

    it('should handle manual subscription creation', async () => {
      // Mock Stripe API responses
      const mockSessionId = 'cs_test_' + Math.random().toString(36).substring(2, 15);

      stripeMocks.mockCheckoutSessionRetrieve({
        id: mockSessionId,
        customer: 'cus_test123',
        subscription: 'sub_test123',
        metadata: {
          userId: TEST_USER_ID,
          planId: 'professional',
          billingCycle: 'annual'
        }
      });

      stripeMocks.mockSubscriptionRetrieve({
        id: 'sub_test123',
        customer: 'cus_test123',
        status: 'active',
        current_period_start: Math.floor(Date.now() / 1000),
        current_period_end: Math.floor(Date.now() / 1000) + 30 * 24 * 60 * 60,
        cancel_at_period_end: false
      });

      // Create a subscription manually
      const response = await apiClient.post('/api/manual-subscription-create', {
        sessionId: mockSessionId
      });

      expect(response).toBeDefined();
      expect(response.success).toBe(true);
      expect(response.message).toContain('Subscription created successfully');

      // Verify the subscription was created in the database
      const { data: createdSubscription } = await supabase
        .from('subscriptions')
        .select('*')
        .eq('user_id', TEST_USER_ID)
        .single();

      expect(createdSubscription).toBeDefined();
      expect(createdSubscription.plan_id).toBe('professional');
      expect(createdSubscription.billing_cycle).toBe('annual');
      expect(createdSubscription.stripe_subscription_id).toBe('sub_test123');
    });

    it('should clear subscription cache', async () => {
      // Create a test subscription
      await createTestSubscription();

      // Clear the cache
      const response = await apiClient.post('/api/clear-cache', {
        type: 'all'
      });

      expect(response).toBeDefined();
      expect(response.success).toBe(true);
      expect(response.message).toContain('Cache cleared');
    });
  });

  describe('Subscription Plan Changes', () => {
    it('should handle upgrading from starter to professional plan', async () => {
      // Create a starter subscription
      const starterSubscription = await createTestSubscription('starter', 'monthly');

      // Mock Stripe API responses for plan change
      stripeMocks.mockSubscriptionRetrieve({
        id: starterSubscription.stripe_subscription_id,
        customer: starterSubscription.stripe_customer_id,
        status: 'active',
        items: {
          data: [{
            id: 'si_test123',
            price: { id: 'price_starter_monthly' }
          }]
        }
      });

      stripeMocks.mockSubscriptionUpdate({
        id: starterSubscription.stripe_subscription_id,
        items: [{
          id: 'si_test123',
          price: 'price_professional_monthly'
        }]
      });

      // Upgrade the subscription
      const response = await apiClient.post('/api/subscription/change-plan', {
        subscriptionId: starterSubscription.stripe_subscription_id,
        newPlanId: 'professional',
        billingCycle: 'monthly'
      });

      expect(response).toBeDefined();
      expect(response.success).toBe(true);

      // Verify the subscription was updated in the database
      const { data: updatedSubscription } = await supabase
        .from('subscriptions')
        .select('*')
        .eq('user_id', TEST_USER_ID)
        .single();

      expect(updatedSubscription).toBeDefined();
      expect(updatedSubscription.plan_id).toBe('professional');
    });

    it('should handle changing billing cycle from monthly to annual', async () => {
      // Create a monthly subscription
      const monthlySubscription = await createTestSubscription('professional', 'monthly');

      // Mock Stripe API responses for billing cycle change
      stripeMocks.mockSubscriptionRetrieve({
        id: monthlySubscription.stripe_subscription_id,
        customer: monthlySubscription.stripe_customer_id,
        status: 'active',
        items: {
          data: [{
            id: 'si_test123',
            price: { id: 'price_professional_monthly' }
          }]
        }
      });

      stripeMocks.mockSubscriptionUpdate({
        id: monthlySubscription.stripe_subscription_id,
        items: [{
          id: 'si_test123',
          price: 'price_professional_annual'
        }]
      });

      // Change the billing cycle
      const response = await apiClient.post('/api/subscription/change-billing-cycle', {
        subscriptionId: monthlySubscription.stripe_subscription_id,
        newBillingCycle: 'annual'
      });

      expect(response).toBeDefined();
      expect(response.success).toBe(true);

      // Verify the subscription was updated in the database
      const { data: updatedSubscription } = await supabase
        .from('subscriptions')
        .select('*')
        .eq('user_id', TEST_USER_ID)
        .single();

      expect(updatedSubscription).toBeDefined();
      expect(updatedSubscription.billing_cycle).toBe('annual');
    });
  });

  describe('Subscription Limits', () => {
    it('should enforce project limits based on subscription plan', async () => {
      // Create a starter subscription
      await createTestSubscription('starter', 'monthly');

      // Create projects up to the limit
      for (let i = 0; i < 3; i++) {
        await supabase
          .from('projects')
          .insert({
            user_id: TEST_USER_ID,
            name: `Test Project ${i}`,
            description: 'Test project for subscription limits',
            status: 'draft'
          });
      }

      // Update usage to reflect the created projects
      await supabase
        .from('usage')
        .upsert({
          user_id: TEST_USER_ID,
          projects_count: 3,
          images_count: 0,
          period_start: new Date().toISOString(),
          period_end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
        });

      // Try to create one more project (should fail)
      const response = await apiClient.post('/api/projects', {
        name: 'Limit Exceeding Project',
        description: 'This project should exceed the limit'
      });

      expect(response).toBeDefined();
      expect(response.error).toBeDefined();
      expect(response.code).toBe('project_limit_reached');
    });

    it('should enforce image limits based on subscription plan', async () => {
      // Create a starter subscription
      await createTestSubscription('starter', 'monthly');

      // Create a test project
      const { data: project } = await supabase
        .from('projects')
        .insert({
          user_id: TEST_USER_ID,
          name: 'Test Project for Image Limits',
          description: 'Test project for image limit testing',
          status: 'draft'
        })
        .select()
        .single();

      // Create images up to the limit
      for (let i = 0; i < 3; i++) {
        await supabase
          .from('project_images')
          .insert({
            project_id: project.id,
            user_id: TEST_USER_ID,
            image_url: `https://example.com/image${i}.jpg`,
            type: 'generated'
          });
      }

      // Try to create one more image (should fail)
      const response = await apiClient.post(`/api/projects/${project.id}/images`, {
        image_url: 'https://example.com/limit-exceeding-image.jpg',
        type: 'generated'
      });

      expect(response).toBeDefined();
      expect(response.error).toBeDefined();
      expect(response.code).toBe('image_limit_reached');
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid subscription IDs', async () => {
      const response = await apiClient.post('/api/subscription/cancel', {
        subscriptionId: 'invalid_subscription_id'
      });

      expect(response).toBeDefined();
      expect(response.error || response.message).toBeDefined();
    });

    it('should handle Stripe API errors gracefully', async () => {
      // Create a test subscription
      const testSubscription = await createTestSubscription();

      // Mock Stripe API error
      stripeMocks.mockSubscriptionUpdateError({
        id: testSubscription.stripe_subscription_id,
        error: {
          type: 'invalid_request_error',
          message: 'Subscription not found'
        }
      });

      // Try to cancel the subscription
      const response = await apiClient.post('/api/subscription/cancel', {
        subscriptionId: testSubscription.stripe_subscription_id
      });

      expect(response).toBeDefined();
      expect(response.error || response.message).toBeDefined();
    });

    it('should handle database errors gracefully', async () => {
      // Create a test subscription
      const testSubscription = await createTestSubscription();

      // Corrupt the subscription ID to cause a database error
      await supabase
        .from('subscriptions')
        .update({ stripe_subscription_id: null })
        .eq('id', testSubscription.id);

      // Try to cancel the subscription
      const response = await apiClient.post('/api/subscription/cancel', {
        subscriptionId: testSubscription.stripe_subscription_id
      });

      expect(response).toBeDefined();
      expect(response.error || response.message).toBeDefined();
    });

    it('should handle network timeouts gracefully', async () => {
      // Create a test subscription
      const testSubscription = await createTestSubscription();

      // Mock a network timeout by delaying the response beyond the timeout
      stripeMocks.mockSubscriptionUpdateWithDelay({
        id: testSubscription.stripe_subscription_id,
        delay: 10000 // 10 seconds, which should exceed the timeout
      });

      // Try to cancel the subscription
      const response = await apiClient.post('/api/subscription/cancel', {
        subscriptionId: testSubscription.stripe_subscription_id
      });

      expect(response).toBeDefined();
      expect(response.error || response.message).toBeDefined();
    });

    it('should handle rate limiting errors from Stripe', async () => {
      // Create a test subscription
      const testSubscription = await createTestSubscription();

      // Mock a rate limit error
      stripeMocks.mockSubscriptionUpdateError({
        id: testSubscription.stripe_subscription_id,
        error: {
          type: 'rate_limit_error',
          message: 'Too many requests in a short period of time'
        },
        statusCode: 429
      });

      // Try to cancel the subscription
      const response = await apiClient.post('/api/subscription/cancel', {
        subscriptionId: testSubscription.stripe_subscription_id
      });

      expect(response).toBeDefined();
      expect(response.error || response.message).toBeDefined();
      expect(response.error || response.message).toContain('rate limit');
    });

    it('should handle authentication errors', async () => {
      // Create an unauthenticated API client
      const unauthenticatedClient = createAuthenticatedApiClient(API_BASE_URL, 'invalid_token');

      // Try to get subscription details
      const response = await unauthenticatedClient.get('/api/subscription');

      expect(response).toBeDefined();
      expect(response.error || response.message).toBeDefined();
      expect(response.status || response.statusCode).toBe(401);
    });

    it('should handle concurrent subscription operations', async () => {
      // Create a test subscription
      const testSubscription = await createTestSubscription();

      // Make multiple concurrent requests to update the subscription
      const promises = [];
      for (let i = 0; i < 5; i++) {
        promises.push(
          apiClient.post('/api/subscription/cancel', {
            subscriptionId: testSubscription.stripe_subscription_id
          })
        );
      }

      // Wait for all requests to complete
      const results = await Promise.all(promises);

      // At least one request should succeed
      const successfulRequests = results.filter(r => r.success);
      expect(successfulRequests.length).toBeGreaterThan(0);

      // Verify the subscription was updated in the database
      const { data: updatedSubscription } = await supabase
        .from('subscriptions')
        .select('*')
        .eq('user_id', TEST_USER_ID)
        .single();

      expect(updatedSubscription).toBeDefined();
      expect(updatedSubscription.cancel_at_period_end).toBe(true);
    });

    it('should handle malformed request data', async () => {
      // Try to create a checkout session with invalid data
      const response = await apiClient.post('/api/create-checkout-session', {
        planId: null,
        billingCycle: 'invalid_cycle',
        requestId: 123 // Should be a string
      });

      expect(response).toBeDefined();
      expect(response.error || response.message).toBeDefined();
    });

    it('should handle server errors during subscription operations', async () => {
      // Create a test subscription
      const testSubscription = await createTestSubscription();

      // Mock a server error
      stripeMocks.mockSubscriptionUpdateError({
        id: testSubscription.stripe_subscription_id,
        error: {
          type: 'server_error',
          message: 'Internal server error'
        },
        statusCode: 500
      });

      // Try to cancel the subscription
      const response = await apiClient.post('/api/subscription/cancel', {
        subscriptionId: testSubscription.stripe_subscription_id
      });

      expect(response).toBeDefined();
      expect(response.error || response.message).toBeDefined();
      expect(response.error || response.message).toContain('server error');
    });
  });
});
