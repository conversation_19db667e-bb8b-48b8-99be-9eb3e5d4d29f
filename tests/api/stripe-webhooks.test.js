/**
 * Tests for Stripe webhook handling
 * 
 * This test suite covers all aspects of Stripe webhook handling:
 * - checkout.session.completed
 * - customer.subscription.created
 * - customer.subscription.updated
 * - customer.subscription.deleted
 * - invoice.payment_succeeded
 * - invoice.payment_failed
 */

const request = require('supertest');
const { createTestDbClient } = require('../utils/db');
const { mockStripeWebhookEvent, mockStripeCheckoutSession, mockStripeSubscription } = require('../utils/stripe');

// Constants
const API_BASE_URL = 'http://localhost:3000';
const TEST_USER_ID = 'test_user_webhook_123';
const TEST_CUSTOMER_ID = 'cus_test_webhook_123';
const TEST_SUBSCRIPTION_ID = 'sub_test_webhook_123';

describe('Stripe Webhook Handling', () => {
  let supabase;

  beforeAll(async () => {
    // Create a Supabase client for direct DB operations
    supabase = createTestDbClient();
  });

  beforeEach(async () => {
    // Clean up test data before each test
    await supabase
      .from('subscriptions')
      .delete()
      .eq('user_id', TEST_USER_ID);

    await supabase
      .from('processed_webhook_events')
      .delete()
      .eq('event_id', /^evt_test_/);
  });

  afterAll(async () => {
    // Clean up test data
    await supabase
      .from('subscriptions')
      .delete()
      .eq('user_id', TEST_USER_ID);

    await supabase
      .from('processed_webhook_events')
      .delete()
      .eq('event_id', /^evt_test_/);
  });

  describe('checkout.session.completed', () => {
    it('should create a new subscription when checkout session is completed', async () => {
      // Create a mock checkout session
      const session = mockStripeCheckoutSession({
        id: 'cs_test_' + Math.random().toString(36).substring(2, 10),
        customer: TEST_CUSTOMER_ID,
        subscription: TEST_SUBSCRIPTION_ID,
        metadata: {
          userId: TEST_USER_ID,
          planId: 'professional',
          billingCycle: 'annual'
        }
      });

      // Create a mock webhook event
      const event = mockStripeWebhookEvent('checkout.session.completed', session);

      // Send the webhook event
      const response = await request(API_BASE_URL)
        .post('/api/stripe-webhook')
        .set('Content-Type', 'application/json')
        .set('Stripe-Signature', 'test_signature')
        .send(event);

      // Check the response
      expect(response.status).toBe(200);
      expect(response.body.received).toBe(true);

      // Verify that the subscription was created in the database
      const { data: subscription } = await supabase
        .from('subscriptions')
        .select('*')
        .eq('user_id', TEST_USER_ID)
        .single();

      expect(subscription).toBeDefined();
      expect(subscription.plan_id).toBe('professional');
      expect(subscription.billing_cycle).toBe('annual');
      expect(subscription.stripe_subscription_id).toBe(TEST_SUBSCRIPTION_ID);
      expect(subscription.stripe_customer_id).toBe(TEST_CUSTOMER_ID);
      expect(subscription.status).toBe('active');
    });

    it('should handle checkout session without metadata', async () => {
      // Create a mock checkout session without metadata
      const session = mockStripeCheckoutSession({
        id: 'cs_test_' + Math.random().toString(36).substring(2, 10),
        customer: TEST_CUSTOMER_ID,
        subscription: TEST_SUBSCRIPTION_ID,
        metadata: {}
      });

      // Create a mock webhook event
      const event = mockStripeWebhookEvent('checkout.session.completed', session);

      // Send the webhook event
      const response = await request(API_BASE_URL)
        .post('/api/stripe-webhook')
        .set('Content-Type', 'application/json')
        .set('Stripe-Signature', 'test_signature')
        .send(event);

      // Check the response
      expect(response.status).toBe(200);
      expect(response.body.received).toBe(true);

      // Verify that no subscription was created in the database
      const { data: subscriptions } = await supabase
        .from('subscriptions')
        .select('*')
        .eq('stripe_subscription_id', TEST_SUBSCRIPTION_ID);

      expect(subscriptions).toHaveLength(0);
    });
  });

  describe('customer.subscription.created', () => {
    it('should create a new subscription when customer.subscription.created event is received', async () => {
      // Create a mock subscription
      const subscription = mockStripeSubscription({
        id: TEST_SUBSCRIPTION_ID,
        customer: TEST_CUSTOMER_ID,
        status: 'active',
        metadata: {
          userId: TEST_USER_ID,
          planId: 'professional',
          billingCycle: 'annual'
        }
      });

      // Create a mock webhook event
      const event = mockStripeWebhookEvent('customer.subscription.created', subscription);

      // Send the webhook event
      const response = await request(API_BASE_URL)
        .post('/api/stripe-webhook')
        .set('Content-Type', 'application/json')
        .set('Stripe-Signature', 'test_signature')
        .send(event);

      // Check the response
      expect(response.status).toBe(200);
      expect(response.body.received).toBe(true);

      // Verify that the subscription was created in the database
      const { data: dbSubscription } = await supabase
        .from('subscriptions')
        .select('*')
        .eq('stripe_subscription_id', TEST_SUBSCRIPTION_ID)
        .single();

      expect(dbSubscription).toBeDefined();
      expect(dbSubscription.stripe_customer_id).toBe(TEST_CUSTOMER_ID);
      expect(dbSubscription.status).toBe('active');
    });
  });

  describe('customer.subscription.updated', () => {
    it('should update an existing subscription when customer.subscription.updated event is received', async () => {
      // Create a test subscription in the database
      await supabase
        .from('subscriptions')
        .insert({
          user_id: TEST_USER_ID,
          plan_id: 'starter',
          billing_cycle: 'monthly',
          status: 'active',
          stripe_customer_id: TEST_CUSTOMER_ID,
          stripe_subscription_id: TEST_SUBSCRIPTION_ID,
          current_period_start: new Date().toISOString(),
          current_period_end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
          cancel_at_period_end: false
        });

      // Create a mock subscription with updated data
      const subscription = mockStripeSubscription({
        id: TEST_SUBSCRIPTION_ID,
        customer: TEST_CUSTOMER_ID,
        status: 'active',
        cancel_at_period_end: true,
        metadata: {
          userId: TEST_USER_ID,
          planId: 'professional',
          billingCycle: 'annual'
        }
      });

      // Create a mock webhook event
      const event = mockStripeWebhookEvent('customer.subscription.updated', subscription);

      // Send the webhook event
      const response = await request(API_BASE_URL)
        .post('/api/stripe-webhook')
        .set('Content-Type', 'application/json')
        .set('Stripe-Signature', 'test_signature')
        .send(event);

      // Check the response
      expect(response.status).toBe(200);
      expect(response.body.received).toBe(true);

      // Verify that the subscription was updated in the database
      const { data: dbSubscription } = await supabase
        .from('subscriptions')
        .select('*')
        .eq('stripe_subscription_id', TEST_SUBSCRIPTION_ID)
        .single();

      expect(dbSubscription).toBeDefined();
      expect(dbSubscription.cancel_at_period_end).toBe(true);
      expect(dbSubscription.plan_id).toBe('professional');
      expect(dbSubscription.billing_cycle).toBe('annual');
    });
  });

  describe('customer.subscription.deleted', () => {
    it('should mark a subscription as canceled when customer.subscription.deleted event is received', async () => {
      // Create a test subscription in the database
      await supabase
        .from('subscriptions')
        .insert({
          user_id: TEST_USER_ID,
          plan_id: 'professional',
          billing_cycle: 'annual',
          status: 'active',
          stripe_customer_id: TEST_CUSTOMER_ID,
          stripe_subscription_id: TEST_SUBSCRIPTION_ID,
          current_period_start: new Date().toISOString(),
          current_period_end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
          cancel_at_period_end: false
        });

      // Create a mock subscription with deleted status
      const subscription = mockStripeSubscription({
        id: TEST_SUBSCRIPTION_ID,
        customer: TEST_CUSTOMER_ID,
        status: 'canceled',
        metadata: {
          userId: TEST_USER_ID
        }
      });

      // Create a mock webhook event
      const event = mockStripeWebhookEvent('customer.subscription.deleted', subscription);

      // Send the webhook event
      const response = await request(API_BASE_URL)
        .post('/api/stripe-webhook')
        .set('Content-Type', 'application/json')
        .set('Stripe-Signature', 'test_signature')
        .send(event);

      // Check the response
      expect(response.status).toBe(200);
      expect(response.body.received).toBe(true);

      // Verify that the subscription was marked as canceled in the database
      const { data: dbSubscription } = await supabase
        .from('subscriptions')
        .select('*')
        .eq('stripe_subscription_id', TEST_SUBSCRIPTION_ID)
        .single();

      expect(dbSubscription).toBeDefined();
      expect(dbSubscription.status).toBe('canceled');
    });
  });

  describe('invoice.payment_succeeded', () => {
    it('should update subscription period when invoice.payment_succeeded event is received', async () => {
      // Create a test subscription in the database
      const oldPeriodEnd = new Date();
      await supabase
        .from('subscriptions')
        .insert({
          user_id: TEST_USER_ID,
          plan_id: 'professional',
          billing_cycle: 'monthly',
          status: 'active',
          stripe_customer_id: TEST_CUSTOMER_ID,
          stripe_subscription_id: TEST_SUBSCRIPTION_ID,
          current_period_start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
          current_period_end: oldPeriodEnd.toISOString(),
          cancel_at_period_end: false
        });

      // Create a mock invoice
      const newPeriodStart = Math.floor(Date.now() / 1000);
      const newPeriodEnd = Math.floor(Date.now() / 1000) + 30 * 24 * 60 * 60;
      
      const invoice = {
        id: 'in_test_' + Math.random().toString(36).substring(2, 10),
        object: 'invoice',
        customer: TEST_CUSTOMER_ID,
        subscription: TEST_SUBSCRIPTION_ID,
        period_start: newPeriodStart,
        period_end: newPeriodEnd,
        lines: {
          data: [{
            period: {
              start: newPeriodStart,
              end: newPeriodEnd
            }
          }]
        }
      };

      // Create a mock webhook event
      const event = mockStripeWebhookEvent('invoice.payment_succeeded', invoice);

      // Send the webhook event
      const response = await request(API_BASE_URL)
        .post('/api/stripe-webhook')
        .set('Content-Type', 'application/json')
        .set('Stripe-Signature', 'test_signature')
        .send(event);

      // Check the response
      expect(response.status).toBe(200);
      expect(response.body.received).toBe(true);

      // Verify that the subscription period was updated in the database
      const { data: dbSubscription } = await supabase
        .from('subscriptions')
        .select('*')
        .eq('stripe_subscription_id', TEST_SUBSCRIPTION_ID)
        .single();

      expect(dbSubscription).toBeDefined();
      
      // Check that the period was updated
      const dbPeriodEnd = new Date(dbSubscription.current_period_end);
      expect(dbPeriodEnd.getTime()).toBeGreaterThan(oldPeriodEnd.getTime());
    });
  });

  describe('invoice.payment_failed', () => {
    it('should mark a subscription as past_due when invoice.payment_failed event is received', async () => {
      // Create a test subscription in the database
      await supabase
        .from('subscriptions')
        .insert({
          user_id: TEST_USER_ID,
          plan_id: 'professional',
          billing_cycle: 'annual',
          status: 'active',
          stripe_customer_id: TEST_CUSTOMER_ID,
          stripe_subscription_id: TEST_SUBSCRIPTION_ID,
          current_period_start: new Date().toISOString(),
          current_period_end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
          cancel_at_period_end: false
        });

      // Create a mock invoice
      const invoice = {
        id: 'in_test_' + Math.random().toString(36).substring(2, 10),
        object: 'invoice',
        customer: TEST_CUSTOMER_ID,
        subscription: TEST_SUBSCRIPTION_ID,
        attempted: true,
        next_payment_attempt: Math.floor(Date.now() / 1000) + 24 * 60 * 60
      };

      // Create a mock webhook event
      const event = mockStripeWebhookEvent('invoice.payment_failed', invoice);

      // Send the webhook event
      const response = await request(API_BASE_URL)
        .post('/api/stripe-webhook')
        .set('Content-Type', 'application/json')
        .set('Stripe-Signature', 'test_signature')
        .send(event);

      // Check the response
      expect(response.status).toBe(200);
      expect(response.body.received).toBe(true);

      // Verify that the subscription was marked as past_due in the database
      const { data: dbSubscription } = await supabase
        .from('subscriptions')
        .select('*')
        .eq('stripe_subscription_id', TEST_SUBSCRIPTION_ID)
        .single();

      expect(dbSubscription).toBeDefined();
      expect(dbSubscription.status).toBe('past_due');
    });
  });

  describe('Error Handling', () => {
    it('should handle duplicate webhook events gracefully', async () => {
      // Create a mock subscription
      const subscription = mockStripeSubscription({
        id: TEST_SUBSCRIPTION_ID,
        customer: TEST_CUSTOMER_ID,
        status: 'active',
        metadata: {
          userId: TEST_USER_ID,
          planId: 'professional',
          billingCycle: 'annual'
        }
      });

      // Create a mock webhook event with a fixed ID
      const eventId = 'evt_test_duplicate';
      const event = {
        ...mockStripeWebhookEvent('customer.subscription.created', subscription),
        id: eventId
      };

      // Send the webhook event
      const response1 = await request(API_BASE_URL)
        .post('/api/stripe-webhook')
        .set('Content-Type', 'application/json')
        .set('Stripe-Signature', 'test_signature')
        .send(event);

      // Check the response
      expect(response1.status).toBe(200);
      expect(response1.body.received).toBe(true);

      // Send the same webhook event again
      const response2 = await request(API_BASE_URL)
        .post('/api/stripe-webhook')
        .set('Content-Type', 'application/json')
        .set('Stripe-Signature', 'test_signature')
        .send(event);

      // Check the response
      expect(response2.status).toBe(200);
      expect(response2.body.received).toBe(true);

      // Verify that only one subscription was created in the database
      const { data: subscriptions } = await supabase
        .from('subscriptions')
        .select('*')
        .eq('stripe_subscription_id', TEST_SUBSCRIPTION_ID);

      expect(subscriptions).toHaveLength(1);

      // Verify that the event was marked as processed
      const { data: processedEvents } = await supabase
        .from('processed_webhook_events')
        .select('*')
        .eq('event_id', eventId);

      expect(processedEvents).toHaveLength(1);
    });

    it('should handle invalid webhook events gracefully', async () => {
      // Create an invalid webhook event
      const event = {
        id: 'evt_test_invalid',
        type: 'invalid.event.type',
        data: {
          object: {}
        }
      };

      // Send the webhook event
      const response = await request(API_BASE_URL)
        .post('/api/stripe-webhook')
        .set('Content-Type', 'application/json')
        .set('Stripe-Signature', 'test_signature')
        .send(event);

      // Check the response
      expect(response.status).toBe(200);
      expect(response.body.received).toBe(true);
    });
  });
});
