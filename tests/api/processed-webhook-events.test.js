const { storage } = require('../../server/storage');

describe('Processed Webhook Events', () => {
  it('should save a processed webhook event with created_at field', async () => {
    // Mock the db.client.from().insert() method
    const mockInsert = jest.fn().mockResolvedValue({ error: null });
    const mockFrom = jest.fn().mockReturnValue({
      insert: mockInsert
    });
    
    // Mock the db.client object
    const originalClient = storage.db.client;
    storage.db.client = {
      from: mockFrom
    };
    
    // Mock the dbLogger.insert method
    const originalInsert = storage.dbLogger.insert;
    storage.dbLogger.insert = jest.fn().mockImplementation((table, callback) => callback());
    
    try {
      // Call the method
      await storage.saveProcessedWebhookEvent('test_event_id', 'test_event_type');
      
      // Check that the insert method was called with the correct parameters
      expect(mockFrom).toHaveBeenCalledWith('processed_webhook_events');
      expect(mockInsert).toHaveBeenCalled();
      
      // Get the parameters passed to insert
      const insertParams = mockInsert.mock.calls[0][0];
      
      // Check that the parameters include event_id, event_type, processed_at, and created_at
      expect(insertParams).toHaveProperty('event_id', 'test_event_id');
      expect(insertParams).toHaveProperty('event_type', 'test_event_type');
      expect(insertParams).toHaveProperty('processed_at');
      expect(insertParams).toHaveProperty('created_at');
    } finally {
      // Restore the original methods
      storage.db.client = originalClient;
      storage.dbLogger.insert = originalInsert;
    }
  });
});
