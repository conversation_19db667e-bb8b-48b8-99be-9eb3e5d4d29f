/**
 * API tests for coupon functionality
 */
import { createTestCoupon, createMockStripeCoupon } from '../fixtures/coupons';
import { storage } from '../../server/storage';
import { couponService } from '../../server/services/coupon';

// Mock Stripe
jest.mock('stripe', () => {
  return jest.fn().mockImplementation(() => {
    return {
      coupons: {
        list: jest.fn().mockImplementation(async () => {
          return { data: [] };
        }),
        create: jest.fn().mockImplementation(async (params) => {
          return {
            id: `coupon_${Math.random().toString(36).substring(2, 15)}`,
            ...params
          };
        })
      }
    };
  });
});

// Mock storage methods
jest.mock('../../server/storage', () => {
  const originalModule = jest.requireActual('../../server/storage');
  return {
    ...originalModule,
    getCouponByCode: jest.fn(),
    getCouponByStripeId: jest.fn(),
    hasUserRedeemedCoupon: jest.fn().mockResolvedValue(false),
    createCoupon: jest.fn().mockImplementation(async (coupon) => {
      return {
        id: 1,
        ...coupon,
        created_at: new Date(),
        updated_at: new Date()
      };
    })
  };
});

describe('Coupon Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('validateCoupon', () => {
    it('should validate a percentage coupon', async () => {
      // Create a test coupon
      const testCoupon = createTestCoupon('percentage');
      const mockStripeCoupon = createMockStripeCoupon(testCoupon);
      
      // Mock Stripe response
      const stripeMock = require('stripe')();
      stripeMock.coupons.list.mockResolvedValueOnce({
        data: [mockStripeCoupon]
      });

      // Mock storage response
      storage.getCouponByCode = jest.fn().mockResolvedValueOnce(null);
      storage.getCouponByStripeId = jest.fn().mockResolvedValueOnce(null);
      
      // Validate the coupon
      const result = await couponService.validateCoupon(testCoupon.code, 'test_user_123');
      
      // Verify the result
      expect(result).toBeDefined();
      expect(result?.code).toBe(testCoupon.code.toUpperCase());
      expect(result?.type).toBe('percentage');
      expect(result?.amount).toBe(25);
    });

    it('should validate a fixed amount coupon', async () => {
      // Create a test coupon
      const testCoupon = createTestCoupon('fixed_amount');
      const mockStripeCoupon = createMockStripeCoupon(testCoupon);
      
      // Mock Stripe response
      const stripeMock = require('stripe')();
      stripeMock.coupons.list.mockResolvedValueOnce({
        data: [mockStripeCoupon]
      });

      // Mock storage response
      storage.getCouponByCode = jest.fn().mockResolvedValueOnce(null);
      storage.getCouponByStripeId = jest.fn().mockResolvedValueOnce(null);
      
      // Validate the coupon
      const result = await couponService.validateCoupon(testCoupon.code, 'test_user_123');
      
      // Verify the result
      expect(result).toBeDefined();
      expect(result?.code).toBe(testCoupon.code.toUpperCase());
      expect(result?.type).toBe('fixed_amount');
      expect(result?.amount).toBe(20);
    });

    it('should validate a free months coupon', async () => {
      // Create a test coupon
      const testCoupon = createTestCoupon('free_months');
      const mockStripeCoupon = createMockStripeCoupon(testCoupon);
      
      // Mock Stripe response
      const stripeMock = require('stripe')();
      stripeMock.coupons.list.mockResolvedValueOnce({
        data: [mockStripeCoupon]
      });

      // Mock storage response
      storage.getCouponByCode = jest.fn().mockResolvedValueOnce(null);
      storage.getCouponByStripeId = jest.fn().mockResolvedValueOnce(null);
      
      // Validate the coupon
      const result = await couponService.validateCoupon(testCoupon.code, 'test_user_123');
      
      // Verify the result
      expect(result).toBeDefined();
      expect(result?.code).toBe(testCoupon.code.toUpperCase());
      expect(result?.type).toBe('free_months');
      expect(result?.amount).toBe(1);
      expect(result?.duration).toBe('repeating');
      expect(result?.duration_in_months).toBe(1);
    });

    it('should return undefined for an invalid coupon', async () => {
      // Mock Stripe response with no coupons
      const stripeMock = require('stripe')();
      stripeMock.coupons.list.mockResolvedValueOnce({
        data: []
      });

      // Mock storage response
      storage.getCouponByCode = jest.fn().mockResolvedValueOnce(null);
      
      // Validate the coupon
      const result = await couponService.validateCoupon('INVALID', 'test_user_123');
      
      // Verify the result
      expect(result).toBeUndefined();
    });

    it('should return undefined if user already redeemed the coupon', async () => {
      // Create a test coupon
      const testCoupon = createTestCoupon();
      
      // Mock storage response with an existing coupon
      storage.getCouponByCode = jest.fn().mockResolvedValueOnce({
        id: 1,
        ...testCoupon,
        created_at: new Date(),
        updated_at: new Date()
      });
      
      // Mock that user already redeemed this coupon
      storage.hasUserRedeemedCoupon = jest.fn().mockResolvedValueOnce(true);
      
      // Validate the coupon
      const result = await couponService.validateCoupon(testCoupon.code, 'test_user_123');
      
      // Verify the result
      expect(result).toBeUndefined();
    });
  });

  describe('applyCouponToCheckout', () => {
    it('should apply a valid coupon to checkout', async () => {
      // Create a test coupon
      const testCoupon = createTestCoupon();
      
      // Mock validateCoupon to return the test coupon
      jest.spyOn(couponService, 'validateCoupon').mockResolvedValueOnce({
        id: 1,
        ...testCoupon,
        created_at: new Date(),
        updated_at: new Date()
      });
      
      // Apply the coupon to checkout
      const result = await couponService.applyCouponToCheckout(
        testCoupon.code,
        'test_user_123',
        'cs_test_123'
      );
      
      // Verify the result
      expect(result).toBe(true);
    });

    it('should return false for an invalid coupon', async () => {
      // Mock validateCoupon to return undefined
      jest.spyOn(couponService, 'validateCoupon').mockResolvedValueOnce(undefined);
      
      // Apply the coupon to checkout
      const result = await couponService.applyCouponToCheckout(
        'INVALID',
        'test_user_123',
        'cs_test_123'
      );
      
      // Verify the result
      expect(result).toBe(false);
    });
  });
});
