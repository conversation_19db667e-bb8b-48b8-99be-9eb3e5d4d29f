const { createAuthenticatedApiClient } = require('../utils/auth');
const { createTestUser, cleanupTestData } = require('../utils/db');

const API_BASE_URL = 'http://localhost:3000';
const TEST_USER_ID = 'test_user_123';

describe('Projects API', () => {
  let apiClient: ReturnType<typeof createAuthenticatedApiClient>;

  beforeAll(async () => {
    // Create a test user in the database
    await createTestUser(TEST_USER_ID);

    // Get a test auth token
    const authToken = process.env.TEST_AUTH_TOKEN || 'test-token';

    // Create an authenticated API client
    apiClient = createAuthenticatedApiClient(API_BASE_URL, authToken);
  });

  afterAll(async () => {
    // Clean up test data
    await cleanupTestData();
  });

  it('should create a new project', async () => {
    const projectData = {
      title: 'Test Project API',
      description: 'Created via API test',
      room_type: 'kitchen'
    };

    const response = await apiClient.post('/api/projects', projectData);

    expect(response).toBeDefined();
    expect(response.id).toBeDefined();
    expect(response.title).toBe(projectData.title);
    expect(response.description).toBe(projectData.description);
    expect(response.room_type).toBe(projectData.room_type);
    expect(response.user_id).toBe(TEST_USER_ID);
  });

  it('should get all projects for user', async () => {
    const response = await apiClient.get('/api/projects');

    expect(Array.isArray(response)).toBe(true);

    // Check if our test project is in the list
    const testProject = response.find((p: any) => p.title === 'Test Project API');
    expect(testProject).toBeDefined();
  });

  it('should get a single project by ID', async () => {
    // First get all projects to find our test project
    const projects = await apiClient.get('/api/projects');
    const testProject = projects.find((p: any) => p.title === 'Test Project API');

    if (!testProject) {
      throw new Error('Test project not found');
    }

    // Get the project by ID
    const response = await apiClient.get(`/api/projects/${testProject.id}`);

    expect(response).toBeDefined();
    expect(response.id).toBe(testProject.id);
    expect(response.title).toBe(testProject.title);
    expect(response.description).toBe(testProject.description);
  });

  it('should create a custom modification for a project', async () => {
    // First get all projects to find our test project
    const projects = await apiClient.get('/api/projects');
    const testProject = projects.find((p: any) => p.title === 'Test Project API');

    if (!testProject) {
      throw new Error('Test project not found');
    }

    const modificationData = {
      type: 'custom',
      description: 'Test custom modification via API',
      referenceImageIds: []
    };

    const response = await apiClient.post(`/api/projects/${testProject.id}/modifications`, modificationData);

    expect(response).toBeDefined();
    expect(response.id).toBeDefined();
    expect(response.description).toBe(modificationData.description);
    expect(response.project_id).toBe(testProject.id);
  });

  it('should create a structured floor replacement modification', async () => {
    // First get all projects to find our test project
    const projects = await apiClient.get('/api/projects');
    const testProject = projects.find((p: any) => p.title === 'Test Project API');

    if (!testProject) {
      throw new Error('Test project not found');
    }

    const modificationData = {
      type: 'replace_floor',
      description: 'Replace the floor with dark hardwood',
      referenceImageIds: [],
      options: {
        material: 'hardwood',
        color: 'dark',
        customDescription: 'With wide planks',
        useReferenceImage: false
      }
    };

    const response = await apiClient.post(`/api/projects/${testProject.id}/modifications`, modificationData);

    expect(response).toBeDefined();
    expect(response.id).toBeDefined();
    expect(response.description).toBe(modificationData.description);
    expect(response.project_id).toBe(testProject.id);
    expect(response.type).toBe('replace_floor');
    expect(response.options).toBeDefined();
    expect(response.options.material).toBe('hardwood');
  });

  it('should create a floor replacement with reference image', async () => {
    // First get all projects to find our test project
    const projects = await apiClient.get('/api/projects');
    const testProject = projects.find((p: any) => p.title === 'Test Project API');

    if (!testProject) {
      throw new Error('Test project not found');
    }

    // For this test, we'll assume reference image ID 1 exists
    // In a real test, you would first upload a reference image and get its ID
    const referenceImageId = 1;

    const modificationData = {
      type: 'replace_floor',
      description: 'Replace the floor using reference image',
      referenceImageIds: [referenceImageId],
      primaryReferenceImageId: referenceImageId,
      options: {
        useReferenceImage: true,
        referenceImageId: referenceImageId,
        customDescription: 'Make it look modern'
      }
    };

    const response = await apiClient.post(`/api/projects/${testProject.id}/modifications`, modificationData);

    expect(response).toBeDefined();
    expect(response.id).toBeDefined();
    expect(response.description).toBe(modificationData.description);
    expect(response.project_id).toBe(testProject.id);
    expect(response.type).toBe('replace_floor');
    expect(response.options).toBeDefined();
    expect(response.options.useReferenceImage).toBe(true);
    expect(response.primary_reference_image_id).toBe(referenceImageId);
  });
});
