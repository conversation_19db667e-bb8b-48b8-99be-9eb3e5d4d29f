const { createAuthenticatedApiClient } = require('../utils/auth');
const { createTestUser, cleanupTestData } = require('../utils/db');

const API_BASE_URL = 'http://localhost:3000';
const TEST_USER_ID = 'test_user_123';

describe('Reference Library API', () => {
  let apiClient: ReturnType<typeof createAuthenticatedApiClient>;
  let testCategoryId: number;

  beforeAll(async () => {
    // Create a test user in the database
    await createTestUser(TEST_USER_ID);

    // Get a test auth token
    const authToken = process.env.TEST_AUTH_TOKEN || 'test-token';

    // Create an authenticated API client
    apiClient = createAuthenticatedApiClient(API_BASE_URL, authToken);
  });

  afterAll(async () => {
    // Clean up test data
    await cleanupTestData();
  });

  it('should create a new reference category', async () => {
    const categoryData = {
      name: 'Test Category API',
      description: 'Created via API test'
    };

    const response = await apiClient.post('/api/reference-categories', categoryData);

    expect(response).toBeDefined();
    expect(response.id).toBeDefined();
    expect(response.name).toBe(categoryData.name);
    expect(response.description).toBe(categoryData.description);
    expect(response.user_id).toBe(TEST_USER_ID);

    // Save the category ID for later tests
    testCategoryId = response.id;
  });

  it('should get all reference categories for user', async () => {
    const response = await apiClient.get('/api/reference-categories');

    expect(Array.isArray(response)).toBe(true);

    // Check if our test category is in the list
    const testCategory = response.find((c: any) => c.name === 'Test Category API');
    expect(testCategory).toBeDefined();
  });

  it('should get a single reference category by ID', async () => {
    const response = await apiClient.get(`/api/reference-categories/${testCategoryId}`);

    expect(response).toBeDefined();
    expect(response.id).toBe(testCategoryId);
    expect(response.name).toBe('Test Category API');
    expect(response.description).toBe('Created via API test');
  });

  it('should update a reference category', async () => {
    const updateData = {
      name: 'Updated Test Category',
      description: 'Updated via API test'
    };

    const response = await apiClient.put(`/api/reference-categories/${testCategoryId}`, updateData);

    expect(response).toBeDefined();
    expect(response.id).toBe(testCategoryId);
    expect(response.name).toBe(updateData.name);
    expect(response.description).toBe(updateData.description);
  });

  // Note: Testing reference item creation would require file upload,
  // which is more complex in API tests. This would be better tested in E2E tests.
});
