const { createAuthenticatedApiClient } = require('../utils/auth');
const { createTestUser, cleanupTestData, createTestDbClient } = require('../utils/db');

// Define TypeScript types
type ApiClient = ReturnType<typeof createAuthenticatedApiClient>;

const API_BASE_URL = 'http://localhost:3000';
const TEST_USER_ID = 'test_user_123';

describe('Subscription API', () => {
  let apiClient;
  let supabase;

  beforeAll(async () => {
    // Create a test user in the database
    await createTestUser(TEST_USER_ID);

    // Get a test auth token
    const authToken = process.env.TEST_AUTH_TOKEN || 'test-token';

    // Create an authenticated API client
    apiClient = createAuthenticatedApiClient(API_BASE_URL, authToken);

    // Create a Supabase client for direct DB operations
    supabase = createTestDbClient();
  });

  afterAll(async () => {
    // Clean up test data
    await cleanupTestData();

    // Clean up test subscriptions
    await supabase
      .from('subscriptions')
      .delete()
      .eq('user_id', TEST_USER_ID);
  });

  // Helper function to create a test subscription
  async function createTestSubscription(planId = 'professional', billingCycle = 'annual') {
    const { data, error } = await supabase
      .from('subscriptions')
      .upsert({
        user_id: TEST_USER_ID,
        plan_id: planId,
        billing_cycle: billingCycle,
        status: 'active',
        stripe_customer_id: 'cus_test123',
        stripe_subscription_id: 'sub_test123',
        current_period_start: new Date().toISOString(),
        current_period_end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        cancel_at_period_end: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create test subscription: ${error.message}`);
    }

    return data;
  }

  // Helper function to clean up test subscriptions
  async function cleanupTestSubscriptions() {
    await supabase
      .from('subscriptions')
      .delete()
      .eq('user_id', TEST_USER_ID);
  }

  beforeEach(async () => {
    // Clean up any existing test subscriptions before each test
    await cleanupTestSubscriptions();
  });

  it('should return free tier when no subscription exists', async () => {
    const response = await apiClient.get('/api/subscription');

    expect(response).toBeDefined();
    expect(response.subscription).toBeNull();
    expect(response.limits).toBeDefined();
    expect(response.limits.projects).toBe(1);
    expect(response.limits.imagesPerProject).toBe(1);
  });

  it('should return subscription details when subscription exists', async () => {
    // Create a test subscription
    const testSubscription = await createTestSubscription('professional', 'annual');

    // Get subscription details
    const response = await apiClient.get('/api/subscription');

    expect(response).toBeDefined();
    expect(response.subscription).toBeDefined();
    expect(response.subscription.subscription).toBeDefined();
    expect(response.subscription.subscription.plan_id).toBe('professional');
    expect(response.subscription.subscription.billing_cycle).toBe('annual');
    expect(response.subscription.subscription.status).toBe('active');
    expect(response.limits).toBeDefined();
    expect(response.limits.projects).toBe(10); // Professional plan limit
  });

  it('should cancel a subscription', async () => {
    // Create a test subscription
    const testSubscription = await createTestSubscription();

    // Cancel the subscription
    const response = await apiClient.post('/api/subscription/cancel', {
      subscriptionId: testSubscription.stripe_subscription_id
    });

    expect(response).toBeDefined();
    expect(response.success).toBe(true);

    // Verify the subscription was updated in the database
    const { data: updatedSubscription } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', TEST_USER_ID)
      .single();

    expect(updatedSubscription).toBeDefined();
    expect(updatedSubscription.cancel_at_period_end).toBe(true);
  });

  it('should reactivate a cancelled subscription', async () => {
    // Create a test subscription that's cancelled
    const testSubscription = await createTestSubscription();
    await supabase
      .from('subscriptions')
      .update({ cancel_at_period_end: true })
      .eq('id', testSubscription.id);

    // Reactivate the subscription
    const response = await apiClient.post('/api/subscription/reactivate', {
      subscriptionId: testSubscription.stripe_subscription_id
    });

    expect(response).toBeDefined();
    expect(response.success).toBe(true);

    // Verify the subscription was updated in the database
    const { data: updatedSubscription } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', TEST_USER_ID)
      .single();

    expect(updatedSubscription).toBeDefined();
    expect(updatedSubscription.cancel_at_period_end).toBe(false);
  });

  it('should handle both nested and flat subscription structures', async () => {
    // Create a test subscription
    const testSubscription = await createTestSubscription();

    // Test the debug endpoint to get raw subscription data
    const debugResponse = await apiClient.get('/api/debug/subscription');

    expect(debugResponse).toBeDefined();
    expect(debugResponse.rawSubscription).toBeDefined();
    expect(debugResponse.rawSubscription.plan_id).toBe('professional');

    // Get the regular subscription endpoint
    const response = await apiClient.get('/api/subscription');

    expect(response).toBeDefined();
    expect(response.subscription).toBeDefined();

    // Check that we can access the subscription data in both formats
    // Nested format (subscription.subscription.plan_id)
    expect(response.subscription.subscription).toBeDefined();
    expect(response.subscription.subscription.plan_id).toBe('professional');

    // The client should be able to handle both formats
    const planId = response.subscription.subscription?.plan_id || response.subscription.plan_id;
    expect(planId).toBe('professional');
  });

  it('should clear subscription cache', async () => {
    // Create a test subscription
    await createTestSubscription();

    // Clear the cache
    const response = await apiClient.post('/api/clear-cache', {
      type: 'all'
    });

    expect(response).toBeDefined();
    expect(response.success).toBe(true);
    expect(response.message).toContain('Cache cleared');
  });
});
