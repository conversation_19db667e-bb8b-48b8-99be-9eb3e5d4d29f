import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import { createCanvas } from 'canvas';

// Use a different approach for __dirname in ESM
const __dirname = dirname(fileURLToPath(new URL(import.meta.url)));

/**
 * Create test images for automated testing
 */
async function setupTestFixtures() {
  console.log('Setting up test fixtures...');

  const fixturesDir = path.join(__dirname, 'fixtures');

  // Ensure fixtures directory exists
  if (!fs.existsSync(fixturesDir)) {
    fs.mkdirSync(fixturesDir, { recursive: true });
  }

  // Create test room image
  createTestImage(
    path.join(fixturesDir, 'test-room.jpg'),
    1024, 768,
    'Room',
    '#f5f5f5', // Light gray walls
    '#8b4513'  // Brown floor
  );

  // Create reference image
  createTestImage(
    path.join(fixturesDir, 'reference-image.jpg'),
    1024, 768,
    'Reference',
    '#e6f7ff', // Light blue walls
    '#8b4513'  // Brown floor
  );

  // Create test image
  createTestImage(
    path.join(fixturesDir, 'test-image.jpg'),
    1024, 768,
    'Test',
    '#ffffff', // White walls
    '#8b4513'  // Brown floor
  );

  console.log('Test fixtures created successfully!');
}

/**
 * Create a test image with the specified parameters
 */
function createTestImage(filePath, width, height, label, wallColor, floorColor) {
  console.log(`Creating test image: ${path.basename(filePath)}`);

  // Create canvas
  const canvas = createCanvas(width, height);
  const ctx = canvas.getContext('2d');

  // Draw walls (top 2/3)
  ctx.fillStyle = wallColor;
  ctx.fillRect(0, 0, width, height * 2/3);

  // Draw floor (bottom 1/3)
  ctx.fillStyle = floorColor;
  ctx.fillRect(0, height * 2/3, width, height * 1/3);

  // Draw some furniture
  drawFurniture(ctx, width, height);

  // Add label
  ctx.fillStyle = '#000000';
  ctx.font = 'bold 40px Arial';
  ctx.textAlign = 'center';
  ctx.fillText(`${label} Image`, width / 2, 50);

  // Save to file
  const buffer = canvas.toBuffer('image/jpeg');
  fs.writeFileSync(filePath, buffer);

  console.log(`Created ${filePath}`);
}

/**
 * Draw some simple furniture on the canvas
 */
function drawFurniture(ctx, width, height) {
  const floorY = height * 2/3;

  // Draw a table
  ctx.fillStyle = '#8b4513';
  ctx.fillRect(width / 2 - 100, floorY - 50, 200, 20);
  ctx.fillRect(width / 2 - 90, floorY - 50, 10, 50);
  ctx.fillRect(width / 2 + 80, floorY - 50, 10, 50);

  // Draw a sofa
  ctx.fillStyle = '#666666';
  ctx.fillRect(100, floorY - 80, 300, 80);
  ctx.fillStyle = '#444444';
  ctx.fillRect(100, floorY - 120, 300, 40);

  // Draw a lamp
  ctx.fillStyle = '#8b4513';
  ctx.fillRect(width - 150, floorY - 100, 20, 100);
  ctx.fillStyle = '#ffff99';
  ctx.beginPath();
  ctx.arc(width - 140, floorY - 120, 40, 0, Math.PI * 2);
  ctx.fill();
}

// Run the setup
setupTestFixtures().catch(error => {
  console.error('Error setting up test fixtures:', error);
  process.exit(1);
});
