# Testing Framework TODO List

This document outlines the remaining work needed to complete the testing framework for Renovision.Studio.

## API Tests

The API tests have been updated to support ESM modules. The following tasks have been completed:

1. **Fix Module Format Issues**:
   - [x] Update Jest configuration to properly support ESM modules
   - [x] Ensure consistent import/export syntax across all test files
   - [x] Fix the TypeScript configuration to support both ESM and CommonJS modules

Additional tasks that could be completed:

2. **Fix Test Utilities**:
   - [ ] Consolidate auth.ts and auth.e2e.ts into a single utility that works for both API and E2E tests
   - [ ] Update the db.ts utility to work with both ESM and CommonJS modules

3. **Update Test Files**:
   - [x] Fix import statements in all API test files
   - [ ] Update test assertions to match the current API response format
   - [ ] Add proper error handling for API tests

4. **Add Mock Server**:
   - [ ] Create a mock server for API tests to avoid hitting real endpoints
   - [x] Add fixtures for API responses
   - [ ] Configure Jest to use the mock server for tests

## E2E Tests

The E2E tests have been expanded with new tests for coupon functionality and navigation. The following tasks have been completed:

1. **Authentication Tests**:
   - [ ] Create test accounts for E2E tests
   - [ ] Update auth.e2e.ts to use real authentication
   - [ ] Enable the skipped authentication tests

2. **Test Data Management**:
   - [x] Add setup/teardown scripts to create and clean up test data
   - [ ] Create isolated test environments for each test run

3. **Visual Regression Tests**:
   - [x] Add visual regression tests for key UI components
   - [x] Configure Playwright to capture screenshots for comparison

4. **Performance Tests**:
   - [ ] Add performance tests for key user flows
   - [ ] Set up performance monitoring in CI/CD

## General Improvements

1. **Test Coverage**:
   - [x] Add tests for coupon code functionality
   - [x] Add tests for ScrollToTop component
   - [ ] Add tests for remaining API endpoints
   - [ ] Add tests for remaining UI components
   - [ ] Add tests for all user flows

2. **Test Reporting**:
   - [ ] Configure test reporting in CI/CD
   - [ ] Add test coverage reports
   - [ ] Add performance reports

3. **Test Automation**:
   - [ ] Set up automated test runs on PR creation
   - [ ] Add status checks for test results
   - [ ] Configure automatic test failure notifications

4. **Documentation**:
   - [x] Update test documentation with new tests
   - [x] Document test commands in README
   - [ ] Add detailed documentation for all test utilities
   - [ ] Create a test writing guide for contributors
   - [ ] Document test patterns and best practices
