/**
 * Subscription test fixtures
 * 
 * This module provides test fixtures for subscription testing.
 */

/**
 * Create a test subscription object
 * @param {Object} params Subscription parameters
 * @returns {Object} Test subscription object
 */
function createTestSubscription(params = {}) {
  const now = new Date();
  const oneMonthLater = new Date(now);
  oneMonthLater.setMonth(oneMonthLater.getMonth() + 1);
  
  const oneYearLater = new Date(now);
  oneYearLater.setFullYear(oneYearLater.getFullYear() + 1);

  return {
    id: params.id || 1,
    user_id: params.user_id || 'test_user_123',
    plan_id: params.plan_id || 'professional',
    billing_cycle: params.billing_cycle || 'annual',
    status: params.status || 'active',
    stripe_customer_id: params.stripe_customer_id || 'cus_test123',
    stripe_subscription_id: params.stripe_subscription_id || 'sub_test123',
    current_period_start: params.current_period_start || now,
    current_period_end: params.current_period_end || (params.billing_cycle === 'monthly' ? oneMonthLater : oneYearLater),
    cancel_at_period_end: params.cancel_at_period_end || false,
    created_at: params.created_at || now,
    updated_at: params.updated_at || now
  };
}

/**
 * Create a test usage object
 * @param {Object} params Usage parameters
 * @returns {Object} Test usage object
 */
function createTestUsage(params = {}) {
  const now = new Date();
  const oneMonthLater = new Date(now);
  oneMonthLater.setMonth(oneMonthLater.getMonth() + 1);

  return {
    id: params.id || 1,
    user_id: params.user_id || 'test_user_123',
    projects_count: params.projects_count || 0,
    images_count: params.images_count || 0,
    period_start: params.period_start || now,
    period_end: params.period_end || oneMonthLater,
    created_at: params.created_at || now,
    updated_at: params.updated_at || now
  };
}

/**
 * Get plan limits for a specific plan and billing cycle
 * @param {string} planId Plan ID
 * @param {string} billingCycle Billing cycle
 * @returns {Object} Plan limits
 */
function getPlanLimits(planId, billingCycle) {
  const PLAN_LIMITS = {
    starter: {
      monthly: {
        price: 17.99,
        projects: 3,
        imagesPerProject: 3,
        displayName: "Starter"
      },
      annual: {
        price: 14.99,
        annualPrice: 179.88, // 14.99 * 12 = 179.88 (20% discount from monthly)
        projects: 3,
        imagesPerProject: 3,
        displayName: "Starter"
      }
    },
    professional: {
      monthly: {
        price: 58.80,
        projects: 10,
        imagesPerProject: 5,
        displayName: "Professional"
      },
      annual: {
        price: 49,
        annualPrice: 588.00, // 49 * 12 = 588.00 (20% discount from monthly)
        projects: 10,
        imagesPerProject: 5,
        displayName: "Professional"
      }
    }
  };

  return PLAN_LIMITS[planId]?.[billingCycle] || {
    price: 0,
    projects: 1,
    imagesPerProject: 1,
    displayName: "Free"
  };
}

/**
 * Create a test subscription response object
 * @param {Object} params Subscription parameters
 * @returns {Object} Test subscription response object
 */
function createTestSubscriptionResponse(params = {}) {
  const subscription = createTestSubscription(params);
  const usage = createTestUsage(params);
  const limits = getPlanLimits(subscription.plan_id, subscription.billing_cycle);

  return {
    subscription: {
      subscription: subscription
    },
    usage: usage,
    limits: limits
  };
}

/**
 * Create a test subscription data for localStorage
 * @param {Object} params Subscription parameters
 * @returns {Object} Test subscription data for localStorage
 */
function createTestSubscriptionData(params = {}) {
  const subscription = createTestSubscription(params);
  const usage = {
    projects: params.projects_count || 0,
    images: params.images_count || 0
  };
  const limits = getPlanLimits(subscription.plan_id, subscription.billing_cycle);

  return {
    subscription: {
      subscription: subscription
    },
    usage: usage,
    limits: limits
  };
}

module.exports = {
  createTestSubscription,
  createTestUsage,
  getPlanLimits,
  createTestSubscriptionResponse,
  createTestSubscriptionData
};
