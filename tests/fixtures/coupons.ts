/**
 * Test fixtures for coupon tests
 */

export interface TestCoupon {
  code: string;
  description: string;
  type: 'percentage' | 'fixed_amount' | 'free_months';
  amount: number;
  duration: 'once' | 'repeating' | 'forever';
  duration_in_months?: number | null;
  stripe_coupon_id?: string;
}

/**
 * Create test coupon data
 */
export function createTestCoupon(
  type: 'percentage' | 'fixed_amount' | 'free_months' = 'percentage',
  duration: 'once' | 'repeating' | 'forever' = 'once'
): TestCoupon {
  const now = new Date();
  const validUntil = new Date();
  validUntil.setDate(validUntil.getDate() + 30); // Valid for 30 days

  // Generate a random code
  const randomCode = `TEST${Math.floor(Math.random() * 10000)}`;

  // Create different coupon types
  switch (type) {
    case 'percentage':
      return {
        code: randomCode,
        description: 'Test Percentage Coupon - 25% Off',
        type: 'percentage',
        amount: 25,
        duration,
        duration_in_months: duration === 'repeating' ? 3 : null,
        stripe_coupon_id: `coupon_${randomCode.toLowerCase()}`
      };
    
    case 'fixed_amount':
      return {
        code: randomCode,
        description: 'Test Fixed Amount Coupon - $20 Off',
        type: 'fixed_amount',
        amount: 20,
        duration,
        duration_in_months: duration === 'repeating' ? 3 : null,
        stripe_coupon_id: `coupon_${randomCode.toLowerCase()}`
      };
    
    case 'free_months':
      return {
        code: randomCode,
        description: 'Test Free Months Coupon - 1 Month Free',
        type: 'free_months',
        amount: 1,
        duration: 'repeating',
        duration_in_months: 1,
        stripe_coupon_id: `coupon_${randomCode.toLowerCase()}`
      };
    
    default:
      return {
        code: randomCode,
        description: 'Test Coupon - 25% Off',
        type: 'percentage',
        amount: 25,
        duration: 'once',
        stripe_coupon_id: `coupon_${randomCode.toLowerCase()}`
      };
  }
}

/**
 * Mock Stripe coupon data
 */
export function createMockStripeCoupon(testCoupon: TestCoupon) {
  let stripeCoupon: any = {
    id: testCoupon.stripe_coupon_id || `coupon_${testCoupon.code.toLowerCase()}`,
    name: testCoupon.description,
    duration: testCoupon.duration,
    duration_in_months: testCoupon.duration_in_months,
    valid: true,
    times_redeemed: 0,
    metadata: {
      code: testCoupon.code
    }
  };

  // Add type-specific properties
  if (testCoupon.type === 'percentage') {
    stripeCoupon.percent_off = testCoupon.amount;
  } else if (testCoupon.type === 'fixed_amount') {
    stripeCoupon.amount_off = testCoupon.amount * 100; // Convert to cents
    stripeCoupon.currency = 'aud';
  } else if (testCoupon.type === 'free_months') {
    stripeCoupon.percent_off = 100;
    stripeCoupon.duration = 'repeating';
    stripeCoupon.duration_in_months = testCoupon.amount;
  }

  return stripeCoupon;
}
