# Demo User Accounts for Renovision Studio

This document provides comprehensive information about the 10 demo user accounts created for testing purposes in Renovision Studio.

## Overview

10 demo user accounts have been created with usernames following the pattern `acquireuser7` through `acquireuser16`. These accounts are fully functional within the Renovision Studio system and use Clerk's test mode for authentication.

## Demo Account List

| Username | Email | Full Name | Bio | Plan |
|----------|-------|-----------|-----|------|
| acquireuser7 | <EMAIL> | <PERSON> | Interior design enthusiast with a passion for modern aesthetics. | **Professional** ⭐ |
| acquireuser8 | <EMAIL> | <PERSON> | Home renovation expert specializing in sustainable design solutions. | Free |
| acquireuser9 | <EMAIL> | <PERSON> | Architect and designer focused on creating functional living spaces. | Free |
| acquireuser10 | <EMAIL> | <PERSON> | DIY home improvement blogger and renovation project manager. | Free |
| acquireuser11 | <EMAIL> | <PERSON> | Real estate developer with expertise in property transformation. | Free |
| acquireuser12 | <EMAIL> | <PERSON> | Professional contractor specializing in kitchen and bathroom renovations. | Free |
| acquireuser13 | <EMAIL> | Ryan Davis | Home staging consultant helping clients maximize property value. | Free |
| acquireuser14 | <EMAIL> | Lisa Anderson | Interior decorator with 10+ years of residential design experience. | Free |
| acquireuser15 | <EMAIL> | James Wilson | Construction project manager and home renovation specialist. | Free |
| acquireuser16 | <EMAIL> | Maria Garcia | Sustainable design advocate promoting eco-friendly renovation practices. | Free |

## ⭐ Premium Demo Account

**`acquireuser7` (Alex Johnson)** has been upgraded to a **Professional Plan** subscription:

### Professional Plan Benefits
- **10 projects per month** (vs 3 on free tier)
- **5 images per project** (vs 3 on free tier)
- **Access to all renovation styles**
- **High quality image generation**
- **Priority support features**
- **Valid for 1 month** from today

### Perfect for Demonstrations
- Showcase premium features and capabilities
- Demonstrate the value proposition of paid plans
- Test subscription-gated functionality
- Show the difference between free and paid tiers

## Authentication Instructions

### Method 1: Username Sign-In
1. Go to the Renovision Studio sign-in page
2. Enter any of the usernames: `acquireuser7`, `acquireuser8`, ..., `acquireuser16`
3. When prompted for verification, use the test code: **424242**
4. The account will be automatically verified and logged in

### Method 2: Email Sign-In
1. Go to the Renovision Studio sign-in page
2. Enter any of the test email addresses (e.g., `<EMAIL>`)
3. When prompted for verification, use the test code: **424242**
4. The account will be automatically verified and logged in

## Technical Details

### Clerk Integration
- All accounts use Clerk's test mode with the `+clerk_test` email format
- No real emails are sent during authentication
- Verification code `424242` works for all test accounts
- Accounts are marked with metadata indicating they are demo accounts

### Database Records
- All accounts have corresponding records in the Supabase `users` table
- Each account includes realistic profile information (name, bio, etc.)
- Accounts are created with proper timestamps and metadata

### Account Features
- **No Password Required**: Demo accounts use Clerk's `skipPasswordRequirement` feature
- **Test Mode Only**: These accounts only work in development/test environments
- **Metadata Tracking**: Accounts are marked with `isDemoAccount: true` in public metadata
- **Realistic Profiles**: Each account has unique names and professional bios

## Security Considerations

### Development Environment Only
- These accounts are designed for development and testing environments only
- They use Clerk's test mode which is automatically enabled in development instances
- Test email addresses with `+clerk_test` are only functional in test mode

### Production Safety
- Demo accounts will not work in production environments unless test mode is explicitly enabled
- The `+clerk_test` email format ensures these are clearly identified as test accounts
- No real email addresses are used, preventing accidental communications

## Usage Guidelines

### For Testing
- Use these accounts to test user flows, authentication, and application features
- Each account has a unique persona that can be used for different testing scenarios
- Accounts can be used to test multi-user features and interactions

### For Demonstrations
- Accounts provide realistic user profiles for product demonstrations
- Different professional backgrounds allow showcasing various use cases
- Names and bios are designed to be professional and appropriate for client demos

## Maintenance

### Regenerating Accounts
To recreate or update the demo accounts:
```bash
npm run create-demo-accounts
```

### Checking Account Status
To verify demo accounts exist and view their details:
```bash
npm run check-users
```

### Account Cleanup
If needed, demo accounts can be identified and removed using their metadata:
- Look for accounts with `publicMetadata.isDemoAccount: true`
- Filter by email pattern `*+<EMAIL>`
- Check `privateMetadata.accountType: 'demo'`

## Troubleshooting

### Authentication Issues
- **Verification Code Not Working**: Ensure you're using `424242` exactly
- **Account Not Found**: Verify the username/email is typed correctly
- **Test Mode Disabled**: Check that Clerk test mode is enabled in your environment

### Environment Issues
- **Production Environment**: Demo accounts won't work in production unless test mode is enabled
- **Missing Accounts**: Run `npm run create-demo-accounts` to recreate them
- **Database Sync**: Accounts should automatically sync between Clerk and Supabase

## Support

For issues with demo accounts:
1. Check the account exists using `npm run check-users`
2. Verify environment variables are properly configured
3. Ensure Clerk test mode is enabled
4. Recreate accounts if necessary using the creation script

## Management Scripts

### Available Commands
```bash
# Create all 10 demo accounts
npm run create-demo-accounts

# Verify demo accounts exist and view details
npm run check-users

# Test demo account authentication setup
npm run test-demo-auth

# Populate demo accounts with sample data
npm run populate-demo-data

# Upgrade demo account to professional plan
npm run upgrade-demo-subscription

# Verify subscription status and fix if needed
npm run verify-demo-subscription
npm run fix-demo-subscription

# Test subscription system functionality
npm run test-subscription-fix
```

### Script Details
- **create-demo-accounts**: Creates 10 demo users in Clerk and Supabase
- **check-users**: Shows all users including demo accounts with usage stats
- **test-demo-auth**: Verifies all demo accounts are properly configured in Clerk
- **populate-demo-data**: Adds sample projects, categories, and usage data
- **upgrade-demo-subscription**: Upgrades a demo account to professional plan
- **verify-demo-subscription**: Checks subscription status and details
- **fix-demo-subscription**: Fixes subscription status issues
- **test-subscription-fix**: Verifies subscription system works correctly

## Complete Setup Process

### 1. Initial Setup
```bash
# Create all demo accounts
npm run create-demo-accounts

# Populate with sample data
npm run populate-demo-data

# Upgrade acquireuser7 to professional plan
npm run upgrade-demo-subscription

# Verify everything is working
npm run test-demo-auth
npm run verify-demo-subscription
```

### 2. Maintenance
```bash
# Check account status
npm run check-users

# Fix subscription issues if needed
npm run fix-demo-subscription

# Test subscription functionality
npm run test-subscription-fix
```

### 3. Troubleshooting
- **"Syncing" message on subscription page**: Run `npm run fix-demo-subscription`
- **Authentication not working**: Run `npm run test-demo-auth`
- **Missing sample data**: Run `npm run populate-demo-data`
- **Accounts not found**: Run `npm run create-demo-accounts`

## Related Files

- `scripts/create-demo-accounts.ts` - Account creation script
- `scripts/test-demo-auth.ts` - Authentication verification script
- `scripts/populate-demo-data.ts` - Sample data population script
- `scripts/upgrade-demo-subscription.ts` - Professional plan upgrade script
- `scripts/verify-demo-subscription.ts` - Subscription verification script
- `scripts/fix-demo-subscription.ts` - Subscription status fix script
- `scripts/test-subscription-fix.ts` - Subscription system test script
- `scripts/check-users.ts` - Account verification script
- `DEMO_ACCOUNTS_QUICK_REFERENCE.md` - Quick reference guide
- `DEMO_PROFESSIONAL_UPGRADE_SUMMARY.md` - Professional plan upgrade details
- `SUBSCRIPTION_FIX_SUMMARY.md` - Subscription fix documentation
- `AUTH.md` - General authentication documentation
- `.env.example` - Environment variable template
