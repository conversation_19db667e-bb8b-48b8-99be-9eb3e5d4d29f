[vite] connecting...
[vite] connected.
Creating project with data: 
Object {title: "test", description: "test", modificationDescription: ""}
Skipped "image/*" because an invalid file extension was provided.
Warning: validateDOMNesting(...): <a> cannot appear as a descendant of &lt;a&gt;.
    at a
    at a
    at https://2a359c28-07e1-4348-ba68-2f4f74d18ac3-00-2tnnxa9vlndjp.kirk.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/wouter.js?v=e9b7989b:337:18
    at div
    at div
    at div
    at div
    at Project (https://2a359c28-07e1-4348-ba68-2f4f74d18ac3-00-2tnnxa9vlndjp.kirk.replit.dev/src/pages/Project.tsx:29:18)
    at Route (https://2a359c28-07e1-4348-ba68-2f4f74d18ac3-00-2tnnxa9vlndjp.kirk.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/wouter.js?v=e9b7989b:323:16)
    at Switch (https://2a359c28-07e1-4348-ba68-2f4f74d18ac3-00-2tnnxa9vlndjp.kirk.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/wouter.js?v=e9b7989b:379:17)
    at main
    at div
    at Router
    at Provider (https://2a359c28-07e1-4348-ba68-2f4f74d18ac3-00-2tnnxa9vlndjp.kirk.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-UBQ2LXDH.js?v=e9b7989b:22:15)
    at TooltipProvider (https://2a359c28-07e1-4348-ba68-2f4f74d18ac3-00-2tnnxa9vlndjp.kirk.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=e9b7989b:2246:5)
    at QueryClientProvider (https://2a359c28-07e1-4348-ba68-2f4f74d18ac3-00-2tnnxa9vlndjp.kirk.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/@tanstack_react-query.js?v=e9b7989b:2805:3)
    at App
    at ThemeProvider (https://2a359c28-07e1-4348-ba68-2f4f74d18ac3-00-2tnnxa9vlndjp.kirk.replit.dev/src/components/ui/theme-provider.tsx:26:3)
at a
at a
at https://2a359c28-07e1-4348-ba68-2f4f74d18ac3-00-2tnnxa9vlndjp.kirk.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/wouter.js?v=e9b7989b:337:18
at div
at div
at div
at div
at Project (https://2a359c28-07e1-4348-ba68-2f4f74d18ac3-00-2tnnxa9vlndjp.kirk.replit.dev/src/pages/Project.tsx:29:18)
at Route (https://2a359c28-07e1-4348-ba68-2f4f74d18ac3-00-2tnnxa9vlndjp.kirk.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/wouter.js?v=e9b7989b:323:16)
at Switch (https://2a359c28-07e1-4348-ba68-2f4f74d18ac3-00-2tnnxa9vlndjp.kirk.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/wouter.js?v=e9b7989b:379:17)
at main
at div
at Router
at Provider (https://2a359c28-07e1-4348-ba68-2f4f74d18ac3-00-2tnnxa9vlndjp.kirk.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-UBQ2LXDH.js?v=e9b7989b:22:15)
at TooltipProvider (https://2a359c28-07e1-4348-ba68-2f4f74d18ac3-00-2tnnxa9vlndjp.kirk.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=e9b7989b:2246:5)
at QueryClientProvider (https://2a359c28-07e1-4348-ba68-2f4f74d18ac3-00-2tnnxa9vlndjp.kirk.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/@tanstack_react-query.js?v=e9b7989b:2805:3)
at App
at ThemeProvider (https://2a359c28-07e1-4348-ba68-2f4f74d18ac3-00-2tnnxa9vlndjp.kirk.replit.dev/src/components/ui/theme-provider.tsx:26:3)
at t.value (https://2a359c28-07e1-4348-ba68-2f4f74d18ac3-00-2tnnxa9vlndjp.kirk.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:17465)
at new t (https://2a359c28-07e1-4348-ba68-2f4f74d18ac3-00-2tnnxa9vlndjp.kirk.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:12630)
at t.value (https://2a359c28-07e1-4348-ba68-2f4f74d18ac3-00-2tnnxa9vlndjp.kirk.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:32766)
at https://2a359c28-07e1-4348-ba68-2f4f74d18ac3-00-2tnnxa9vlndjp.kirk.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:34400