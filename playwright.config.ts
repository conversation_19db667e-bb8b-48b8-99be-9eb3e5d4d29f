import { defineConfig, devices } from '@playwright/test';
import path from 'path';

export default defineConfig({
  testDir: './tests/e2e',
  timeout: 30 * 1000,
  expect: {
    timeout: 5000,
    toHaveScreenshot: {
      maxDiffPixelRatio: 0.05, // Allow 5% of pixels to be different
      threshold: 0.2, // Pixel RGB difference threshold (0-1)
      animations: 'disabled', // Disable animations for consistent screenshots
    }
  },
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',
  use: {
    actionTimeout: 0,
    baseURL: 'http://localhost:3000',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
    video: 'on-first-retry',
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
    // Visual regression testing project
    {
      name: 'visual-regression',
      testDir: './tests/e2e/visual-regression',
      use: {
        ...devices['Desktop Chrome'],
        viewport: { width: 1280, height: 720 },
        // Use a consistent viewport size for visual testing
        deviceScaleFactor: 1,
        // Ensure consistent rendering
        colorScheme: 'light',
        // Always use light mode for consistent screenshots
        reducedMotion: 'reduce',
        // Disable animations
      },
      snapshotDir: './tests/e2e/visual-regression/snapshots',
      // Store snapshots in a dedicated directory
    },
  ],
  webServer: {
    command: 'npm run dev',
    port: 3000,
    reuseExistingServer: !process.env.CI,
    stdout: 'pipe',
    stderr: 'pipe',
  },
});
