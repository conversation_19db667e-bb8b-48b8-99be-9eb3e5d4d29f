import {
  type User, type InsertUser,
  type Project, type InsertProject,
  type Image, type InsertImage,
  type Modification, type InsertModification,
  type ReferenceCategory, type InsertReferenceCategory,
  type ReferenceItem, type InsertReferenceItem,
  type RenovationPreset, type InsertRenovationPreset,
  type Draft, type InsertDraft,
  type Subscription, type InsertSubscription,
  type Usage, type InsertUsage,
  type Coupon, type InsertCoupon,
  type CouponRedemption, type InsertCouponRedemption,
  type ProjectWithImages, type ReferenceCategoryWithItems
} from "@shared/schema";
import { db } from "./db";
import { createDbLogger } from "./utils/db-logger";

export interface IStorage {
  // Transaction support
  transaction<T>(callback: (trx: any) => Promise<T>): Promise<T>;

  // User methods
  getUser(id: string): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  upsertUser(user: any): Promise<User>;

  // Project methods
  getProject(id: number): Promise<Project | undefined>;
  getProjectWithImages(id: number): Promise<ProjectWithImages | undefined>;
  getProjectsByUserId(userId: string): Promise<Project[]>;
  getDraftProjectsByUserId(userId: string): Promise<Project[]>;
  createProject(project: InsertProject): Promise<Project>;
  updateProject(id: number, data: Partial<InsertProject>): Promise<Project | undefined>;
  updateProjectStatus(id: number, status: string): Promise<Project | undefined>;

  // Image methods
  getImage(id: number): Promise<Image | undefined>;
  getImagesByProjectId(projectId: number): Promise<Image[]>;
  createImage(image: InsertImage): Promise<Image>;

  // Modification methods
  getModification(id: number): Promise<Modification | undefined>;
  getModificationsByProjectId(projectId: number): Promise<Modification[]>;
  createModification(modification: InsertModification): Promise<Modification>;

  // Reference Library methods
  getReferenceCategory(id: number): Promise<ReferenceCategory | undefined>;
  getReferenceCategoriesByUserId(userId: string): Promise<ReferenceCategory[]>;
  getReferenceCategoryWithItems(id: number): Promise<ReferenceCategoryWithItems | undefined>;
  createReferenceCategory(category: InsertReferenceCategory): Promise<ReferenceCategory>;
  updateReferenceCategory(id: number, data: Partial<InsertReferenceCategory>): Promise<ReferenceCategory | undefined>;
  deleteReferenceCategory(id: number): Promise<void>;

  getReferenceItem(id: number): Promise<ReferenceItem | undefined>;
  getReferenceItemsByUserId(userId: string): Promise<ReferenceItem[]>;
  getReferenceItemsByCategoryId(categoryId: number): Promise<ReferenceItem[]>;
  createReferenceItem(item: InsertReferenceItem): Promise<ReferenceItem>;
  updateReferenceItem(id: number, data: Partial<InsertReferenceItem>): Promise<ReferenceItem | undefined>;
  deleteReferenceItem(id: number): Promise<void>;

  // Renovation Preset methods
  getRenovationPreset(id: number): Promise<RenovationPreset | undefined>;
  getRenovationPresets(): Promise<RenovationPreset[]>;
  getRenovationPresetsByRoomType(roomType: string): Promise<RenovationPreset[]>;
  getRenovationPresetsByUserId(userId: string): Promise<RenovationPreset[]>;
  createRenovationPreset(preset: InsertRenovationPreset): Promise<RenovationPreset>;
  updateRenovationPreset(id: number, data: Partial<InsertRenovationPreset>): Promise<RenovationPreset | undefined>;
  deleteRenovationPreset(id: number): Promise<void>;

  // Draft methods
  getDraft(id: number): Promise<Draft | undefined>;
  getDraftsByUserId(userId: string): Promise<Draft[]>;
  createDraft(draft: InsertDraft): Promise<Draft>;
  updateDraft(id: number, data: Partial<InsertDraft>): Promise<Draft | undefined>;
  deleteDraft(id: number): Promise<void>;

  // Subscription methods
  getSubscription(userId: string): Promise<Subscription | undefined>;
  createSubscription(subscription: InsertSubscription): Promise<Subscription>;
  updateSubscription(userId: string, data: Partial<InsertSubscription>): Promise<Subscription | undefined>;
  deleteSubscription(userId: string): Promise<void>;

  // Usage methods
  getUsage(userId: string, periodStart: Date, periodEnd: Date): Promise<Usage | undefined>;
  getCurrentUsage(userId: string): Promise<Usage | undefined>;
  createUsage(usage: InsertUsage): Promise<Usage>;
  updateUsage(id: number, data: Partial<InsertUsage>): Promise<Usage | undefined>;
  incrementProjectCount(userId: string): Promise<Usage | undefined>;
  incrementImageCount(userId: string, count: number): Promise<Usage | undefined>;
  decrementImageCount(userId: string, count: number): Promise<Usage | undefined>;

  // Webhook event processing methods
  getProcessedWebhookEvent(eventId: string): Promise<boolean>;
  saveProcessedWebhookEvent(eventId: string, eventType?: string): Promise<void>;

  // Coupon methods
  getCoupon(id: number): Promise<Coupon | undefined>;
  getCouponByCode(code: string): Promise<Coupon | undefined>;
  getCouponByStripeId(stripeId: string): Promise<Coupon | undefined>;
  listCoupons(includeInactive?: boolean): Promise<Coupon[]>;
  createCoupon(coupon: InsertCoupon): Promise<Coupon>;
  updateCoupon(id: number, data: Partial<InsertCoupon>): Promise<Coupon | undefined>;
  deleteCoupon(id: number): Promise<void>;
  incrementCouponRedemptionCount(id: number): Promise<Coupon | undefined>;

  // Coupon redemption methods
  getCouponRedemption(id: number): Promise<CouponRedemption | undefined>;
  getCouponRedemptionsByUserId(userId: string): Promise<CouponRedemption[]>;
  getCouponRedemptionsByCouponId(couponId: number): Promise<CouponRedemption[]>;
  createCouponRedemption(redemption: InsertCouponRedemption): Promise<CouponRedemption>;
  hasUserRedeemedCoupon(userId: string, couponId: number): Promise<boolean>;
}

export class DatabaseStorage implements IStorage {
  private dbLogger = createDbLogger('storage');

  constructor() {
    // No session store needed - using Clerk for authentication
  }

  /**
   * Execute a callback within a database transaction
   * @param callback Function to execute within the transaction
   * @returns Result of the callback
   */
  async transaction<T>(callback: (trx: any) => Promise<T>): Promise<T> {
    // Supabase doesn't have a direct transaction API, so we'll simulate it
    // by using the same client instance for all operations
    try {
      // Start a transaction
      console.log('Starting database transaction');

      // Execute the callback with the client
      const result = await callback(db.client);

      console.log('Transaction completed successfully');
      return result;
    } catch (error) {
      console.error('Transaction failed:', error);
      throw error;
    }
  }

  // User methods
  async getUser(id: string): Promise<User | undefined> {
    const { data: user } = await db.client
      .from('users')
      .select('*')
      .eq('id', id)
      .single();
    return user;
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const { data: user } = await db.client
      .from('users')
      .select('*')
      .eq('username', username)
      .single();
    return user;
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const { data: user } = await db.client
      .from('users')
      .insert(insertUser)
      .select()
      .single();
    if (!user) throw new Error('Failed to create user');
    return user;
  }

  async upsertUser(userData: any): Promise<User> {
    try {
      // Ensure the updated_at field is set
      const dataToUpsert = {
        ...userData,
        updated_at: new Date().toISOString()
      };

      // Log the upsert operation for debugging
      console.log(`Upserting user with ID: ${userData.id}`);

      // Retry logic for database operations
      const maxRetries = 3;
      let lastError: any;

      for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
          const { data: user, error } = await db.client
            .from('users')
            .upsert(dataToUpsert)
            .select()
            .single();

          if (error) {
            console.error(`Supabase upsert error (attempt ${attempt}):`, error);
            lastError = error;

            // If it's a network error, retry
            if (attempt < maxRetries && (error.message.includes('fetch failed') || error.message.includes('network'))) {
              console.log(`Retrying in ${attempt * 1000}ms...`);
              await new Promise(resolve => setTimeout(resolve, attempt * 1000));
              continue;
            }

            throw new Error(`Failed to upsert user: ${error.message}`);
          }

          if (!user) throw new Error('Failed to upsert user: No data returned');

          console.log(`✅ User upserted successfully on attempt ${attempt}`);
          return user;
        } catch (err: any) {
          lastError = err;

          // If it's a network/fetch error and we have retries left, continue
          if (attempt < maxRetries && (err.message.includes('fetch failed') || err.message.includes('TypeError: fetch failed'))) {
            console.log(`Network error on attempt ${attempt}, retrying in ${attempt * 1000}ms...`);
            await new Promise(resolve => setTimeout(resolve, attempt * 1000));
            continue;
          }

          // If it's the last attempt or a non-retryable error, throw
          throw err;
        }
      }

      throw lastError;
    } catch (err) {
      console.error('Error in upsertUser:', err);

      // If it's a network connectivity issue, provide a more helpful error
      if (err instanceof Error && err.message.includes('fetch failed')) {
        throw new Error('Database connection failed. Please check your internet connection and try again.');
      }

      throw err;
    }
  }

  // Project methods
  async getProject(id: number): Promise<Project | undefined> {
    const { data: project } = await db.client
      .from('projects')
      .select('*')
      .eq('id', id)
      .single();
    return project;
  }

  // Helper to normalize image paths for frontend
  normalizeImagePath(imagePath: string): string {
    if (!imagePath) return imagePath;

    // If it's a URL, return as is
    if (imagePath.startsWith('http')) return imagePath;

    // If it already starts with /uploads/, it's already normalized
    if (imagePath.startsWith('/uploads/')) return imagePath;

    // Extract just the filename from the path
    const parts = imagePath.split('/');
    const filename = parts[parts.length - 1];

    if (filename) {
      return `/uploads/${filename}`;
    }

    return imagePath;
  }

  async getProjectWithImages(id: number): Promise<ProjectWithImages | undefined> {
    const { data: project } = await db.client
      .from('projects')
      .select('*')
      .eq('id', id)
      .single();
    if (!project) return undefined;

    const allImages = (await this.getImagesByProjectId(id)).map(img => ({
      ...img,
      path: this.normalizeImagePath(img.path)
    }));
    const allModifications = await this.getModificationsByProjectId(id);

    return {
      ...project,
      beforeImages: allImages.filter(image => image.type === 'before'),
      afterImages: allImages.filter(image => image.type === 'after'),
      referenceImages: allImages.filter(image => image.type === 'reference'),
      modifications: allModifications
    };
  }

  async getProjectsByUserId(userId: string): Promise<Project[]> {
    const { data: projects } = await db.client
      .from('projects')
      .select('*')
      .eq('user_id', userId);
    return projects || [];
  }

  async getDraftProjectsByUserId(userId: string): Promise<Project[]> {
    const { data: projects } = await db.client
      .from('projects')
      .select('*')
      .eq('user_id', userId)
      .eq('status', 'draft');
    return projects || [];
  }

  async createProject(insertProject: InsertProject): Promise<Project> {
    try {
      // Ensure status is set to 'draft' by default
      const projectData = {
        ...insertProject,
        status: insertProject.status || 'draft'
      };

      console.log('Creating project with data:', projectData);

      const { data: project, error } = await db.client
        .from('projects')
        .insert(projectData)
        .select()
        .single();

      if (error) {
        console.error('Supabase error creating project:', error);
        throw new Error(`Failed to create project: ${error.message}`);
      }

      if (!project) {
        throw new Error('Failed to create project: No data returned');
      }

      console.log('Project created successfully:', project);
      return project;
    } catch (err) {
      console.error('Error in createProject:', err);
      throw err;
    }
  }

  async updateProject(id: number, data: Partial<InsertProject>): Promise<Project | undefined> {
    const { data: project } = await db.client
      .from('projects')
      .update({
        ...data,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();
    return project;
  }

  async updateProjectStatus(id: number, status: string): Promise<Project | undefined> {
    const { data: project } = await db.client
      .from('projects')
      .update({
        status,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();
    return project;
  }

  // Image methods
  async getImage(id: number): Promise<Image | undefined> {
    const { data: image } = await db.client
      .from('images')
      .select('*')
      .eq('id', id)
      .single();
    return image;
  }

  async getImagesByProjectId(projectId: number): Promise<Image[]> {
    const { data: images } = await db.client
      .from('images')
      .select('*')
      .eq('project_id', projectId);
    return (images || []).map(img => ({
      ...img,
      path: this.normalizeImagePath(img.path)
    }));
  }

  async createImage(insertImage: InsertImage): Promise<Image> {
    // Normalize the path before saving
    const imageData = {
      ...insertImage,
      path: insertImage.path
    };

    const { data: image } = await db.client
      .from('images')
      .insert(imageData)
      .select()
      .single();
    if (!image) throw new Error('Failed to create image');

    // Return the image with normalized path
    return {
      ...image,
      path: this.normalizeImagePath(image.path)
    };
  }

  // Modification methods
  async getModification(id: number): Promise<Modification | undefined> {
    const { data: modification } = await db.client
      .from('modifications')
      .select('*')
      .eq('id', id)
      .single();
    return modification;
  }

  async getModificationsByProjectId(projectId: number): Promise<Modification[]> {
    const { data: modifications } = await db.client
      .from('modifications')
      .select('*')
      .eq('project_id', projectId);
    return modifications || [];
  }

  async createModification(insertModification: InsertModification): Promise<Modification> {
    console.log('Creating modification with data:', JSON.stringify(insertModification, null, 2));
    
    // First check if the modifications table exists and is accessible
    try {
      const { count, error: countError } = await db.client
        .from('modifications')
        .select('*', { count: 'exact', head: true });
      
      if (countError) {
        console.error('Cannot access modifications table:', countError);
        throw new Error(`Cannot access modifications table: ${countError.message}`);
      }
      
      console.log('Modifications table accessible, current count:', count);
    } catch (tableError) {
      console.error('Error checking modifications table:', tableError);
      throw new Error(`Cannot verify modifications table: ${tableError}`);
    }
    
    const { data: modification, error } = await db.client
      .from('modifications')
      .insert(insertModification)
      .select()
      .single();
    
    if (error) {
      console.error('Database error creating modification:', error);
      console.error('Error details:', {
        code: error.code,
        details: error.details,
        hint: error.hint,
        message: error.message
      });
      throw new Error(`Failed to create modification: ${error.message}`);
    }
    
    if (!modification) {
      console.error('No modification returned from database insert');
      throw new Error('Failed to create modification: No data returned');
    }
    
    console.log('Successfully created modification:', modification);
    return modification;
  }

  // Reference Category methods
  async getReferenceCategory(id: number): Promise<ReferenceCategory | undefined> {
    const { data: category } = await db.client
      .from('reference_categories')
      .select('*')
      .eq('id', id)
      .single();
    return category;
  }

  async getReferenceCategoriesByUserId(userId: string): Promise<ReferenceCategory[]> {
    const { data: categories } = await db.client
      .from('reference_categories')
      .select('*')
      .eq('user_id', userId);
    return categories || [];
  }

  async getReferenceCategoryWithItems(id: number): Promise<ReferenceCategoryWithItems | undefined> {
    const { data: category } = await db.client
      .from('reference_categories')
      .select('*')
      .eq('id', id)
      .single();
    if (!category) return undefined;

    const items = await this.getReferenceItemsByCategoryId(id);

    return {
      ...category,
      items
    };
  }

  async createReferenceCategory(category: InsertReferenceCategory): Promise<ReferenceCategory> {
    const { data: newCategory } = await db.client
      .from('reference_categories')
      .insert(category)
      .select()
      .single();
    if (!newCategory) throw new Error('Failed to create reference category');
    return newCategory;
  }

  async updateReferenceCategory(id: number, data: Partial<InsertReferenceCategory>): Promise<ReferenceCategory | undefined> {
    const { data: category } = await db.client
      .from('reference_categories')
      .update(data)
      .eq('id', id)
      .select()
      .single();
    return category;
  }

  async deleteReferenceCategory(id: number): Promise<void> {
    await db.client
      .from('reference_categories')
      .delete()
      .eq('id', id);
  }

  // Reference Item methods
  async getReferenceItem(id: number): Promise<ReferenceItem | undefined> {
    const { data: item } = await db.client
      .from('reference_items')
      .select('*')
      .eq('id', id)
      .single();

    if (item && item.image_path) {
      return {
        ...item,
        image_path: this.normalizeImagePath(item.image_path)
      };
    }

    return item;
  }

  async getReferenceItemsByUserId(userId: string): Promise<ReferenceItem[]> {
    const { data: items } = await db.client
      .from('reference_items')
      .select('*')
      .eq('user_id', userId);

    // Normalize image paths for all items
    return (items || []).map(item => ({
      ...item,
      image_path: this.normalizeImagePath(item.image_path)
    }));
  }

  async getReferenceItemsByCategoryId(categoryId: number): Promise<ReferenceItem[]> {
    const { data: items } = await db.client
      .from('reference_items')
      .select('*')
      .eq('category_id', categoryId);

    // Normalize image paths for all items
    return (items || []).map(item => ({
      ...item,
      image_path: this.normalizeImagePath(item.image_path)
    }));
  }

  async createReferenceItem(item: InsertReferenceItem): Promise<ReferenceItem> {
    // Store the original path in the database
    const { data: newItem } = await db.client
      .from('reference_items')
      .insert(item)
      .select()
      .single();

    if (!newItem) throw new Error('Failed to create reference item');

    // Return the item with normalized path for the frontend
    return {
      ...newItem,
      image_path: this.normalizeImagePath(newItem.image_path)
    };
  }

  async updateReferenceItem(id: number, data: Partial<InsertReferenceItem>): Promise<ReferenceItem | undefined> {
    const { data: item } = await db.client
      .from('reference_items')
      .update(data)
      .eq('id', id)
      .select()
      .single();

    if (item && item.image_path) {
      return {
        ...item,
        image_path: this.normalizeImagePath(item.image_path)
      };
    }

    return item;
  }

  async deleteReferenceItem(id: number): Promise<void> {
    await db.client
      .from('reference_items')
      .delete()
      .eq('id', id);
  }

  // Renovation Preset methods
  async getRenovationPreset(id: number): Promise<RenovationPreset | undefined> {
    const { data: preset } = await db.client
      .from('renovation_presets')
      .select('*')
      .eq('id', id)
      .single();
    return preset;
  }

  async getRenovationPresets(): Promise<RenovationPreset[]> {
    const { data: presets } = await db.client
      .from('renovation_presets')
      .select('*');
    return presets || [];
  }

  async getRenovationPresetsByRoomType(roomType: string): Promise<RenovationPreset[]> {
    const { data: presets } = await db.client
      .from('renovation_presets')
      .select('*')
      .eq('room_type', roomType);
    return presets || [];
  }

  async getRenovationPresetsByUserId(userId: string): Promise<RenovationPreset[]> {
    const { data: presets } = await db.client
      .from('renovation_presets')
      .select('*')
      .eq('created_by', userId)
      .eq('is_public', false);
    return presets || [];
  }

  async createRenovationPreset(preset: InsertRenovationPreset): Promise<RenovationPreset> {
    const { data: newPreset } = await db.client
      .from('renovation_presets')
      .insert(preset)
      .select()
      .single();
    if (!newPreset) throw new Error('Failed to create renovation preset');
    return newPreset;
  }

  async updateRenovationPreset(id: number, data: Partial<InsertRenovationPreset>): Promise<RenovationPreset | undefined> {
    const { data: preset } = await db.client
      .from('renovation_presets')
      .update(data)
      .eq('id', id)
      .select()
      .single();
    return preset;
  }

  async deleteRenovationPreset(id: number): Promise<void> {
    await db.client
      .from('renovation_presets')
      .delete()
      .eq('id', id);
  }

  // Draft methods
  async getDraft(id: number): Promise<Draft | undefined> {
    const { data: draft } = await db.client
      .from('drafts')
      .select('*')
      .eq('id', id)
      .single();
    return draft;
  }

  async getDraftsByUserId(userId: string): Promise<Draft[]> {
    const { data: drafts } = await db.client
      .from('drafts')
      .select('*')
      .eq('user_id', userId);
    return drafts || [];
  }

  async createDraft(draft: InsertDraft): Promise<Draft> {
    const { data: newDraft } = await db.client
      .from('drafts')
      .insert(draft)
      .select()
      .single();
    if (!newDraft) throw new Error('Failed to create draft');
    return newDraft;
  }

  async updateDraft(id: number, data: Partial<InsertDraft>): Promise<Draft | undefined> {
    const { data: draft } = await db.client
      .from('drafts')
      .update({
        ...data,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();
    return draft;
  }

  async deleteDraft(id: number): Promise<void> {
    await db.client
      .from('drafts')
      .delete()
      .eq('id', id);
  }

  // Subscription methods
  async getSubscription(userId: string): Promise<Subscription | undefined> {
    return this.dbLogger.query(`getSubscription(${userId})`, async () => {
      const { data: subscription } = await db.client
        .from('subscriptions')
        .select('*')
        .eq('user_id', userId)
        .single();
      return subscription;
    });
  }

  async createSubscription(subscription: InsertSubscription): Promise<Subscription> {
    return this.dbLogger.insert('subscriptions', async () => {
      const { data: newSubscription } = await db.client
        .from('subscriptions')
        .insert(subscription)
        .select()
        .single();
      if (!newSubscription) throw new Error('Failed to create subscription');
      return newSubscription;
    });
  }

  /**
   * Create subscription and usage records in a transaction
   * @param subscription Subscription data
   * @param usage Usage data
   * @returns Created subscription
   */
  async createSubscriptionWithUsage(
    subscription: InsertSubscription,
    usage: InsertUsage
  ): Promise<Subscription> {
    return this.dbLogger.transaction('createSubscriptionWithUsage', async () => {
      return this.transaction(async (client) => {
        try {
          // Create subscription
          const { data: newSubscription, error: subscriptionError } = await client
            .from('subscriptions')
            .insert(subscription)
            .select()
            .single();

          if (subscriptionError || !newSubscription) {
            console.error('Error creating subscription in transaction:', subscriptionError);
            throw new Error('Failed to create subscription: ' + (subscriptionError?.message || 'Unknown error'));
          }

          // Create usage record
          const { data: newUsage, error: usageError } = await client
            .from('usage')
            .insert(usage)
            .select()
            .single();

          if (usageError || !newUsage) {
            console.error('Error creating usage in transaction:', usageError);
            throw new Error('Failed to create usage record: ' + (usageError?.message || 'Unknown error'));
          }

          return newSubscription;
        } catch (error) {
          console.error('Transaction failed:', error);
          throw error;
        }
      });
    });
  }

  async updateSubscription(userId: string, data: Partial<InsertSubscription>): Promise<Subscription | undefined> {
    return this.dbLogger.update('subscriptions', async () => {
      const { data: subscription } = await db.client
        .from('subscriptions')
        .update({
          ...data,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId)
        .select()
        .single();
      return subscription;
    });
  }

  async deleteSubscription(userId: string): Promise<void> {
    return this.dbLogger.delete('subscriptions', async () => {
      await db.client
        .from('subscriptions')
        .delete()
        .eq('user_id', userId);
    });
  }

  // Usage methods
  async getUsage(userId: string, periodStart: Date, periodEnd: Date): Promise<Usage | undefined> {
    const { data: usage } = await db.client
      .from('usage')
      .select('*')
      .eq('user_id', userId)
      .gte('period_start', periodStart.toISOString())
      .lte('period_end', periodEnd.toISOString())
      .single();
    return usage;
  }

  async getCurrentUsage(userId: string): Promise<Usage | undefined> {
    const now = new Date();
    const { data: usage } = await db.client
      .from('usage')
      .select('*')
      .eq('user_id', userId)
      .lte('period_start', now.toISOString())
      .gte('period_end', now.toISOString())
      .single();
    return usage;
  }

  async createUsage(usage: InsertUsage): Promise<Usage> {
    const { data: newUsage } = await db.client
      .from('usage')
      .insert(usage)
      .select()
      .single();
    if (!newUsage) throw new Error('Failed to create usage record');
    return newUsage;
  }

  async updateUsage(id: number, data: Partial<InsertUsage>): Promise<Usage | undefined> {
    const { data: usage } = await db.client
      .from('usage')
      .update({
        ...data,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();
    return usage;
  }

  async incrementProjectCount(userId: string): Promise<Usage | undefined> {
    try {
      console.log(`Incrementing project count for user: ${userId}`);

      // Get current usage period
      const now = new Date();
      const { data: usage, error } = await db.client
        .from('usage')
        .select('*')
        .eq('user_id', userId)
        .lte('period_start', now.toISOString())
        .gte('period_end', now.toISOString())
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 is "no rows returned" error
        console.error('Error fetching usage record:', error);
      }

      if (!usage) {
        // No current usage period found, create one
        // Default to monthly period
        const periodEnd = new Date(now);
        periodEnd.setMonth(periodEnd.getMonth() + 1);

        console.log(`No usage record found for user ${userId}, creating new record for period ${now.toISOString()} to ${periodEnd.toISOString()}`);

        try {
          const newUsage = await this.createUsage({
            user_id: userId,
            projects_count: 1,
            images_count: 0,
            period_start: now,
            period_end: periodEnd
          });

          console.log(`Created new usage record with ID: ${newUsage.id}, projects_count: 1`);
          return newUsage;
        } catch (createError) {
          console.error(`Error creating usage record for user ${userId}:`, createError);
          throw createError;
        }
      }

      // Increment project count
      console.log(`Updating existing usage record ID: ${usage.id}, current projects_count: ${usage.projects_count}`);

      const { data: updatedUsage, error: updateError } = await db.client
        .from('usage')
        .update({
          projects_count: usage.projects_count + 1,
          updated_at: now.toISOString()
        })
        .eq('id', usage.id)
        .select()
        .single();

      if (updateError) {
        console.error(`Error updating usage record ${usage.id}:`, updateError);
        throw updateError;
      }

      if (!updatedUsage) {
        console.error(`Failed to update usage record ${usage.id}: No data returned`);
        throw new Error('Failed to update usage record');
      }

      console.log(`Successfully updated usage record ID: ${updatedUsage.id}, new projects_count: ${updatedUsage.projects_count}`);
      return updatedUsage;
    } catch (error) {
      console.error(`Error in incrementProjectCount for user ${userId}:`, error);
      throw error;
    }
  }

  async incrementImageCount(userId: string, count: number = 1): Promise<Usage | undefined> {
    try {
      console.log(`Incrementing image count for user: ${userId} by ${count}`);

      // Validate input
      if (!userId || typeof userId !== 'string') {
        throw new Error('Invalid userId provided');
      }
      if (count <= 0) {
        throw new Error('Count must be positive');
      }

      // Get current usage period
      const now = new Date();
      const { data: usage, error } = await db.client
        .from('usage')
        .select('*')
        .eq('user_id', userId)
        .lte('period_start', now.toISOString())
        .gte('period_end', now.toISOString())
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 is "no rows returned" error
        console.error('Error fetching usage record:', error);
        throw error;
      }

      if (!usage) {
        // No current usage period found, create one
        // Default to monthly period
        const periodEnd = new Date(now);
        periodEnd.setMonth(periodEnd.getMonth() + 1);

        console.log(`No usage record found for user ${userId}, creating new record for period ${now.toISOString()} to ${periodEnd.toISOString()}`);

        try {
          const newUsage = await this.createUsage({
            user_id: userId,
            projects_count: 0,
            images_count: count,
            period_start: now,
            period_end: periodEnd
          });

          console.log(`Created new usage record with ID: ${newUsage.id}, images_count: ${count}`);
          return newUsage;
        } catch (createError) {
          console.error(`Error creating usage record for user ${userId}:`, createError);
          throw createError;
        }
      }

      // Increment image count
      console.log(`Updating existing usage record ID: ${usage.id}, current images_count: ${usage.images_count}, adding: ${count}`);

      const { data: updatedUsage, error: updateError } = await db.client
        .from('usage')
        .update({
          images_count: usage.images_count + count,
          updated_at: now.toISOString()
        })
        .eq('id', usage.id)
        .select()
        .single();

      if (updateError) {
        console.error(`Error updating usage record ${usage.id}:`, updateError);
        throw updateError;
      }

      if (!updatedUsage) {
        console.error(`Failed to update usage record ${usage.id}: No data returned`);
        throw new Error('Failed to update usage record');
      }

      console.log(`Successfully updated usage record ID: ${updatedUsage.id}, new images_count: ${updatedUsage.images_count}`);
      return updatedUsage;
    } catch (error) {
      console.error(`Error in incrementImageCount for user ${userId}:`, error);
      throw error;
    }
  }

  async decrementImageCount(userId: string, count: number = 1): Promise<Usage | undefined> {
    try {
      console.log(`Decrementing image count for user: ${userId} by ${count}`);

      // Validate input
      if (!userId || typeof userId !== 'string') {
        throw new Error('Invalid userId provided');
      }
      if (count <= 0) {
        throw new Error('Count must be positive');
      }

      // Get current usage period
      const now = new Date();
      const { data: usage, error } = await db.client
        .from('usage')
        .select('*')
        .eq('user_id', userId)
        .lte('period_start', now.toISOString())
        .gte('period_end', now.toISOString())
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 is "no rows returned" error
        console.error('Error fetching usage record:', error);
        throw error;
      }

      if (!usage) {
        console.warn(`No usage record found for user ${userId} when trying to decrement image count`);
        return undefined;
      }

      // Calculate new count, ensuring it doesn't go below 0
      const newImageCount = Math.max(0, usage.images_count - count);

      console.log(`Updating existing usage record ID: ${usage.id}, current images_count: ${usage.images_count}, subtracting: ${count}, new count: ${newImageCount}`);

      const { data: updatedUsage, error: updateError } = await db.client
        .from('usage')
        .update({
          images_count: newImageCount,
          updated_at: now.toISOString()
        })
        .eq('id', usage.id)
        .select()
        .single();

      if (updateError) {
        console.error(`Error updating usage record ${usage.id}:`, updateError);
        throw updateError;
      }

      if (!updatedUsage) {
        console.error(`Failed to update usage record ${usage.id}: No data returned`);
        throw new Error('Failed to update usage record');
      }

      console.log(`Successfully decremented usage record ID: ${updatedUsage.id}, new images_count: ${updatedUsage.images_count}`);
      return updatedUsage;
    } catch (error) {
      console.error(`Error in decrementImageCount for user ${userId}:`, error);
      throw error;
    }
  }

  // Webhook event processing methods
  async getProcessedWebhookEvent(eventId: string): Promise<boolean> {
    return this.dbLogger.query(`getProcessedWebhookEvent(${eventId})`, async () => {
      try {
        // Check if this event has already been processed
        const { data, error } = await db.client
          .from('processed_webhook_events')
          .select('*')
          .eq('event_id', eventId)
          .single();

        if (error && error.code !== 'PGRST116') { // PGRST116 is "no rows returned" error
          console.error('Error checking processed webhook event:', error);
        }

        return !!data; // Return true if the event exists (has been processed)
      } catch (error) {
        console.error('Error checking processed webhook event:', error);
        return false; // Safer to return false and potentially process again
      }
    });
  }

  async saveProcessedWebhookEvent(eventId: string, eventType?: string): Promise<void> {
    return this.dbLogger.insert('processed_webhook_events', async () => {
      try {
        const now = new Date();

        // Insert the event as processed
        const { error } = await db.client
          .from('processed_webhook_events')
          .insert({
            event_id: eventId,
            event_type: eventType || 'unknown',
            processed_at: now.toISOString(),
            created_at: now.toISOString()
          });

        if (error) {
          // If it's a duplicate key error, that's fine - the event was already processed
          if (error.code === '23505') { // Unique constraint violation
            console.log(`Event ${eventId} already marked as processed`);
            return;
          }

          console.error('Error saving processed webhook event:', error);
          throw error;
        }
      } catch (error) {
        console.error('Error saving processed webhook event:', error);
        throw error;
      }
    });
  }

  // Coupon methods
  async getCoupon(id: number): Promise<Coupon | undefined> {
    return this.dbLogger.query(`getCoupon(${id})`, async () => {
      const { data: coupon } = await db.client
        .from('coupons')
        .select('*')
        .eq('id', id)
        .single();
      return coupon;
    });
  }

  async getCouponByCode(code: string): Promise<Coupon | undefined> {
    return this.dbLogger.query(`getCouponByCode(${code})`, async () => {
      const { data: coupon } = await db.client
        .from('coupons')
        .select('*')
        .eq('code', code)
        .single();
      return coupon;
    });
  }

  async getCouponByStripeId(stripeId: string): Promise<Coupon | undefined> {
    return this.dbLogger.query(`getCouponByStripeId(${stripeId})`, async () => {
      const { data: coupon } = await db.client
        .from('coupons')
        .select('*')
        .eq('stripe_coupon_id', stripeId)
        .single();
      return coupon;
    });
  }

  async listCoupons(includeInactive = false): Promise<Coupon[]> {
    return this.dbLogger.query(`listCoupons(includeInactive=${includeInactive})`, async () => {
      let query = db.client
        .from('coupons')
        .select('*');

      if (!includeInactive) {
        query = query.eq('is_active', true);
      }

      const { data: coupons } = await query.order('created_at', { ascending: false });
      return coupons || [];
    });
  }

  async createCoupon(coupon: InsertCoupon): Promise<Coupon> {
    return this.dbLogger.insert('coupons', async () => {
      try {
        // First check if a coupon with this code already exists
        const existingByCode = await this.getCouponByCode(coupon.code);
        if (existingByCode) {
          this.dbLogger.info(`Found existing coupon by code: ${coupon.code}`);
          return existingByCode;
        }

        // Then check if a coupon with this Stripe ID already exists
        if (coupon.stripe_coupon_id) {
          const existingByStripeId = await this.getCouponByStripeId(coupon.stripe_coupon_id);
          if (existingByStripeId) {
            this.dbLogger.info(`Found existing coupon by Stripe ID: ${coupon.stripe_coupon_id}`);
            return existingByStripeId;
          }
        }

        // If no existing coupon, create a new one
        const { data: newCoupon, error } = await db.client
          .from('coupons')
          .insert({
            ...coupon,
            code: coupon.code.toUpperCase(), // Ensure code is uppercase
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .select()
          .single();

        if (error) {
          // If there's a unique constraint violation, try to get the existing coupon
          if (error.code === '23505') { // PostgreSQL unique violation code
            this.dbLogger.warn(`Unique constraint violation while creating coupon: ${coupon.code}`);

            // Try to get by code first
            const retryByCode = await this.getCouponByCode(coupon.code);
            if (retryByCode) {
              this.dbLogger.info(`Retrieved existing coupon by code after conflict: ${coupon.code}`);
              return retryByCode;
            }

            // Then try by Stripe ID if available
            if (coupon.stripe_coupon_id) {
              const retryByStripeId = await this.getCouponByStripeId(coupon.stripe_coupon_id);
              if (retryByStripeId) {
                this.dbLogger.info(`Retrieved existing coupon by Stripe ID after conflict: ${coupon.stripe_coupon_id}`);
                return retryByStripeId;
              }
            }

            // If we still can't find it, something is wrong
            throw new Error(`Failed to create or retrieve coupon after unique constraint violation: ${error.message}`);
          }

          // For other errors, throw them
          this.dbLogger.error(`Error creating coupon: ${coupon.code}`, error);
          throw new Error(`Failed to create coupon: ${error.message}`);
        }

        if (!newCoupon) {
          this.dbLogger.error(`No data returned when creating coupon: ${coupon.code}`);
          throw new Error('Failed to create coupon: No data returned');
        }

        this.dbLogger.info(`Successfully created new coupon: ${coupon.code}`);
        return newCoupon;
      } catch (error) {
        this.dbLogger.error(`Failed to create coupon: ${coupon.code}`, error);
        throw new Error(`Failed to create coupon: ${(error as Error).message}`);
      }
    });
  }

  async updateCoupon(id: number, data: Partial<InsertCoupon>): Promise<Coupon | undefined> {
    return this.dbLogger.update(`coupons(${id})`, async () => {
      const { data: coupon } = await db.client
        .from('coupons')
        .update({
          ...data,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();
      return coupon;
    });
  }

  async deleteCoupon(id: number): Promise<void> {
    return this.dbLogger.delete(`coupons(${id})`, async () => {
      await db.client
        .from('coupons')
        .delete()
        .eq('id', id);
    });
  }

  async incrementCouponRedemptionCount(id: number): Promise<Coupon | undefined> {
    return this.dbLogger.update(`coupons(${id})`, async () => {
      // Get current coupon
      const { data: coupon } = await db.client
        .from('coupons')
        .select('*')
        .eq('id', id)
        .single();

      if (!coupon) return undefined;

      // Increment redemption count
      const { data: updatedCoupon } = await db.client
        .from('coupons')
        .update({
          redemption_count: coupon.redemption_count + 1,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      return updatedCoupon;
    });
  }

  // Coupon redemption methods
  async getCouponRedemption(id: number): Promise<CouponRedemption | undefined> {
    return this.dbLogger.query(`getCouponRedemption(${id})`, async () => {
      const { data: redemption } = await db.client
        .from('coupon_redemptions')
        .select('*')
        .eq('id', id)
        .single();
      return redemption;
    });
  }

  async getCouponRedemptionsByUserId(userId: string): Promise<CouponRedemption[]> {
    return this.dbLogger.query(`getCouponRedemptionsByUserId(${userId})`, async () => {
      const { data: redemptions } = await db.client
        .from('coupon_redemptions')
        .select('*')
        .eq('user_id', userId);
      return redemptions || [];
    });
  }

  async getCouponRedemptionsByCouponId(couponId: number): Promise<CouponRedemption[]> {
    return this.dbLogger.query(`getCouponRedemptionsByCouponId(${couponId})`, async () => {
      const { data: redemptions } = await db.client
        .from('coupon_redemptions')
        .select('*')
        .eq('coupon_id', couponId);
      return redemptions || [];
    });
  }

  async createCouponRedemption(redemption: InsertCouponRedemption): Promise<CouponRedemption> {
    return this.dbLogger.insert('coupon_redemptions', async () => {
      try {
        // Check if this user has already redeemed this coupon
        const hasRedeemed = await this.hasUserRedeemedCoupon(redemption.user_id, redemption.coupon_id);

        if (hasRedeemed) {
          // If already redeemed, get the existing redemption
          const { data: existingRedemptions } = await db.client
            .from('coupon_redemptions')
            .select('*')
            .eq('user_id', redemption.user_id)
            .eq('coupon_id', redemption.coupon_id);

          if (existingRedemptions && existingRedemptions.length > 0) {
            return existingRedemptions[0];
          }
        }

        // Create the redemption
        const { data: newRedemption, error } = await db.client
          .from('coupon_redemptions')
          .insert(redemption)
          .select()
          .single();

        if (error) {
          // If there's a unique constraint violation, try to get the existing redemption
          if (error.code === '23505') { // PostgreSQL unique violation code
            const { data: existingRedemptions } = await db.client
              .from('coupon_redemptions')
              .select('*')
              .eq('user_id', redemption.user_id)
              .eq('coupon_id', redemption.coupon_id);

            if (existingRedemptions && existingRedemptions.length > 0) {
              return existingRedemptions[0];
            }
          }

          throw new Error(`Failed to create coupon redemption: ${error.message}`);
        }

        if (!newRedemption) throw new Error('Failed to create coupon redemption: No data returned');
        return newRedemption;
      } catch (error) {
        throw new Error(`Failed to create coupon redemption: ${(error as Error).message}`);
      }
    });
  }

  async hasUserRedeemedCoupon(userId: string, couponId: number): Promise<boolean> {
    return this.dbLogger.query(`hasUserRedeemedCoupon(${userId}, ${couponId})`, async () => {
      const { data: redemptions } = await db.client
        .from('coupon_redemptions')
        .select('id')
        .eq('user_id', userId)
        .eq('coupon_id', couponId);
      return (redemptions || []).length > 0;
    });
  }
}

export const storage = new DatabaseStorage();
