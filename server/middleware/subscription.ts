import { Request, Response, NextFunction } from 'express';
import { subscriptionService } from '../services/subscription';
import { logger } from '../utils/logger';

/**
 * Middleware to check if a user has an active subscription
 *
 * Note: This middleware now allows free tier access with limited functionality
 * The actual limits are enforced by checkProjectLimit and checkImageLimit middleware
 */
export const requireSubscription = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Skip check if no user is authenticated (other middleware will handle this)
    if (!req.clerkUser?.id) {
      return next();
    }

    // Allow all authenticated users to access features
    // Limits will be enforced by checkProjectLimit and checkImageLimit middleware
    next();
  } catch (error) {
    logger.error('Error checking subscription status', { error });
    return res.status(500).json({
      error: 'Internal server error',
      message: 'An error occurred while checking subscription status'
    });
  }
};

/**
 * Middleware to check if a user has reached their project limit
 */
export const checkProjectLimit = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Skip check if no user is authenticated (other middleware will handle this)
    if (!req.clerkUser?.id) {
      return next();
    }

    const userId = req.clerkUser.id;
    const hasReachedLimit = await subscriptionService.hasReachedProjectLimit(userId);

    if (hasReachedLimit) {
      logger.warn(`User ${userId} attempted to create project but has reached limit`);
      return res.status(403).json({
        error: 'Project limit reached',
        message: 'You have reached your project limit for this billing period',
        code: 'project_limit_reached'
      });
    }

    next();
  } catch (error) {
    logger.error('Error checking project limit', { error });
    return res.status(500).json({
      error: 'Internal server error',
      message: 'An error occurred while checking project limit'
    });
  }
};

/**
 * Middleware to check if a user has reached their image limit for a project
 */
export const checkImageLimit = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Skip check if no user is authenticated (other middleware will handle this)
    if (!req.clerkUser?.id) {
      return next();
    }

    const userId = req.clerkUser.id;
    // Try to get project ID from different possible locations in the request
    const projectId = parseInt(req.params.id || req.params.projectId || req.body.projectId || req.body.project_id);

    if (isNaN(projectId)) {
      logger.error('Invalid project ID in image limit check', {
        params: req.params,
        body: req.body,
        url: req.url
      });
      return res.status(400).json({
        error: 'Invalid project ID',
        message: 'A valid project ID is required'
      });
    }

    logger.info(`Checking image limit for user ${userId} on project ${projectId}`);

    const hasReachedLimit = await subscriptionService.hasReachedImageLimit(userId, projectId);

    if (hasReachedLimit) {
      logger.warn(`User ${userId} attempted to add image but has reached limit for project ${projectId}`);
      return res.status(403).json({
        error: 'Image limit reached',
        message: 'You have reached your image limit for this project',
        code: 'image_limit_reached'
      });
    }

    next();
  } catch (error) {
    logger.error('Error checking image limit', { error });
    return res.status(500).json({
      error: 'Internal server error',
      message: 'An error occurred while checking image limit'
    });
  }
};
