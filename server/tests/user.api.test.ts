import request from 'supertest';
import app from '../app'; // Adjust if your Express app is exported elsewhere

describe('User API', () => {
  let token: string;
  let createdProjectId: number;

  beforeAll(async () => {
    // TODO: Replace with a real or mock Clerk test token
    token = process.env.TEST_CLERK_TOKEN || 'test-valid-jwt';
  });

  it('should create a new project', async () => {
    const res = await request(app)
      .post('/api/projects')
      .set('Authorization', `Bearer ${token}`)
      .send({ title: 'API Test Project', description: 'desc' });
    expect(res.status).toBe(201);
    expect(res.body.title).toBe('API Test Project');
    createdProjectId = res.body.id;
  });

  it('should fetch the created project', async () => {
    const res = await request(app)
      .get(`/api/projects/${createdProjectId}`)
      .set('Authorization', `Bearer ${token}`);
    expect(res.status).toBe(200);
    expect(res.body.id).toBe(createdProjectId);
  });

  it('should delete the created project', async () => {
    const res = await request(app)
      .delete(`/api/projects/${createdProjectId}`)
      .set('Authorization', `Bearer ${token}`);
    expect([200, 204, 404]).toContain(res.status); // Accept 404 if already deleted
  });
}); 