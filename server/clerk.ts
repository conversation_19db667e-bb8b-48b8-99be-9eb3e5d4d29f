import { Express, Request, Response, NextFunction } from 'express';
import { clerkClient } from '@clerk/express';
import * as jose from 'jose';

// Configure logging for Clerk authentication
const logger = {
  debug: (message: string, ...args: any[]) => {
    console.debug(`[CLERK-AUTH] ${message}`, ...args);
  },
  info: (message: string, ...args: any[]) => {
    console.info(`[CLERK-AUTH] ${message}`, ...args);
  },
  warn: (message: string, ...args: any[]) => {
    console.warn(`[CLERK-AUTH] ${message}`, ...args);
  },
  error: (message: string, ...args: any[]) => {
    console.error(`[CLERK-AUTH] ${message}`, ...args);
  }
};

// Middleware to verify Clerk auth tokens
const requireAuth = (req: Request, res: Response, next: NextFunction) => {
  try {
    console.log('[DEBUG-CLERK] requireAuth - Processing authentication request for path:', req.path);
    logger.debug('Processing authentication request', {
      path: req.path,
      method: req.method,
      hasAuthHeader: !!req.headers.authorization,
      headers: Object.keys(req.headers)
    });

    // Check for authorization header
    const authHeader = req.headers.authorization;
    console.log('[DEBUG-CLERK] requireAuth - Auth header exists:', !!authHeader);

    // For development/testing only - bypass auth for specific endpoints
    const bypassAuthForTesting = process.env.NODE_ENV === 'development' &&
                                (req.path === '/api/create-payment-intent');

    if (bypassAuthForTesting) {
      console.log('[DEBUG-CLERK] requireAuth - Bypassing auth for testing on path:', req.path);
      // Set a test user ID for development
      req.clerkUser = {
        id: 'user_test123',
        sessionId: 'sess_test123',
        issuedAt: new Date(),
        expiresAt: new Date(Date.now() + 3600 * 1000)
      };
      return next();
    }

    // Normal auth flow
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      console.log('[DEBUG-CLERK] requireAuth - Missing or invalid token format');
      if (authHeader) {
        console.log('[DEBUG-CLERK] requireAuth - Auth header preview:', authHeader.substring(0, 20));
      }

      logger.warn('Missing or invalid token format', {
        path: req.path,
        authHeader: authHeader ? `${authHeader.substring(0, 15)}...` : 'undefined'
      });

      // For subscription-related endpoints, return default data instead of 401
      if (req.path.includes('/api/subscription')) {
        console.log('[DEBUG-CLERK] requireAuth - Subscription endpoint with no auth, returning default data');

        // Set a flag to indicate this is an unauthenticated request
        req.isUnauthenticated = true;

        // Allow the request to continue to the handler
        return next();
      }

      return res.status(401).json({ message: "Unauthorized: Missing or invalid token format" });
    }

    const token = authHeader.split('Bearer ')[1];

    // Validate token before proceeding
    if (!token || token === 'null' || token === 'undefined' || token.trim() === '') {
      console.log('[DEBUG-CLERK] requireAuth - Token is null, undefined, or empty');
      logger.warn('Invalid token format - token is null, undefined, or empty');
      return res.status(401).json({ message: "Unauthorized: Invalid token format" });
    }

    console.log('[DEBUG-CLERK] requireAuth - Token extracted, preview:', `${token.substring(0, 15)}...`);
    logger.debug('Token extracted from Authorization header', {
      tokenPreview: `${token.substring(0, 10)}...`
    });

    try {
      // In production, we should use Clerk's SDK for verification
      // For now, we'll decode the JWT to get the user ID
      console.log('[DEBUG-CLERK] requireAuth - Attempting to decode token');

      // Additional validation before decoding
      if (!token.includes('.') || token.split('.').length !== 3) {
        console.log('[DEBUG-CLERK] requireAuth - Token does not have valid JWT format');
        logger.warn('Invalid JWT format - token does not have three parts separated by dots');
        return res.status(401).json({ message: "Unauthorized: Invalid token format" });
      }

      const decoded = jose.decodeJwt(token);
      console.log('[DEBUG-CLERK] requireAuth - Token decoded successfully');
      console.log('[DEBUG-CLERK] requireAuth - User ID from token:', decoded.sub);
      console.log('[DEBUG-CLERK] requireAuth - Token claims:', Object.keys(decoded));

      if (decoded.exp) {
        const expiryDate = new Date(decoded.exp * 1000);
        console.log('[DEBUG-CLERK] requireAuth - Token expires at:', expiryDate.toISOString());
      }

      logger.debug('Token decoded', {
        userId: decoded.sub,
        tokenExpiry: decoded.exp ? new Date(decoded.exp * 1000).toISOString() : 'unknown',
        tokenIssued: decoded.iat ? new Date(decoded.iat * 1000).toISOString() : 'unknown',
        claims: Object.keys(decoded)
      });

      // Check if token is expired
      if (decoded.exp && decoded.exp < Math.floor(Date.now() / 1000)) {
        console.log('[DEBUG-CLERK] requireAuth - Token is expired');
        logger.warn('Token expired', {
          expiry: new Date(decoded.exp * 1000).toISOString(),
          now: new Date().toISOString()
        });
        return res.status(401).json({ message: "Unauthorized: Token expired" });
      }

      if (!decoded.sub) {
        console.log('[DEBUG-CLERK] requireAuth - Token missing subject claim');
        logger.warn('Invalid token - no subject claim found');
        return res.status(401).json({ message: "Unauthorized: Invalid session" });
      }

      // Add user details to the request for use in route handlers
      req.clerkUser = {
        id: decoded.sub,
        // Add more decoded claims as needed
        sessionId: decoded.sid || null,
        issuedAt: decoded.iat ? new Date(decoded.iat * 1000) : null,
        expiresAt: decoded.exp ? new Date(decoded.exp * 1000) : null
      };

      console.log('[DEBUG-CLERK] requireAuth - User authenticated successfully:', decoded.sub);
      console.log('[DEBUG-CLERK] requireAuth - Added clerkUser to request:', req.clerkUser);

      logger.info('User authenticated successfully', {
        userId: decoded.sub,
        path: req.path
      });

      next();
    } catch (err) {
      console.log('[DEBUG-CLERK] requireAuth - Token verification error:', err);
      logger.error('Token verification error:', err);
      console.error('Token verification error details:', err);
      return res.status(401).json({ message: "Unauthorized: Invalid token" });
    }
  } catch (error) {
    console.log('[DEBUG-CLERK] requireAuth - Auth middleware error:', error);
    logger.error('Auth middleware error:', error);
    console.error('Auth middleware error details:', error);
    return res.status(500).json({ message: "Internal server error during authentication" });
  }
};

// Middleware that can be used as a drop-in replacement for isAuthenticated
export const clerkAuth = (req: Request, res: Response, next: NextFunction) => {
  console.log('[DEBUG-CLERK] clerkAuth middleware called for path:', req.path);
  console.log('[DEBUG-CLERK] Authorization header present:', !!req.headers.authorization);
  if (req.headers.authorization) {
    console.log('[DEBUG-CLERK] Auth header preview:', req.headers.authorization.substring(0, 20) + '...');
  }

  // Check for session cookie first (if we're using Clerk's session cookies)
  if (req.cookies && req.cookies.__session) {
    console.log('[DEBUG-CLERK] Session cookie found, attempting to validate');
    logger.debug('Session cookie found, attempting to validate');
    // Process session cookie logic here
    // For now, we'll fall back to token-based auth
  } else {
    console.log('[DEBUG-CLERK] No session cookie found');
  }

  // Fall back to token-based auth
  console.log('[DEBUG-CLERK] Proceeding with token-based auth');
  return requireAuth(req, res, next);
};

// Add isAuthenticated method to all requests
export const addAuthHelpers = (req: Request, res: Response, next: NextFunction) => {
  // Add isAuthenticated method to the request object
  req.isAuthenticated = function() {
    return !!req.clerkUser && !!req.clerkUser.id;
  };

  // Add user property for backward compatibility
  if (req.clerkUser) {
    req.user = {
      id: req.clerkUser.id,
      // Add other properties as needed
    };
  }

  next();
};

// Initialize Clerk with proper configuration
export function initializeClerk() {
  // Check if CLERK_SECRET_KEY is set
  if (!process.env.CLERK_SECRET_KEY) {
    logger.warn('CLERK_SECRET_KEY is not set. Authentication will not work properly.');
  } else {
    logger.info('Clerk initialized with secret key');
  }
}

export function setupClerkRoutes(app: Express) {
  // Initialize Clerk
  initializeClerk();

  // Add auth helpers to all requests
  app.use(addAuthHelpers);

  // API endpoint to get user details
  app.get("/api/auth/user", requireAuth, async (req: Request, res: Response) => {
    try {
      if (!req.clerkUser || !req.clerkUser.id) {
        logger.warn('User not authenticated in /api/auth/user endpoint');
        return res.status(401).json({ message: "Unauthorized: User not authenticated" });
      }

      logger.debug('Fetching user details from Clerk', { userId: req.clerkUser.id });

      try {
        const user = await clerkClient.users.getUser(req.clerkUser.id);
        logger.info('User details fetched successfully', { userId: user.id });

        return res.json({
          id: user.id,
          firstName: user.firstName,
          lastName: user.lastName,
          emailAddress: user.emailAddresses[0]?.emailAddress,
          imageUrl: user.imageUrl,
          username: user.username || user.firstName || 'User',
        });
      } catch (clerkError) {
        logger.error('Error fetching user from Clerk API:', clerkError);

        // If we can't fetch from Clerk, return basic user info from the token
        return res.json({
          id: req.clerkUser.id,
          username: 'User',
        });
      }
    } catch (error) {
      logger.error('Error in /api/auth/user endpoint:', error);
      return res.status(500).json({ message: "Failed to fetch user data" });
    }
  });

  // Add a health check endpoint for Clerk authentication
  app.get("/api/auth/status", (req: Request, res: Response) => {
    const isClerkConfigured = !!process.env.CLERK_SECRET_KEY;
    logger.debug('Auth status check', { isClerkConfigured });

    res.json({
      status: "ok",
      auth: {
        provider: "clerk",
        configured: isClerkConfigured,
      }
    });
  });

  // Add a debug endpoint to check authentication
  app.get("/api/auth/debug", (req: Request, res: Response) => {
    console.log('[DEBUG-CLERK] Debug endpoint called');
    console.log('[DEBUG-CLERK] Headers:', req.headers);

    const authHeader = req.headers.authorization;
    const hasToken = !!authHeader && authHeader.startsWith('Bearer ');

    let tokenInfo = null;
    if (hasToken) {
      try {
        const token = authHeader.split('Bearer ')[1];
        const decoded = jose.decodeJwt(token);
        tokenInfo = {
          sub: decoded.sub,
          exp: decoded.exp ? new Date(decoded.exp * 1000).toISOString() : null,
          iat: decoded.iat ? new Date(decoded.iat * 1000).toISOString() : null,
          claims: Object.keys(decoded)
        };
      } catch (err) {
        tokenInfo = { error: 'Failed to decode token' };
      }
    }

    res.json({
      authenticated: !!req.clerkUser,
      hasAuthHeader: !!authHeader,
      hasValidTokenFormat: hasToken,
      user: req.clerkUser || null,
      tokenInfo
    });
  });
}

// Add isAuthenticated method to Request
export function isAuthenticated(req: Request): boolean {
  return !!req.clerkUser && !!req.clerkUser.id;
}

// Add User interface to Express Request
declare global {
  namespace Express {
    interface Request {
      clerkUser?: {
        id: string;
        sessionId: string | null;
        issuedAt: Date | null;
        expiresAt: Date | null;
      };
      isAuthenticated: () => boolean;
      isUnauthenticated?: boolean; // Flag for requests that continue without auth
      user: any; // For backward compatibility
    }
  }
}