import { stripe } from '../lib/stripe';
import { logger } from '../utils/logger';
import { SubscriptionStatus } from '../types/subscription';

export async function getCustomerByUserId(userId: string) {
  try {
    const customers = await stripe.customers.list({
      email: userId,
      limit: 1,
    });

    logger.info('Retrieved customer for user', { userId });
    return customers.data[0];
  } catch (error) {
    logger.error('Error retrieving customer', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId,
    });
    throw error;
  }
}

export async function handleSubscriptionChange(
  customerId: string,
  subscriptionId: string,
  newPriceId: string,
  currentSubscription: any
) {
  try {
    logger.info('Processing subscription change', {
      customerId,
      subscriptionId,
      newPriceId,
      currentStatus: currentSubscription.status,
    });

    // Handle different subscription states
    switch (currentSubscription.status) {
      case 'active':
        if (currentSubscription.cancel_at_period_end) {
          // Reactivating a subscription that was set to cancel
          const updatedSubscription = await stripe.subscriptions.update(subscriptionId, {
            cancel_at_period_end: false,
            proration_behavior: 'create_prorations',
            items: [{
              id: currentSubscription.items.data[0].id,
              price: newPriceId,
            }],
          });

          logger.info('Reactivated subscription with new plan', {
            customerId,
            subscriptionId,
            newPriceId,
          });

          return updatedSubscription;
        } else {
          // Regular plan change for active subscription
          const updatedSubscription = await stripe.subscriptions.update(subscriptionId, {
            proration_behavior: 'create_prorations',
            items: [{
              id: currentSubscription.items.data[0].id,
              price: newPriceId,
            }],
          });

          logger.info('Updated active subscription plan', {
            customerId,
            subscriptionId,
            newPriceId,
          });

          return updatedSubscription;
        }

      case 'past_due':
        logger.warn('Attempting to change past due subscription', {
          customerId,
          subscriptionId,
        });
        throw new Error('Cannot change plan while subscription is past due');

      case 'canceled':
        // Create new subscription for canceled subscriptions
        const newSubscription = await stripe.subscriptions.create({
          customer: customerId,
          items: [{
            price: newPriceId,
          }],
          payment_behavior: 'default_incomplete',
          expand: ['latest_invoice.payment_intent'],
        });

        logger.info('Created new subscription after cancellation', {
          customerId,
          newSubscriptionId: newSubscription.id,
          newPriceId,
        });

        return newSubscription;

      case 'incomplete':
      case 'incomplete_expired':
        // Cancel the incomplete subscription and create a new one
        await stripe.subscriptions.del(subscriptionId);
        const subscription = await stripe.subscriptions.create({
          customer: customerId,
          items: [{
            price: newPriceId,
          }],
          payment_behavior: 'default_incomplete',
          expand: ['latest_invoice.payment_intent'],
        });

        logger.info('Replaced incomplete subscription', {
          customerId,
          oldSubscriptionId: subscriptionId,
          newSubscriptionId: subscription.id,
          newPriceId,
        });

        return subscription;

      default:
        logger.error('Invalid subscription status for plan change', {
          customerId,
          subscriptionId,
          status: currentSubscription.status,
        });
        throw new Error(`Cannot change plan with current subscription status: ${currentSubscription.status}`);
    }
  } catch (error) {
    logger.error('Error handling subscription change', {
      error: error instanceof Error ? error.message : 'Unknown error',
      customerId,
      subscriptionId,
      newPriceId,
    });
    throw error;
  }
}

export async function getSubscriptionStatus(subscriptionId: string): Promise<SubscriptionStatus> {
  try {
    const subscription = await stripe.subscriptions.retrieve(subscriptionId);
    
    logger.info('Retrieved subscription status', {
      subscriptionId,
      status: subscription.status,
    });

    return {
      status: subscription.status,
      cancelAtPeriodEnd: subscription.cancel_at_period_end,
      currentPeriodEnd: new Date(subscription.current_period_end * 1000).toISOString(),
      plan: subscription.items.data[0].price.lookup_key?.split('_')[0] || '',
      interval: subscription.items.data[0].price.lookup_key?.split('_')[1] || '',
    };
  } catch (error) {
    logger.error('Error retrieving subscription status', {
      error: error instanceof Error ? error.message : 'Unknown error',
      subscriptionId,
    });
    throw error;
  }
}

export async function validatePriceId(priceId: string) {
  try {
    const price = await stripe.prices.retrieve(priceId);
    
    if (!price.active) {
      logger.warn('Attempted to use inactive price', { priceId });
      throw new Error('Selected price is not active');
    }

    logger.info('Validated price', { priceId });
    return price;
  } catch (error) {
    logger.error('Error validating price', {
      error: error instanceof Error ? error.message : 'Unknown error',
      priceId,
    });
    throw error;
  }
} 