import nodemailer from 'nodemailer';
import { logger } from '../utils/logger';

// Email configuration
const SMTP_HOST = process.env.SMTP_HOST || 'smtp.gmail.com';
const SMTP_PORT = parseInt(process.env.SMTP_PORT || '587', 10);
const SMTP_USER = process.env.SMTP_USER || '<EMAIL>';
const SMTP_PASS = process.env.SMTP_PASS || '';
const EMAIL_FROM = process.env.EMAIL_FROM || '<EMAIL>';
const CONTACT_EMAIL = process.env.CONTACT_EMAIL || '<EMAIL>';

// For development/testing, we'll use Ethereal (fake SMTP service)
let testAccount: nodemailer.TestAccount | null = null;

// Create a transporter object
async function createTransporter() {
  // Always use Ethereal for development to avoid real email sending issues
  if (process.env.NODE_ENV !== 'production') {
    if (!testAccount) {
      logger.info('Creating test email account with Ethereal...');
      try {
        testAccount = await nodemailer.createTestAccount();
        logger.info(`Test email account created: ${testAccount.user}`);
      } catch (error) {
        logger.error('Error creating test email account:', error);
        // Fallback to a console transport that just logs emails
        return {
          sendMail: async (mailOptions: any) => {
            logger.info('Email would have been sent:', mailOptions);
            return { messageId: 'test-message-id' };
          }
        } as any;
      }
    }

    return nodemailer.createTransport({
      host: 'smtp.ethereal.email',
      port: 587,
      secure: false,
      auth: {
        user: testAccount?.user || 'test',
        pass: testAccount?.pass || 'test',
      },
    });
  }

  // Use real SMTP settings for production
  logger.info(`Setting up production email with: ${SMTP_HOST}:${SMTP_PORT}`);

  // Log the credentials being used (without showing the actual password)
  logger.info(`Using email credentials - User: ${SMTP_USER}, Password: ${SMTP_PASS ? '********' : 'not set'}`);

  const transport = nodemailer.createTransport({
    host: SMTP_HOST,
    port: SMTP_PORT,
    secure: SMTP_PORT === 465, // true for 465, false for other ports
    auth: {
      user: SMTP_USER,
      pass: SMTP_PASS,
    },
    logger: false, // Disable detailed logging for security
    debug: false, // Disable debug information for security
  });

  // Verify the connection configuration
  try {
    await transport.verify();
    logger.info('SMTP connection verified successfully');
  } catch (error) {
    logger.error('SMTP connection verification failed:', error);
    // Still return the transport - we'll handle errors when sending
  }

  return transport;
}

/**
 * Send an email
 * @param to Recipient email address
 * @param subject Email subject
 * @param text Plain text email body
 * @param html HTML email body (optional)
 */
export async function sendEmail(to: string, subject: string, text: string, html?: string) {
  try {
    const transporter = await createTransporter();

    const info = await transporter.sendMail({
      from: `"Renovision.Studio" <${EMAIL_FROM}>`,
      to,
      subject,
      text,
      html: html || text,
    });

    logger.info(`Email sent: ${info.messageId}`);

    // If using Ethereal, log the preview URL
    if (testAccount) {
      logger.info(`Preview URL: ${nodemailer.getTestMessageUrl(info)}`);
    }

    return info;
  } catch (error) {
    logger.error('Error sending email:', error);
    throw new Error('Failed to send email');
  }
}

/**
 * Send a contact form notification email
 * @param name Name from the contact form
 * @param email Email from the contact form
 * @param subject Subject from the contact form
 * @param message Message from the contact form
 */
export async function sendContactNotification(name: string, email: string, subject: string, message: string) {
  const emailSubject = `New Contact Form Submission: ${subject}`;
  const emailText = `
    You have received a new contact form submission from Renovision.Studio:

    Name: ${name}
    Email: ${email}
    Subject: ${subject}

    Message:
    ${message}

    ---
    This email was sent automatically from the Renovision.Studio contact form.
  `;

  const emailHtml = `
    <h2>New Contact Form Submission</h2>
    <p>You have received a new contact form submission from Renovision.Studio:</p>

    <table style="border-collapse: collapse; width: 100%; margin-bottom: 20px;">
      <tr>
        <td style="padding: 8px; border: 1px solid #ddd; width: 100px;"><strong>Name:</strong></td>
        <td style="padding: 8px; border: 1px solid #ddd;">${name}</td>
      </tr>
      <tr>
        <td style="padding: 8px; border: 1px solid #ddd;"><strong>Email:</strong></td>
        <td style="padding: 8px; border: 1px solid #ddd;"><a href="mailto:${email}">${email}</a></td>
      </tr>
      <tr>
        <td style="padding: 8px; border: 1px solid #ddd;"><strong>Subject:</strong></td>
        <td style="padding: 8px; border: 1px solid #ddd;">${subject}</td>
      </tr>
    </table>

    <h3>Message:</h3>
    <div style="padding: 15px; background-color: #f9f9f9; border-radius: 5px; margin-bottom: 20px;">
      ${message.replace(/\n/g, '<br>')}
    </div>

    <p style="color: #777; font-size: 12px; margin-top: 30px; border-top: 1px solid #eee; padding-top: 10px;">
      This email was sent automatically from the Renovision.Studio contact form.
    </p>
  `;

  return sendEmail(CONTACT_EMAIL, emailSubject, emailText, emailHtml);
}

/**
 * Send an auto-reply to the contact form submitter
 * @param name Name from the contact form
 * @param email Email from the contact form
 * @param subject Subject from the contact form
 */
export async function sendContactAutoReply(name: string, email: string, subject: string) {
  const emailSubject = `Thank you for contacting Renovision.Studio`;
  const emailText = `
    Dear ${name},

    Thank you for contacting Renovision.Studio. We have received your message regarding "${subject}".

    Our team will review your inquiry and get back to you as soon as possible. We typically respond within 1-2 business days.

    If your matter is urgent, please reply to this email with "URGENT" in the subject line.

    Best regards,
    The Renovision.Studio Team
  `;

  const emailHtml = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #333;">Thank You for Contacting Us</h2>

      <p>Dear ${name},</p>

      <p>Thank you for contacting Renovision.Studio. We have received your message regarding <strong>"${subject}"</strong>.</p>

      <p>Our team will review your inquiry and get back to you as soon as possible. We typically respond within 1-2 business days.</p>

      <p>If your matter is urgent, please reply to this email with "URGENT" in the subject line.</p>

      <p>Best regards,<br>
      The Renovision.Studio Team</p>

      <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; text-align: center;">
        <img src="https://renovision.studio/images/logo/Renovision_transparent.png" alt="Renovision.Studio" style="max-width: 100px;">
        <p style="color: #777; font-size: 12px;">
          Made with ❤️ in Canberra, Australia
        </p>
      </div>
    </div>
  `;

  return sendEmail(email, emailSubject, emailText, emailHtml);
}
