import Stripe from 'stripe';
import { storage } from '../storage';
import { InsertSubscription, InsertUsage } from '@shared/schema';
import { logger } from '../utils/logger';

// Initialize Stripe with the secret key
if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error('Missing required Stripe secret: STRIPE_SECRET_KEY');
}

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: "2025-03-31.basil" as any,
});

// Define plan limits
export const PLAN_LIMITS = {
  starter: {
    monthly: {
      price: 17.99,
      projects: 3,
      imagesPerProject: 3,
      displayName: "Starter"
    },
    annual: {
      price: 14.99,
      annualPrice: 179.88, // 14.99 * 12 = 179.88 (20% discount from monthly)
      projects: 3,
      imagesPerProject: 3,
      displayName: "Starter"
    }
  },
  professional: {
    monthly: {
      price: 58.80,
      projects: 10,
      imagesPerProject: 5,
      displayName: "Professional"
    },
    annual: {
      price: 49,
      annualPrice: 588.00, // 49 * 12 = 588.00 (20% discount from monthly)
      projects: 10,
      imagesPerProject: 5,
      displayName: "Professional"
    }
  }
};

export class SubscriptionService {
  /**
   * Create a Stripe customer for a user
   */
  async createCustomer(userId: string, email: string | null, name: string | null): Promise<string> {
    try {
      const customer = await stripe.customers.create({
        email: email || undefined,
        name: name || undefined,
        metadata: {
          userId
        }
      });

      logger.info(`Created Stripe customer for user ${userId}`, { customerId: customer.id });
      return customer.id;
    } catch (error) {
      logger.error('Error creating Stripe customer', 'system', { error, userId });
      throw new Error('Failed to create customer');
    }
  }

  /**
   * Create a subscription for a user
   */
  async createSubscription(
    userId: string,
    planId: string,
    billingCycle: 'monthly' | 'annual',
    stripeCustomerId: string,
    stripeSubscriptionId?: string
  ): Promise<InsertSubscription> {
    try {
      // Calculate period dates based on billing cycle
      const now = new Date();
      const periodEnd = new Date(now);

      if (billingCycle === 'annual') {
        periodEnd.setFullYear(periodEnd.getFullYear() + 1);
      } else {
        periodEnd.setMonth(periodEnd.getMonth() + 1);
      }

      const subscription: InsertSubscription = {
        user_id: userId,
        stripe_customer_id: stripeCustomerId,
        stripe_subscription_id: stripeSubscriptionId || null,
        plan_id: planId,
        status: 'active',
        current_period_start: now,
        current_period_end: periodEnd,
        cancel_at_period_end: false,
        billing_cycle: billingCycle
      };

      // Create usage record for the subscription period
      const usage: InsertUsage = {
        user_id: userId,
        projects_count: 0,
        images_count: 0,
        period_start: now,
        period_end: periodEnd
      };

      // Use transaction to ensure both operations succeed or fail together
      try {
        logger.info(`Creating subscription for user ${userId} with transaction`, { planId, billingCycle });
        await storage.createSubscriptionWithUsage(subscription, usage);
        logger.info(`Successfully created subscription and usage for user ${userId}`, { planId, billingCycle });
      } catch (transactionError: any) {
        logger.error('Transaction failed when creating subscription', {
          error: transactionError,
          errorCode: transactionError.code,
          userId,
          planId
        });

        // Handle specific error types
        if (transactionError.code === '23505') { // Unique constraint violation
          logger.warn('Duplicate subscription record', { userId, planId });

          // Get the existing subscription
          const existingSubscription = await storage.getSubscription(userId);
          if (existingSubscription) {
            // Update the existing subscription if needed
            if (existingSubscription.stripe_subscription_id !== stripeSubscriptionId ||
                existingSubscription.plan_id !== planId ||
                existingSubscription.billing_cycle !== billingCycle) {

              await storage.updateSubscription(userId, {
                stripe_subscription_id: stripeSubscriptionId || null,
                plan_id: planId,
                billing_cycle: billingCycle,
                status: 'active',
                current_period_start: now,
                current_period_end: periodEnd,
                cancel_at_period_end: false
              });

              logger.info(`Updated existing subscription for user ${userId}`, { planId, billingCycle });
            }

            // Return the existing subscription data with our updates
            return {
              ...subscription,
              id: existingSubscription.id,
              created_at: existingSubscription.created_at,
              updated_at: new Date().toISOString()
            };
          }
        } else if (transactionError.code === '23503') { // Foreign key violation
          logger.error('Invalid user ID for subscription', { userId });
          throw new Error(`User not found: ${userId}`);
        } else {
          // Attempt to create records individually as fallback for other errors
          try {
            logger.info('Attempting fallback to individual operations');
            await storage.createSubscription(subscription);
            await storage.createUsage(usage);
            logger.info('Fallback successful - created subscription and usage individually');
          } catch (fallbackError: any) {
            logger.error('Fallback operations failed', {
              error: fallbackError,
              errorCode: fallbackError.code,
              userId,
              planId
            });

            // Handle specific fallback errors
            if (fallbackError.code === '23505') { // Unique constraint violation
              logger.warn('Duplicate subscription record in fallback', { userId, planId });
              // Get the existing subscription
              const existingSubscription = await storage.getSubscription(userId);
              if (existingSubscription) {
                return {
                  ...subscription,
                  id: existingSubscription.id,
                  created_at: existingSubscription.created_at,
                  updated_at: existingSubscription.updated_at
                };
              }
            }

            throw fallbackError;
          }
        }
      }

      return subscription;
    } catch (error: any) {
      logger.error('Error creating subscription', {
        error,
        errorCode: error.code,
        userId,
        planId
      });

      if (error.code === '23505') {
        throw new Error(`Subscription already exists for user: ${userId}`);
      } else if (error.code === '23503') {
        throw new Error(`User not found: ${userId}`);
      } else {
        throw new Error(`Failed to create subscription: ${error.message || 'Unknown error'}`);
      }
    }
  }

  /**
   * Check if a user has an active subscription
   */
  async hasActiveSubscription(userId: string): Promise<boolean> {
    try {
      const subscription = await storage.getSubscription(userId);
      return !!subscription && subscription.status === 'active';
    } catch (error) {
      logger.error('Error checking subscription status', { error, userId });
      return false;
    }
  }

  /**
   * Check if a user has reached their project limit
   */
  async hasReachedProjectLimit(userId: string): Promise<boolean> {
    try {
      const subscription = await storage.getSubscription(userId);

      // If no subscription, use free tier limit (1 project)
      if (!subscription) {
        const usage = await storage.getCurrentUsage(userId);
        if (!usage) {
          return false; // No usage record means no projects created yet
        }
        // Free tier allows 1 project
        return usage.projects_count >= 1;
      }

      // Check if subscription is active
      if (subscription.status !== 'active') {
        return true; // Inactive subscription means limit reached
      }

      const usage = await storage.getCurrentUsage(userId);
      if (!usage) {
        return false; // No usage record means no projects created yet
      }

      const planLimits = PLAN_LIMITS[subscription.plan_id as keyof typeof PLAN_LIMITS]?.[subscription.billing_cycle as 'monthly' | 'annual'];
      if (!planLimits) {
        logger.error('Invalid plan or billing cycle', { userId, plan: subscription.plan_id, cycle: subscription.billing_cycle });
        return true; // Invalid plan means limit reached
      }

      return usage.projects_count >= planLimits.projects;
    } catch (error) {
      logger.error('Error checking project limit', { error, userId });
      return true; // Error means limit reached (safer)
    }
  }

  /**
   * Check if a user has reached their image limit for a project
   */
  async hasReachedImageLimit(userId: string, projectId: number): Promise<boolean> {
    try {
      const subscription = await storage.getSubscription(userId);

      // If no subscription, use free tier limit (1 image per project)
      if (!subscription) {
        // Count images for this project
        const images = await storage.getImagesByProjectId(projectId);
        // Free tier allows 1 image per project
        return images.length >= 1;
      }

      // Check if subscription is active
      if (subscription.status !== 'active') {
        return true; // Inactive subscription means limit reached
      }

      const planLimits = PLAN_LIMITS[subscription.plan_id as keyof typeof PLAN_LIMITS]?.[subscription.billing_cycle as 'monthly' | 'annual'];
      if (!planLimits) {
        logger.error('Invalid plan or billing cycle', { userId, plan: subscription.plan_id, cycle: subscription.billing_cycle });
        return true; // Invalid plan means limit reached
      }

      // Count images for this project
      const images = await storage.getImagesByProjectId(projectId);
      return images.length >= planLimits.imagesPerProject;
    } catch (error) {
      logger.error('Error checking image limit', { error, userId, projectId });
      return true; // Error means limit reached (safer)
    }
  }

  /**
   * Handle Stripe webhook events
   */
  async handleWebhookEvent(event: any): Promise<void> {
    try {
      switch (event.type) {
        case 'checkout.session.completed':
          await this.handleCheckoutSessionCompleted(event.data.object);
          break;
        case 'customer.subscription.created':
        case 'customer.subscription.updated':
          await this.handleSubscriptionUpdated(event.data.object);
          break;
        case 'customer.subscription.deleted':
          await this.handleSubscriptionDeleted(event.data.object);
          break;
        case 'invoice.payment_succeeded':
          await this.handlePaymentSucceeded(event.data.object);
          break;
        case 'invoice.payment_failed':
          await this.handlePaymentFailed(event.data.object);
          break;
        default:
          logger.info(`Unhandled webhook event: ${event.type}`);
      }
    } catch (error) {
      logger.error('Error handling webhook event', { error, eventType: event.type });
      throw error;
    }
  }

  /**
   * Handle checkout.session.completed webhook event
   */
  private async handleCheckoutSessionCompleted(session: Stripe.Checkout.Session): Promise<void> {
    try {
      logger.info(`Processing checkout session completed: ${session.id}`, {
        metadata: session.metadata,
        hasCustomer: !!session.customer,
        hasSubscription: !!session.subscription,
        mode: session.mode
      });

      // Check if this is a subscription checkout
      if (session.mode !== 'subscription') {
        logger.info(`Ignoring non-subscription checkout session: ${session.id}`);
        return;
      }

      // Get the customer ID
      let customerId: string;

      // Handle different formats of customer data
      if (typeof session.customer === 'string') {
        customerId = session.customer;
      } else if (typeof session.customer === 'object' && session.customer !== null) {
        // If it's an object, extract the ID
        customerId = (session.customer as any).id || '';
      } else {
        logger.error('Customer ID not found in checkout session', {
          sessionId: session.id,
          customerType: typeof session.customer,
          customerValue: session.customer
        });
        return;
      }

      if (!customerId) {
        logger.error('Empty customer ID in checkout session', { sessionId: session.id });
        return;
      }

      // Get the subscription ID
      let subscriptionId: string;

      // Handle different formats of subscription data
      if (typeof session.subscription === 'string') {
        subscriptionId = session.subscription;
      } else if (typeof session.subscription === 'object' && session.subscription !== null) {
        // If it's an object, extract the ID
        subscriptionId = (session.subscription as any).id || '';
      } else {
        logger.error('Subscription ID not found in checkout session', {
          sessionId: session.id,
          subscriptionType: typeof session.subscription,
          subscriptionValue: session.subscription
        });
        return;
      }

      if (!subscriptionId) {
        logger.error('Empty subscription ID in checkout session', { sessionId: session.id });
        return;
      }

      // Get metadata from the session
      let userId = session.metadata?.userId;
      const planId = session.metadata?.planId;
      const billingCycle = session.metadata?.billingCycle;

      // If userId is missing from metadata, try to get it from the customer object
      if (!userId) {
        try {
          // Get the customer details from Stripe
          const customer = await stripe.customers.retrieve(customerId);

          // Check if customer has userId in metadata
          if (customer && 'metadata' in customer && customer.metadata.userId) {
            userId = customer.metadata.userId;
            logger.info(`Found userId in customer metadata: ${userId}`);
          } else if (customer && 'email' in customer && customer.email) {
            // Try to find user by email
            logger.info(`Trying to find user by email: ${customer.email}`);
            // This would require additional implementation to look up users by email
          }
        } catch (customerError) {
          logger.error('Error retrieving customer details', { error: customerError, customerId });
        }
      }

      if (!userId) {
        logger.error('Could not determine userId for checkout session', {
          sessionId: session.id,
          customerId
        });
        return;
      }

      if (!planId || !billingCycle) {
        logger.error('Missing plan metadata in checkout session', {
          sessionId: session.id,
          planId: planId || 'missing',
          billingCycle: billingCycle || 'missing'
        });
        return;
      }

      // Get the subscription details from Stripe
      const subscription = await stripe.subscriptions.retrieve(subscriptionId);

      // Handle coupon if present
      let couponRedemptionId: number | undefined;
      if (subscription.discount?.coupon?.id) {
        try {
          const stripeCouponId = subscription.discount.coupon.id;
          logger.info(`Processing coupon for subscription: ${stripeCouponId}`);

          // Get or create the coupon in our database
          const coupon = await storage.getCouponByStripeId(stripeCouponId);
          if (coupon) {
            // Create coupon redemption
            const redemption = await storage.createCouponRedemption({
              coupon_id: coupon.id,
              user_id: userId,
              checkout_session_id: session.id
            });

            // Increment the redemption count
            await storage.incrementCouponRedemptionCount(coupon.id);

            couponRedemptionId = redemption.id;
            logger.info(`Created coupon redemption: ${redemption.id} for coupon: ${coupon.id}`);
          } else {
            logger.warn(`Coupon not found in database: ${stripeCouponId}`);
          }
        } catch (couponError) {
          logger.error('Error processing coupon', {
            error: couponError,
            stripeCouponId: subscription.discount.coupon.id
          });
          // Continue with subscription creation even if coupon processing fails
        }
      }

      // Create or update the subscription in our database
      await this.createSubscription(
        userId,
        planId,
        billingCycle as 'monthly' | 'annual',
        customerId,
        subscriptionId
      );

      // If we have a coupon redemption, update the subscription with it
      if (couponRedemptionId) {
        await storage.updateSubscription(userId, {
          coupon_redemption_id: couponRedemptionId
        });
      }

      logger.info(`Successfully processed checkout session: ${session.id} for user ${userId}`, {
        hasCoupon: !!couponRedemptionId
      });
    } catch (error) {
      logger.error('Error handling checkout session completed', { error, sessionId: session.id });
      throw error;
    }
  }

  /**
   * Handle subscription updated webhook event
   */
  private async handleSubscriptionUpdated(subscription: Stripe.Subscription): Promise<void> {
    try {
      // Extract the customer ID
      let customerId: string;

      if (typeof subscription.customer === 'string') {
        customerId = subscription.customer;
      } else if (typeof subscription.customer === 'object' && subscription.customer !== null) {
        customerId = (subscription.customer as any).id || '';
      } else {
        logger.error('Invalid customer data in subscription', {
          subscriptionId: subscription.id,
          customerType: typeof subscription.customer
        });
        return;
      }

      if (!customerId) {
        logger.error('Empty customer ID in subscription', { subscriptionId: subscription.id });
        return;
      }

      const { data: customers } = await stripe.customers.search({
        query: `id:'${customerId}'`,
      });

      if (!customers || customers.length === 0) {
        logger.error('Customer not found', { customerId });
        return;
      }

      const userId = customers[0].metadata.userId;
      if (!userId) {
        logger.error('User ID not found in customer metadata', { customerId });
        return;
      }

      // Handle coupon changes
      let couponRedemptionId: number | undefined;
      const existingSubscription = await storage.getSubscription(userId);

      if (subscription.discount?.coupon?.id) {
        try {
          const stripeCouponId = subscription.discount.coupon.id;
          logger.info(`Processing coupon update for subscription: ${stripeCouponId}`);

          // Get or create the coupon in our database
          const coupon = await storage.getCouponByStripeId(stripeCouponId);
          if (coupon) {
            // Check if this coupon is already redeemed by this user
            const hasRedeemed = await storage.hasUserRedeemedCoupon(userId, coupon.id);

            if (!hasRedeemed) {
              // Create new coupon redemption
              const redemption = await storage.createCouponRedemption({
                coupon_id: coupon.id,
                user_id: userId,
                subscription_id: existingSubscription?.id
              });

              // Increment the redemption count
              await storage.incrementCouponRedemptionCount(coupon.id);

              couponRedemptionId = redemption.id;
              logger.info(`Created coupon redemption: ${redemption.id} for coupon: ${coupon.id}`);
            } else {
              // Get the existing redemption
              const existingRedemptions = await storage.getCouponRedemptionsByUserId(userId);
              const existingRedemption = existingRedemptions.find(r => r.coupon_id === coupon.id);
              if (existingRedemption) {
                couponRedemptionId = existingRedemption.id;
                logger.info(`Using existing coupon redemption: ${existingRedemption.id}`);
              }
            }
          } else {
            logger.warn(`Coupon not found in database: ${stripeCouponId}`);
          }
        } catch (couponError) {
          logger.error('Error processing coupon update', {
            error: couponError,
            stripeCouponId: subscription.discount.coupon.id
          });
          // Continue with subscription update even if coupon processing fails
        }
      }

      // Update subscription in database
      await storage.updateSubscription(userId, {
        status: subscription.status,
        current_period_start: new Date(subscription.current_period_start * 1000),
        current_period_end: new Date(subscription.current_period_end * 1000),
        cancel_at_period_end: subscription.cancel_at_period_end,
        coupon_redemption_id: couponRedemptionId
      });

      logger.info(`Updated subscription for user ${userId}`, {
        status: subscription.status,
        hasCoupon: !!couponRedemptionId
      });
    } catch (error) {
      logger.error('Error handling subscription updated', { error, subscriptionId: subscription.id });
      throw error;
    }
  }

  /**
   * Handle subscription deleted webhook event
   */
  private async handleSubscriptionDeleted(subscription: Stripe.Subscription): Promise<void> {
    try {
      // Extract the customer ID
      let customerId: string;

      if (typeof subscription.customer === 'string') {
        customerId = subscription.customer;
      } else if (typeof subscription.customer === 'object' && subscription.customer !== null) {
        customerId = (subscription.customer as any).id || '';
      } else {
        logger.error('Invalid customer data in subscription', {
          subscriptionId: subscription.id,
          customerType: typeof subscription.customer
        });
        return;
      }

      if (!customerId) {
        logger.error('Empty customer ID in subscription', { subscriptionId: subscription.id });
        return;
      }

      const { data: customers } = await stripe.customers.search({
        query: `id:'${customerId}'`,
      });

      if (!customers || customers.length === 0) {
        logger.error('Customer not found', { customerId });
        return;
      }

      const userId = customers[0].metadata.userId;
      if (!userId) {
        logger.error('User ID not found in customer metadata', { customerId });
        return;
      }

      // Update subscription in database
      await storage.updateSubscription(userId, {
        status: 'canceled'
      });

      logger.info(`Canceled subscription for user ${userId}`);
    } catch (error) {
      logger.error('Error handling subscription deleted', { error, subscriptionId: subscription.id });
      throw error;
    }
  }

  /**
   * Handle payment succeeded webhook event
   */
  private async handlePaymentSucceeded(invoice: Stripe.Invoice): Promise<void> {
    try {
      // Extract the customer ID
      let customerId: string;

      if (typeof invoice.customer === 'string') {
        customerId = invoice.customer;
      } else if (typeof invoice.customer === 'object' && invoice.customer !== null) {
        customerId = (invoice.customer as any).id || '';
      } else {
        logger.error('Invalid customer data in invoice', {
          invoiceId: invoice.id,
          customerType: typeof invoice.customer
        });
        return;
      }

      if (!customerId) {
        logger.error('Empty customer ID in invoice', { invoiceId: invoice.id });
        return;
      }

      const { data: customers } = await stripe.customers.search({
        query: `id:'${customerId}'`,
      });

      if (!customers || customers.length === 0) {
        logger.error('Customer not found', { customerId });
        return;
      }

      const userId = customers[0].metadata.userId;
      if (!userId) {
        logger.error('User ID not found in customer metadata', { customerId });
        return;
      }

      // Update subscription in database if needed
      const subscription = await storage.getSubscription(userId);
      if (subscription && subscription.status === 'past_due') {
        await storage.updateSubscription(userId, {
          status: 'active'
        });

        logger.info(`Reactivated subscription for user ${userId} after payment`);
      }
    } catch (error) {
      logger.error('Error handling payment succeeded', { error, invoiceId: invoice.id });
      throw error;
    }
  }

  /**
   * Handle payment failed webhook event
   */
  private async handlePaymentFailed(invoice: Stripe.Invoice): Promise<void> {
    try {
      // Extract the customer ID
      let customerId: string;

      if (typeof invoice.customer === 'string') {
        customerId = invoice.customer;
      } else if (typeof invoice.customer === 'object' && invoice.customer !== null) {
        customerId = (invoice.customer as any).id || '';
      } else {
        logger.error('Invalid customer data in invoice', {
          invoiceId: invoice.id,
          customerType: typeof invoice.customer
        });
        return;
      }

      if (!customerId) {
        logger.error('Empty customer ID in invoice', { invoiceId: invoice.id });
        return;
      }

      const { data: customers } = await stripe.customers.search({
        query: `id:'${customerId}'`,
      });

      if (!customers || customers.length === 0) {
        logger.error('Customer not found', { customerId });
        return;
      }

      const userId = customers[0].metadata.userId;
      if (!userId) {
        logger.error('User ID not found in customer metadata', { customerId });
        return;
      }

      // Update subscription in database
      await storage.updateSubscription(userId, {
        status: 'past_due'
      });

      logger.info(`Marked subscription as past_due for user ${userId} after payment failure`);
    } catch (error) {
      logger.error('Error handling payment failed', { error, invoiceId: invoice.id });
      throw error;
    }
  }
}

export const subscriptionService = new SubscriptionService();
