import Stripe from 'stripe';
import { storage } from '../storage';
import { InsertCoupon, InsertCouponRedemption, Coupon } from '@shared/schema';
import { logger } from '../utils/logger';

// Initialize Stripe with the secret key
if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error('Missing required Stripe secret: STRIPE_SECRET_KEY');
}

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: "2025-03-31.basil" as any,
});

export class CouponService {
  /**
   * Create a new coupon in both Stripe and our database
   */
  async createCoupon(couponData: Omit<InsertCoupon, 'stripe_coupon_id'>): Promise<Coupon> {
    try {
      // Create the coupon in Stripe first
      const stripeParams: Stripe.CouponCreateParams = {
        name: couponData.description || `Coupon: ${couponData.code}`,
        metadata: {
          code: couponData.code,
          type: couponData.type,
        }
      };

      // Set up the coupon parameters based on the type
      if (couponData.type === 'percentage' && couponData.amount) {
        stripeParams.percent_off = couponData.amount;
      } else if (couponData.type === 'fixed_amount' && couponData.amount) {
        stripeParams.amount_off = Math.round(couponData.amount * 100); // Convert to cents
        stripeParams.currency = 'aud';
      } else if (couponData.type === 'free_months') {
        // For free months, we'll use a 100% discount for a specific duration
        stripeParams.percent_off = 100;
        stripeParams.duration = 'repeating';
        stripeParams.duration_in_months = couponData.amount ? Math.round(couponData.amount) : 1;
      }

      // Set the duration
      if (couponData.type !== 'free_months') {
        stripeParams.duration = couponData.duration as Stripe.CouponCreateParams.Duration;
        if (couponData.duration === 'repeating' && couponData.duration_in_months) {
          stripeParams.duration_in_months = couponData.duration_in_months;
        }
      }

      // Set max redemptions if provided
      if (couponData.max_redemptions) {
        stripeParams.max_redemptions = couponData.max_redemptions;
      }

      // Set expiration if valid_until is provided
      if (couponData.valid_until) {
        stripeParams.redeem_by = Math.floor(couponData.valid_until.getTime() / 1000);
      }

      // Create the coupon in Stripe
      const stripeCoupon = await stripe.coupons.create(stripeParams);

      logger.info(`Created Stripe coupon: ${stripeCoupon.id}`, 'system', { code: couponData.code });

      // Now create the coupon in our database
      const coupon: InsertCoupon = {
        ...couponData,
        stripe_coupon_id: stripeCoupon.id,
      };

      return await storage.createCoupon(coupon);
    } catch (error) {
      logger.error('Error creating coupon', 'system', { error, code: couponData.code });
      throw new Error(`Failed to create coupon: ${(error as Error).message}`);
    }
  }

  /**
   * Get a coupon by its code
   */
  async getCouponByCode(code: string): Promise<Coupon | undefined> {
    try {
      return await storage.getCouponByCode(code);
    } catch (error) {
      logger.error('Error getting coupon by code', 'system', { error, code });
      throw new Error(`Failed to get coupon: ${(error as Error).message}`);
    }
  }

  /**
   * Validate a coupon code
   * Returns the coupon if valid, undefined if not
   * Uses Stripe as the source of truth and stores the coupon in our database if it's valid
   */
  async validateCoupon(code: string, userId: string): Promise<Coupon | undefined> {
    try {
      // First, check if we already have this coupon in our database
      let coupon = await storage.getCouponByCode(code);
      let stripeCoupon;

      // Try to find the coupon in Stripe (this is the source of truth)
      try {
        // List coupons with the given code
        const stripeCoupons = await stripe.coupons.list({
          limit: 1
        });

        // Find the coupon with matching code (case-insensitive)
        stripeCoupon = stripeCoupons.data.find(
          c => (c.name?.toLowerCase() === code.toLowerCase()) ||
               (c.id.toLowerCase() === code.toLowerCase()) ||
               (c.metadata?.code?.toLowerCase() === code.toLowerCase())
        );

        if (!stripeCoupon) {
          logger.info(`Coupon not found in Stripe: ${code}`, 'system');
          return undefined;
        }

        // Check if the coupon is valid in Stripe
        if (!stripeCoupon.valid) {
          logger.info(`Coupon is not valid in Stripe: ${code}`, 'system');
          return undefined;
        }

        // Check if the coupon has expired
        if (stripeCoupon.redeem_by && stripeCoupon.redeem_by * 1000 < Date.now()) {
          logger.info(`Coupon has expired in Stripe: ${code}`, 'system');
          return undefined;
        }

        // Check if the coupon has reached max redemptions
        if (stripeCoupon.max_redemptions &&
            stripeCoupon.times_redeemed >= stripeCoupon.max_redemptions) {
          logger.info(`Coupon reached max redemptions in Stripe: ${code}`, 'system');
          return undefined;
        }

      } catch (stripeError) {
        logger.error(`Error retrieving coupon from Stripe: ${code}`, 'system', { error: stripeError });
        return undefined;
      }

      // If we don't have the coupon in our database but it exists in Stripe, create it
      if (!coupon && stripeCoupon) {
        // Determine coupon type and amount
        let type, amount;
        if (stripeCoupon.percent_off) {
          type = 'percentage';
          amount = stripeCoupon.percent_off;
        } else if (stripeCoupon.amount_off) {
          type = 'fixed_amount';
          amount = stripeCoupon.amount_off / 100; // Convert from cents to dollars
        } else {
          // Special case for free months (100% off for X months)
          if (stripeCoupon.duration === 'repeating' &&
              stripeCoupon.duration_in_months &&
              stripeCoupon.percent_off === 100) {
            type = 'free_months';
            amount = stripeCoupon.duration_in_months;
          } else {
            logger.error(`Unsupported coupon type: ${code}`, 'system');
            return undefined;
          }
        }

        // Create the coupon in our database
        const now = new Date();
        const validUntil = stripeCoupon.redeem_by ? new Date(stripeCoupon.redeem_by * 1000) : null;

        try {
          // First check if the coupon already exists with this stripe_coupon_id
          const existingCoupon = await storage.getCouponByStripeId(stripeCoupon.id);

          if (existingCoupon) {
            // If it exists, use it
            coupon = existingCoupon;
            logger.info(`Found existing coupon in database with Stripe ID: ${stripeCoupon.id}`, 'system');
          } else {
            // Try to create it
            try {
              const newCoupon: InsertCoupon = {
                code: code.toUpperCase(),
                stripe_coupon_id: stripeCoupon.id,
                description: stripeCoupon.name || `${type === 'percentage' ? amount + '%' : '$' + amount} off`,
                type,
                amount,
                duration: stripeCoupon.duration,
                duration_in_months: stripeCoupon.duration_in_months || null,
                max_redemptions: stripeCoupon.max_redemptions || null,
                valid_from: now,
                valid_until: validUntil || undefined,
                is_active: true,
                redemption_count: stripeCoupon.times_redeemed || 0
              };

              coupon = await storage.createCoupon(newCoupon);
              logger.info(`Created coupon in database from Stripe: ${code}`, 'system');
            } catch (createError) {
              // If creation fails, try to get by code as a fallback
              // This handles the case where the coupon was created in a race condition
              const fallbackCoupon = await storage.getCouponByCode(code.toUpperCase());
              if (fallbackCoupon) {
                coupon = fallbackCoupon;
                logger.info(`Found existing coupon by code after creation failed: ${code}`, 'system');
              } else {
                logger.error(`Error creating coupon in database: ${code}`, 'system', { error: createError });
                // Create a temporary coupon object to return
                coupon = {
                  id: -1, // Temporary ID
                  code: code.toUpperCase(),
                  stripe_coupon_id: stripeCoupon.id,
                  description: stripeCoupon.name || `${type === 'percentage' ? amount + '%' : '$' + amount} off`,
                  type,
                  amount,
                  duration: stripeCoupon.duration,
                  duration_in_months: stripeCoupon.duration_in_months || null,
                  max_redemptions: stripeCoupon.max_redemptions || null,
                  redemption_count: stripeCoupon.times_redeemed || 0,
                  valid_from: now,
                  valid_until: validUntil || null,
                  is_active: true,
                  created_at: now,
                  updated_at: now
                };
              }
            }
          }
        } catch (dbError) {
          logger.error(`Error handling coupon in database: ${code}`, 'system', { error: dbError });
          // Create a temporary coupon object to return
          coupon = {
            id: -1, // Temporary ID
            code: code.toUpperCase(),
            stripe_coupon_id: stripeCoupon.id,
            description: stripeCoupon.name || `${type === 'percentage' ? amount + '%' : '$' + amount} off`,
            type,
            amount,
            duration: stripeCoupon.duration,
            duration_in_months: stripeCoupon.duration_in_months || null,
            max_redemptions: stripeCoupon.max_redemptions || null,
            redemption_count: stripeCoupon.times_redeemed || 0,
            valid_from: now,
            valid_until: validUntil || null,
            is_active: true,
            created_at: now,
            updated_at: now
          };
        }
      }

      // If we have the coupon in our database, check additional validations
      if (coupon) {
        // Check if the coupon is active in our database
        if (!coupon.is_active) {
          logger.info(`Coupon is inactive in database: ${code}`, 'system');
          return undefined;
        }

        // Check if the coupon is expired in our database
        const now = new Date();
        if (coupon.valid_from > now) {
          logger.info(`Coupon not yet valid: ${code}`, 'system', { valid_from: coupon.valid_from });
          return undefined;
        }

        if (coupon.valid_until && coupon.valid_until < now) {
          logger.info(`Expired coupon: ${code}`, 'system', { valid_until: coupon.valid_until });
          return undefined;
        }

        // Check if the coupon has max_redemptions per user set to 1
        // This is determined by the unique constraint in the database
        // Each user can only redeem a specific coupon once

        // Check if the user has already redeemed this coupon
        const hasRedeemed = await storage.hasUserRedeemedCoupon(userId, coupon.id);
        if (hasRedeemed) {
          logger.info(`User already redeemed coupon: ${code}`, 'system', { userId });
          return undefined;
        }
      } else if (stripeCoupon) {
        // If we couldn't create the coupon in our database but it exists in Stripe,
        // create a temporary coupon object to return
        const now = new Date();
        const validUntil = stripeCoupon.redeem_by ? new Date(stripeCoupon.redeem_by * 1000) : null;

        // Determine coupon type and amount
        let type, amount;
        if (stripeCoupon.percent_off) {
          type = 'percentage';
          amount = stripeCoupon.percent_off;
        } else if (stripeCoupon.amount_off) {
          type = 'fixed_amount';
          amount = stripeCoupon.amount_off / 100; // Convert from cents to dollars
        } else {
          // Special case for free months (100% off for X months)
          if (stripeCoupon.duration === 'repeating' &&
              stripeCoupon.duration_in_months &&
              stripeCoupon.percent_off === 100) {
            type = 'free_months';
            amount = stripeCoupon.duration_in_months;
          } else {
            logger.error(`Unsupported coupon type: ${code}`, 'system');
            return undefined;
          }
        }

        coupon = {
          id: -1, // Temporary ID
          code: code.toUpperCase(),
          stripe_coupon_id: stripeCoupon.id,
          description: stripeCoupon.name || `${type === 'percentage' ? amount + '%' : '$' + amount} off`,
          type,
          amount,
          duration: stripeCoupon.duration,
          duration_in_months: stripeCoupon.duration_in_months || null,
          max_redemptions: stripeCoupon.max_redemptions || null,
          redemption_count: stripeCoupon.times_redeemed || 0,
          valid_from: now,
          valid_until: validUntil || null,
          is_active: true,
          created_at: now,
          updated_at: now
        };
      }

      // Coupon is valid
      return coupon;
    } catch (error) {
      logger.error('Error validating coupon', 'system', { error, code });
      throw new Error(`Failed to validate coupon: ${(error as Error).message}`);
    }
  }

  /**
   * Apply a coupon to a checkout session
   */
  async applyCouponToCheckout(
    code: string,
    userId: string,
    checkoutSessionId: string
  ): Promise<boolean> {
    try {
      // Validate the coupon
      const coupon = await this.validateCoupon(code, userId);
      if (!coupon) {
        return false;
      }

      // If this is a temporary coupon object (id = -1), we need to create it in our database first
      let couponId = coupon.id;
      if (couponId === -1) {
        try {
          // First check if the coupon already exists with this stripe_coupon_id
          const existingCoupon = await storage.getCouponByStripeId(coupon.stripe_coupon_id);

          if (existingCoupon) {
            // If it exists, use it
            couponId = existingCoupon.id;
            logger.info(`Found existing coupon in database with Stripe ID: ${coupon.stripe_coupon_id}`, 'system');
          } else {
            // Also check if it exists by code
            const codeMatch = await storage.getCouponByCode(coupon.code);

            if (codeMatch) {
              couponId = codeMatch.id;
              logger.info(`Found existing coupon in database with code: ${coupon.code}`, 'system');
            } else {
              // Try to create it
              try {
                // Create the coupon in our database
                const newCoupon = await storage.createCoupon({
                  code: coupon.code,
                  stripe_coupon_id: coupon.stripe_coupon_id,
                  description: coupon.description,
                  type: coupon.type,
                  amount: coupon.amount,
                  duration: coupon.duration,
                  duration_in_months: coupon.duration_in_months,
                  max_redemptions: coupon.max_redemptions,
                  valid_from: coupon.valid_from,
                  valid_until: coupon.valid_until || undefined,
                  is_active: true,
                  redemption_count: 0
                });

                couponId = newCoupon.id;
                logger.info(`Created coupon in database for redemption: ${code}`, 'system');
              } catch (createError) {
                // If creation fails, try to get by code one more time as a fallback
                // This handles the case where the coupon was created in a race condition
                const fallbackCoupon = await storage.getCouponByCode(coupon.code);
                if (fallbackCoupon) {
                  couponId = fallbackCoupon.id;
                  logger.info(`Found existing coupon by code after creation failed: ${code}`, 'system');
                } else {
                  logger.error(`Error creating coupon in database for redemption: ${code}`, 'system', { error: createError });
                  // If we can't create the coupon in our database, we can't record the redemption
                  // but we can still apply the coupon to the checkout session in Stripe
                  return true;
                }
              }
            }
          }
        } catch (dbError) {
          logger.error(`Error handling coupon in database for redemption: ${code}`, 'system', { error: dbError });
          // If we can't create the coupon in our database, we can't record the redemption
          // but we can still apply the coupon to the checkout session in Stripe
          return true;
        }
      }

      // Record the redemption in our database
      try {
        const redemption: InsertCouponRedemption = {
          coupon_id: couponId,
          user_id: userId,
          checkout_session_id: checkoutSessionId,
          redeemed_at: new Date()
        };

        await storage.createCouponRedemption(redemption);

        // Increment the redemption count
        if (couponId !== -1) {
          await storage.incrementCouponRedemptionCount(couponId);
        }

        logger.info(`Applied coupon to checkout: ${code}`, 'system', { userId, checkoutSessionId });
      } catch (redemptionError) {
        logger.error(`Error recording coupon redemption: ${code}`, 'system', { error: redemptionError });
        // If we can't record the redemption, we can still apply the coupon to the checkout session
      }

      return true;
    } catch (error) {
      logger.error('Error applying coupon to checkout', 'system', { error, code, userId });
      throw new Error(`Failed to apply coupon: ${(error as Error).message}`);
    }
  }

  /**
   * Update a coupon's active status
   */
  async updateCouponStatus(id: number, isActive: boolean): Promise<Coupon | undefined> {
    try {
      const coupon = await storage.getCoupon(id);
      if (!coupon) {
        return undefined;
      }

      // Update the coupon in Stripe if we're deactivating it
      if (!isActive) {
        await stripe.coupons.update(coupon.stripe_coupon_id, {
          metadata: {
            ...coupon,
            is_active: 'false'
          }
        });
      }

      // Update in our database
      return await storage.updateCoupon(id, { is_active: isActive });
    } catch (error) {
      logger.error('Error updating coupon status', 'system', { error, id, isActive });
      throw new Error(`Failed to update coupon status: ${(error as Error).message}`);
    }
  }

  /**
   * Delete a coupon
   */
  async deleteCoupon(id: number): Promise<boolean> {
    try {
      const coupon = await storage.getCoupon(id);
      if (!coupon) {
        return false;
      }

      // Delete the coupon in Stripe
      await stripe.coupons.del(coupon.stripe_coupon_id);

      // Delete in our database
      await storage.deleteCoupon(id);
      return true;
    } catch (error) {
      logger.error('Error deleting coupon', 'system', { error, id });
      throw new Error(`Failed to delete coupon: ${(error as Error).message}`);
    }
  }

  /**
   * List all coupons
   */
  async listCoupons(includeInactive = false): Promise<Coupon[]> {
    try {
      return await storage.listCoupons(includeInactive);
    } catch (error) {
      logger.error('Error listing coupons', 'system', { error });
      throw new Error(`Failed to list coupons: ${(error as Error).message}`);
    }
  }
}

export const couponService = new CouponService();
