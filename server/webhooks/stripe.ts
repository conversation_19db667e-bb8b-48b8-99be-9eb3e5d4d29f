import Stripe from 'stripe';
import { Request, Response } from 'express';
import { subscriptionService } from '../services/subscription';
import { couponService } from '../services/coupon';
import { logger } from '../utils/logger';
import { storage } from '../storage';

// Initialize Stripe with the secret key
if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error('Missing required Stripe secret: STRIPE_SECRET_KEY');
}

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: "2025-03-31.basil" as any,
});

/**
 * Handle Stripe webhook events
 */
export async function handleStripeWebhook(req: Request, res: Response) {
  const sig = req.headers['stripe-signature'] as string;
  let event: Stripe.Event;

  try {
    // Log the raw body for debugging
    logger.info(`Received Stripe webhook request`, {
      hasSignature: !!sig,
      hasWebhookSecret: !!process.env.STRIPE_WEBHOOK_SECRET,
      contentType: req.headers['content-type'],
      bodyLength: req.body ? (typeof req.body === 'string' ? req.body.length : JSON.stringify(req.body).length) : 0
    });

    // Verify webhook signature if webhook secret is set
    if (process.env.STRIPE_WEBHOOK_SECRET) {
      event = stripe.webhooks.constructEvent(
        req.body,
        sig,
        process.env.STRIPE_WEBHOOK_SECRET
      );
    } else {
      // For development, just parse the payload
      event = req.body as Stripe.Event;
    }

    logger.info(`Received Stripe webhook: ${event.type}`, {
      eventId: event.id,
      eventType: event.type,
      objectId: event.data.object.id
    });

    // Special handling for checkout.session.completed events
    if (event.type === 'checkout.session.completed') {
      const session = event.data.object as Stripe.Checkout.Session;

      logger.info(`Processing checkout.session.completed webhook`, {
        sessionId: session.id,
        hasCustomer: !!session.customer,
        hasSubscription: !!session.subscription,
        hasMetadata: !!session.metadata,
        metadata: session.metadata
      });

      // If this is a subscription checkout, manually create the subscription
      if (session.mode === 'subscription' && session.subscription && session.customer) {
        try {
          // Get metadata from the session
          let userId = session.metadata?.userId;
          const planId = session.metadata?.planId;
          const billingCycle = session.metadata?.billingCycle;
          const couponCode = session.metadata?.couponCode;
          const stripeCouponId = session.metadata?.stripeCouponId;

          // If userId is missing from metadata, try to get it from the customer object
          if (!userId) {
            try {
              // Get the customer details from Stripe
              const customer = await stripe.customers.retrieve(session.customer as string);

              // Check if customer has userId in metadata
              if (customer && 'metadata' in customer && customer.metadata.userId) {
                userId = customer.metadata.userId;
                logger.info(`Found userId in customer metadata: ${userId}`);
              }
            } catch (customerError) {
              logger.error('Error retrieving customer details', { error: customerError, customerId: session.customer });
            }
          }

          if (userId && planId && billingCycle) {
            // Check if a subscription already exists for this user
            const existingSubscription = await storage.getSubscription(userId);

            if (existingSubscription) {
              logger.info(`Subscription already exists for user: ${userId}`, {
                existingId: existingSubscription.id,
                stripeId: existingSubscription.stripe_subscription_id
              });

              // If the subscription exists but has a different Stripe subscription ID, update it
              if (existingSubscription.stripe_subscription_id !== session.subscription) {
                await storage.updateSubscription(userId, {
                  stripe_subscription_id: session.subscription as string,
                  stripe_customer_id: session.customer as string,
                  plan_id: planId,
                  billing_cycle: billingCycle as 'monthly' | 'annual',
                  status: 'active',
                  cancel_at_period_end: false
                });

                logger.info(`Updated existing subscription for user: ${userId}`);
              }
            } else {
              // Create a new subscription in our database
              await subscriptionService.createSubscription(
                userId,
                planId,
                billingCycle as 'monthly' | 'annual',
                session.customer as string,
                session.subscription as string
              );

              logger.info(`Successfully created subscription for user: ${userId}`, {
                planId,
                billingCycle,
                subscriptionId: session.subscription
              });
            }

            // Process coupon redemption if a coupon was used
            if ((couponCode || stripeCouponId) && userId) {
              try {
                // If we have a Stripe coupon ID, use it directly
                if (stripeCouponId) {
                  logger.info(`Processing coupon from Stripe ID in webhook: ${stripeCouponId}`);

                  // Get the coupon details from Stripe
                  const stripeCoupon = await stripe.coupons.retrieve(stripeCouponId);

                  if (stripeCoupon && stripeCoupon.valid) {
                    // Create or get the coupon in our database
                    let coupon = await storage.getCouponByStripeId(stripeCouponId);

                    // If the coupon doesn't exist in our database, create it
                    if (!coupon) {
                      // Determine coupon type and amount
                      let type, amount;
                      if (stripeCoupon.percent_off) {
                        type = 'percentage';
                        amount = stripeCoupon.percent_off;
                      } else if (stripeCoupon.amount_off) {
                        type = 'fixed_amount';
                        amount = stripeCoupon.amount_off / 100; // Convert from cents to dollars
                      } else {
                        // Special case for free months (100% off for X months)
                        if (stripeCoupon.duration === 'repeating' &&
                            stripeCoupon.duration_in_months &&
                            stripeCoupon.percent_off === 100) {
                          type = 'free_months';
                          amount = stripeCoupon.duration_in_months;
                        } else {
                          logger.error(`Unsupported coupon type in webhook: ${stripeCouponId}`);
                          // Continue without recording the coupon
                          return;
                        }
                      }

                      // Create the coupon in our database
                      const now = new Date();
                      const validUntil = stripeCoupon.redeem_by ? new Date(stripeCoupon.redeem_by * 1000) : null;

                      try {
                        coupon = await storage.createCoupon({
                          code: couponCode || stripeCouponId,
                          stripe_coupon_id: stripeCouponId,
                          description: stripeCoupon.name || `${type === 'percentage' ? amount + '%' : '$' + amount} off`,
                          type,
                          amount,
                          duration: stripeCoupon.duration,
                          duration_in_months: stripeCoupon.duration_in_months || null,
                          valid_from: now,
                          valid_until: validUntil || undefined,
                          is_active: true,
                          redemption_count: 0
                        });

                        logger.info(`Created coupon in database from webhook: ${couponCode || stripeCouponId}`);
                      } catch (dbError) {
                        logger.error(`Error creating coupon in database from webhook: ${couponCode || stripeCouponId}`, { error: dbError });
                        // Continue without recording the coupon
                        return;
                      }
                    }

                    // Record the redemption
                    if (coupon) {
                      const subscription = await storage.getSubscription(userId);

                      if (subscription) {
                        try {
                          // Check if the user has already redeemed this coupon
                          const hasRedeemed = await storage.hasUserRedeemedCoupon(userId, coupon.id);

                          if (!hasRedeemed) {
                            // Create the redemption
                            const redemption = await storage.createCouponRedemption({
                              coupon_id: coupon.id,
                              user_id: userId,
                              subscription_id: subscription.id,
                              checkout_session_id: session.id,
                              redeemed_at: new Date()
                            });

                            // Increment the redemption count
                            await storage.incrementCouponRedemptionCount(coupon.id);

                            // Update the subscription record with the redemption
                            await storage.updateSubscription(userId, {
                              coupon_redemption_id: redemption.id
                            });

                            logger.info(`Recorded coupon redemption in webhook: ${couponCode || stripeCouponId} for user ${userId}`);
                          } else {
                            // User has already redeemed this coupon, get the existing redemption
                            const redemptions = await storage.getCouponRedemptionsByUserId(userId);
                            const existingRedemption = redemptions.find(r => r.coupon_id === coupon.id);

                            if (existingRedemption) {
                              // Update the subscription record with the existing redemption
                              await storage.updateSubscription(userId, {
                                coupon_redemption_id: existingRedemption.id
                              });

                              logger.info(`Updated subscription with existing coupon redemption in webhook: ${couponCode || stripeCouponId} for user ${userId}`);
                            }
                          }
                        } catch (redemptionError) {
                          logger.error(`Error handling coupon redemption in webhook: ${couponCode || stripeCouponId}`, 'system', {
                            error: redemptionError,
                            userId
                          });
                          // Continue with subscription creation even if redemption handling fails
                        }
                      }
                    }
                  }
                } else if (couponCode) {
                  try {
                    // Use the coupon service to handle the redemption
                    // This will validate the coupon with Stripe and create it in our database if needed
                    await couponService.applyCouponToCheckout(couponCode, userId, session.id);

                    // Update the subscription record with the coupon info
                    const subscription = await storage.getSubscription(userId);
                    if (subscription) {
                      // Get all redemptions for this user
                      const redemptions = await storage.getCouponRedemptionsByUserId(userId);

                      // Find the redemption for this session
                      const redemption = redemptions.find(r => r.checkout_session_id === session.id);

                      if (redemption) {
                        // Update the subscription record with the redemption
                        await storage.updateSubscription(userId, {
                          coupon_redemption_id: redemption.id
                        });

                        logger.info(`Updated subscription with coupon redemption in webhook: ${couponCode} for user ${userId}`);
                      } else {
                        // If no redemption was found for this session, look for any redemption for this coupon
                        // This handles the case where the user has already redeemed this coupon
                        const coupon = await storage.getCouponByCode(couponCode);
                        if (coupon) {
                          const existingRedemption = redemptions.find(r => r.coupon_id === coupon.id);
                          if (existingRedemption) {
                            // Update the subscription record with the existing redemption
                            await storage.updateSubscription(userId, {
                              coupon_redemption_id: existingRedemption.id
                            });

                            logger.info(`Updated subscription with existing coupon redemption in webhook: ${couponCode} for user ${userId}`);
                          }
                        }
                      }
                    }
                  } catch (couponError) {
                    logger.error(`Error processing coupon in webhook: ${couponCode}`, 'system', {
                      error: couponError,
                      userId
                    });
                    // Continue with subscription creation even if coupon processing fails
                  }
                }
              } catch (couponError) {
                logger.error('Error processing coupon redemption in webhook', 'system', {
                  error: couponError,
                  couponCode,
                  stripeCouponId,
                  userId,
                  sessionId: session.id
                });
                // Continue with subscription creation even if coupon processing fails
              }
            }
          } else {
            logger.error('Missing required metadata for subscription creation', 'system', {
              sessionId: session.id,
              userId: userId || 'missing',
              planId: planId || 'missing',
              billingCycle: billingCycle || 'missing'
            });
          }
        } catch (error) {
          logger.error('Error processing checkout.session.completed webhook', 'system', { error, sessionId: session.id });
        }
      }
    }

    // Check if we've already processed this event
    const hasProcessed = await storage.getProcessedWebhookEvent(event.id);
    if (hasProcessed) {
      logger.info(`Skipping already processed event: ${event.id} (${event.type})`);
      return res.json({ received: true, status: 'already_processed' });
    }

    try {
      // Handle the event with the subscription service
      await subscriptionService.handleWebhookEvent(event);

      // Mark the event as processed
      await storage.saveProcessedWebhookEvent(event.id, event.type);

      // Return a 200 response to acknowledge receipt of the event
      res.json({ received: true, status: 'processed' });
    } catch (processingError) {
      logger.error(`Error processing webhook event: ${event.id}`, { error: processingError });

      // Don't mark as processed if there was an error
      // Return 200 to acknowledge receipt (Stripe will not retry if we return non-200)
      res.json({
        received: true,
        status: 'error',
        message: 'Event received but processing failed'
      });
    }
  } catch (err: any) {
    logger.error(`Webhook Error: ${err.message}`, { error: err });
    res.status(400).send(`Webhook Error: ${err.message}`);
  }
}
