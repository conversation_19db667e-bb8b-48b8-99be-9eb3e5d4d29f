import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client for database operations only
const supabaseUrl = process.env.SUPABASE_URL as string;
const supabaseKey = process.env.SUPABASE_DB_KEY as string;

if (!supabaseUrl || !supabaseKey) {
  throw new Error('Missing Supabase configuration. Check environment variables.');
}

// Create Supabase client with auth completely disabled
export const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    persistSession: false, // Disable auth session persistence
    autoRefreshToken: false, // Disable token refresh
    detectSessionInUrl: false // Disable detecting session in URL
  }
});