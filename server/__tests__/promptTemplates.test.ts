import { generatePrompt, promptTemplates } from '../promptTemplates';

describe('Prompt Templates', () => {
  describe('generatePrompt', () => {
    it('should generate a prompt for custom modification', () => {
      const params = {
        description: 'Change the wall color to light blue',
        options: {},
        hasPrimaryReferenceImage: false,
        hasAdditionalReferenceImages: false,
        referenceImageCount: 0
      };
      
      const prompt = generatePrompt('custom', params);
      
      expect(prompt).toContain('Change the wall color to light blue');
      expect(prompt).toContain('Make the changes look realistic and professional');
    });
    
    it('should generate a prompt for floor replacement with reference image', () => {
      const params = {
        description: 'Replace the floor',
        options: {
          useReferenceImage: true,
          customDescription: 'Make it look modern'
        },
        hasPrimaryReferenceImage: true,
        hasAdditionalReferenceImages: false,
        referenceImageCount: 1
      };
      
      const prompt = generatePrompt('replace_floor', params);
      
      expect(prompt).toContain('Please replace the floor in the original image using the provided reference image');
      expect(prompt).toContain('Additional details: Make it look modern');
      expect(prompt).toContain('The reference image shows the exact floor style');
    });
    
    it('should generate a prompt for floor replacement with material and color', () => {
      const params = {
        description: 'Replace the floor',
        options: {
          useReferenceImage: false,
          material: 'hardwood',
          color: 'dark',
          customDescription: 'With wide planks'
        },
        hasPrimaryReferenceImage: false,
        hasAdditionalReferenceImages: true,
        referenceImageCount: 2
      };
      
      const prompt = generatePrompt('replace_floor', params);
      
      expect(prompt).toContain('Please replace the floor in the original image with dark hardwood flooring');
      expect(prompt).toContain('Additional details: With wide planks');
      expect(prompt).toContain('Use the provided reference images as additional inspiration');
    });
    
    it('should fall back to custom template if no template exists for the modification type', () => {
      const params = {
        description: 'Do something custom',
        options: {},
        hasPrimaryReferenceImage: false,
        hasAdditionalReferenceImages: false,
        referenceImageCount: 0
      };
      
      // @ts-ignore - Testing with an invalid modification type
      const prompt = generatePrompt('non_existent_type', params);
      
      expect(prompt).toContain('Do something custom');
      expect(prompt).toContain('Make the changes look realistic and professional');
    });
    
    it('should include reference image guidance when primary reference is available', () => {
      const params = {
        description: 'Custom modification',
        options: {},
        hasPrimaryReferenceImage: true,
        hasAdditionalReferenceImages: true,
        referenceImageCount: 3
      };
      
      const prompt = generatePrompt('custom', params);
      
      expect(prompt).toContain('Use the first reference image as the primary inspiration');
      expect(prompt).toContain('Also consider the other 2 reference images');
    });
  });
  
  describe('promptTemplates', () => {
    it('should have a template for each supported modification type', () => {
      expect(promptTemplates['custom']).toBeDefined();
      expect(promptTemplates['replace_floor']).toBeDefined();
      expect(promptTemplates['change_wall_color']).toBeDefined();
      expect(promptTemplates['update_cabinets']).toBeDefined();
      expect(promptTemplates['change_countertops']).toBeDefined();
    });
    
    it('should generate appropriate prompts for each template', () => {
      const baseParams = {
        description: 'Test description',
        options: {},
        hasPrimaryReferenceImage: false,
        hasAdditionalReferenceImages: false,
        referenceImageCount: 0
      };
      
      // Test each template
      Object.entries(promptTemplates).forEach(([type, templateFn]) => {
        const prompt = templateFn(baseParams);
        expect(prompt).toBeTruthy();
        expect(typeof prompt).toBe('string');
        
        // Each template should include quality instructions
        expect(prompt).toContain('realistic');
      });
    });
  });
});
