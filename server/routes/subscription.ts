// Add the change-plan route
router.post('/change-plan', requireAuth, async (req: Request, res: Response) => {
  const { newPlan, billingCycle } = req.body;
  const userId = req.auth?.userId;

  if (!userId) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  if (!newPlan || !billingCycle) {
    return res.status(400).json({ message: 'Missing required fields' });
  }

  try {
    logger.info('Changing subscription plan', {
      userId,
      newPlan,
      billingCycle,
    });

    // Get the customer
    const customer = await getCustomerByUserId(userId);
    if (!customer) {
      logger.error('Customer not found', { userId });
      return res.status(404).json({ message: 'Customer not found' });
    }

    // Get current subscription
    const subscriptions = await stripe.subscriptions.list({
      customer: customer.id,
      status: 'active',
      limit: 1,
    });

    const currentSubscription = subscriptions.data[0];
    if (!currentSubscription) {
      logger.error('No active subscription found', { userId, customerId: customer.id });
      return res.status(404).json({ message: 'No active subscription found' });
    }

    // Get the price ID for the new plan
    const prices = await stripe.prices.list({
      lookup_keys: [`${newPlan}_${billingCycle}`],
      active: true,
    });

    const newPrice = prices.data[0];
    if (!newPrice) {
      logger.error('Price not found for plan', { newPlan, billingCycle });
      return res.status(404).json({ message: 'Price not found for the selected plan' });
    }

    // Update the subscription
    const updatedSubscription = await stripe.subscriptions.update(
      currentSubscription.id,
      {
        items: [
          {
            id: currentSubscription.items.data[0].id,
            price: newPrice.id,
          },
        ],
        proration_behavior: 'always_invoice',
      }
    );

    logger.info('Subscription plan changed successfully', {
      userId,
      subscriptionId: updatedSubscription.id,
      newPlan,
      billingCycle,
    });

    // Update the subscription in the database
    await db.subscription.update({
      where: {
        userId: userId,
      },
      data: {
        planId: newPlan,
        billingCycle: billingCycle,
        updatedAt: new Date(),
      },
    });

    return res.status(200).json({
      message: 'Subscription updated successfully',
      subscription: updatedSubscription,
    });
  } catch (error) {
    logger.error('Error changing subscription plan', {
      error: error instanceof Error ? error.message : 'Unknown error',
      ...(error instanceof Error && error.stack ? { stack: error.stack } : {}),
    });

    return res.status(500).json({
      message: 'Failed to change subscription plan',
      error: error instanceof Error ? error.message : 'An unexpected error occurred',
    });
  }
}); 