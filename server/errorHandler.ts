import { Request, Response, NextFunction } from 'express';

// Define error types
export enum ErrorType {
  VALIDATION = 'VALIDATION_ERROR',
  AUTHENTICATION = 'AUTHENTICATION_ERROR',
  AUTHORIZATION = 'AUTHORIZATION_ERROR',
  NOT_FOUND = 'NOT_FOUND_ERROR',
  DATABASE = 'DATABASE_ERROR',
  EXTERNAL_SERVICE = 'EXTERNAL_SERVICE_ERROR',
  INTERNAL = 'INTERNAL_ERROR',
}

// Custom error class
export class AppError extends Error {
  type: ErrorType;
  statusCode: number;
  details?: any;

  constructor(message: string, type: ErrorType, statusCode: number, details?: any) {
    super(message);
    this.type = type;
    this.statusCode = statusCode;
    this.details = details;
    this.name = 'AppError';
  }
}

// Helper functions to create specific error types
export const createValidationError = (message: string, details?: any) => {
  return new AppError(message, ErrorType.VALIDATION, 400, details);
};

export const createAuthenticationError = (message: string = 'Authentication required') => {
  return new AppError(message, ErrorType.AUTHENTICATION, 401);
};

export const createAuthorizationError = (message: string = 'You do not have permission to perform this action') => {
  return new AppError(message, ErrorType.AUTHORIZATION, 403);
};

export const createNotFoundError = (message: string = 'Resource not found') => {
  return new AppError(message, ErrorType.NOT_FOUND, 404);
};

export const createDatabaseError = (message: string, details?: any) => {
  return new AppError(message, ErrorType.DATABASE, 500, details);
};

export const createExternalServiceError = (message: string, details?: any) => {
  return new AppError(message, ErrorType.EXTERNAL_SERVICE, 503, details);
};

export const createInternalError = (message: string = 'Internal server error', details?: any) => {
  return new AppError(message, ErrorType.INTERNAL, 500, details);
};

// Global error handler middleware
export const errorHandler = (err: Error, req: Request, res: Response, next: NextFunction) => {
  console.error('Error:', err);

  // Handle AppError instances
  if (err instanceof AppError) {
    return res.status(err.statusCode).json({
      error: {
        type: err.type,
        message: err.message,
        ...(process.env.NODE_ENV === 'development' && err.details ? { details: err.details } : {})
      }
    });
  }

  // Handle Supabase errors
  if (err.name === 'PostgrestError' || (err as any).code?.startsWith('22') || (err as any).code?.startsWith('23')) {
    return res.status(400).json({
      error: {
        type: ErrorType.DATABASE,
        message: 'Database operation failed',
        ...(process.env.NODE_ENV === 'development' ? { details: err.message } : {})
      }
    });
  }

  // Handle validation errors (e.g., from Zod)
  if (err.name === 'ZodError') {
    return res.status(400).json({
      error: {
        type: ErrorType.VALIDATION,
        message: 'Validation failed',
        details: process.env.NODE_ENV === 'development' ? err : undefined
      }
    });
  }

  // Default error response
  return res.status(500).json({
    error: {
      type: ErrorType.INTERNAL,
      message: 'Something went wrong',
      ...(process.env.NODE_ENV === 'development' ? { details: err.message } : {})
    }
  });
};

// Async handler to catch errors in async route handlers
export const asyncHandler = (fn: (req: Request, res: Response, next: NextFunction) => Promise<any>) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};
