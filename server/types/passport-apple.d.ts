declare module 'passport-apple' {
  import { Strategy as PassportStrategy } from 'passport';

  interface AppleStrategyOptions {
    clientID: string;
    teamID: string;
    keyID: string;
    privateKeyString: string;
    callbackURL: string;
    scope?: string[];
    passReqToCallback?: boolean;
  }

  interface AppleProfile {
    id: string;
    name?: {
      firstName?: string;
      lastName?: string;
    };
    emails?: Array<{ value: string }>;
  }

  class Strategy extends PassportStrategy {
    constructor(
      options: AppleStrategyOptions,
      verify: (
        accessToken: string,
        refreshToken: string,
        profile: AppleProfile,
        done: (error: any, user?: any, info?: any) => void
      ) => void
    );
  }

  export default Strategy;
}