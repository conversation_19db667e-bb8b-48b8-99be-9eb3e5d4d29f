import OpenAI from "openai";
import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';
import FormData from 'form-data';
import { generatePrompt } from './promptTemplates';

// Load environment variables from .env file
dotenv.config();

// Check if OpenAI API key is available
const apiKey = process.env.OPENAI_API_KEY;
if (!apiKey) {
  console.warn('[WARN] OpenAI API key is not configured. Image generation will not work.');
  console.warn('Please add your OpenAI API key to the .env file:');
  console.warn('OPENAI_API_KEY="your-api-key-here"');
} else {
  console.log(`[INFO] Using OpenAI API key: ${apiKey.substring(0, 8)}...${apiKey.substring(apiKey.length - 4)}`);
}

// Initialize OpenAI with API key
const openai = new OpenAI({
  apiKey: apiKey || 'dummy-key-for-initialization-only'  // Use a dummy key if not available
});

// Function to ensure directory exists
const ensureDir = async (dirPath: string) => {
  try {
    await fs.promises.mkdir(dirPath, { recursive: true });
  } catch (error) {
    // If the error is other than 'directory already exists', rethrow it
    if ((error as NodeJS.ErrnoException)?.code !== 'EEXIST') {
      throw error;
    }
  }
};

/**
 * Process the response from OpenAI API and save the image
 * @param response The response from OpenAI API
 * @returns Path to the saved image
 */
async function processOpenAIResponse(response: any): Promise<string> {
  // Log the response structure for debugging
  console.log(`OpenAI response received with structure: created=${response.created}, data.length=${response.data.length}`);

  // Verify we have data in the response
  if (!response.data || response.data.length === 0) {
    console.error('Empty data array in OpenAI response');
    throw new Error('No image data returned from OpenAI');
  }

  // Get the image data from the response
  const imageData = response.data[0];

  // Log token usage if available
  // Use type assertion since the OpenAI SDK types might not be up to date
  const responseWithUsage = response as any;
  if (responseWithUsage.usage) {
    console.log(`OpenAI token usage: input=${responseWithUsage.usage.input_tokens}, output=${responseWithUsage.usage.output_tokens}, total=${responseWithUsage.usage.total_tokens}`);
  }

  let buffer;

  // Check if we have a URL or base64 data
  if (imageData.url) {
    // Handle URL-based response
    const imageUrl = imageData.url;
    console.log(`Image URL received from OpenAI: ${imageUrl}`);

    // Download the image from the URL
    const fetch = (await import('node-fetch')).default;
    const imageResponse = await fetch(imageUrl);

    if (!imageResponse.ok) {
      throw new Error(`Failed to download image: ${imageResponse.status} ${imageResponse.statusText}`);
    }

    buffer = Buffer.from(await imageResponse.arrayBuffer());
  } else if (imageData.b64_json) {
    // Handle base64-encoded response
    console.log(`Base64 image data received from OpenAI (length: ${imageData.b64_json.length} chars)`);
    buffer = Buffer.from(imageData.b64_json, 'base64');
  } else {
    // No valid image data found
    console.error('No image URL or base64 data in response:', JSON.stringify(imageData));
    throw new Error('No valid image data returned from OpenAI');
  }

  // Create directory and save image
  const timestamp = Date.now();
  const outputDir = path.join(process.cwd(), 'uploads', 'generated');
  await ensureDir(outputDir);
  const outputPath = path.join(outputDir, `generated_${timestamp}.png`);
  await fs.promises.writeFile(outputPath, buffer);
  console.log(`OpenAI generated image saved at: ${outputPath}`);
  return outputPath;
}

/**
 * Generate an image using OpenAI's gpt-image-1 model
 * @param prompt The text prompt describing the modifications to make
 * @param imagePaths Array of image paths to use as input (first one is the main image, others are reference)
 * @returns Path to the generated image
 */
export async function generateImage(prompt: string, imagePaths: string[] = []): Promise<string> {
  try {
    console.log(`Generating image with OpenAI gpt-image-1 using prompt: ${prompt}`);
    console.log(`Using ${imagePaths.length} images as input`);

    // Check if we have images to use
    if (imagePaths.length === 0) {
      console.log('No input images provided, using text-only generation');

      // Generate image with OpenAI gpt-image-1 using text only
      const response = await openai.images.generate({
        model: "gpt-image-1",
        prompt: prompt,
        n: 1,
        size: "1024x1024",
      });

      // Process the response
      return processOpenAIResponse(response);
    }

    // We have at least one image, use the edit endpoint
    console.log(`Using ${imagePaths.length} images with the edit endpoint`);

    try {
      // Read the main image (first in the array)
      const mainImagePath = imagePaths[0];
      console.log(`Main image path: ${mainImagePath}`);

      if (!fs.existsSync(mainImagePath)) {
        console.error(`Main image not found at path: ${mainImagePath}`);
        throw new Error('Main image not found');
      }

      // Read the main image file
      const mainImageFile = fs.createReadStream(mainImagePath);

      // Prepare reference images if available
      const referenceImageFiles = [];
      if (imagePaths.length > 1) {
        for (let i = 1; i < imagePaths.length; i++) {
          const refPath = imagePaths[i];
          if (fs.existsSync(refPath)) {
            console.log(`Reading reference image: ${refPath}`);
            const refFile = fs.createReadStream(refPath);
            referenceImageFiles.push(refFile);
          } else {
            console.warn(`Reference image not found: ${refPath}`);
          }
        }
      }

      // Log what we're sending to the API
      console.log(`Sending to OpenAI: 1 main image and ${referenceImageFiles.length} reference images`);

      // Create a FormData-like object for the API call
      const formData = new FormData();

      // Add the model
      formData.append('model', 'gpt-image-1');

      // Add the prompt
      formData.append('prompt', prompt);

      // Add the main image
      formData.append('image[]', mainImageFile, {
        filename: path.basename(mainImagePath),
        contentType: 'image/jpeg',
      });

      // Add reference images if available
      for (let i = 0; i < referenceImageFiles.length; i++) {
        const refFile = referenceImageFiles[i];
        const refPath = imagePaths[i + 1];
        formData.append('image[]', refFile, {
          filename: path.basename(refPath),
          contentType: 'image/jpeg',
        });
      }

      // Make a direct fetch call to the OpenAI API
      const fetch = (await import('node-fetch')).default;
      const apiResponse = await fetch('https://api.openai.com/v1/images/edits', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,
        },
        body: formData,
      });

      if (!apiResponse.ok) {
        const errorText = await apiResponse.text();
        console.error(`OpenAI API error: ${apiResponse.status} ${apiResponse.statusText}`);
        console.error(`Error details: ${errorText}`);
        throw new Error(`OpenAI API error: ${apiResponse.status} ${apiResponse.statusText}`);
      }

      // Parse the response
      const responseData = await apiResponse.json();
      console.log(`OpenAI response received with structure:`, JSON.stringify(Object.keys(responseData as object)));

      // Process the response
      return processOpenAIResponse(responseData);
    } catch (imageError) {
      console.error('Error processing images with edit endpoint:', imageError);
      console.log('Falling back to text-only generation');

      // Fall back to text-only generation
      const response = await openai.images.generate({
        model: "gpt-image-1",
        prompt: prompt,
        n: 1,
        size: "1024x1024",
      });

      // Process the response
      return processOpenAIResponse(response);
    }


  } catch (error: any) {
    console.error(`[ERROR] OpenAI gpt-image-1 generation failed:`, error);

    // Use the createErrorImage helper function for consistent error handling
    return createErrorImage(`Error generating image: ${error.message || 'Unknown error'}`);
  }
}

/**
 * Generate image based on before image and modification description using OpenAI gpt-image-1 only
 * @param beforeImagePath Path to the before image
 * @param modificationDescription The text prompt describing the modifications to make
 * @param referenceImagePaths Optional array of paths to reference images
 * @returns Path to the generated image
 */
export interface StructuredModification {
  type: string;
  description?: string;
  referenceImageIds?: number[];
  options?: {
    material?: string;
    color?: string;
    style?: string;
    customDescription?: string;
    [key: string]: any;
  };
}

export async function generateRenovationImage(
  beforeImagePath: string,
  modificationData: string | StructuredModification,
  referenceImagePaths: string[] = []
): Promise<string> {
  try {
    // Verify before image exists
    if (!fs.existsSync(beforeImagePath)) {
      console.error(`Before image not found at path: ${beforeImagePath}`);

      // Try to find the image with a different path format
      // If the path starts with /uploads/, try to find it in the project root
      if (beforeImagePath.startsWith('/uploads/')) {
        const altPath = path.join(process.cwd(), beforeImagePath);
        console.log(`Trying alternative path: ${altPath}`);

        if (fs.existsSync(altPath)) {
          console.log(`Found image at alternative path: ${altPath}`);
          beforeImagePath = altPath;
        } else {
          return createErrorImage('Before image could not be found. Please try uploading it again.');
        }
      } else {
        return createErrorImage('Before image could not be found. Please try uploading it again.');
      }
    }

    // Check if OpenAI API key is configured
    if (!process.env.OPENAI_API_KEY) {
      console.error('OpenAI API key is not configured');
      return createErrorImage('AI image generation is not available. Please contact support to enable this feature.');
    }

    // Double-check that the API key is valid (not the dummy key)
    if (process.env.OPENAI_API_KEY === 'dummy-key-for-initialization-only') {
      console.error('Using dummy OpenAI API key');
      return createErrorImage('AI image generation is not properly configured. Please contact support.');
    }

    // Use the prompt generation functions

    // Determine if we have a string description or structured data
    let modificationDescription: string;
    let modificationType: string = 'custom';
    let options: any = {};
    let primaryReferenceImagePath: string | null = null;
    let otherReferenceImagePaths: string[] = [];

    if (typeof modificationData === 'string') {
      modificationDescription = modificationData;
    } else {
      modificationDescription = modificationData.description || '';
      modificationType = modificationData.type;
      options = modificationData.options || {};

      // Check if we have a primary reference image
      if (modificationData.primaryReferenceImageId && referenceImagePaths.length > 0) {
        // Find the primary reference image path
        // Since we don't have direct access to the mapping between IDs and paths,
        // we'll use the first reference image as the primary one if available
        primaryReferenceImagePath = referenceImagePaths[0];
        otherReferenceImagePaths = referenceImagePaths.slice(1);
        console.log(`Using primary reference image: ${primaryReferenceImagePath}`);
      } else {
        otherReferenceImagePaths = referenceImagePaths;
      }
    }

    // Prepare parameters for prompt generation
    const promptParams = {
      description: modificationDescription,
      options: options,
      hasPrimaryReferenceImage: !!primaryReferenceImagePath,
      hasAdditionalReferenceImages: otherReferenceImagePaths.length > 0,
      referenceImageCount: (primaryReferenceImagePath ? 1 : 0) + otherReferenceImagePaths.length
    };

    // Generate the prompt using our template system
    const prompt = generatePrompt(modificationType, promptParams);

    // Log the prompt for debugging
    console.log(`Generated prompt for ${modificationType} modification:`, prompt);

    // Log reference image usage
    if (primaryReferenceImagePath) {
      console.log(`Using 1 primary reference image and ${otherReferenceImagePaths.length} additional reference images`);
    } else if (otherReferenceImagePaths.length > 0) {
      console.log(`Using ${otherReferenceImagePaths.length} reference images for the modification`);
    }

    // Note: Quality instructions are already included in the prompt templates
    // No need to add additional instructions here

    try {
      // Log the full prompt for debugging
      console.log(`Full prompt for OpenAI: ${prompt}`);

      // Prepare the list of image paths to pass to the API
      const imagePaths = [beforeImagePath];

      // Add reference images if available
      if (referenceImagePaths.length > 0) {
        console.log(`Adding ${referenceImagePaths.length} reference images to the API call`);
        imagePaths.push(...referenceImagePaths);
      }

      // Generate the image with OpenAI gpt-image-1, passing both the prompt and images
      return await generateImage(prompt, imagePaths);
    } catch (apiError: any) {
      // Enhanced error handling with more specific error types
      console.error('OpenAI API error details:', JSON.stringify(apiError, null, 2));

      // Handle specific API errors
      if (apiError.code === 'invalid_api_key') {
        console.error('Invalid OpenAI API key');
        return createErrorImage('AI service is temporarily unavailable. Our team has been notified.');
      } else if (apiError.status === 429) {
        console.error('OpenAI API rate limit exceeded');
        return createErrorImage('Our AI service is experiencing high demand. Please try again in a few minutes.');
      } else if (apiError.status === 400) {
        console.error(`OpenAI API bad request: ${apiError.message || 'Unknown error'}`);
        return createErrorImage('There was a problem with your request. Please try a different description.');
      } else if (apiError.status === 500) {
        console.error(`OpenAI API server error: ${apiError.message || 'Unknown error'}`);
        return createErrorImage('The AI service is currently experiencing issues. Please try again later.');
      } else {
        console.error(`OpenAI API error: ${apiError.message || 'Unknown error'}`);
        return createErrorImage('There was a problem generating your renovation image. Please try again.');
      }
    }
  } catch (error: any) {
    console.error(`[ERROR] Renovation image generation failed (gpt-image-1):`, error);
    return createErrorImage('An unexpected error occurred. Our team has been notified.');
  }
}

// Helper function to create a user-friendly error image
async function createErrorImage(errorMessage: string): Promise<string> {
  try {
    // First, try to create a fallback image
    const errorOutputDir = path.join(process.cwd(), 'uploads', 'generated');
    await ensureDir(errorOutputDir);

    // Path for the fallback image
    const fallbackImagePath = path.join(errorOutputDir, `error_${Date.now()}.png`);

    // Check if we have a default error image to use
    const defaultErrorImagePath = path.join(process.cwd(), 'assets', 'error-image.png');

    if (fs.existsSync(defaultErrorImagePath)) {
      // Copy the default error image to the output path
      await fs.promises.copyFile(defaultErrorImagePath, fallbackImagePath);
      console.log(`Created fallback error image at: ${fallbackImagePath}`);
      return fallbackImagePath;
    }

    // If no default image exists, create a simple text file as fallback
    const errorOutputPath = path.join(errorOutputDir, `error_${Date.now()}.txt`);

    // Save a user-friendly error message
    await fs.promises.writeFile(errorOutputPath, errorMessage);
    console.log(`Created error text file at: ${errorOutputPath}`);

    return errorOutputPath;
  } catch (error) {
    console.error('Error creating fallback error image:', error);
    // Last resort fallback - return a path that at least won't crash the app
    return path.join(process.cwd(), 'uploads', 'generated', `error_${Date.now()}.txt`);
  }
}