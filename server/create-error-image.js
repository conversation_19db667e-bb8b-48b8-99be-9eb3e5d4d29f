import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create a simple error image
const createErrorImage = () => {
  // Base64 encoded small PNG image with error message
  const errorImageBase64 = `
    iVBORw0KGgoAAAANSUhEUgAAAfQAAAH0CAYAAADL1t+KAAAMP2lDQ1BJQ0MgUHJvZmlsZQAASImVVwdYU8kWnluSkEBooUsJvQkiUgJICaGF3puohCRAKDEGgoodXVRw7WIBGzsqoqArsGtiJ4ti74sFFWVdLNiVNymg677yvfm+ufPff87858y5M/feAUDjBFciyUU1AcgTF0hjQgNZE5JTWKRuQAYMoAP0gCGXlydiR0dHAFiG2r+Xd9cBIm2v2Eu1/tn/X4sWXyDiAYBEQ5zGF/HyID4IAF7FE0kLACAq9JZTCyVSDCvQlsIAIV4sxRlKXCXFaUq8V24TF8OBuAUAMpXLlWYAoH4Z8qxCXgbUUO+D2EnMF4kB0GBD7JeXN5kPcRrENlBGArFUn5Xxi+YZ31XP0Eo1uZyMCq7MRV4oBSKRhDs9+P9Mzv+WvFxJuQ9rWKmZ0rAY6ZxhXm/lTA6XYhXE3eK0yCiItSF+L+TL7SFGKZmSsASlfVTPE3FgzoAmxE58bng4xEYQh4hzIyMqeGm6whA2xHCFoNOEhew4iPUgXiQQBcdW2GyRTo6p8IXWpUs5nAp+HlcqH1Pq60FOfDy7Qv9VJp9doY/pF2XGxUPMgNiiUJgYBbE6xI6inPjwCpvRRZmciHIbqSQmGn4/C4gFwqBApdZYQZo0JKbCvjRPVD5f7GZmFjuqAu/PzIwLU+YHO8Pjyr8fzgUXCMTxFXrYqAmJEeVz4QkCg0OUc8eeC8QJcRU6HyRFgbHKsXhKbm50BT3eIsgNlPJ2EDtLCmMrxuKpQrgglfp4hqQwOk6ZR7woWxodrcwHXgYiAAcEARaQwJoGJoNsIGrvruvCb8qeEMAFUpABBMChgikfkSTvEcNnLCgCf0IkAKKKcYHyXgEohPzXCq7y6QDSZb2F8hE54AmI80A4yIXfJfJR4gpv8eAxZET/8M6FlQfzzYVV2v/P/DL2O8OGTEQFIyn3yNJQNwwkBmGCMGQYFmuIvbEf9oMvH1gdsQf2Kc/juz3hCaGD8JBwndBJuD1JtFjyS5ZjQSfUD6nMRdqvucC2UNMTh2BfqA7VsQb2ABbYFfphYX/o2Q2ynIq4pVlh/aL9tzP47dVU2JGdyChZg+xPtv11pLqzulelirTWv+ZHmeu0yrw7vfzaP+cX2efDNuLXlthC7AB2FjuJncOOYo2AhZ3AmrA27JgUV6yIx3IronyLmJxODtQR/eOvwrNUmkmRc6Nzl/Ojsi9QME36jQLOZMl0qShDUMhiw6+DgMURc5xGsTydnJ0BkH5flK+vt2Hy7wbCuPCdK3gLgMDSgYGBw9+5sGYA9nnA5X/kO2fNgl8PZgCcbeNJpYVKDksfBPgW0YA7Sh8YAwtgC+fjDNyBDwgAwSAcRIM4kAymwOxnwnUuBVPBTDAfFINSsBKsA5vAFrAD7Ab7wSHQCI6DU+AcuAiugJvgPlwdPeA56Adv 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 AAAAagJpAAUAAACwAAAAcAJpAAUAAAC4AAAAdgJpAAUAAADAAAAAewJpAAUAAADIAAAAgQJpAAUAAADQAAAAhwJpAAUAAADYAAAAjQJpAAUAAADgAAAAkwJpAAUAAADoAAAAmQJpAAUAAADwAAAAnwJpAAUAAAD4AAAApQJpAAUAAAAAAgAAqwJpAAUAAAAIAgAAsQJpAAUAAAAQAgAAtwJpAAUAAAAYAgAAvQJpAAUAAAAgAgAAwwJpAAUAAAAoAgAAyQJpAAUAAAAwAgAAzwJpAAUAAAA4AgAA1QJpAAUAAABAAgAA2wJpAAUAAABIAgAA4QJpAAUAAABQAgAA5wJpAAUAAABYAgAA7QJpAAUAAABgAgAA8wJpAAUAAABoAgAA+QJpAAUAAABwAgAA/wJpAAUAAAB4AgAABQNpAAUAAACAAAAACwNpAAUAAACIAAAAEQNpAAUAAACQAAAAFwNpAAUAAACYAAAAHQNpAAUAAACgAAAAIwNpAAUAAACo AAAAKQNpAAUAAACwAAAALwNpAAUAAAC4AAAANQNpAAUAAADAAAAAOwNpAAUAAADIAAAAQQNpAAUAAADQAAAARwNpAAUAAADYAAAATQNpAAUAAADgAAAAUwNpAAUAAADoAAAAWQNpAAUAAADwAAAAXwNpAAUAAAD4AAAAZQNpAAUAAAAAAwAAawNpAAUAAAAIAwAA/gIDAAAAAQAAAAAAAQMAAQAAAEAAAAABAQMAAQAAAEAAAAACAQMAAQAAAQAAAAADAQMAAQAAAQAAAAA=
  `;

  // Remove whitespace and newlines
  const cleanBase64 = errorImageBase64.replace(/\\s/g, '');

  // Write the image to a file
  const outputPath = path.join(__dirname, '..', 'assets', 'error-image.png');
  const imageBuffer = Buffer.from(cleanBase64, 'base64');

  fs.writeFileSync(outputPath, imageBuffer);
  console.log(`Created error image at: ${outputPath}`);
}

createErrorImage();
