import { OpenAI } from 'openai';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables from .env file
dotenv.config({ path: path.join(__dirname, '..', '.env') });

async function verifyOpenAIKey() {
  const apiKey = process.env.OPENAI_API_KEY;

  if (!apiKey) {
    console.error('❌ OPENAI_API_KEY is not set in the environment variables.');
    console.log('Please add your OpenAI API key to the .env file:');
    console.log('OPENAI_API_KEY="your-api-key-here"');
    return false;
  }

  console.log(`Found OpenAI API key: ${apiKey.substring(0, 8)}...${apiKey.substring(apiKey.length - 4)}`);

  try {
    // Initialize OpenAI with the API key
    const openai = new OpenAI({ apiKey });

    // Make a simple API call to verify the key works
    console.log('Testing OpenAI API key with a simple models.list call...');
    const models = await openai.models.list();

    console.log(`✅ OpenAI API key is valid! Found ${models.data.length} models.`);
    return true;
  } catch (error) {
    console.error('❌ Error verifying OpenAI API key:', error.message);
    console.log('Please check that your API key is correct and has not expired.');
    return false;
  }
}

// Run the verification
verifyOpenAIKey().then(isValid => {
  if (!isValid) {
    process.exit(1);
  }
});
