import { createClient } from '@supabase/supabase-js';
import * as schema from "@shared/schema";

if (!process.env.SUPABASE_URL || !process.env.SUPABASE_DB_KEY) {
  throw new Error(
    "SUPABASE_URL and SUPABASE_DB_KEY must be set. Did you forget to set up Supabase?"
  );
}

// Initialize Supabase client with database key for full database access
// Auth is completely disabled - using Supabase only as a database
export const supabase = createClient(
  process.env.SUPABASE_URL as string,
  process.env.SUPABASE_DB_KEY as string,
  {
    auth: {
      persistSession: false, // Disable auth session persistence
      autoRefreshToken: false, // Disable token refresh
      detectSessionInUrl: false // Disable detecting session in URL
    }
  }
);

// Export database interface for consistency with previous code
export const db = {
  client: supabase,
  schema: schema
};
