import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create a simple error image
const createErrorImage = () => {
  // Create the assets directory if it doesn't exist
  const assetsDir = path.join(__dirname, '..', 'assets');
  if (!fs.existsSync(assetsDir)) {
    fs.mkdirSync(assetsDir, { recursive: true });
  }

  // This is a minimal 100x100 pixel PNG file (red color)
  // A simple red square that will be visible as an error indicator
  const pngData = Buffer.from([
    0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
    0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x64, 0x00, 0x00, 0x00, 0x64,
    0x08, 0x02, 0x00, 0x00, 0x00, 0xFF, 0x80, 0x02, 0x03, 0x00, 0x00, 0x00,
    0x06, 0x62, 0x4B, 0x47, 0x44, 0x00, 0xFF, 0x00, 0x00, 0x00, 0xFF, 0xFF,
    0x00, 0x00, 0x00, 0x09, 0x70, 0x48, 0x59, 0x73, 0x00, 0x00, 0x0B, 0x13,
    0x00, 0x00, 0x0B, 0x13, 0x01, 0x00, 0x9A, 0x9C, 0x18, 0x00, 0x00, 0x00,
    0x07, 0x74, 0x49, 0x4D, 0x45, 0x07, 0xE6, 0x04, 0x1C, 0x0F, 0x25, 0x24,
    0xAD, 0xD6, 0xE3, 0xE9, 0x00, 0x00, 0x00, 0x1D, 0x69, 0x54, 0x58, 0x74,
    0x43, 0x6F, 0x6D, 0x6D, 0x65, 0x6E, 0x74, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x45, 0x72, 0x72, 0x6F, 0x72, 0x20, 0x49, 0x6D, 0x61, 0x67, 0x65, 0xAE,
    0xBA, 0x91, 0x74, 0x00, 0x00, 0x00, 0x15, 0x49, 0x44, 0x41, 0x54, 0x78,
    0xDA, 0xED, 0xC1, 0x01, 0x01, 0x00, 0x00, 0x00, 0x82, 0x20, 0xFF, 0xAF,
    0x6E, 0x48, 0x40, 0x01, 0x00, 0x00, 0x00, 0xEF, 0x06, 0x10, 0x20, 0x00,
    0x01, 0x44, 0x68, 0x7E, 0x69, 0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4E,
    0x44, 0xAE, 0x42, 0x60, 0x82
  ]);

  const errorImagePath = path.join(assetsDir, 'error-image.png');
  fs.writeFileSync(errorImagePath, pngData);

  console.log(`Created error image at: ${errorImagePath}`);
};

createErrorImage();
