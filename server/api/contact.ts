import { Request, Response } from 'express';
import { logger } from '../utils/logger';
import { supabase } from '../db';
import { sendContactNotification, sendContactAutoReply } from '../services/email';
import { insertContactSubmissionSchema } from '@shared/schema';

/**
 * Handle contact form submissions
 *
 * @param req Express request object
 * @param res Express response object
 */
export async function handleContactForm(req: Request, res: Response) {
  try {
    // Validate the request body
    const validatedData = insertContactSubmissionSchema.parse(req.body);

    // Log the contact form submission
    logger.info('Contact form submission received', {
      name: validatedData.name,
      email: validatedData.email,
      subject: validatedData.subject,
    });

    // 1. Store the contact form submission in the database
    const { data: submission, error: dbError } = await supabase
      .from('contact_submissions')
      .insert({
        name: validatedData.name,
        email: validatedData.email,
        subject: validatedData.subject,
        message: validatedData.message,
      })
      .select()
      .single();

    if (dbError) {
      logger.error('Error storing contact form submission in database', { error: dbError });
      throw new Error('Failed to store contact form submission');
    }

    logger.info('Contact form submission stored in database', { id: submission.id });

    // 2. Send an email notification to the support team
    try {
      await sendContactNotification(
        validatedData.name,
        validatedData.email,
        validatedData.subject,
        validatedData.message
      );
      logger.info('Contact notification email sent to support team');
    } catch (emailError) {
      logger.error('Error sending contact notification email', { error: emailError });
      // Continue processing even if email fails
    }

    // 3. Send a confirmation email to the user
    try {
      await sendContactAutoReply(
        validatedData.name,
        validatedData.email,
        validatedData.subject
      );
      logger.info('Auto-reply email sent to user', { email: validatedData.email });
    } catch (emailError) {
      logger.error('Error sending auto-reply email', { error: emailError });
      // Continue processing even if email fails
    }

    // Return a success response
    return res.status(200).json({
      success: true,
      message: 'Contact form submitted successfully',
    });
  } catch (error) {
    // Log the error
    logger.error('Error handling contact form submission', { error });

    // Return an error response
    if (error.name === 'ZodError') {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: error.errors,
      });
    }

    return res.status(500).json({
      success: false,
      message: 'An error occurred while processing your request',
    });
  }
}
