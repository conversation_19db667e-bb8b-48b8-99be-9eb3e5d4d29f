import { Request, Response } from 'express';
import { z } from 'zod';
import { logger } from '../utils/logger';
import { db } from '../db';

// Define the newsletter subscription schema for validation
const newsletterSubscriptionSchema = z.object({
  email: z.string().email(),
});

/**
 * Handle newsletter subscription requests
 * 
 * @param req Express request object
 * @param res Express response object
 */
export async function handleNewsletterSubscription(req: Request, res: Response) {
  try {
    // Validate the request body
    const validatedData = newsletterSubscriptionSchema.parse(req.body);
    const { email } = validatedData;
    
    // Log the subscription attempt
    logger.info('Newsletter subscription received', { email });
    
    try {
      // Check if the email already exists and get its status
      const { data: existingSubscription, error: fetchError } = await db.client
        .from('contact_subscription')
        .select('*')
        .eq('email', email)
        .single();

      if (fetchError && fetchError.code !== 'PGRST116') { // PGRST116 is "no rows returned"
        logger.error('Error checking existing subscription', { error: fetchError, email });
        throw new Error('Failed to check subscription status');
      }

      if (existingSubscription) {
        if (existingSubscription.status === 'active') {
          return res.status(200).json({
            success: true,
            message: 'You are already subscribed to our newsletter',
          });
        }

        // If previously unsubscribed, reactivate the subscription
        const { error: updateError } = await db.client
          .from('contact_subscription')
          .update({
            status: 'active',
            unsubscribed_at: null,
            updated_at: new Date().toISOString()
          })
          .eq('email', email);

        if (updateError) {
          logger.error('Error reactivating subscription', { error: updateError, email });
          throw new Error('Failed to reactivate subscription');
        }

        logger.info('Reactivated newsletter subscription', { email });
        return res.status(200).json({
          success: true,
          message: 'Your newsletter subscription has been reactivated',
        });
      }

      // Add new subscription
      const { error: insertError } = await db.client
        .from('contact_subscription')
        .insert([
          {
            email,
            status: 'active'
          }
        ]);

      if (insertError) {
        logger.error('Error creating subscription', { error: insertError, email });
        throw new Error('Failed to create subscription');
      }

      logger.info('New newsletter subscription created', { email });
      
      // Return a success response
      return res.status(200).json({
        success: true,
        message: 'Successfully subscribed to the newsletter',
      });
    } catch (dbError) {
      logger.error('Database error while processing subscription', { error: dbError, email });
      throw new Error('Failed to process subscription');
    }
  } catch (error) {
    // Log the error
    logger.error('Error handling newsletter subscription', { error });
    
    // Return an error response
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: 'Invalid email address',
        errors: error.errors,
      });
    }
    
    return res.status(500).json({
      success: false,
      message: 'An error occurred while processing your request',
    });
  }
}

/**
 * Handle newsletter unsubscription requests
 * 
 * @param req Express request object
 * @param res Express response object
 */
export async function handleNewsletterUnsubscription(req: Request, res: Response) {
  try {
    // Validate the request body
    const validatedData = newsletterSubscriptionSchema.parse(req.body);
    const { email } = validatedData;
    
    // Log the unsubscription attempt
    logger.info('Newsletter unsubscription received', { email });
    
    try {
      // Update the subscription status
      const { error: updateError } = await db.client
        .from('contact_subscription')
        .update({
          status: 'unsubscribed',
          unsubscribed_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('email', email)
        .eq('status', 'active');

      if (updateError) {
        logger.error('Error updating subscription status', { error: updateError, email });
        throw new Error('Failed to process unsubscription');
      }

      logger.info('Newsletter unsubscription processed', { email });
      
      // Return a success response
      return res.status(200).json({
        success: true,
        message: 'Successfully unsubscribed from the newsletter',
      });
    } catch (dbError) {
      logger.error('Database error while processing unsubscription', { error: dbError, email });
      throw new Error('Failed to process unsubscription');
    }
  } catch (error) {
    // Log the error
    logger.error('Error handling newsletter unsubscription', { error });
    
    // Return an error response
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: 'Invalid email address',
        errors: error.errors,
      });
    }
    
    return res.status(500).json({
      success: false,
      message: 'An error occurred while processing your request',
    });
  }
}
