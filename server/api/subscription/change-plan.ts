import { Request, Response } from 'express';
import { stripe } from '../../lib/stripe';
import { getAuth } from '@clerk/nextjs/server';
import { logger } from '../../utils/logger';
import { getCustomerByUserId } from '../../services/stripe-service';
import { PLAN_LIMITS } from '../../lib/subscription-plans';

export default async function handler(req: Request, res: Response) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    const { userId } = getAuth(req);
    if (!userId) {
      logger.warn('Unauthorized plan change attempt');
      return res.status(401).json({ message: 'Unauthorized' });
    }

    const { newPlan, billingCycle } = req.body;

    if (!newPlan || !billingCycle) {
      logger.warn('Invalid plan change request', { userId, newPlan, billingCycle });
      return res.status(400).json({ message: 'Missing required fields' });
    }

    logger.info('Processing subscription plan change', {
      userId,
      newPlan,
      billingCycle,
      requestId: req.id
    });

    // Get the customer
    const customer = await getCustomerByUserId(userId);
    if (!customer) {
      logger.error('Customer not found for plan change', { userId });
      return res.status(404).json({ message: 'Customer not found' });
    }

    // Get current subscription
    const subscriptions = await stripe.subscriptions.list({
      customer: customer.id,
      status: 'all',
      limit: 1
    });

    const currentSubscription = subscriptions.data[0];
    
    if (!currentSubscription) {
      logger.error('No subscription found for customer', { 
        userId, 
        customerId: customer.id 
      });
      return res.status(404).json({ message: 'No subscription found' });
    }

    // Handle different subscription states
    switch (currentSubscription.status) {
      case 'active':
        // Handle active subscription
        if (currentSubscription.cancel_at_period_end) {
          // If subscription is set to cancel, remove the cancellation
          await stripe.subscriptions.update(currentSubscription.id, {
            cancel_at_period_end: false,
            proration_behavior: 'create_prorations',
            items: [{
              id: currentSubscription.items.data[0].id,
              price: `${newPlan}_${billingCycle}`,
            }],
          });
          logger.info('Reactivated and changed plan for subscription', {
            userId,
            subscriptionId: currentSubscription.id,
            newPlan,
            billingCycle
          });
        } else {
          // Regular plan change
          await stripe.subscriptions.update(currentSubscription.id, {
            proration_behavior: 'create_prorations',
            items: [{
              id: currentSubscription.items.data[0].id,
              price: `${newPlan}_${billingCycle}`,
            }],
          });
          logger.info('Changed plan for active subscription', {
            userId,
            subscriptionId: currentSubscription.id,
            newPlan,
            billingCycle
          });
        }
        break;

      case 'past_due':
        // Handle past due subscriptions
        logger.warn('Attempting to change plan for past due subscription', {
          userId,
          subscriptionId: currentSubscription.id
        });
        return res.status(400).json({ 
          message: 'Cannot change plan while subscription is past due. Please update payment method first.' 
        });

      case 'canceled':
        // Create new subscription for canceled subscriptions
        const subscription = await stripe.subscriptions.create({
          customer: customer.id,
          items: [{
            price: `${newPlan}_${billingCycle}`,
          }],
          payment_behavior: 'default_incomplete',
          expand: ['latest_invoice.payment_intent'],
        });
        logger.info('Created new subscription after cancellation', {
          userId,
          newSubscriptionId: subscription.id,
          newPlan,
          billingCycle
        });
        break;

      default:
        logger.error('Invalid subscription status for plan change', {
          userId,
          status: currentSubscription.status
        });
        return res.status(400).json({ 
          message: 'Cannot change plan with current subscription status' 
        });
    }

    return res.status(200).json({ message: 'Subscription updated successfully' });
  } catch (error) {
    logger.error('Error changing subscription plan', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: getAuth(req)?.userId
    });
    return res.status(500).json({ 
      message: 'Error updating subscription',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
} 