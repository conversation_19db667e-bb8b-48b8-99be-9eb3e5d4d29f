import type { Express, Request, Response, NextFunction } from "express";
import express from "express";
import { createServer, type Server } from "http";
import Stripe from "stripe";
import { storage } from "./storage";
import { db } from "./db";
import multer from "multer";
import path from "path";
import fs from "fs";
import { generateRenovationImage } from "./openai"; // OpenAI fallback for image generation
import contactRoutes from "./routes/contact";
import newsletterRoutes from "./routes/newsletter";
import {
  asyncHandler,
  createNotFoundError,
  createValidationError,
  createInternalError,
  createAuthenticationError,
  createAuthorizationError
} from "./errorHandler";
import {
  insertProjectSchema,
  insertModificationSchema,
  insertReferenceCategorySchema,
  insertReferenceItemSchema,
  insertRenovationPresetSchema,
  insertDraftSchema,
  ReferenceCategory
} from "@shared/schema";
import { clerkAuth } from "./clerk"; // Import Clerk authentication
import { requireSubscription, checkProjectLimit, checkImageLimit } from "./middleware/subscription";
import { subscriptionService, PLAN_LIMITS } from "./services/subscription";
import { couponService } from "./services/coupon";
import { z } from "zod";
import { withRetry } from "./utils/retry";
import { logger as backendLogger } from "./utils/logger";

// Initialize Stripe with the secret key
if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error('Missing required Stripe secret: STRIPE_SECRET_KEY');
}
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: "2025-03-31.basil" as any,
});

// Configure multer for file uploads
const storage_config = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = path.join(process.cwd(), "uploads");
    // Ensure the directory exists
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + "-" + Math.round(Math.random() * 1e9);
    cb(
      null,
      file.fieldname + "-" + uniqueSuffix + path.extname(file.originalname)
    );
  },
});

const upload = multer({
  storage: storage_config,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = [
      "image/jpeg",
      "image/png",
      "image/webp",
      "image/jpg",
    ];
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error("Unsupported file type. Please upload JPEG, PNG, or WEBP images only.") as any);
    }
  },
});

// Helper function to get user ID from Clerk user
function getUserId(req: Request): string {
  // Check if this is an unauthenticated request that was allowed to proceed
  if (req.isUnauthenticated) {
    throw createAuthenticationError("User not authenticated");
  }

  if (!req.clerkUser || !req.clerkUser.id) {
    throw createAuthenticationError("User not authenticated");
  }
  // Return the Clerk user ID directly - no need to parse as UUID
  return req.clerkUser.id;
}

// Helper function to check if user is an admin
function isAdmin(req: Request): boolean {
  // Check if the user has admin role in Clerk metadata
  // This is a simple implementation - you might want to enhance this with proper role management
  if (!req.clerkUser) return false;

  // Check for admin role in public metadata
  const publicMetadata = req.clerkUser.publicMetadata || {};
  if (publicMetadata.role === 'admin') return true;

  // Check for admin role in private metadata
  const privateMetadata = req.clerkUser.privateMetadata || {};
  if (privateMetadata.role === 'admin') return true;

  // Hardcoded admin IDs for initial setup
  const adminIds = process.env.ADMIN_USER_IDS ? process.env.ADMIN_USER_IDS.split(',') : [];
  return adminIds.includes(req.clerkUser.id);
}

// Add a simple structured logger for the backend
const backendLogger = {
  info: (...args: any[]) => console.info('[API]', ...args),
  warn: (...args: any[]) => console.warn('[API]', ...args),
  error: (...args: any[]) => console.error('[API]', ...args),
  debug: (...args: any[]) => {
    if (process.env.NODE_ENV === 'development') {
      console.debug('[API]', ...args);
    }
  },
};

export async function registerRoutes(app: Express): Promise<Server> {
  // Parse JSON bodies for all requests
  app.use(express.json());

  // Set up static file serving from the uploads directory
  app.use("/uploads", express.static(path.join(process.cwd(), "uploads")));

  // Register additional route modules
  app.use(contactRoutes);
  app.use(newsletterRoutes);

  // API routes
  app.get("/api/projects", clerkAuth, asyncHandler(async (req: Request, res: Response) => {
    console.log('[DEBUG] GET /api/projects - Request received');
    console.log('[DEBUG] GET /api/projects - Auth headers:', req.headers.authorization ? `${req.headers.authorization.substring(0, 20)}...` : 'none');
    console.log('[DEBUG] GET /api/projects - Clerk user:', req.clerkUser);

    try {
      const userId = getUserId(req);
      console.log(`[DEBUG] GET /api/projects - User ID: ${userId}`);

      const projects = await storage.getProjectsByUserId(userId);
      console.log(`[DEBUG] GET /api/projects - Found ${projects.length} projects for user`);

      // Get projects with images
      const projectsWithImages = await Promise.all(
        projects.map(async (project) => {
          return await storage.getProjectWithImages(project.id);
        })
      );

      console.log(`[DEBUG] GET /api/projects - Returning ${projectsWithImages.length} projects with images`);
      res.json(projectsWithImages);
    } catch (error) {
      console.error('[DEBUG] GET /api/projects - Error:', error);
      throw error; // Let asyncHandler handle the error
    }
  }));

  app.get("/api/projects/:id", asyncHandler(async (req: Request, res: Response) => {
    const projectId = parseInt(req.params.id);
    if (isNaN(projectId)) {
      throw createValidationError("Invalid project ID");
    }

    const project = await storage.getProjectWithImages(projectId);

    if (!project) {
      throw createNotFoundError("Project not found");
    }

    res.json(project);
  }));

  app.post("/api/projects", clerkAuth, requireSubscription, checkProjectLimit, async (req: Request, res: Response) => {
    try {
      const userId = getUserId(req);

      // Ensure user exists in the users table
      let user = await storage.getUser(userId);
      if (!user) {
        // Insert user with available Clerk info (only id is guaranteed)
        const userData = {
          id: userId,
          username: 'User',
          email: null,
          first_name: null,
          last_name: null,
          bio: null,
          profile_image_url: null,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };
        user = await storage.upsertUser(userData);
        backendLogger.info(`[USER] Created new user in DB: ${userId}`);
      } else {
        backendLogger.info(`[USER] User already exists in DB: ${userId}`);
      }

      const parsedData = insertProjectSchema.parse({
        ...req.body,
        user_id: userId, // Use user_id to match the database schema
      });

      const project = await storage.createProject(parsedData);

      // Only increment project count if this is not a draft
      // Projects are counted when they transition from draft to processing
      if (parsedData.status && parsedData.status !== 'draft') {
        backendLogger.info(`[USAGE] Incrementing project count for non-draft project: ${project.id}`);
        await storage.incrementProjectCount(userId);
      } else {
        backendLogger.info(`[USAGE] Not counting draft project: ${project.id}`);
      }

      res.status(201).json(project);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: "Invalid project data", errors: error.errors });
      }
      console.error("Error creating project:", error);
      res.status(500).json({ message: "Error creating project" });
    }
  });

  // Upload before images
  app.post(
    "/api/projects/:id/images/before",
    clerkAuth,
    requireSubscription,
    checkImageLimit,
    upload.array("images", 10),
    async (req: Request, res: Response) => {
      try {
        const userId = getUserId(req);
        const projectId = parseInt(req.params.id);
        const project = await storage.getProject(projectId);

        if (!project) {
          return res.status(404).json({ message: "Project not found" });
        }

        // Verify project belongs to user
        if (project.user_id !== userId) {
          return res.status(403).json({ message: "Not authorized to modify this project" });
        }

        const files = req.files as Express.Multer.File[];
        if (!files || files.length === 0) {
          return res.status(400).json({ message: "No files uploaded" });
        }

        const savedImages = await Promise.all(
          files.map(async (file) => {
            return await storage.createImage({
              project_id: projectId,
              type: "before",
              path: file.path,
              original_filename: file.originalname,
            });
          })
        );

        // Note: Image usage is counted when images are successfully generated via OpenAI API,
        // not when uploaded. This prevents abuse and ensures fair usage tracking.
        backendLogger.info(`[USAGE] Uploaded ${savedImages.length} before images for project ${projectId} - usage will be counted on successful generation`);

        res.status(201).json(savedImages);
      } catch (error) {
        console.error("Error uploading before images:", error);
        res.status(500).json({ message: "Error uploading images" });
      }
    }
  );

  // Upload reference images
  app.post(
    "/api/projects/:id/images/reference",
    clerkAuth,
    requireSubscription,
    checkImageLimit,
    upload.array("images", 10),
    async (req: Request, res: Response) => {
      try {
        const userId = getUserId(req);
        const projectId = parseInt(req.params.id);
        const project = await storage.getProject(projectId);

        if (!project) {
          return res.status(404).json({ message: "Project not found" });
        }

        // Verify project belongs to user
        if (project.user_id !== userId) {
          return res.status(403).json({ message: "Not authorized to modify this project" });
        }

        const files = req.files as Express.Multer.File[];
        if (!files || files.length === 0) {
          return res.status(400).json({ message: "No files uploaded" });
        }

        const savedImages = await Promise.all(
          files.map(async (file) => {
            return await storage.createImage({
              project_id: projectId,
              type: "reference",
              path: file.path,
              original_filename: file.originalname,
            });
          })
        );

        // Note: Reference images don't count toward usage - only generated images do
        backendLogger.info(`[USAGE] Uploaded ${savedImages.length} reference images for project ${projectId} - these don't count toward usage`);

        // Automatically save reference images to user's reference library
        try {
          // Get or create a default category for auto-saved reference images
          let defaultCategory = await getOrCreateDefaultReferenceCategory(userId);

          // Save each image to the reference library
          await Promise.all(
            files.map(async (file) => {
              const filename = file.originalname || 'Reference image';
              const projectTitle = project.title || 'Project';

              await storage.createReferenceItem({
                user_id: userId,
                category_id: defaultCategory.id,
                name: `${filename} (from ${projectTitle})`,
                description: `Auto-saved from project: ${projectTitle}`,
                image_path: file.path,
                tags: ['auto-saved', 'project-reference']
              });
            })
          );
        } catch (saveError) {
          // Log error but don't fail the main request
          console.error("Error saving reference images to library:", saveError);
        }

        res.status(201).json(savedImages);
      } catch (error) {
        console.error("Error uploading reference images:", error);
        res.status(500).json({ message: "Error uploading images" });
      }
    }
  );

  // Helper function to get or create a default reference category for auto-saved images
  async function getOrCreateDefaultReferenceCategory(userId: string): Promise<ReferenceCategory> {
    const DEFAULT_CATEGORY_NAME = "Auto-saved References";

    // Try to find an existing default category
    const { data: categories } = await db.client
      .from('reference_categories')
      .select('*')
      .eq('user_id', userId)
      .eq('name', DEFAULT_CATEGORY_NAME);

    if (categories && categories.length > 0) {
      return categories[0];
    }

    // Create a new default category if none exists
    const newCategory = await storage.createReferenceCategory({
      user_id: userId,
      name: DEFAULT_CATEGORY_NAME,
      description: "Automatically saved reference images from your projects"
    });

    return newCategory;
  }

  // Create a modification and generate the after image
  app.post("/api/projects/:id/modifications", clerkAuth, async (req: Request, res: Response) => {
    try {
      console.log('=== CREATE MODIFICATION REQUEST ===');
      console.log('Request body:', JSON.stringify(req.body, null, 2));
      console.log('Project ID from params:', req.params.id);
      
      const userId = getUserId(req);
      console.log('User ID:', userId);
      
      const projectId = parseInt(req.params.id);
      const project = await storage.getProject(projectId);

      if (!project) {
        console.log('Project not found:', projectId);
        return res.status(404).json({ message: "Project not found" });
      }

      console.log('Project found:', { id: project.id, user_id: project.user_id });

      // Ensure user owns the project
      if (project.user_id !== userId) {
        console.log('Access denied - project user_id:', project.user_id, 'request user_id:', userId);
        return res.status(403).json({ message: "Access denied" });
      }

      console.log('Parsing modification data...');
      
      const parsedData = insertModificationSchema.parse({
        ...req.body,
        project_id: projectId,
      });
      console.log('Parsed data:', JSON.stringify(parsedData, null, 2));

      // Create the modification
      const modification = await storage.createModification(parsedData);

      // Start processing the images (async)
      processModification(projectId, modification.id).catch(console.error);

      res.status(201).json(modification);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: "Invalid modification data", errors: error.errors });
      }
      console.error("Error creating modification:", error);
      res.status(500).json({ message: "Error creating modification" });
    }
  });

  // Reference categories endpoints
  app.get("/api/reference-categories", clerkAuth, async (req: Request, res: Response) => {
    try {
      const userId = getUserId(req); // Don't parse as integer, Clerk IDs are strings
      const categories = await storage.getReferenceCategoriesByUserId(userId);
      res.json(categories);
    } catch (error) {
      console.error("Error fetching reference categories:", error);
      res.status(500).json({ error: "Failed to fetch reference categories" });
    }
  });

  app.get("/api/reference-categories/:id", clerkAuth, async (req: Request, res: Response) => {
    try {
      const userId = getUserId(req); // Don't parse as integer, Clerk IDs are strings

      const categoryId = parseInt(req.params.id);
      const category = await storage.getReferenceCategoryWithItems(categoryId);

      if (!category) {
        return res.status(404).json({ error: "Reference category not found" });
      }

      // Check that this user owns the category
      if (category.user_id !== userId) {
        return res.status(403).json({ error: "Not authorized to access this reference category" });
      }

      res.json(category);
    } catch (error) {
      console.error("Error fetching reference category:", error);
      res.status(500).json({ error: "Failed to fetch reference category" });
    }
  });

  app.post("/api/reference-categories", clerkAuth, async (req: Request, res: Response) => {
    try {
      const userId = getUserId(req); // Don't parse as integer, Clerk IDs are strings

      const parsedData = insertReferenceCategorySchema.parse({
        ...req.body,
        user_id: userId, // Use user_id to match the database schema
      });

      const category = await storage.createReferenceCategory(parsedData);
      res.status(201).json(category);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ error: "Invalid category data", details: error.errors });
      }
      console.error("Error creating reference category:", error);
      res.status(500).json({ error: "Failed to create reference category" });
    }
  });

  app.patch("/api/reference-categories/:id", clerkAuth, async (req: Request, res: Response) => {
    try {
      const userId = getUserId(req); // Don't parse as integer, Clerk IDs are strings

      const categoryId = parseInt(req.params.id);
      const category = await storage.getReferenceCategory(categoryId);

      if (!category) {
        return res.status(404).json({ error: "Reference category not found" });
      }

      // Check that this user owns the category
      if (category.user_id !== userId) {
        return res.status(403).json({ error: "Not authorized to modify this reference category" });
      }

      // Parse and validate the update data
      const updateData = insertReferenceCategorySchema.partial().parse(req.body);
      // Make sure user_id can't be changed
      delete updateData.user_id;

      const updatedCategory = await storage.updateReferenceCategory(categoryId, updateData);
      res.json(updatedCategory);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ error: "Invalid category data", details: error.errors });
      }
      console.error("Error updating reference category:", error);
      res.status(500).json({ error: "Failed to update reference category" });
    }
  });

  app.delete("/api/reference-categories/:id", clerkAuth, async (req: Request, res: Response) => {
    try {
      const userId = getUserId(req); // Don't parse as integer, Clerk IDs are strings

      const categoryId = parseInt(req.params.id);
      const category = await storage.getReferenceCategory(categoryId);

      if (!category) {
        return res.status(404).json({ error: "Reference category not found" });
      }

      // Check that this user owns the category
      if (category.user_id !== userId) {
        return res.status(403).json({ error: "Not authorized to delete this reference category" });
      }

      await storage.deleteReferenceCategory(categoryId);
      res.status(204).end();
    } catch (error) {
      console.error("Error deleting reference category:", error);
      res.status(500).json({ error: "Failed to delete reference category" });
    }
  });

  // Reference items endpoints
  app.get("/api/reference-items", clerkAuth, async (req: Request, res: Response) => {
    try {
      const userId = getUserId(req); // Don't parse as integer, Clerk IDs are strings
      const items = await storage.getReferenceItemsByUserId(userId);
      res.json(items);
    } catch (error) {
      console.error("Error fetching reference items:", error);
      res.status(500).json({ error: "Failed to fetch reference items" });
    }
  });

  app.get("/api/reference-items/:id", clerkAuth, async (req: Request, res: Response) => {
    try {
      const userId = getUserId(req); // Don't parse as integer, Clerk IDs are strings

      const itemId = parseInt(req.params.id);
      const item = await storage.getReferenceItem(itemId);

      if (!item) {
        return res.status(404).json({ error: "Reference item not found" });
      }

      // Check that this user owns the item
      const currentUserId = (req.user as any).claims?.sub || req.user.id;
      if (item.user_id !== currentUserId) {
        return res.status(403).json({ error: "Not authorized to access this reference item" });
      }

      res.json(item);
    } catch (error) {
      console.error("Error fetching reference item:", error);
      res.status(500).json({ error: "Failed to fetch reference item" });
    }
  });

  // Upload and create reference item
  app.post(
    "/api/reference-items",
    clerkAuth,
    upload.single("image"),
    async (req: Request, res: Response) => {
      try {
        const userId = getUserId(req); // Don't parse as integer, Clerk IDs are strings

        const file = req.file;
        if (!file) {
          return res.status(400).json({ error: "No image file uploaded" });
        }

        // Parse the text fields
        const data = JSON.parse(req.body.data || "{}");
        // Map possible camelCase fields from the client to the snake_case
        // fields expected by the Zod schema
        const parsedData = insertReferenceItemSchema.parse({
          ...data,
          category_id: data.categoryId ?? data.category_id,
          user_id: userId, // Use user_id to match the database schema
          image_path: file.path, // Use image_path to match the database schema
        });

        // If a category is specified, check that it exists and belongs to this user
        if (parsedData.category_id) {
          const category = await storage.getReferenceCategory(parsedData.category_id);
          if (!category) {
            return res.status(404).json({ error: "Reference category not found" });
          }
          const currentUserId = (req.user as any).claims?.sub || req.user.id;
          if (category.user_id !== currentUserId) {
            return res.status(403).json({ error: "Not authorized to add items to this category" });
          }
        }

        const item = await storage.createReferenceItem(parsedData);
        res.status(201).json(item);
      } catch (error) {
        if (error instanceof z.ZodError) {
          return res.status(400).json({ error: "Invalid item data", details: error.errors });
        }
        console.error("Error creating reference item:", error);
        res.status(500).json({ error: "Failed to create reference item" });
      }
    }
  );

  app.patch("/api/reference-items/:id", clerkAuth, async (req: Request, res: Response) => {
    try {
      // Check if user is authenticated
      if (!req.isAuthenticated()) {
        return res.status(401).json({ error: "Not authenticated" });
      }

      const itemId = parseInt(req.params.id);
      const item = await storage.getReferenceItem(itemId);

      if (!item) {
        return res.status(404).json({ error: "Reference item not found" });
      }

      // Check that this user owns the item
      const currentUserId = (req.user as any).claims?.sub || req.user.id;
      if (item.user_id !== currentUserId) {
        return res.status(403).json({ error: "Not authorized to modify this reference item" });
      }

      // Parse and validate the update data
      // Map camelCase field from the client to the expected snake_case one
      const updateData = insertReferenceItemSchema.partial().parse({
        ...req.body,
        category_id: (req.body as any).categoryId ?? req.body.category_id,
      });
      // Make sure user_id and image_path can't be changed (use a separate endpoint for image updates)
      delete updateData.user_id;
      delete updateData.image_path;

      // If category is being changed, verify it exists and belongs to user
      if (updateData.category_id) {
        const category = await storage.getReferenceCategory(updateData.category_id);
        if (!category) {
          return res.status(404).json({ error: "Reference category not found" });
        }
        const currentUserId = (req.user as any).claims?.sub || req.user.id;
        if (category.user_id !== currentUserId) {
          return res.status(403).json({ error: "Not authorized to move items to this category" });
        }
      }

      const updatedItem = await storage.updateReferenceItem(itemId, updateData);
      res.json(updatedItem);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ error: "Invalid item data", details: error.errors });
      }
      console.error("Error updating reference item:", error);
      res.status(500).json({ error: "Failed to update reference item" });
    }
  });

  app.delete("/api/reference-items/:id", clerkAuth, async (req: Request, res: Response) => {
    try {
      // Check if user is authenticated
      if (!req.isAuthenticated()) {
        return res.status(401).json({ error: "Not authenticated" });
      }

      const itemId = parseInt(req.params.id);
      const item = await storage.getReferenceItem(itemId);

      if (!item) {
        return res.status(404).json({ error: "Reference item not found" });
      }

      // Check that this user owns the item
      const currentUserIdDel = (req.user as any).claims?.sub || req.user.id;
      if (item.user_id !== currentUserIdDel) {
        return res.status(403).json({ error: "Not authorized to delete this reference item" });
      }

      // Delete the image file
      try {
        if (fs.existsSync(item.image_path)) {
          fs.unlinkSync(item.image_path);
        }
      } catch (fileError) {
        console.error("Error deleting reference item image file:", fileError);
        // Continue with the deletion even if file removal fails
      }

      await storage.deleteReferenceItem(itemId);
      res.status(204).end();
    } catch (error) {
      console.error("Error deleting reference item:", error);
      res.status(500).json({ error: "Failed to delete reference item" });
    }
  });

  // Drafts endpoints - Legacy API (will be deprecated)
  app.get("/api/drafts", clerkAuth, async (req: Request, res: Response) => {
    try {
      const userId = getUserId(req);

      // Get drafts from both the drafts table and projects table
      const legacyDrafts = await storage.getDraftsByUserId(userId);
      const projectDrafts = await storage.getDraftProjectsByUserId(userId);

      // Convert project drafts to the same format as legacy drafts
      const formattedProjectDrafts = projectDrafts.map(project => ({
        id: project.id,
        user_id: project.user_id,
        title: project.title,
        description: project.description,
        modification_description: project.modification_description,
        project_id: project.id,
        before_images: project.before_images,
        reference_images: project.reference_images,
        step: project.step,
        modification_type: project.modification_type,
        modification_options: project.modification_options,
        created_at: project.created_at,
        updated_at: project.updated_at
      }));

      // Combine both types of drafts
      const allDrafts = [...legacyDrafts, ...formattedProjectDrafts];

      res.json(allDrafts);
    } catch (error) {
      console.error("Error fetching drafts:", error);
      res.status(500).json({ error: "Failed to fetch drafts" });
    }
  });

  app.get("/api/drafts/:id", clerkAuth, async (req: Request, res: Response) => {
    try {
      const draftId = parseInt(req.params.id);
      const userId = getUserId(req);

      // First try to get from legacy drafts table
      const draft = await storage.getDraft(draftId);

      if (draft) {
        // Check if the user owns this draft
        if (draft.user_id !== userId) {
          return res.status(403).json({ error: "Not authorized to access this draft" });
        }

        return res.json(draft);
      }

      // If not found in drafts table, try to get from projects table
      const project = await storage.getProject(draftId);

      if (!project) {
        return res.status(404).json({ error: "Draft not found" });
      }

      // Check if the user owns this project and it's a draft
      if (project.user_id !== userId) {
        return res.status(403).json({ error: "Not authorized to access this draft" });
      }

      if (project.status !== 'draft') {
        return res.status(404).json({ error: "Draft not found" });
      }

      // Convert project to draft format
      const projectAsDraft = {
        id: project.id,
        user_id: project.user_id,
        title: project.title,
        description: project.description,
        modification_description: project.modification_description,
        project_id: project.id,
        before_images: project.before_images,
        reference_images: project.reference_images,
        step: project.step,
        modification_type: project.modification_type,
        modification_options: project.modification_options,
        created_at: project.created_at,
        updated_at: project.updated_at
      };

      res.json(projectAsDraft);
    } catch (error) {
      console.error("Error fetching draft:", error);
      res.status(500).json({ error: "Failed to fetch draft" });
    }
  });

  /**
   * POST /api/drafts - Create or retrieve existing draft
   * 
   * Implements atomic draft deduplication using PostgreSQL advisory locks
   * to prevent duplicate drafts from being created by rapid user actions
   * or network issues.
   * 
   * Flow:
   * 1. Generate unique lock ID from user + session
   * 2. Attempt to acquire PostgreSQL advisory lock
   * 3. If lock acquired: check for existing drafts, create if none found
   * 4. If lock not acquired: wait for other request, return result
   * 5. Always release lock in finally block
   * 
   * Returns:
   * - 201: New draft created
   * - 200: Existing draft returned (deduplication)
   * - 429: Too many concurrent requests
   * - 400: Invalid data
   * - 500: Server error
   */
  app.post("/api/drafts", clerkAuth, async (req: Request, res: Response) => {
    const userId = getUserId(req);
    const sessionId = req.body.session_id;

    console.log('Draft creation request:', {
      userId,
      sessionId,
      body: req.body,
      headers: Object.keys(req.headers)
    });

    // If no session ID provided, create immediately (fallback behavior)
    if (!sessionId) {
      try {
        const projectData = {
          user_id: userId,
          title: req.body.title || 'Untitled Project',
          description: req.body.description,
          status: 'draft',
          modification_description: req.body.modification_description,
          before_images: req.body.before_images,
          reference_images: req.body.reference_images,
          step: req.body.step,
          modification_type: req.body.modification_type,
          modification_options: req.body.modification_options,
        };

        const project = await storage.createProject(projectData);
        
        const projectAsDraft = {
          id: project.id,
          user_id: project.user_id,
          title: project.title,
          description: project.description,
          modification_description: project.modification_description,
          project_id: project.id,
          before_images: project.before_images,
          reference_images: project.reference_images,
          step: project.step,
          modification_type: project.modification_type,
          modification_options: project.modification_options,
          created_at: project.created_at,
          updated_at: project.updated_at
        };

        return res.status(201).json(projectAsDraft);
      } catch (error) {
        console.error("Error creating draft without session ID:", error);
        return res.status(500).json({
          error: "Failed to create draft",
          details: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    // ATOMIC DATABASE-LEVEL DEDUPLICATION using advisory locks
    try {
      // Create a unique lock ID from user and session to ensure atomicity
      // Format: MD5(draft_${userId}_${sessionId}) -> 32-bit integer
      const crypto = await import('crypto');
      const sessionHash = crypto.createHash('md5').update(`draft_${userId}_${sessionId}`).digest('hex');
      const lockId = parseInt(sessionHash.substring(0, 8), 16); // Convert to 32-bit integer

      console.log('Attempting to acquire advisory lock for session:', sessionId, 'lockId:', lockId);

      // Use PostgreSQL advisory lock to ensure atomic "find or create" operation
      // This prevents race conditions where multiple requests could create duplicates
      let lockResult;
      try {
        lockResult = await db.client.rpc('pg_try_advisory_lock', { key: lockId });
      } catch (lockError) {
        console.error('Advisory lock not available, falling back to time-based deduplication:', lockError);
        
        // Fallback: Check for very recent drafts (within last 10 seconds)
        const { data: veryRecentDrafts } = await db.client
          .from('projects')
          .select('*')
          .eq('user_id', userId)
          .eq('status', 'draft')
          .eq('title', req.body.title || 'Untitled Project')
          .gte('created_at', new Date(Date.now() - 10000).toISOString())
          .order('created_at', { ascending: false })
          .limit(1);

        if (veryRecentDrafts && veryRecentDrafts.length > 0) {
          const existingDraft = veryRecentDrafts[0];
          console.log('Found very recent draft in fallback mode:', existingDraft.id);
          
          const projectAsDraft = {
            id: existingDraft.id,
            user_id: existingDraft.user_id,
            title: existingDraft.title,
            description: existingDraft.description,
            modification_description: existingDraft.modification_description,
            project_id: existingDraft.id,
            before_images: existingDraft.before_images,
            reference_images: existingDraft.reference_images,
            step: existingDraft.step,
            modification_type: existingDraft.modification_type,
            modification_options: existingDraft.modification_options,
            created_at: existingDraft.created_at,
            updated_at: existingDraft.updated_at
          };

          return res.status(200).json(projectAsDraft);
        }
        
        // No recent draft found, create new one without lock protection
        const projectData = {
          user_id: userId,
          title: req.body.title || 'Untitled Project',
          description: req.body.description,
          status: 'draft',
          modification_description: req.body.modification_description,
          before_images: req.body.before_images,
          reference_images: req.body.reference_images,
          step: req.body.step,
          modification_type: req.body.modification_type,
          modification_options: req.body.modification_options,
        };

        const project = await storage.createProject(projectData);
        
        const projectAsDraft = {
          id: project.id,
          user_id: project.user_id,
          title: project.title,
          description: project.description,
          modification_description: project.modification_description,
          project_id: project.id,
          before_images: project.before_images,
          reference_images: project.reference_images,
          step: project.step,
          modification_type: project.modification_type,
          modification_options: project.modification_options,
          created_at: project.created_at,
          updated_at: project.updated_at
        };

        return res.status(201).json(projectAsDraft);
      }
      
      if (!lockResult.data) {
        console.log('Could not acquire lock, checking for existing draft...');
        
        // Another request is processing this session ID, wait briefly for it to complete
        // then check if it created a draft we can return
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // Check if draft was created by the other request
        const { data: existingDrafts } = await db.client
          .from('projects')
          .select('*')
          .eq('user_id', userId)
          .eq('status', 'draft')
          .eq('title', req.body.title || 'Untitled Project')
          .gte('created_at', new Date(Date.now() - 30000).toISOString()) // Within last 30 seconds
          .order('created_at', { ascending: false })
          .limit(1);

        if (existingDrafts && existingDrafts.length > 0) {
          const existingDraft = existingDrafts[0];
          console.log('Found existing draft created by concurrent request:', existingDraft.id);
          
          const projectAsDraft = {
            id: existingDraft.id,
            user_id: existingDraft.user_id,
            title: existingDraft.title,
            description: existingDraft.description,
            modification_description: existingDraft.modification_description,
            project_id: existingDraft.id,
            before_images: existingDraft.before_images,
            reference_images: existingDraft.reference_images,
            step: existingDraft.step,
            modification_type: existingDraft.modification_type,
            modification_options: existingDraft.modification_options,
            created_at: existingDraft.created_at,
            updated_at: existingDraft.updated_at
          };

          return res.status(200).json(projectAsDraft);
        }
        
        return res.status(429).json({
          error: "Too many concurrent requests",
          details: "Please wait a moment and try again"
        });
      }

      console.log('Advisory lock acquired successfully');

      try {
        // Double-check for existing draft now that we have exclusive access via the lock
        // This handles edge cases where a draft was created just before we acquired the lock
        const { data: existingDrafts } = await db.client
          .from('projects')
          .select('*')
          .eq('user_id', userId)
          .eq('status', 'draft')
          .eq('title', req.body.title || 'Untitled Project')
          .gte('created_at', new Date(Date.now() - 5 * 60 * 1000).toISOString()) // Within last 5 minutes
          .order('created_at', { ascending: false })
          .limit(1);

        if (existingDrafts && existingDrafts.length > 0) {
          const existingDraft = existingDrafts[0];
          console.log('Found existing recent draft during locked check:', existingDraft.id);
          
          const projectAsDraft = {
            id: existingDraft.id,
            user_id: existingDraft.user_id,
            title: existingDraft.title,
            description: existingDraft.description,
            modification_description: existingDraft.modification_description,
            project_id: existingDraft.id,
            before_images: existingDraft.before_images,
            reference_images: existingDraft.reference_images,
            step: existingDraft.step,
            modification_type: existingDraft.modification_type,
            modification_options: existingDraft.modification_options,
            created_at: existingDraft.created_at,
            updated_at: existingDraft.updated_at
          };

          return res.status(200).json(projectAsDraft);
        }

        // No existing draft found, create new one
        const projectData = {
          user_id: userId,
          title: req.body.title || 'Untitled Project',
          description: req.body.description,
          status: 'draft',
          modification_description: req.body.modification_description,
          before_images: req.body.before_images,
          reference_images: req.body.reference_images,
          step: req.body.step,
          modification_type: req.body.modification_type,
          modification_options: req.body.modification_options,
        };

        console.log('Creating new draft with lock protection');
        const project = await storage.createProject(projectData);
        console.log('Project created successfully with ID:', project.id);

        const projectAsDraft = {
          id: project.id,
          user_id: project.user_id,
          title: project.title,
          description: project.description,
          modification_description: project.modification_description,
          project_id: project.id,
          before_images: project.before_images,
          reference_images: project.reference_images,
          step: project.step,
          modification_type: project.modification_type,
          modification_options: project.modification_options,
          created_at: project.created_at,
          updated_at: project.updated_at
        };

        return res.status(201).json(projectAsDraft);

      } finally {
        // CRITICAL: Always release the advisory lock to prevent deadlocks
        // Even if an error occurred, we must release the lock
        try {
          await db.client.rpc('pg_advisory_unlock', { key: lockId });
          console.log('Advisory lock released for session:', sessionId);
        } catch (unlockError) {
          console.error('Error releasing advisory lock:', unlockError);
          // Continue execution - this shouldn't block the response
          // PostgreSQL will auto-release session locks on connection close
        }
      }

    } catch (error) {
      console.error("Error in atomic draft creation:", error);
      
      if (error instanceof z.ZodError) {
        return res.status(400).json({ error: "Invalid draft data", details: error.errors });
      }
      
      return res.status(500).json({
        error: "Failed to create draft",
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  app.put("/api/drafts/:id", clerkAuth, async (req: Request, res: Response) => {
    try {
      const draftId = parseInt(req.params.id);
      const userId = getUserId(req);

      // First check if it's a legacy draft
      const existingDraft = await storage.getDraft(draftId);

      if (existingDraft) {
        // It's a legacy draft
        if (existingDraft.user_id !== userId) {
          return res.status(403).json({ error: "Not authorized to update this draft" });
        }

        // Update the legacy draft
        const updatedDraft = await storage.updateDraft(draftId, req.body);
        return res.json(updatedDraft);
      }

      // If not a legacy draft, check if it's a project draft
      const existingProject = await storage.getProject(draftId);

      if (!existingProject) {
        return res.status(404).json({ error: "Draft not found" });
      }

      if (existingProject.user_id !== userId) {
        return res.status(403).json({ error: "Not authorized to update this draft" });
      }

      if (existingProject.status !== 'draft') {
        return res.status(404).json({ error: "Draft not found" });
      }

      // Update the project
      const updatedProject = await storage.updateProject(draftId, {
        title: req.body.title,
        description: req.body.description,
        modification_description: req.body.modification_description,
        before_images: req.body.before_images,
        reference_images: req.body.reference_images,
        step: req.body.step,
        modification_type: req.body.modification_type,
        modification_options: req.body.modification_options
      });

      if (!updatedProject) {
        return res.status(500).json({ error: "Failed to update draft" });
      }

      // Convert project to draft format for response
      const projectAsDraft = {
        id: updatedProject.id,
        user_id: updatedProject.user_id,
        title: updatedProject.title,
        description: updatedProject.description,
        modification_description: updatedProject.modification_description,
        project_id: updatedProject.id,
        before_images: updatedProject.before_images,
        reference_images: updatedProject.reference_images,
        step: updatedProject.step,
        modification_type: updatedProject.modification_type,
        modification_options: updatedProject.modification_options,
        created_at: updatedProject.created_at,
        updated_at: updatedProject.updated_at
      };

      res.json(projectAsDraft);
    } catch (error) {
      console.error("Error updating draft:", error);
      res.status(500).json({ error: "Failed to update draft" });
    }
  });

  app.delete("/api/drafts/:id", clerkAuth, async (req: Request, res: Response) => {
    try {
      const draftId = parseInt(req.params.id);
      const userId = getUserId(req);

      // First check if it's a legacy draft
      const existingDraft = await storage.getDraft(draftId);

      if (existingDraft) {
        // It's a legacy draft
        if (existingDraft.user_id !== userId) {
          return res.status(403).json({ error: "Not authorized to delete this draft" });
        }

        // Delete the legacy draft
        await storage.deleteDraft(draftId);
        return res.status(204).send();
      }

      // If not a legacy draft, check if it's a project draft
      const existingProject = await storage.getProject(draftId);

      if (!existingProject) {
        return res.status(404).json({ error: "Draft not found" });
      }

      if (existingProject.user_id !== userId) {
        return res.status(403).json({ error: "Not authorized to delete this draft" });
      }

      if (existingProject.status !== 'draft') {
        return res.status(404).json({ error: "Draft not found" });
      }

      // For project drafts, we don't actually delete the project,
      // we just mark it as 'deleted' to preserve the data
      await storage.updateProjectStatus(draftId, 'deleted');

      res.status(204).send();
    } catch (error) {
      console.error("Error deleting draft:", error);
      res.status(500).json({ error: "Failed to delete draft" });
    }
  });

  // Renovation Presets endpoints
  app.get("/api/renovation-presets", async (req: Request, res: Response) => {
    try {
      const roomType = req.query.roomType as string | undefined;

      let presets;
      if (roomType) {
        // Filter by room type
        presets = await storage.getRenovationPresetsByRoomType(roomType);
      } else {
        // Get all presets
        presets = await storage.getRenovationPresets();
      }

      // Filter to show default/public presets, plus any user-created presets if logged in
      const publicPresets = presets.filter(preset => preset.is_default || preset.is_public);

      if (req.isAuthenticated()) {
        // Include user's own presets
        const userId = (req.user as any).claims?.sub || req.user.id;
        const userPresets = await storage.getRenovationPresetsByUserId(userId);
        const combinedPresets = [...publicPresets, ...userPresets.filter(up =>
          !publicPresets.some(pp => pp.id === up.id)
        )];

        return res.json(combinedPresets);
      }

      res.json(publicPresets);
    } catch (error) {
      console.error("Error fetching renovation presets:", error);
      res.status(500).json({ error: "Failed to fetch renovation presets" });
    }
  });

  app.get("/api/renovation-presets/:id", async (req: Request, res: Response) => {
    try {
      const presetId = parseInt(req.params.id);
      const preset = await storage.getRenovationPreset(presetId);

      if (!preset) {
        return res.status(404).json({ error: "Renovation preset not found" });
      }

      // Check access permissions
      if (!preset.is_default && !preset.is_public) {
        // Only creator can access private presets
        if (!req.isAuthenticated() || preset.created_by !== ((req.user as any).claims?.sub || req.user.id)) {
          return res.status(403).json({ error: "Not authorized to access this preset" });
        }
      }

      res.json(preset);
    } catch (error) {
      console.error("Error fetching renovation preset:", error);
      res.status(500).json({ error: "Failed to fetch renovation preset" });
    }
  });

  app.post("/api/renovation-presets", clerkAuth, async (req: Request, res: Response) => {
    try {
      // Check if user is authenticated
      if (!req.isAuthenticated()) {
        return res.status(401).json({ error: "Not authenticated" });
      }

      const parsedData = insertRenovationPresetSchema.parse({
        ...req.body,
        created_by: (req.user as any).claims?.sub || req.user.id, // Use created_by to match the database schema
      });

      const preset = await storage.createRenovationPreset(parsedData);
      res.status(201).json(preset);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ error: "Invalid preset data", details: error.errors });
      }
      console.error("Error creating renovation preset:", error);
      res.status(500).json({ error: "Failed to create renovation preset" });
    }
  });

  app.patch("/api/renovation-presets/:id", clerkAuth, async (req: Request, res: Response) => {
    try {
      // Check if user is authenticated
      if (!req.isAuthenticated()) {
        return res.status(401).json({ error: "Not authenticated" });
      }

      const presetId = parseInt(req.params.id);
      const preset = await storage.getRenovationPreset(presetId);

      if (!preset) {
        return res.status(404).json({ error: "Renovation preset not found" });
      }

      // Check that this user owns the preset or it's an admin (future feature)
      if (preset.created_by !== ((req.user as any).claims?.sub || req.user.id)) {
        return res.status(403).json({ error: "Not authorized to modify this preset" });
      }

      // Parse and validate the update data
      const updateData = insertRenovationPresetSchema.partial().parse(req.body);
      // Make sure created_by can't be changed
      delete updateData.created_by;

      const updatedPreset = await storage.updateRenovationPreset(presetId, updateData);
      res.json(updatedPreset);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ error: "Invalid preset data", details: error.errors });
      }
      console.error("Error updating renovation preset:", error);
      res.status(500).json({ error: "Failed to update renovation preset" });
    }
  });

  app.delete("/api/renovation-presets/:id", clerkAuth, async (req: Request, res: Response) => {
    try {
      // Check if user is authenticated
      if (!req.isAuthenticated()) {
        return res.status(401).json({ error: "Not authenticated" });
      }

      const presetId = parseInt(req.params.id);
      const preset = await storage.getRenovationPreset(presetId);

      if (!preset) {
        return res.status(404).json({ error: "Renovation preset not found" });
      }

      // Check that this user owns the preset
      if (preset.created_by !== ((req.user as any).claims?.sub || req.user.id)) {
        return res.status(403).json({ error: "Not authorized to delete this preset" });
      }

      // Don't allow deleting default presets
      if (preset.is_default) {
        return res.status(403).json({ error: "Cannot delete default presets" });
      }

      await storage.deleteRenovationPreset(presetId);
      res.status(204).send();
    } catch (error) {
      console.error("Error deleting renovation preset:", error);
      res.status(500).json({ error: "Failed to delete renovation preset" });
    }
  });

  // Draft cleanup function - removes old draft sessions (older than 24 hours)
  // TODO: Enable after adding session_created_at column
  const cleanupOldDraftSessions = async () => {
    try {
      // Temporarily disabled until migration is complete
      console.log('Draft cleanup temporarily disabled - waiting for migration');
      return;
      
      const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
      
      const { data: oldDrafts, error } = await db.client
        .from('projects')
        .delete()
        .eq('status', 'draft')
        .lt('session_created_at', twentyFourHoursAgo.toISOString())
        .select('id, session_id');

      if (error) {
        console.error('Error cleaning up old draft sessions:', error);
      } else if (oldDrafts && oldDrafts.length > 0) {
        console.log(`Cleaned up ${oldDrafts.length} old draft sessions`);
      }
    } catch (error) {
      console.error('Error in cleanupOldDraftSessions:', error);
    }
  };

  // Run cleanup every hour (disabled for now)
  // setInterval(cleanupOldDraftSessions, 60 * 60 * 1000);
  
  // Run cleanup on startup (disabled for now)  
  // cleanupOldDraftSessions();

  // Draft deduplication now handled by database advisory locks
  console.log('Draft deduplication: Using PostgreSQL advisory locks for atomic operations');

  // Track request IDs to prevent duplicate checkout sessions
  const processedCheckoutRequests = new Set<string>();

  // Stripe Checkout Routes
  app.post("/api/create-checkout-session", clerkAuth, asyncHandler(async (req: Request, res: Response) => {
    // Check for duplicate requests using the request ID
    const requestId = req.headers['x-request-id'] as string || req.body.requestId;

    if (requestId && processedCheckoutRequests.has(requestId)) {
      backendLogger.warn(`Duplicate checkout request detected with ID: ${requestId}`);
      return res.status(409).json({
        message: "This checkout request has already been processed",
        error: "duplicate_request"
      });
    }

    // Log the request for debugging
    backendLogger.info(`Creating checkout session for user: ${req.clerkUser?.id}`, {
      headers: {
        authorization: req.headers.authorization ? 'present' : 'missing',
        contentType: req.headers['content-type'],
        requestId: requestId || 'missing'
      }
    });

    const userId = getUserId(req);
    const { planId, billingCycle, couponCode } = req.body;

    // Add request to processed set if we have an ID
    if (requestId) {
      processedCheckoutRequests.add(requestId);

      // Clean up old request IDs periodically (keep only the last 1000)
      if (processedCheckoutRequests.size > 1000) {
        const idsToRemove = Array.from(processedCheckoutRequests).slice(0, processedCheckoutRequests.size - 1000);
        idsToRemove.forEach(id => processedCheckoutRequests.delete(id));
      }
    }

    backendLogger.info(`Checkout request data:`, { planId, billingCycle, userId, requestId, couponCode });

    if (!planId || !['starter', 'professional'].includes(planId)) {
      return res.status(400).json({ message: "Valid plan ID required" });
    }

    if (!billingCycle || !['monthly', 'annual'].includes(billingCycle)) {
      return res.status(400).json({ message: "Valid billing cycle required" });
    }

    // Get user details for Stripe customer creation
    let user = await storage.getUser(userId);

    // Create user if it doesn't exist
    if (!user) {
      backendLogger.info(`User ${userId} not found in database, creating new user record`);
      // Create a basic user record
      user = await storage.upsertUser({
        id: userId,
        username: 'User',
        email: null,
        first_name: null,
        last_name: null,
        bio: null,
        profile_image_url: null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });
      backendLogger.info(`Created new user record for ${userId}`);
    }

    // Create or get Stripe customer
    let stripeCustomerId;

    // Use a global cache for Stripe customer IDs to prevent multiple creations
    // This is a simple in-memory cache that persists between requests
    if (!global.stripeCustomerCache) {
      global.stripeCustomerCache = {};
    }

    // Check if we already have this customer ID in our global cache
    if (global.stripeCustomerCache[userId]) {
      const cachedCustomerId = global.stripeCustomerCache[userId];

      // Check if the cached value is a JSON string or object
      if (typeof cachedCustomerId === 'string') {
        if (cachedCustomerId.startsWith('{')) {
          try {
            // Try to parse it as JSON and extract the ID
            const customerObj = JSON.parse(cachedCustomerId);
            stripeCustomerId = customerObj.id;
            backendLogger.info(`Extracted customer ID from cached JSON: ${stripeCustomerId}`);

            // Update the cache with just the ID
            global.stripeCustomerCache[userId] = stripeCustomerId;
          } catch (parseError) {
            // If parsing fails, use the string as is
            stripeCustomerId = cachedCustomerId;
            backendLogger.warn(`Failed to parse cached customer ID as JSON, using as is: ${stripeCustomerId}`);
          }
        } else {
          // Use the ID as is
          stripeCustomerId = cachedCustomerId;
        }
      } else if (typeof cachedCustomerId === 'object' && cachedCustomerId !== null) {
        // If it's already an object, extract the ID
        stripeCustomerId = cachedCustomerId.id || '';
        backendLogger.info(`Extracted customer ID from cached object: ${stripeCustomerId}`);

        // Update the cache with just the ID
        global.stripeCustomerCache[userId] = stripeCustomerId;
      } else {
        // Use the value as is
        stripeCustomerId = cachedCustomerId;
      }

      backendLogger.info(`Using globally cached Stripe customer ID: ${stripeCustomerId}`);
    } else {
      // Check if user has an existing subscription with a customer ID
      const existingSubscription = await storage.getSubscription(userId);

      if (existingSubscription?.stripe_customer_id) {
        // Extract the customer ID if it's a JSON object
        if (typeof existingSubscription.stripe_customer_id === 'string' &&
            existingSubscription.stripe_customer_id.startsWith('{')) {
          try {
            // Try to parse it as JSON and extract the ID
            const customerObj = JSON.parse(existingSubscription.stripe_customer_id);
            stripeCustomerId = customerObj.id;
            backendLogger.info(`Extracted customer ID from JSON: ${stripeCustomerId}`);

            // Update the database with just the ID
            await storage.updateSubscription(userId, {
              stripe_customer_id: stripeCustomerId
            });
          } catch (parseError) {
            // If parsing fails, use the string as is
            stripeCustomerId = existingSubscription.stripe_customer_id;
            backendLogger.warn(`Failed to parse customer ID as JSON, using as is: ${stripeCustomerId}`);
          }
        } else {
          // Use the ID as is
          stripeCustomerId = existingSubscription.stripe_customer_id;
        }

        backendLogger.info(`Using existing Stripe customer ID: ${stripeCustomerId}`);

        // Add to global cache
        global.stripeCustomerCache[userId] = stripeCustomerId;
      } else {
        try {
          // Create a new customer in Stripe
          stripeCustomerId = await subscriptionService.createCustomer(
            userId,
            user.email,
            `${user.first_name || ''} ${user.last_name || ''}`.trim() || user.username
          );
          backendLogger.info(`Created new Stripe customer ID: ${stripeCustomerId}`);

          // Add to global cache
          global.stripeCustomerCache[userId] = stripeCustomerId;
        } catch (error: any) {
          // If we hit a rate limit, wait and try to find an existing customer
          if (error.type === 'StripeRateLimitError') {
            backendLogger.warn(`Stripe rate limit hit when creating customer. Checking for existing customers.`);

            // Try to find if this customer was already created in Stripe
            try {
              const customers = await stripe.customers.list({
                email: user.email,
                limit: 1
              });

              if (customers.data.length > 0) {
                stripeCustomerId = customers.data[0].id;
                backendLogger.info(`Found existing Stripe customer by email: ${stripeCustomerId}`);

                // Add to global cache
                global.stripeCustomerCache[userId] = stripeCustomerId;
              } else {
                throw error; // Re-throw if no customer found
              }
            } catch (listError) {
              // If we can't list customers, re-throw the original error
              throw error;
            }
          } else {
            // For other errors, just re-throw
            throw error;
          }
        }
      }
    }

    // Get the plan details
    const planLimits = PLAN_LIMITS[planId as keyof typeof PLAN_LIMITS];
    if (!planLimits) {
      return res.status(400).json({ message: "Invalid plan ID" });
    }

    const planDetails = planLimits[billingCycle as 'monthly' | 'annual'];
    if (!planDetails) {
      return res.status(400).json({ message: "Invalid billing cycle for this plan" });
    }

    // Calculate the amount based on billing cycle
    let amount, interval;

    if (billingCycle === 'annual') {
      // For annual billing, use the annual price (which is the total amount for the year)
      amount = planDetails.annualPrice;
      interval = 'year';

      backendLogger.info(`Using annual price: ${amount} for the year`);
    } else {
      // For monthly billing, use the monthly price
      amount = planDetails.price;
      interval = 'month';

      backendLogger.info(`Using monthly price: ${amount} per month`);
    }

    // Format plan name for display
    const planName = planDetails.displayName || (planId === 'starter' ? 'Starter' : 'Professional');

    backendLogger.info(`Creating Stripe checkout session for plan: ${planName}, billing cycle: ${billingCycle}, amount: ${amount}, interval: ${interval}`);

    // Create a Stripe Checkout Session
    let session;
    try {
      // Set up the checkout session parameters
      const sessionParams: Stripe.Checkout.SessionCreateParams = {
        payment_method_types: ['card'],
        customer: stripeCustomerId,
        line_items: [
          {
            price_data: {
              currency: 'aud',
              product_data: {
                name: `${planName} Plan (${billingCycle === 'annual' ? 'Annual' : 'Monthly'})`,
                description: `${planDetails.projects} projects, ${planDetails.imagesPerProject} images per project`,
              },
              unit_amount: Math.round(amount * 100), // Convert to cents
              recurring: {
                interval: interval,
              },
            },
            quantity: 1,
          },
        ],
        mode: 'subscription',
        success_url: `${req.protocol}://${req.get('host')}/payment-success?session_id={CHECKOUT_SESSION_ID}`,
        cancel_url: `${req.protocol}://${req.get('host')}/pricing`,
        metadata: {
          userId,
          planId,
          billingCycle
        },
      };

      // Check if a coupon code was provided
      if (couponCode) {
        backendLogger.info(`Coupon code provided: ${couponCode}`);

        // First, try to find the coupon in Stripe directly
        let stripeCouponId;
        try {
          // List coupons with the given code
          const stripeCoupons = await stripe.coupons.list({
            limit: 10
          });

          // Find the coupon with matching code (case-insensitive)
          const stripeCoupon = stripeCoupons.data.find(
            c => (c.name?.toLowerCase() === couponCode.toLowerCase()) ||
                 (c.id.toLowerCase() === couponCode.toLowerCase()) ||
                 (c.metadata?.code?.toLowerCase() === couponCode.toLowerCase())
          );

          if (stripeCoupon && stripeCoupon.valid) {
            stripeCouponId = stripeCoupon.id;
            backendLogger.info(`Found valid coupon in Stripe: ${couponCode}`, {
              id: stripeCoupon.id,
              percent_off: stripeCoupon.percent_off,
              amount_off: stripeCoupon.amount_off
            });
          }
        } catch (stripeError) {
          backendLogger.error('Error retrieving coupon from Stripe', { error: stripeError, couponCode });
        }

        // If we found a valid coupon in Stripe, use it directly
        if (stripeCouponId) {
          // Add the coupon to the checkout session
          sessionParams.discounts = [{
            coupon: stripeCouponId
          }];

          // Add coupon info to metadata
          sessionParams.metadata = {
            ...sessionParams.metadata,
            couponCode: couponCode,
            stripeCouponId: stripeCouponId
          };
        } else {
          // If we didn't find a valid coupon in Stripe, try our database as a fallback
          const coupon = await couponService.validateCoupon(couponCode, userId);

          if (coupon) {
            backendLogger.info(`Valid coupon found in database: ${couponCode}`, {
              type: coupon.type,
              amount: coupon.amount
            });

            // Add the coupon to the checkout session
            sessionParams.discounts = [{
              coupon: coupon.stripe_coupon_id
            }];

            // Add coupon info to metadata
            sessionParams.metadata = {
              ...sessionParams.metadata,
              couponCode: couponCode,
              stripeCouponId: coupon.stripe_coupon_id
            };
          } else {
            backendLogger.warn(`Invalid coupon code provided: ${couponCode}`);
            // Continue without applying the coupon
          }
        }
      }

      // Create the checkout session
      session = await stripe.checkout.sessions.create(sessionParams);
    } catch (error: any) {
      // Handle Stripe rate limit errors
      if (error.type === 'StripeRateLimitError') {
        backendLogger.warn(`Stripe rate limit exceeded: ${error.message}`);
        return res.status(429).json({
          message: "Too many requests to payment processor. Please try again in a few seconds.",
          error: "rate_limit_exceeded"
        });
      }

      // Handle other Stripe errors
      backendLogger.error(`Stripe error: ${error.message}`, error);
      return res.status(500).json({
        message: "Error creating checkout session",
        error: error.message
      });
    }

    backendLogger.info(`Created checkout session: ${session.id}`, {
      sessionUrl: session.url ? 'present' : 'missing',
      sessionId: session.id
    });

    // If a valid coupon was applied, record the redemption when the session is created
    if (couponCode && session.discounts && session.discounts.length > 0) {
      try {
        await couponService.applyCouponToCheckout(couponCode, userId, session.id);
        backendLogger.info(`Recorded coupon redemption: ${couponCode} for user ${userId}`);
      } catch (couponError) {
        backendLogger.error('Error recording coupon redemption', { error: couponError, couponCode, userId });
        // Continue with checkout even if recording the redemption fails
      }
    }

    // Return the session ID and URL
    res.json({
      sessionId: session.id,
      url: session.url
    });
  }))

  // Cache for checkout sessions to prevent multiple API calls to Stripe
  const checkoutSessionCache = new Map<string, { data: any, timestamp: number }>();
  const SESSION_CACHE_TTL = 60 * 1000; // 1 minute cache TTL

  // Get checkout session details
  app.get("/api/checkout-session/:sessionId", clerkAuth, asyncHandler(async (req: Request, res: Response) => {
    const { sessionId } = req.params;
    backendLogger.info(`Retrieving checkout session: ${sessionId}`);

    if (!sessionId) {
      return res.status(400).json({ message: "Session ID is required" });
    }

    // Check if we have a cached version of this session
    const cachedSession = checkoutSessionCache.get(sessionId);
    const now = Date.now();

    if (cachedSession && (now - cachedSession.timestamp < SESSION_CACHE_TTL)) {
      backendLogger.info(`Using cached checkout session: ${sessionId}`);

      // Set cache control headers
      res.set('Cache-Control', 'private, max-age=60');
      res.set('ETag', `W/"session-${sessionId}"`);

      return res.json(cachedSession.data);
    }

    try {
      // Retrieve the checkout session from Stripe
      const session = await stripe.checkout.sessions.retrieve(sessionId, {
        expand: ['subscription', 'customer']
      });

      // Prepare the response data
      const responseData = {
        success: true,
        session: {
          id: session.id,
          status: session.status,
          customer: session.customer,
          subscription: session.subscription,
          metadata: session.metadata
        }
      };

      // Cache the session data
      checkoutSessionCache.set(sessionId, {
        data: responseData,
        timestamp: now
      });

      // Clean up old cache entries periodically
      if (checkoutSessionCache.size > 100) {
        const keysToDelete = [];
        for (const [key, value] of checkoutSessionCache.entries()) {
          if (now - value.timestamp > SESSION_CACHE_TTL) {
            keysToDelete.push(key);
          }
        }
        keysToDelete.forEach(key => checkoutSessionCache.delete(key));
      }

      // Set cache control headers
      res.set('Cache-Control', 'private, max-age=60');
      res.set('ETag', `W/"session-${sessionId}"`);

      // Return the session details
      return res.json(responseData);
    } catch (error) {
      backendLogger.error(`Error retrieving checkout session: ${sessionId}`, { error });
      return res.status(500).json({
        success: false,
        error: "Failed to retrieve checkout session"
      });
    }
  }));

  // Cache for user subscriptions to prevent multiple API calls
  const subscriptionCache = new Map<string, { data: any, timestamp: number }>();
  const SUBSCRIPTION_CACHE_TTL = 30 * 1000; // 30 seconds cache TTL

  // Get current subscription
  app.get("/api/subscription", clerkAuth, asyncHandler(async (req: Request, res: Response) => {
    // Handle unauthenticated requests gracefully
    if (req.isUnauthenticated) {
      backendLogger.info(`Unauthenticated request to /api/subscription, returning free tier defaults`);

      // Return a default free tier response for unauthenticated users
      const responseData = {
        subscription: null,
        usage: null,
        limits: {
          projects: 1,
          imagesPerProject: 1,
          price: 0
        }
      };

      // Set cache control headers
      res.set('Cache-Control', 'private, max-age=30');
      res.set('ETag', `W/"subscription-anonymous"`);

      return res.json(responseData);
    }

    const userId = getUserId(req);
    backendLogger.info(`Fetching subscription for user: ${userId}`);

    // Check for cache busting parameter
    const cacheBuster = req.query._cb;
    const now = Date.now();

    // Check if we have a cached version of this subscription (unless cache busting is requested)
    if (!cacheBuster) {
      const cachedSubscription = subscriptionCache.get(userId);

      if (cachedSubscription && (now - cachedSubscription.timestamp < SUBSCRIPTION_CACHE_TTL)) {
        backendLogger.info(`Using cached subscription for user: ${userId}`);

        // Set cache control headers
        res.set('Cache-Control', 'private, max-age=30');
        res.set('ETag', `W/"subscription-${userId}"`);

        return res.json(cachedSubscription.data);
      }
    }

    // Ensure user exists in database
    let user = await storage.getUser(userId);
    if (!user) {
      backendLogger.info(`User ${userId} not found in database, creating new user record`);
      try {
        // Create a basic user record
        user = await storage.upsertUser({
          id: userId,
          username: 'User',
          email: null,
          first_name: null,
          last_name: null,
          bio: null,
          profile_image_url: null,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });
        backendLogger.info(`Created new user record for ${userId}`);
      } catch (error) {
        backendLogger.error(`Failed to create user record for ${userId}:`, error);

        // If it's a database connection issue, return a helpful error
        if (error instanceof Error && error.message.includes('Database connection failed')) {
          return res.status(503).json({
            error: {
              type: 'DATABASE_CONNECTION_ERROR',
              message: 'Unable to connect to database. Please check your internet connection and try again.',
              details: 'The application is temporarily unable to access the database. This may be due to network connectivity issues.'
            }
          });
        }

        // For other errors, return a generic error
        return res.status(500).json({
          error: {
            type: 'INTERNAL_ERROR',
            message: 'An error occurred while setting up your account. Please try again.',
            details: error.message
          }
        });
      }
    }

    // Get subscription and usage information
    const subscription = await storage.getSubscription(userId);
    const usage = await storage.getCurrentUsage(userId);

    if (!subscription) {
      // Create a default free tier response instead of returning 404
      backendLogger.info(`No subscription found for user: ${userId}, returning free tier defaults`);

      // Create a default usage record if none exists
      let defaultUsage = usage;
      if (!defaultUsage) {
        const now = new Date();
        const periodEnd = new Date(now);
        periodEnd.setMonth(periodEnd.getMonth() + 1);

        try {
          defaultUsage = await storage.createUsage({
            user_id: userId,
            projects_count: 0,
            images_count: 0,
            period_start: now,
            period_end: periodEnd
          });
          backendLogger.info(`Created default usage record for user: ${userId}`);
        } catch (error) {
          backendLogger.error(`Error creating default usage record: ${error}`);
          // Continue with null usage if creation fails
        }
      }

      // Create a response with free tier limits
      const responseData = {
        subscription: null,
        usage: defaultUsage,
        limits: {
          projects: 1,
          imagesPerProject: 1,
          price: 0
        }
      };

      // Cache the response
      subscriptionCache.set(userId, {
        data: responseData,
        timestamp: now
      });

      return res.json(responseData);
    }

    // If there's a Stripe subscription ID, get the latest details from Stripe
    // Skip Stripe API calls for demo subscriptions (they have fake IDs)
    if (subscription.stripe_subscription_id && !subscription.stripe_subscription_id.startsWith('demo_sub_')) {
      try {
        const subscriptionId = subscription.stripe_subscription_id;
        backendLogger.info(`Fetching Stripe subscription details: ${subscriptionId}`);
        const stripeSubscription = await stripe.subscriptions.retrieve(subscriptionId);

        // Update our database with the latest status from Stripe
        if (stripeSubscription.status !== subscription.status ||
            stripeSubscription.cancel_at_period_end !== subscription.cancel_at_period_end) {

          backendLogger.info(`Updating subscription status from Stripe: ${stripeSubscription.status}, cancel_at_period_end: ${stripeSubscription.cancel_at_period_end}`);

          await storage.updateSubscription(userId, {
            status: stripeSubscription.status,
            cancel_at_period_end: stripeSubscription.cancel_at_period_end,
            current_period_start: new Date(stripeSubscription.current_period_start * 1000),
            current_period_end: new Date(stripeSubscription.current_period_end * 1000)
          });

          // Refresh subscription data
          const updatedSubscription = await storage.getSubscription(userId);
          if (updatedSubscription) {
            subscription.status = updatedSubscription.status;
            subscription.cancel_at_period_end = updatedSubscription.cancel_at_period_end;
            subscription.current_period_start = updatedSubscription.current_period_start;
            subscription.current_period_end = updatedSubscription.current_period_end;
          }
        }
      } catch (stripeError) {
        backendLogger.error(`Error fetching Stripe subscription: ${stripeError}`);

        // Mark the subscription as having an error
        if (subscription.status !== 'error') {
          await storage.updateSubscription(userId, {
            status: 'error'
          });

          // Update the local subscription object
          subscription.status = 'error';
        }

        // Continue with the data we have in our database
      }
    }

    // Prepare the response data
    const planLimits = PLAN_LIMITS[subscription.plan_id as keyof typeof PLAN_LIMITS]?.[subscription.billing_cycle as 'monthly' | 'annual'];

    backendLogger.info(`Preparing subscription response for user: ${userId}`, {
      subscriptionId: subscription.id,
      planId: subscription.plan_id,
      status: subscription.status,
      billingCycle: subscription.billing_cycle,
      hasLimits: !!planLimits
    });

    // The client expects the subscription data in a nested format
    const responseData = {
      subscription: {
        subscription: subscription  // Nest the subscription data
      },
      usage,
      limits: planLimits
    };

    // Cache the response
    subscriptionCache.set(userId, {
      data: responseData,
      timestamp: now
    });

    // Clean up old cache entries periodically
    if (subscriptionCache.size > 100) {
      const keysToDelete = [];
      for (const [key, value] of subscriptionCache.entries()) {
        if (now - value.timestamp > SUBSCRIPTION_CACHE_TTL) {
          keysToDelete.push(key);
        }
      }
      keysToDelete.forEach(key => subscriptionCache.delete(key));
    }

    // Set cache control headers
    res.set('Cache-Control', 'private, max-age=30');
    res.set('ETag', `W/"subscription-${userId}"`);

    res.json(responseData);
  }));

  // Cancel subscription
  app.post("/api/subscription/cancel", clerkAuth, asyncHandler(async (req: Request, res: Response) => {
    const userId = getUserId(req);
    const { subscriptionId } = req.body;

    backendLogger.info(`Cancelling subscription for user: ${userId}, subscription: ${subscriptionId}`);

    if (!subscriptionId) {
      return res.status(400).json({ message: "Subscription ID is required" });
    }

    // Verify the subscription belongs to this user
    const subscription = await storage.getSubscription(userId);

    backendLogger.info(`Subscription found for user ${userId}:`, {
      hasSubscription: !!subscription,
      subscriptionId: subscription?.id,
      stripeSubscriptionId: subscription?.stripe_subscription_id,
      requestedId: subscriptionId,
      matches: subscription?.stripe_subscription_id === subscriptionId
    });

    if (!subscription || subscription.stripe_subscription_id !== subscriptionId) {
      backendLogger.warn(`Unauthorized subscription cancellation attempt: ${userId}, ${subscriptionId}`);
      return res.status(403).json({ message: "Unauthorized" });
    }

    // Handle demo subscriptions differently (don't call Stripe API)
    if (subscriptionId.startsWith('demo_sub_')) {
      backendLogger.info(`Cancelling demo subscription: ${subscriptionId}`);

      // Update our database only
      await storage.updateSubscription(userId, {
        cancel_at_period_end: true
      });

      res.json({
        success: true,
        subscription: { id: subscriptionId, cancel_at_period_end: true }
      });
    } else {
      // Cancel the subscription at the end of the current period
      const updatedSubscription = await stripe.subscriptions.update(subscriptionId, {
        cancel_at_period_end: true
      });

      // Update our database
      await storage.updateSubscription(userId, {
        cancel_at_period_end: true
      });

      res.json({
        success: true,
        subscription: updatedSubscription
      });
    }
  }));

  // Reactivate subscription
  app.post("/api/subscription/reactivate", clerkAuth, asyncHandler(async (req: Request, res: Response) => {
    const userId = getUserId(req);
    const { subscriptionId } = req.body;

    backendLogger.info(`Reactivating subscription for user: ${userId}, subscription: ${subscriptionId}`);

    if (!subscriptionId) {
      return res.status(400).json({ message: "Subscription ID is required" });
    }

    // Verify the subscription belongs to this user
    const subscription = await storage.getSubscription(userId);

    backendLogger.info(`Subscription found for reactivation for user ${userId}:`, {
      hasSubscription: !!subscription,
      subscriptionId: subscription?.id,
      stripeSubscriptionId: subscription?.stripe_subscription_id,
      requestedId: subscriptionId,
      matches: subscription?.stripe_subscription_id === subscriptionId
    });

    if (!subscription || subscription.stripe_subscription_id !== subscriptionId) {
      backendLogger.warn(`Unauthorized subscription reactivation attempt: ${userId}, ${subscriptionId}`);
      return res.status(403).json({ message: "Unauthorized" });
    }

    // Handle demo subscriptions differently (don't call Stripe API)
    if (subscriptionId.startsWith('demo_sub_')) {
      backendLogger.info(`Reactivating demo subscription: ${subscriptionId}`);

      // Update our database only
      await storage.updateSubscription(userId, {
        cancel_at_period_end: false
      });

      res.json({
        success: true,
        subscription: { id: subscriptionId, cancel_at_period_end: false }
      });
    } else {
      // Reactivate the subscription
      const updatedSubscription = await stripe.subscriptions.update(subscriptionId, {
        cancel_at_period_end: false
      });

      // Update our database
      await storage.updateSubscription(userId, {
        cancel_at_period_end: false
      });

      res.json({
        success: true,
        subscription: updatedSubscription
      });
    }
  }));

  // Payment webhook to handle Stripe events
  app.post("/api/stripe-webhook", express.raw({ type: 'application/json' }), async (req: Request, res: Response) => {
    // Import the webhook handler here to avoid circular dependencies
    const { handleStripeWebhook } = await import('./webhooks/stripe.js');
    await handleStripeWebhook(req, res);
  });

  // Coupon validation endpoint
  app.post("/api/validate-coupon", clerkAuth, asyncHandler(async (req: Request, res: Response) => {
    const { code } = req.body;

    if (!code) {
      return res.status(400).json({
        success: false,
        error: 'Coupon code is required'
      });
    }

    if (!req.clerkUser?.id) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    try {
      // First, try to find the coupon in Stripe directly
      let stripeCoupon;
      try {
        // List coupons with the given code
        const stripeCoupons = await stripe.coupons.list({
          limit: 10
        });

        // Find the coupon with matching code (case-insensitive)
        stripeCoupon = stripeCoupons.data.find(
          c => (c.name?.toLowerCase() === code.toLowerCase()) ||
               (c.id.toLowerCase() === code.toLowerCase()) ||
               (c.metadata?.code?.toLowerCase() === code.toLowerCase())
        );
      } catch (stripeError) {
        backendLogger.error('Error retrieving coupon from Stripe', { error: stripeError, code });
      }

      // If we found a valid coupon in Stripe, use it
      if (stripeCoupon && stripeCoupon.valid) {
        backendLogger.info(`Found valid coupon in Stripe: ${code}`);

        // Determine coupon type and amount
        let type, amount, duration, duration_in_months;

        if (stripeCoupon.percent_off) {
          type = 'percentage';
          amount = stripeCoupon.percent_off;
        } else if (stripeCoupon.amount_off) {
          type = 'fixed_amount';
          amount = stripeCoupon.amount_off / 100; // Convert from cents to dollars
        } else {
          backendLogger.warn(`Unsupported coupon type in Stripe: ${code}`);
          return res.status(400).json({
            success: false,
            error: 'Unsupported coupon type'
          });
        }

        duration = stripeCoupon.duration;
        duration_in_months = stripeCoupon.duration_in_months;

        // Return coupon details directly from Stripe
        return res.json({
          success: true,
          coupon: {
            code: code,
            description: stripeCoupon.name || `${type === 'percentage' ? amount + '%' : '$' + amount} off`,
            type,
            amount,
            duration,
            duration_in_months,
            stripe_coupon_id: stripeCoupon.id
          }
        });
      }

      // If we didn't find a valid coupon in Stripe, try our database as a fallback
      const coupon = await couponService.validateCoupon(code, req.clerkUser.id);

      if (!coupon) {
        return res.status(400).json({
          success: false,
          error: 'Invalid or expired coupon code'
        });
      }

      // Return coupon details (excluding sensitive fields)
      return res.json({
        success: true,
        coupon: {
          code: coupon.code,
          description: coupon.description,
          type: coupon.type,
          amount: coupon.amount,
          duration: coupon.duration,
          duration_in_months: coupon.duration_in_months
        }
      });
    } catch (error) {
      backendLogger.error('Error validating coupon', { error, code });
      return res.status(500).json({
        success: false,
        error: 'An error occurred while validating the coupon'
      });
    }
  }));

  // Admin routes for coupon management
  // Create a new coupon
  app.post("/api/admin/coupons", clerkAuth, asyncHandler(async (req: Request, res: Response) => {
    // Check if user is an admin
    if (!req.clerkUser?.id || !isAdmin(req)) {
      return res.status(403).json({
        success: false,
        error: 'Unauthorized'
      });
    }

    const {
      code,
      description,
      type,
      amount,
      duration,
      duration_in_months,
      max_redemptions,
      valid_from,
      valid_until
    } = req.body;

    // Validate required fields
    if (!code || !type || !duration) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields'
      });
    }

    try {
      const coupon = await couponService.createCoupon({
        code,
        description,
        type,
        amount,
        duration,
        duration_in_months,
        max_redemptions,
        valid_from: valid_from ? new Date(valid_from) : new Date(),
        valid_until: valid_until ? new Date(valid_until) : undefined,
        is_active: true
      });

      return res.status(201).json({
        success: true,
        coupon
      });
    } catch (error) {
      backendLogger.error('Error creating coupon', { error, code });
      return res.status(500).json({
        success: false,
        error: 'An error occurred while creating the coupon'
      });
    }
  }));

  // List all coupons
  app.get("/api/admin/coupons", clerkAuth, asyncHandler(async (req: Request, res: Response) => {
    // Check if user is an admin
    if (!req.clerkUser?.id || !isAdmin(req)) {
      return res.status(403).json({
        success: false,
        error: 'Unauthorized'
      });
    }

    const includeInactive = req.query.includeInactive === 'true';

    try {
      const coupons = await couponService.listCoupons(includeInactive);

      return res.json({
        success: true,
        coupons
      });
    } catch (error) {
      backendLogger.error('Error listing coupons', { error });
      return res.status(500).json({
        success: false,
        error: 'An error occurred while listing coupons'
      });
    }
  }));

  // Update a coupon
  app.put("/api/admin/coupons/:id", clerkAuth, asyncHandler(async (req: Request, res: Response) => {
    // Check if user is an admin
    if (!req.clerkUser?.id || !isAdmin(req)) {
      return res.status(403).json({
        success: false,
        error: 'Unauthorized'
      });
    }

    const id = parseInt(req.params.id);
    if (isNaN(id)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid coupon ID'
      });
    }

    const { is_active } = req.body;

    try {
      const coupon = await couponService.updateCouponStatus(id, is_active);

      if (!coupon) {
        return res.status(404).json({
          success: false,
          error: 'Coupon not found'
        });
      }

      return res.json({
        success: true,
        coupon
      });
    } catch (error) {
      backendLogger.error('Error updating coupon', { error, id });
      return res.status(500).json({
        success: false,
        error: 'An error occurred while updating the coupon'
      });
    }
  }));

  // Delete a coupon
  app.delete("/api/admin/coupons/:id", clerkAuth, asyncHandler(async (req: Request, res: Response) => {
    // Check if user is an admin
    if (!req.clerkUser?.id || !isAdmin(req)) {
      return res.status(403).json({
        success: false,
        error: 'Unauthorized'
      });
    }

    const id = parseInt(req.params.id);
    if (isNaN(id)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid coupon ID'
      });
    }

    try {
      const success = await couponService.deleteCoupon(id);

      if (!success) {
        return res.status(404).json({
          success: false,
          error: 'Coupon not found'
        });
      }

      return res.json({
        success: true
      });
    } catch (error) {
      backendLogger.error('Error deleting coupon', { error, id });
      return res.status(500).json({
        success: false,
        error: 'An error occurred while deleting the coupon'
      });
    }
  }));

  // Manual subscription creation endpoint (fallback for when webhooks aren't working)
  app.post("/api/manual-subscription-create", clerkAuth, asyncHandler(async (req: Request, res: Response) => {
    const userId = getUserId(req);
    const { sessionId } = req.body;

    backendLogger.info(`Manual subscription creation requested for user: ${userId}, session: ${sessionId}`);

    if (!sessionId) {
      return res.status(400).json({ message: "Session ID is required" });
    }

    try {
      // Retrieve the checkout session from Stripe with retry logic
      const session = await withRetry(
        async () => {
          backendLogger.info(`Retrieving checkout session: ${sessionId}`);
          return await stripe.checkout.sessions.retrieve(sessionId, {
            expand: ['subscription', 'customer']
          });
        },
        3, // Max retries
        200 // Base delay in ms
      );

      backendLogger.info(`Retrieved checkout session: ${sessionId}`, {
        status: session.status,
        hasSubscription: !!session.subscription,
        hasCustomer: !!session.customer,
        hasMetadata: !!session.metadata,
        metadata: session.metadata
      });

      // Check if this is a subscription checkout
      if (session.mode !== 'subscription') {
        backendLogger.warn(`Ignoring non-subscription checkout session: ${sessionId}`);
        return res.status(400).json({ message: "Not a subscription checkout session" });
      }

      // Get the customer ID
      let customerId: string;

      // Handle different formats of customer data
      if (typeof session.customer === 'string') {
        customerId = session.customer;
      } else if (typeof session.customer === 'object' && session.customer !== null) {
        // If it's an object, extract the ID
        customerId = (session.customer as any).id || '';
      } else {
        backendLogger.error('Customer ID not found in checkout session', {
          sessionId,
          customerType: typeof session.customer,
          customerValue: session.customer
        });
        return res.status(400).json({ message: "Customer ID not found in checkout session" });
      }

      if (!customerId) {
        backendLogger.error('Empty customer ID in checkout session', { sessionId });
        return res.status(400).json({ message: "Empty customer ID in checkout session" });
      }

      // Get the subscription ID
      let subscriptionId: string;

      // Handle different formats of subscription data
      if (typeof session.subscription === 'string') {
        subscriptionId = session.subscription;
      } else if (typeof session.subscription === 'object' && session.subscription !== null) {
        // If it's an object, extract the ID
        subscriptionId = (session.subscription as any).id || '';
      } else {
        backendLogger.error('Subscription ID not found in checkout session', {
          sessionId,
          subscriptionType: typeof session.subscription,
          subscriptionValue: session.subscription
        });
        return res.status(400).json({ message: "Subscription ID not found in checkout session" });
      }

      if (!subscriptionId) {
        backendLogger.error('Empty subscription ID in checkout session', { sessionId });
        return res.status(400).json({ message: "Empty subscription ID in checkout session" });
      }

      // Get metadata from the session
      const planId = session.metadata?.planId;
      const billingCycle = session.metadata?.billingCycle;

      if (!planId || !billingCycle) {
        backendLogger.error('Missing metadata in checkout session', {
          sessionId,
          planId: planId || 'missing',
          billingCycle: billingCycle || 'missing'
        });
        return res.status(400).json({ message: "Missing metadata in checkout session" });
      }

      // Check if a subscription already exists for this user
      const existingSubscription = await storage.getSubscription(userId);

      if (existingSubscription) {
        backendLogger.info(`Subscription already exists for user: ${userId}`, {
          existingId: existingSubscription.id,
          stripeId: existingSubscription.stripe_subscription_id
        });

        // If the subscription exists but has a different Stripe subscription ID, update it
        if (existingSubscription.stripe_subscription_id !== subscriptionId) {
          await storage.updateSubscription(userId, {
            stripe_subscription_id: subscriptionId,
            stripe_customer_id: customerId,
            plan_id: planId,
            billing_cycle: billingCycle,
            status: 'active',
            cancel_at_period_end: false
          });

          backendLogger.info(`Updated existing subscription for user: ${userId}`);

          // Get the updated subscription
          const updatedSubscription = await storage.getSubscription(userId);

          return res.json({
            success: true,
            message: "Subscription updated with new details",
            subscription: updatedSubscription
          });
        }

        // Even if the subscription ID is the same, ensure the status is active
        if (existingSubscription.status !== 'active') {
          await storage.updateSubscription(userId, {
            status: 'active',
            cancel_at_period_end: false
          });

          backendLogger.info(`Reactivated existing subscription for user: ${userId}`);

          // Get the updated subscription
          const updatedSubscription = await storage.getSubscription(userId);

          return res.json({
            success: true,
            message: "Subscription reactivated",
            subscription: updatedSubscription
          });
        }

        return res.json({
          success: true,
          message: "Subscription already exists and is active",
          subscription: existingSubscription
        });
      }

      // Create a new subscription in our database with retry logic
      const subscription = await withRetry(
        async () => {
          backendLogger.info(`Creating subscription for user: ${userId} with retry logic`);
          return await subscriptionService.createSubscription(
            userId,
            planId,
            billingCycle as 'monthly' | 'annual',
            customerId,
            subscriptionId
          );
        },
        2, // Max retries
        300 // Base delay in ms
      );

      backendLogger.info(`Successfully created subscription for user: ${userId}`, {
        planId,
        billingCycle,
        subscriptionId
      });

      return res.json({
        success: true,
        message: "Subscription created successfully",
        subscription
      });
    } catch (error: any) {
      backendLogger.error(`Error creating subscription manually: ${error.message}`, { error, sessionId });
      return res.status(500).json({
        success: false,
        message: "Failed to create subscription",
        error: error.message
      });
    }
  }));

  // Health check endpoint
  app.get("/api/health", asyncHandler(async (req: Request, res: Response) => {
    const start = Date.now();
    const healthStatus = {
      status: "ok",
      timestamp: new Date().toISOString(),
      services: {
        database: "unknown",
        openai: "unknown"
      }
    };

    // Test database connectivity
    try {
      await db.client.from('users').select('count').limit(1);
      healthStatus.services.database = "connected";
    } catch (error) {
      healthStatus.services.database = "disconnected";
      healthStatus.status = "degraded";
      backendLogger.error('Database health check failed:', error);
    }

    // Test OpenAI connectivity (if API key is available)
    if (process.env.OPENAI_API_KEY) {
      try {
        // Simple test to check if OpenAI is reachable
        const response = await fetch('https://api.openai.com/v1/models', {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,
            'Content-Type': 'application/json'
          }
        });
        healthStatus.services.openai = response.ok ? "connected" : "error";
      } catch (error) {
        healthStatus.services.openai = "disconnected";
        if (healthStatus.status === "ok") healthStatus.status = "degraded";
      }
    }

    const duration = Date.now() - start;
    const statusCode = healthStatus.status === "ok" ? 200 : 503;

    backendLogger.info(`[HEALTH] /api/health responded in ${duration}ms with status: ${healthStatus.status}`);
    res.status(statusCode).json(healthStatus);
  }));

  // Debug endpoint to check subscription data structure
  app.get("/api/debug/subscription", clerkAuth, asyncHandler(async (req: Request, res: Response) => {
    // Handle unauthenticated requests gracefully
    if (req.isUnauthenticated) {
      backendLogger.info(`[DEBUG] Unauthenticated request to /api/debug/subscription, returning default data`);

      // Return default data for unauthenticated users
      return res.json({
        rawSubscription: null,
        rawUsage: null,
        planLimits: null,
        authStatus: 'unauthenticated'
      });
    }

    const userId = getUserId(req);
    backendLogger.info(`[DEBUG] Fetching raw subscription data for user: ${userId}`);

    const subscription = await storage.getSubscription(userId);
    const usage = await storage.getCurrentUsage(userId);

    // Return the raw data without any transformations
    res.json({
      rawSubscription: subscription,
      rawUsage: usage,
      planLimits: subscription ? PLAN_LIMITS[subscription.plan_id as keyof typeof PLAN_LIMITS]?.[subscription.billing_cycle as 'monthly' | 'annual'] : null,
      authStatus: 'authenticated'
    });
  }));

  // Clear cache endpoint (for debugging and troubleshooting)
  app.post("/api/clear-cache", clerkAuth, asyncHandler(async (req: Request, res: Response) => {
    const userId = getUserId(req);
    const { type } = req.body;

    backendLogger.info(`Clearing cache for user: ${userId}, type: ${type}`);

    if (type === 'customer' || type === 'all') {
      // Clear customer cache for this user
      if (global.stripeCustomerCache && global.stripeCustomerCache[userId]) {
        delete global.stripeCustomerCache[userId];
        backendLogger.info(`Cleared customer cache for user: ${userId}`);
      }
    }

    if (type === 'subscription' || type === 'all') {
      // Clear subscription cache for this user
      subscriptionCache.delete(userId);
      backendLogger.info(`Cleared subscription cache for user: ${userId}`);
    }

    return res.json({
      success: true,
      message: `Cache cleared for user: ${userId}, type: ${type || 'all'}`
    });
  }));

  const httpServer = createServer(app);
  return httpServer;
}

// Helper function to process a modification and generate images
async function processModification(projectId: number, modificationId: number) {
  backendLogger.info(`Starting image processing for project ${projectId}, modification ${modificationId}`);

  try {
    // Get the project and modification
    const project = await storage.getProjectWithImages(projectId);
    const modification = await storage.getModification(modificationId);

    if (!project || !modification) {
      backendLogger.error(`[ERROR] Project or modification not found: project=${!!project}, modification=${!!modification}`);
      return;
    }

    // Get the user ID from the project
    const userId = project.user_id;

    backendLogger.info(`Project found: ${project.title}, status: ${project.status}`);
    backendLogger.info(`Modification found: ${modification.id}, description: ${modification.description}`);

    // Update project status to processing (if it's not already processing or completed)
    if (project.status === "draft") {
      await storage.updateProjectStatus(projectId, "processing");
      backendLogger.info(`Updated project status from 'draft' to 'processing'`);

      // Count the project now that it's being processed (not just created as draft)
      try {
        await storage.incrementProjectCount(userId);
        backendLogger.info(`[USAGE] Incremented project count for user ${userId} - project ${projectId} moved from draft to processing`);
      } catch (usageError) {
        backendLogger.error(`[ERROR] Failed to increment project count: ${usageError}`);
        // Continue processing even if usage tracking fails
      }
    } else {
      backendLogger.info(`Project status is already '${project.status}', not changing to 'processing'`);
    }

    // Get before images and reference images
    // Get the raw images from the database to get the actual file paths
    const { data: rawBeforeImages } = await db.client
      .from('images')
      .select('*')
      .eq('project_id', projectId)
      .eq('type', 'before');

    const beforeImages = rawBeforeImages || [];

    // Get all reference images for the project
    let referenceImages = [];
    const { data: rawReferenceImages } = await db.client
      .from('images')
      .select('*')
      .eq('project_id', projectId)
      .eq('type', 'reference');

    referenceImages = rawReferenceImages || [];

    backendLogger.info(`Found ${beforeImages.length} before images and ${referenceImages.length} reference images`);
    backendLogger.debug(`Reference image IDs: ${JSON.stringify(modification.reference_image_ids)}`);

    if (beforeImages.length === 0) {
      backendLogger.error(`[ERROR] No before images found for project ${projectId}`);
      await storage.updateProjectStatus(projectId, "failed");
      return;
    }

    // Process each before image
    let generatedImagesCount = 0;
    let submittedImagesCount = 0;

    for (const beforeImage of beforeImages) {
      backendLogger.info(`Processing before image: ${beforeImage.id}, path: ${beforeImage.path}`);

      // Increment usage count on submission to prevent abuse
      try {
        await storage.incrementImageCount(userId, 1);
        submittedImagesCount++;
        backendLogger.info(`[USAGE] Incremented image count for user ${userId} on submission - image ${beforeImage.id}`);
      } catch (usageError) {
        backendLogger.error(`[ERROR] Failed to increment image count on submission: ${usageError}`);
        // Continue processing even if usage tracking fails
      }

      try {
        // Generate with OpenAI gpt-image-1 only
        backendLogger.info(`Calling OpenAI gpt-image-1 to generate modification...`);
        backendLogger.debug(`Parameters: beforeImagePath=${beforeImage.path}, description=${modification.description}`);

        // Get reference image paths
        let refImagePaths = referenceImages.map(img => img.path);
        backendLogger.debug(`Reference image paths: ${refImagePaths.join(', ')}`);

        // Check if we have structured modification data
        let modificationData: any;

        if (modification.type) {
          // We have structured data
          modificationData = {
            type: modification.type,
            description: modification.description,
            options: modification.options || {},
            referenceImageIds: modification.reference_image_ids,
            primaryReferenceImageId: modification.primary_reference_image_id
          };

          backendLogger.info(`Using structured modification data of type: ${modification.type}`);

          // If we have a primary reference image, make sure it's first in the refImagePaths array
          if (modification.primary_reference_image_id && refImagePaths.length > 0) {
            // Find the primary reference image
            const primaryRefImage = referenceImages.find(img => img.id === modification.primary_reference_image_id);

            if (primaryRefImage) {
              // Reorder the refImagePaths to put the primary image first
              const primaryImagePath = path.join(process.cwd(), 'uploads', primaryRefImage.path);

              // Remove the primary image from the array if it's already there
              const otherPaths = refImagePaths.filter(p => p !== primaryImagePath);

              // Put the primary image first
              refImagePaths = [primaryImagePath, ...otherPaths];

              backendLogger.info(`Prioritized primary reference image ID ${modification.primary_reference_image_id}`);
            }
          }
        } else {
          // Legacy format - just use the description
          modificationData = modification.description;
          backendLogger.info(`Using legacy modification format with description only`);
        }

        // Pass reference images to the generation function
        let afterImagePath = await generateRenovationImage(
          beforeImage.path,
          modificationData,
          refImagePaths
        );

        // Check if the generation was successful
        if (afterImagePath.endsWith('.txt')) {
          backendLogger.error(`OpenAI gpt-image-1 generation failed for image ${beforeImage.id}`);

          // Decrement usage count since generation failed
          try {
            await storage.decrementImageCount(userId, 1);
            backendLogger.info(`[USAGE] Decremented image count for user ${userId} due to generation failure - image ${beforeImage.id}`);
          } catch (usageError) {
            backendLogger.error(`[ERROR] Failed to decrement image count after generation failure: ${usageError}`);
          }

          // Try to use a fallback image instead of a text file
          const fallbackImagePath = path.join(process.cwd(), 'assets', 'error-image.png');
          if (fs.existsSync(fallbackImagePath)) {
            const outputDir = path.join(process.cwd(), 'uploads', 'generated');
            const timestamp = Date.now();
            const newPath = path.join(outputDir, `error_${timestamp}.png`);

            try {
              // Copy the fallback image to the uploads directory
              await fs.promises.copyFile(fallbackImagePath, newPath);
              backendLogger.info(`Using fallback error image for failed generation: ${newPath}`);
              afterImagePath = newPath;
            } catch (copyError) {
              backendLogger.error(`Failed to copy fallback image: ${copyError}`);
              // Keep the original error text file path
            }
          }
        } else {
          backendLogger.info(`OpenAI gpt-image-1 generated image successfully, saved at: ${afterImagePath}`);
          // Count this as a successful generation
          generatedImagesCount++;
        }
        // Save the after image
        const afterImage = await storage.createImage({
          project_id: projectId,
          type: "after",
          path: afterImagePath,
          original_filename: `after_${path.basename(beforeImage.path)}`,
        });
        backendLogger.info(`After image saved to database with ID: ${afterImage.id}`);

      } catch (error) {
        backendLogger.error(`[ERROR] Error processing image ${beforeImage.id}:`, error);

        // Decrement usage count since processing failed
        try {
          await storage.decrementImageCount(userId, 1);
          backendLogger.info(`[USAGE] Decremented image count for user ${userId} due to processing error - image ${beforeImage.id}`);
        } catch (usageError) {
          backendLogger.error(`[ERROR] Failed to decrement image count after processing error: ${usageError}`);
        }
      }
    }

    // Usage tracking is now handled during processing:
    // - Images are counted on submission (to prevent abuse)
    // - Images are decremented on failure (to ensure fair billing)
    // - Only successful generations count toward final usage
    backendLogger.info(`[USAGE] Processing complete - ${generatedImagesCount} images successfully generated out of ${submittedImagesCount} submitted`);

    if (generatedImagesCount !== submittedImagesCount) {
      backendLogger.warn(`[USAGE] Usage discrepancy detected: ${submittedImagesCount} submitted, ${generatedImagesCount} successful`);
    }

    // Update project status to completed
    await storage.updateProjectStatus(projectId, "completed");
    backendLogger.info(`Updated project status to 'completed'`);
  } catch (error) {
    backendLogger.error(`[ERROR] Error processing modification for project ${projectId}:`, error);
    // Update project status to failed
    await storage.updateProjectStatus(projectId, "failed");
    backendLogger.info(`Updated project status to 'failed' due to error`);
  }
}

// Helper to wrap async handlers with timing logs
function withTimingLogs(handlerName: string, handler: (req: Request, res: Response) => Promise<any>) {
  return async (req: Request, res: Response) => {
    const start = Date.now();
    backendLogger.info(`[API] ${handlerName} started`);
    try {
      await handler(req, res);
      const duration = Date.now() - start;
      backendLogger.info(`[API] ${handlerName} completed in ${duration}ms`);
    } catch (error) {
      const duration = Date.now() - start;
      backendLogger.error(`[API] ${handlerName} failed in ${duration}ms`, error);
      throw error;
    }
  };
}

// No need for duplicate import
