-- Add indexes for common queries to improve performance

-- Users table
CREATE INDEX IF NOT EXISTS idx_users_id ON users(id);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);

-- Projects table
CREATE INDEX IF NOT EXISTS idx_projects_user_id ON projects(user_id);
CREATE INDEX IF NOT EXISTS idx_projects_status ON projects(status);

-- Images table
CREATE INDEX IF NOT EXISTS idx_images_project_id ON images(project_id);
CREATE INDEX IF NOT EXISTS idx_images_type ON images(type);

-- Modifications table
CREATE INDEX IF NOT EXISTS idx_modifications_project_id ON modifications(project_id);

-- Reference categories table
CREATE INDEX IF NOT EXISTS idx_reference_categories_user_id ON reference_categories(user_id);

-- Reference items table
CREATE INDEX IF NOT EXISTS idx_reference_items_user_id ON reference_items(user_id);
CREATE INDEX IF NOT EXISTS idx_reference_items_category_id ON reference_items(category_id);

-- Renovation presets table
CREATE INDEX IF NOT EXISTS idx_renovation_presets_room_type ON renovation_presets(room_type);
CREATE INDEX IF NOT EXISTS idx_renovation_presets_created_by ON renovation_presets(created_by);

-- Drafts table
CREATE INDEX IF NOT EXISTS idx_drafts_user_id ON drafts(user_id);

-- Subscriptions table
CREATE INDEX IF NOT EXISTS idx_subscriptions_user_id ON subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_stripe_subscription_id ON subscriptions(stripe_subscription_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_stripe_customer_id ON subscriptions(stripe_customer_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_status ON subscriptions(status);

-- Usage table
CREATE INDEX IF NOT EXISTS idx_usage_user_id ON usage(user_id);
CREATE INDEX IF NOT EXISTS idx_usage_period ON usage(period_start, period_end);

-- Processed webhook events table
CREATE INDEX IF NOT EXISTS idx_processed_webhook_events_event_id ON processed_webhook_events(event_id);
CREATE INDEX IF NOT EXISTS idx_processed_webhook_events_event_type ON processed_webhook_events(event_type);
