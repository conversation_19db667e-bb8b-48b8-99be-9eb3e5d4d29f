/**
 * Prompt Templates for Renovation Modifications
 *
 * This file contains all the prompt templates used for different modification types.
 * Each template is a function that takes relevant parameters and returns a formatted prompt.
 *
 * To add a new modification type:
 * 1. Create a new template function
 * 2. Add it to the promptTemplates object
 * 3. Update the ModificationType type in shared/schema.ts
 */

// Types for prompt generation
interface PromptParams {
  description?: string;
  options?: any;
  hasPrimaryReferenceImage: boolean;
  hasAdditionalReferenceImages: boolean;
  referenceImageCount: number;
}

/**
 * Template for custom modifications
 */
function customModificationTemplate(params: PromptParams): string {
  let prompt = `Please modify the original image according to this request: "${params.description}".`;

  // Add reference image guidance
  if (params.hasPrimaryReferenceImage) {
    prompt += `\nUse the first reference image as the primary inspiration for the changes.`;

    if (params.hasAdditionalReferenceImages) {
      prompt += `\nAlso consider the other ${params.referenceImageCount - 1} reference images for additional inspiration.`;
    }
  } else if (params.hasAdditionalReferenceImages) {
    prompt += `\nUse the provided reference images as inspiration for the changes.`;
  }

  // Add quality instructions
  prompt += `
  Make the changes look realistic and professional.
  Maintain the original perspective, lighting, and overall composition.
  Focus specifically on the requested modifications while keeping the rest of the image intact.`;

  return prompt;
}

/**
 * Template for floor replacement
 */
function replaceFloorTemplate(params: PromptParams): string {
  const options = params.options || {};
  let prompt = '';

  if (options.useReferenceImage && params.hasPrimaryReferenceImage) {
    // Using reference image for floor replacement
    prompt = `Please replace the floor in the original image using the provided reference image as inspiration for the new flooring.`;

    if (options.customDescription) {
      prompt += `\nAdditional details: ${options.customDescription}.`;
    }

    // Add specific instructions for reference-based floor replacement
    prompt += `
    The reference image shows the exact floor style, pattern, and color that should be used.
    Make sure the new floor looks realistic and properly integrated with the rest of the room.
    Maintain the same perspective and lighting as the original image.
    The floor should have appropriate reflections and shadows.
    Keep all other elements of the room exactly the same.`;
  } else {
    // Standard floor replacement with material and color
    const material = options.material || '';
    const color = options.color || '';

    prompt = `Please replace the floor in the original image with ${color} ${material} flooring.`;

    if (options.customDescription) {
      prompt += `\nAdditional details: ${options.customDescription}.`;
    }

    // Add specific instructions for floor replacement
    prompt += `
    Make sure the new floor looks realistic and properly integrated with the rest of the room.
    Maintain the same perspective and lighting as the original image.
    The floor should have appropriate reflections and shadows.
    Keep all other elements of the room exactly the same.`;
  }

  // Add reference image guidance for additional reference images
  if (!options.useReferenceImage && params.hasAdditionalReferenceImages) {
    prompt += `\nUse the provided reference images as additional inspiration for the floor style.`;
  } else if (options.useReferenceImage && params.hasAdditionalReferenceImages && params.referenceImageCount > 1) {
    prompt += `\nAlso consider the other ${params.referenceImageCount - 1} reference images for additional inspiration.`;
  }

  return prompt;
}

/**
 * Template for wall color changes
 */
function changeWallColorTemplate(params: PromptParams): string {
  const options = params.options || {};
  let prompt = '';

  if (options.useReferenceImage && params.hasPrimaryReferenceImage) {
    // Using reference image for wall color
    prompt = `Please change the wall color in the original image using the provided reference image as inspiration for the new wall color and finish.`;

    if (options.customDescription) {
      prompt += `\nAdditional details: ${options.customDescription}.`;
    }

    // Add specific instructions for reference-based wall color change
    prompt += `
    The reference image shows the exact wall color, tone, and finish that should be used.
    Make sure the new wall color looks realistic and properly integrated with the rest of the room.
    Maintain the same lighting conditions and shadows on the walls.
    The walls should reflect light appropriately based on the new color.
    Keep all other elements of the room exactly the same.`;
  } else {
    // Standard color and finish selection
    const color = options.color || '';
    const finish = options.finish || '';

    prompt = `Please change the wall color in the original image to ${color}`;
    if (finish) {
      prompt += ` with ${finish} finish`;
    }
    prompt += `.`;

    if (params.description) {
      prompt += `\nSpecific request: "${params.description}"`;
    }

    if (options.customDescription) {
      prompt += `\nAdditional details: ${options.customDescription}.`;
    }

    // Add specific instructions for color-based wall change
    prompt += `
    Make sure the new wall color looks realistic and properly integrated with the rest of the room.
    Maintain the same lighting conditions and shadows on the walls.
    The walls should reflect light appropriately based on the new color and finish.
    Keep all other elements of the room exactly the same.
    Ensure the new wall color complements the existing decor and furniture.`;
  }

  // Add reference image guidance for additional reference images
  if (!options.useReferenceImage && params.hasAdditionalReferenceImages) {
    prompt += `\nUse the provided reference images as additional inspiration for the wall color and finish.`;
  } else if (options.useReferenceImage && params.hasAdditionalReferenceImages && params.referenceImageCount > 1) {
    prompt += `\nAlso consider the other ${params.referenceImageCount - 1} reference images for additional inspiration.`;
  }

  return prompt;
}

/**
 * Template for cabinet updates
 */
function updateCabinetsTemplate(params: PromptParams): string {
  const options = params.options || {};
  let prompt = '';

  if (options.useReferenceImage && params.hasPrimaryReferenceImage) {
    // Using reference image for cabinet updates
    prompt = `Please update the cabinets in the original image using the provided reference image as inspiration for the new cabinet design.`;

    if (options.customDescription) {
      prompt += `\nAdditional details: ${options.customDescription}.`;
    }

    // Add specific instructions for reference-based cabinet updates
    prompt += `
    The reference image shows the exact cabinet style, color, material, and hardware that should be used.
    Make sure the new cabinets look realistic and properly integrated with the rest of the room.
    Maintain the same lighting conditions and shadows.
    The cabinets should have appropriate reflections and depth.
    Keep all other elements of the room exactly the same.
    Ensure the cabinet hardware (handles, knobs) matches the reference image style.`;
  } else {
    // Standard cabinet options selection
    prompt = `Please update the cabinets in the original image`;

    const updates = [];
    if (options.color) updates.push(`color to ${options.color}`);
    if (options.material) updates.push(`material to ${options.material}`);
    if (options.style) updates.push(`style to ${options.style}`);

    if (updates.length > 0) {
      prompt += ` with ${updates.join(", ")}`;
    }
    prompt += `.`;

    if (params.description) {
      prompt += `\nSpecific request: "${params.description}"`;
    }

    if (options.customDescription) {
      prompt += `\nAdditional details: ${options.customDescription}.`;
    }

    // Add specific instructions for option-based cabinet updates
    prompt += `
    Make sure the new cabinets look realistic and properly integrated with the rest of the room.
    Maintain the same lighting conditions and shadows.
    The cabinets should have appropriate reflections and depth.
    Keep all other elements of the room exactly the same.
    Ensure the cabinet hardware (handles, knobs) is updated to match the new style and material.
    Pay attention to cabinet door and drawer details to maintain realistic proportions.`;
  }

  // Add reference image guidance for additional reference images
  if (!options.useReferenceImage && params.hasAdditionalReferenceImages) {
    prompt += `\nUse the provided reference images as additional inspiration for the cabinet design.`;
  } else if (options.useReferenceImage && params.hasAdditionalReferenceImages && params.referenceImageCount > 1) {
    prompt += `\nAlso consider the other ${params.referenceImageCount - 1} reference images for additional inspiration.`;
  }

  return prompt;
}

/**
 * Template for countertop changes
 * Note: This is a placeholder for future implementation
 */
function changeCountertopsTemplate(params: PromptParams): string {
  const options = params.options || {};
  let prompt = `Please change the countertops in the original image.`;

  if (params.description) {
    prompt += `\nSpecific request: "${params.description}"`;
  }

  if (options.material) {
    prompt += `\nChange the countertop material to ${options.material}.`;
  }

  if (options.color) {
    prompt += `\nChange the countertop color to ${options.color}.`;
  }

  if (options.customDescription) {
    prompt += `\nAdditional details: ${options.customDescription}.`;
  }

  // Add reference image guidance
  if (params.hasPrimaryReferenceImage) {
    prompt += `\nUse the first reference image as inspiration for the countertop design.`;

    if (params.hasAdditionalReferenceImages) {
      prompt += `\nAlso consider the other ${params.referenceImageCount - 1} reference images for additional inspiration.`;
    }
  } else if (params.hasAdditionalReferenceImages) {
    prompt += `\nUse the provided reference images as inspiration for the countertop design.`;
  }

  // Add quality instructions
  prompt += `
  Make sure the new countertops look realistic and properly integrated with the rest of the room.
  Maintain the same lighting conditions and shadows.
  Keep all other elements of the room exactly the same.
  Ensure the countertop edges and seams look realistic.`;

  return prompt;
}

/**
 * Template for kitchen remodel
 */
function kitchenRemodelTemplate(params: PromptParams): string {
  const options = params.options || {};
  let prompt = '';

  if (options.useReferenceImage && params.hasPrimaryReferenceImage) {
    prompt = `Please perform a comprehensive kitchen remodel in the original image using the provided reference image as inspiration for the new kitchen design.`;

    if (options.customDescription) {
      prompt += `\nAdditional details: ${options.customDescription}.`;
    }

    prompt += `
    The reference image shows the desired kitchen style, layout, cabinetry, countertops, appliances, and overall aesthetic.
    Transform the kitchen to match the reference while maintaining the original room's proportions and architectural features.
    Update cabinets, countertops, appliances, backsplash, lighting, and any other kitchen elements as shown in the reference.
    Ensure all elements work together cohesively for a modern, functional kitchen design.`;
  } else {
    prompt = `Please perform a comprehensive kitchen remodel in the original image`;

    const updates = [];
    if (options.cabinetStyle) updates.push(`${options.cabinetStyle} style cabinets`);
    if (options.countertopMaterial) updates.push(`${options.countertopMaterial} countertops`);
    if (options.applianceStyle) updates.push(`${options.applianceStyle} appliances`);
    if (options.colorScheme) updates.push(`${options.colorScheme} color scheme`);

    if (updates.length > 0) {
      prompt += ` featuring ${updates.join(", ")}`;
    }
    prompt += `.`;

    if (params.description) {
      prompt += `\nSpecific request: "${params.description}"`;
    }

    if (options.customDescription) {
      prompt += `\nAdditional details: ${options.customDescription}.`;
    }

    prompt += `
    Create a modern, functional kitchen with updated cabinetry, countertops, appliances, and layout.
    Ensure proper lighting, storage solutions, and workflow efficiency.
    Maintain the original room's architectural features while modernizing all kitchen elements.
    Pay attention to realistic proportions, materials, and finishes.`;
  }

  if (!options.useReferenceImage && params.hasAdditionalReferenceImages) {
    prompt += `\nUse the provided reference images as inspiration for the kitchen design elements.`;
  } else if (options.useReferenceImage && params.hasAdditionalReferenceImages && params.referenceImageCount > 1) {
    prompt += `\nAlso consider the other ${params.referenceImageCount - 1} reference images for additional design inspiration.`;
  }

  return prompt;
}

/**
 * Template for bathroom remodel
 */
function bathroomRemodelTemplate(params: PromptParams): string {
  const options = params.options || {};
  let prompt = '';

  if (options.useReferenceImage && params.hasPrimaryReferenceImage) {
    prompt = `Please perform a comprehensive bathroom remodel in the original image using the provided reference image as inspiration for the new bathroom design.`;

    if (options.customDescription) {
      prompt += `\nAdditional details: ${options.customDescription}.`;
    }

    prompt += `
    The reference image shows the desired bathroom style, fixtures, tiles, lighting, and overall spa-like aesthetic.
    Transform the bathroom to match the reference while maintaining the original room's proportions and plumbing locations.
    Update fixtures, tiles, vanity, lighting, mirrors, and any other bathroom elements as shown in the reference.
    Create a modern, spa-like retreat with attention to luxury and functionality.`;
  } else {
    prompt = `Please perform a comprehensive bathroom remodel in the original image`;

    const updates = [];
    if (options.fixtureStyle) updates.push(`${options.fixtureStyle} fixtures`);
    if (options.tileStyle) updates.push(`${options.tileStyle} tiles`);
    if (options.vanityStyle) updates.push(`${options.vanityStyle} vanity`);
    if (options.colorScheme) updates.push(`${options.colorScheme} color scheme`);

    if (updates.length > 0) {
      prompt += ` featuring ${updates.join(", ")}`;
    }
    prompt += `.`;

    if (params.description) {
      prompt += `\nSpecific request: "${params.description}"`;
    }

    if (options.customDescription) {
      prompt += `\nAdditional details: ${options.customDescription}.`;
    }

    prompt += `
    Create a modern, spa-like bathroom with updated fixtures, tiles, lighting, and layout.
    Focus on creating a relaxing, luxurious atmosphere with proper lighting and storage.
    Maintain realistic proportions and ensure all elements work together cohesively.
    Pay attention to water-resistant materials and proper bathroom functionality.`;
  }

  if (!options.useReferenceImage && params.hasAdditionalReferenceImages) {
    prompt += `\nUse the provided reference images as inspiration for the bathroom design elements.`;
  } else if (options.useReferenceImage && params.hasAdditionalReferenceImages && params.referenceImageCount > 1) {
    prompt += `\nAlso consider the other ${params.referenceImageCount - 1} reference images for additional design inspiration.`;
  }

  return prompt;
}

/**
 * Template for interior painting
 */
function interiorPaintingTemplate(params: PromptParams): string {
  const options = params.options || {};
  let prompt = '';

  if (options.useReferenceImage && params.hasPrimaryReferenceImage) {
    prompt = `Please refresh the interior with new paint colors using the provided reference image as inspiration for the color scheme and painting approach.`;

    if (options.customDescription) {
      prompt += `\nAdditional details: ${options.customDescription}.`;
    }

    prompt += `
    The reference image shows the desired color palette, paint finishes, and overall ambiance.
    Apply the new paint colors to walls and ceilings to transform the space's atmosphere.
    Ensure the new colors complement existing furniture and decor.
    Maintain proper lighting effects and realistic paint application.`;
  } else {
    prompt = `Please refresh the interior with new paint colors`;

    const updates = [];
    if (options.wallColor) updates.push(`${options.wallColor} walls`);
    if (options.ceilingColor) updates.push(`${options.ceilingColor} ceiling`);
    if (options.accentWall) updates.push(`${options.accentWall} accent wall`);
    if (options.finish) updates.push(`${options.finish} finish`);

    if (updates.length > 0) {
      prompt += ` featuring ${updates.join(", ")}`;
    }
    prompt += `.`;

    if (params.description) {
      prompt += `\nSpecific request: "${params.description}"`;
    }

    if (options.customDescription) {
      prompt += `\nAdditional details: ${options.customDescription}.`;
    }

    prompt += `
    Transform the ambiance of the space with fresh paint colors on walls and ceilings.
    Ensure the new colors create the desired mood and complement existing decor.
    Maintain realistic lighting effects and proper paint coverage.
    Keep all furniture, fixtures, and other elements exactly the same.`;
  }

  if (!options.useReferenceImage && params.hasAdditionalReferenceImages) {
    prompt += `\nUse the provided reference images as inspiration for the color scheme and painting approach.`;
  } else if (options.useReferenceImage && params.hasAdditionalReferenceImages && params.referenceImageCount > 1) {
    prompt += `\nAlso consider the other ${params.referenceImageCount - 1} reference images for additional color inspiration.`;
  }

  return prompt;
}

/**
 * Template for lighting upgrades
 */
function lightingUpgradesTemplate(params: PromptParams): string {
  const options = params.options || {};
  let prompt = '';

  if (options.useReferenceImage && params.hasPrimaryReferenceImage) {
    prompt = `Please enhance the lighting in the original image using the provided reference image as inspiration for the new lighting fixtures and ambiance.`;

    if (options.customDescription) {
      prompt += `\nAdditional details: ${options.customDescription}.`;
    }

    prompt += `
    The reference image shows the desired lighting fixtures, placement, and overall ambiance.
    Update ceiling lights, wall sconces, lamps, and any other lighting elements as shown.
    Enhance the room's ambiance and energy efficiency with modern lighting solutions.
    Ensure proper light distribution and realistic lighting effects throughout the space.`;
  } else {
    prompt = `Please enhance the lighting in the original image`;

    const updates = [];
    if (options.fixtureStyle) updates.push(`${options.fixtureStyle} fixtures`);
    if (options.lightingType) updates.push(`${options.lightingType} lighting`);
    if (options.ambiance) updates.push(`${options.ambiance} ambiance`);
    if (options.smartControls) updates.push(`smart lighting controls`);

    if (updates.length > 0) {
      prompt += ` with ${updates.join(", ")}`;
    }
    prompt += `.`;

    if (params.description) {
      prompt += `\nSpecific request: "${params.description}"`;
    }

    if (options.customDescription) {
      prompt += `\nAdditional details: ${options.customDescription}.`;
    }

    prompt += `
    Enhance the room's ambiance and energy efficiency with updated lighting fixtures and smart controls.
    Focus on proper light distribution, modern fixtures, and improved functionality.
    Maintain realistic lighting effects and ensure fixtures complement the room's style.
    Keep all other elements of the room exactly the same while improving the lighting.`;
  }

  if (!options.useReferenceImage && params.hasAdditionalReferenceImages) {
    prompt += `\nUse the provided reference images as inspiration for the lighting design and fixtures.`;
  } else if (options.useReferenceImage && params.hasAdditionalReferenceImages && params.referenceImageCount > 1) {
    prompt += `\nAlso consider the other ${params.referenceImageCount - 1} reference images for additional lighting inspiration.`;
  }

  return prompt;
}

/**
 * Template for exterior repainting
 */
function exteriorRepaintingTemplate(params: PromptParams): string {
  const options = params.options || {};
  let prompt = '';

  if (options.useReferenceImage && params.hasPrimaryReferenceImage) {
    prompt = `Please give the home's facade a fresh new look with updated exterior paint using the provided reference image as inspiration for the color scheme and painting approach.`;

    if (options.customDescription) {
      prompt += `\nAdditional details: ${options.customDescription}.`;
    }

    prompt += `
    The reference image shows the desired exterior color palette, trim colors, and overall curb appeal.
    Apply the new paint colors to siding, trim, doors, and other exterior elements as shown.
    Ensure the new colors enhance the home's architectural features and neighborhood appeal.
    Maintain realistic paint application and weather-appropriate finishes.`;
  } else {
    prompt = `Please give the home's facade a fresh new look with updated exterior paint`;

    const updates = [];
    if (options.mainColor) updates.push(`${options.mainColor} main color`);
    if (options.trimColor) updates.push(`${options.trimColor} trim`);
    if (options.accentColor) updates.push(`${options.accentColor} accents`);
    if (options.doorColor) updates.push(`${options.doorColor} door`);

    if (updates.length > 0) {
      prompt += ` featuring ${updates.join(", ")}`;
    }
    prompt += `.`;

    if (params.description) {
      prompt += `\nSpecific request: "${params.description}"`;
    }

    if (options.customDescription) {
      prompt += `\nAdditional details: ${options.customDescription}.`;
    }

    prompt += `
    Transform the home's curb appeal with fresh exterior paint colors.
    Ensure the new colors enhance architectural features and complement the neighborhood.
    Maintain realistic paint application, proper coverage, and weather-appropriate finishes.
    Keep landscaping, windows, and other non-painted elements exactly the same.`;
  }

  if (!options.useReferenceImage && params.hasAdditionalReferenceImages) {
    prompt += `\nUse the provided reference images as inspiration for the exterior color scheme.`;
  } else if (options.useReferenceImage && params.hasAdditionalReferenceImages && params.referenceImageCount > 1) {
    prompt += `\nAlso consider the other ${params.referenceImageCount - 1} reference images for additional color inspiration.`;
  }

  return prompt;
}

/**
 * Map of modification types to their prompt templates
 * Add new modification types here
 */
export const promptTemplates: Record<string, (params: PromptParams) => string> = {
  'custom': customModificationTemplate,
  'replace_floor': replaceFloorTemplate,
  'change_wall_color': changeWallColorTemplate,
  'update_cabinets': updateCabinetsTemplate,
  'change_countertops': changeCountertopsTemplate,
  'kitchen_remodel': kitchenRemodelTemplate,
  'bathroom_remodel': bathroomRemodelTemplate,
  'interior_painting': interiorPaintingTemplate,
  'lighting_upgrades': lightingUpgradesTemplate,
  'exterior_repainting': exteriorRepaintingTemplate,
};

/**
 * Generate a prompt for a specific modification type
 *
 * @param modificationType The type of modification
 * @param params Parameters for the prompt template
 * @returns Formatted prompt string
 */
export function generatePrompt(
  modificationType: string,
  params: PromptParams
): string {
  // Get the template function for this modification type
  const templateFn = promptTemplates[modificationType];

  // If no template exists, fall back to custom modification
  if (!templateFn) {
    console.warn(`No prompt template found for modification type: ${modificationType}. Using custom template.`);
    return promptTemplates['custom'](params);
  }

  // Generate the prompt using the template
  return templateFn(params);
}

// Note: Each template already includes quality instructions specific to that modification type
