import 'dotenv/config';
import express, { type Request, Response, NextFunction } from "express";
import { registerRoutes } from "./routes";
import { setupVite, serveStatic, log } from "./vite";
// Import Clerk routes
import { setupClerkRoutes } from "./clerk";
import { errorHandler } from "./errorHandler";

const app = express();
app.use(express.json());
app.use(express.urlencoded({ extended: false }));

app.use((req, res, next) => {
  const start = Date.now();
  const path = req.path;
  let capturedJsonResponse: Record<string, any> | undefined = undefined;

  const originalResJson = res.json;
  res.json = function (bodyJson, ...args) {
    capturedJsonResponse = bodyJson;
    return originalResJson.apply(res, [bodyJson, ...args]);
  };

  res.on("finish", () => {
    const duration = Date.now() - start;
    if (path.startsWith("/api")) {
      let logLine = `${req.method} ${path} ${res.statusCode} in ${duration}ms`;
      if (capturedJsonResponse) {
        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;
      }

      if (logLine.length > 80) {
        logLine = logLine.slice(0, 79) + "…";
      }

      log(logLine);
    }
  });

  next();
});

console.log("[BOOT] server/index.ts starting up...");

// Check for required environment variables and log warnings if missing
const requiredEnvVars = [
  "SUPABASE_URL",
  "SUPABASE_KEY",
  "STRIPE_SECRET_KEY",
];

// Check for optional but important environment variables
const optionalEnvVars = [
  "OPENAI_API_KEY",
];
for (const envVar of requiredEnvVars) {
  if (!process.env[envVar]) {
    console.warn(`[WARN] Missing required environment variable: ${envVar}`);
  }
}

for (const envVar of optionalEnvVars) {
  if (!process.env[envVar]) {
    console.warn(`[WARN] Missing optional environment variable: ${envVar}. Some features may not work.`);
  } else {
    console.log(`[INFO] Found ${envVar} environment variable.`);
  }
}

(async () => {
  // Setup Clerk routes
  setupClerkRoutes(app);

  const server = await registerRoutes(app);

  // Use our centralized error handler
  app.use(errorHandler);

  // importantly only setup vite in development and after
  // setting up all the other routes so the catch-all route
  // doesn't interfere with the other routes
  if (app.get("env") === "development") {
    await setupVite(app, server);
  } else {
    serveStatic(app);
  }

  // Check if we're running on Replit
  const isReplit = process.env.REPL_ID !== undefined;

  // Use port 5000 on Replit to match the forwarded port configuration
  // Otherwise use PORT env var or default to 3000
  const port = isReplit ? 5000 : (process.env.PORT || 3000);

  server.listen(port, () => {
    log(`Server is running on http://localhost:${port}`);
    if (isReplit) {
      log(`Running on Replit - external port 80 is forwarded to internal port ${port}`);
    }
  });
})().catch((err) => {
  console.error("[FATAL] Server failed to start:", err);
  process.exit(1);
});
