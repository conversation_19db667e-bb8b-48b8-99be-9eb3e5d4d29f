-- Create contact_submissions table
CREATE TABLE IF NOT EXISTS public.contact_submissions (
  id SERIAL PRIMARY KEY,
  name TEXT NOT NULL,
  email TEXT NOT NULL,
  subject TEXT NOT NULL,
  message TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  status TEXT DEFAULT 'new' -- 'new', 'read', 'replied', etc.
);

-- Add RLS policies
ALTER TABLE public.contact_submissions ENABLE ROW LEVEL SECURITY;

-- Only allow admins to view contact submissions (we'll handle this through server-side auth)
CREATE POLICY "Admin can view contact submissions" ON public.contact_submissions
  FOR SELECT USING (auth.role() = 'service_role');

-- Only allow server to insert contact submissions
CREATE POLICY "Server can insert contact submissions" ON public.contact_submissions
  FOR INSERT WITH CHECK (auth.role() = 'service_role');
