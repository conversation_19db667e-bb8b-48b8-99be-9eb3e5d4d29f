import { OpenAI } from 'openai';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables from .env file
dotenv.config({ path: path.join(__dirname, '..', '.env') });

// Manually read the .env file to find the API key
try {
  const envContent = fs.readFileSync(path.join(__dirname, '..', '.env'), 'utf8');
  const apiKeyMatch = envContent.match(/OPENAI_API_KEY="([^"]+)"/);
  if (apiKeyMatch && apiKeyMatch[1]) {
    process.env.OPENAI_API_KEY = apiKeyMatch[1];
    console.log('Manually loaded API key from .env file');
  }
} catch (error) {
  console.error('Error reading .env file:', error.message);
}

async function testOpenAI() {
  const apiKey = process.env.OPENAI_API_KEY;

  if (!apiKey) {
    console.error('❌ OPENAI_API_KEY is not set in the environment variables.');
    console.log('Please add your OpenAI API key to the .env file:');
    console.log('OPENAI_API_KEY="your-api-key-here"');
    return false;
  }

  console.log(`Found OpenAI API key: ${apiKey.substring(0, 8)}...${apiKey.substring(apiKey.length - 4)}`);

  try {
    // Initialize OpenAI with the API key
    const openai = new OpenAI({ apiKey });

    // Test a simple image generation
    console.log('Testing OpenAI image generation with gpt-image-1...');

    const response = await openai.images.generate({
      model: "gpt-image-1",
      prompt: "A simple living room with hardwood floors and white walls",
      n: 1,
      size: "1024x1024",
    });

    console.log('Response received:', JSON.stringify(response, null, 2));

    if (response.data && response.data.length > 0) {
      console.log('Response data:', response.data[0]);

      // Check if we have a URL or b64_json
      const imageData = response.data[0];

      if (!imageData.url && !imageData.b64_json) {
        console.error('❌ No image URL or base64 data in response');
        return false;
      }

      // Create directory for test images
      const outputDir = path.join(__dirname, '..', 'test-images');
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
      }

      let buffer;

      if (imageData.url) {
        console.log(`✅ Image generation successful! URL: ${imageData.url}`);

        // Download the image from URL
        const fetch = (await import('node-fetch')).default;
        const imageResponse = await fetch(imageData.url);

        if (!imageResponse.ok) {
          throw new Error(`Failed to download image: ${imageResponse.status} ${imageResponse.statusText}`);
        }

        buffer = Buffer.from(await imageResponse.arrayBuffer());
      } else if (imageData.b64_json) {
        console.log(`✅ Image generation successful! Received base64 data (length: ${imageData.b64_json.length})`);

        // Decode base64 data
        buffer = Buffer.from(imageData.b64_json, 'base64');
      }

      const outputPath = path.join(outputDir, `test-image-${Date.now()}.png`);
      fs.writeFileSync(outputPath, buffer);

      console.log(`Image saved to: ${outputPath}`);
      return true;
    } else {
      console.error('❌ No image data in response');
      return false;
    }
  } catch (error) {
    console.error('❌ Error testing OpenAI:', error.message);
    console.log('Full error:', error);
    return false;
  }
}

// Run the test
testOpenAI().then(success => {
  if (success) {
    console.log('✅ OpenAI test completed successfully!');
  } else {
    console.error('❌ OpenAI test failed.');
    process.exit(1);
  }
});
