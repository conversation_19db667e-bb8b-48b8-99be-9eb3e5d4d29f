/**
 * Enhanced logger utility for the server
 *
 * This logger respects the DEBUG_MODE environment variable configuration
 * and provides consistent logging across the application.
 */
import { debugConfig } from './debug-config';

// Define log categories
export type LogCategory = 'api' | 'auth' | 'db' | 'ui' | 'system' | string;

// Get timestamp for logs
const getTimestamp = (): string => {
  return new Date().toISOString();
};

// Format the log message with timestamp and category
const formatLogMessage = (level: string, category: LogCategory, message: string): string => {
  const categoryStr = typeof category === 'string' ? category.toUpperCase() : 'UNKNOWN';
  return `[${getTimestamp()}] [${level}] [${categoryStr}] ${message}`;
};

export const logger = {
  /**
   * Log an informational message
   */
  info: (message: string, category: LogCategory = 'system', meta?: any) => {
    console.info(formatLogMessage('INFO', category, message), meta ? meta : '');
  },

  /**
   * Log a warning message
   */
  warn: (message: string, category: LogCategory = 'system', meta?: any) => {
    console.warn(formatLogMessage('WARN', category, message), meta ? meta : '');
  },

  /**
   * Log an error message
   */
  error: (message: string, category: LogCategory = 'system', meta?: any) => {
    console.error(formatLogMessage('ERROR', category, message), meta ? meta : '');
  },

  /**
   * Log a debug message - only shown when DEBUG_MODE includes the specified category
   */
  debug: (message: string, category: LogCategory = 'system', meta?: any) => {
    if (debugConfig.isEnabled(category)) {
      console.debug(formatLogMessage('DEBUG', category, message), meta ? meta : '');
    }
  },

  /**
   * Log a trace message - most detailed level, only shown when DEBUG_MODE includes the specified category
   */
  trace: (message: string, category: LogCategory = 'system', meta?: any) => {
    if (debugConfig.isEnabled(category)) {
      console.debug(formatLogMessage('TRACE', category, message), meta ? meta : '');
    }
  }
};
