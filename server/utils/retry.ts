import { logger } from './logger';

/**
 * Determines if an error is retryable
 * @param error The error to check
 * @returns True if the error is retryable, false otherwise
 */
export function isRetryableError(error: any): boolean {
  // Network errors are generally retryable
  if (error.name === 'FetchError' || error.name === 'NetworkError') {
    return true;
  }

  // Database connection errors are retryable
  if (error.code === 'ECONNREFUSED' || error.code === 'ETIMEDOUT') {
    return true;
  }

  // Rate limit errors are retryable
  if (error.code === '429' || (error.status && error.status === 429)) {
    return true;
  }

  // Supabase specific connection errors
  if (error.code === 'PGRST301' || error.code === 'PGRST302') {
    return true;
  }

  // Some Stripe errors are retryable
  if (error.type === 'StripeConnectionError' ||
      error.type === 'StripeRateLimitError' ||
      error.type === 'StripeAPIError' ||
      error.type === 'StripeTimeoutError' ||
      error.type === 'StripeInvalidRequestError' && error.message.includes('No such subscription')) {
    return true;
  }

  // Non-retryable errors:
  // - Validation errors (400)
  // - Authentication errors (401, 403)
  // - Not found errors (404)
  // - Conflict errors (409)
  // - Database constraint violations (23505, 23503)

  return false;
}

/**
 * Executes an operation with retry logic for transient errors
 * @param operation Function to execute
 * @param maxRetries Maximum number of retry attempts
 * @param baseDelay Base delay in milliseconds between retries
 * @returns Result of the operation
 */
export async function withRetry<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 100
): Promise<T> {
  let lastError: any;

  for (let attempt = 1; attempt <= maxRetries + 1; attempt++) {
    try {
      return await operation();
    } catch (error: any) {
      lastError = error;

      // Only retry on certain error types
      if (attempt > maxRetries || !isRetryableError(error)) {
        logger.error(`Operation failed permanently after ${attempt} attempt(s)`, {
          error,
          errorCode: error.code,
          errorType: error.type,
          errorName: error.name
        });
        throw error;
      }

      // Exponential backoff with jitter
      const jitter = Math.random() * 0.3 + 0.85; // Random value between 0.85 and 1.15
      const delay = Math.floor(baseDelay * Math.pow(2, attempt - 1) * jitter);

      logger.warn(`Retrying operation after error (attempt ${attempt}/${maxRetries})`, {
        error: error.message,
        errorCode: error.code,
        delay,
        attempt
      });

      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  // This should never happen, but TypeScript requires a return
  throw lastError;
}
