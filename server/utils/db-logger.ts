import { logger } from './logger';

/**
 * Logs database operations with timing information
 * @param operation Name of the database operation
 * @param fn Function to execute
 * @returns Result of the function
 */
export async function logDatabaseOperation<T>(
  operation: string,
  fn: () => Promise<T>
): Promise<T> {
  const start = Date.now();
  try {
    const result = await fn();
    const duration = Date.now() - start;
    
    // Log different levels based on duration
    if (duration > 1000) {
      logger.warn(`DB operation: ${operation} took ${duration}ms (slow)`);
    } else if (duration > 500) {
      logger.info(`DB operation: ${operation} took ${duration}ms (medium)`);
    } else {
      logger.debug(`DB operation: ${operation} completed in ${duration}ms`);
    }
    
    return result;
  } catch (error: any) {
    const duration = Date.now() - start;
    logger.error(`DB operation: ${operation} failed after ${duration}ms`, { 
      error: error.message,
      code: error.code,
      details: error.details
    });
    throw error;
  }
}

/**
 * Creates a database logger for a specific context
 * @param context Context name for the logger
 * @returns Object with logging methods
 */
export function createDbLogger(context: string) {
  return {
    /**
     * Log a database query operation
     * @param operation Operation name
     * @param fn Function to execute
     * @returns Result of the function
     */
    query: async <T>(operation: string, fn: () => Promise<T>): Promise<T> => {
      return logDatabaseOperation(`${context}.${operation}`, fn);
    },
    
    /**
     * Log a database insert operation
     * @param table Table name
     * @param fn Function to execute
     * @returns Result of the function
     */
    insert: async <T>(table: string, fn: () => Promise<T>): Promise<T> => {
      return logDatabaseOperation(`${context}.insert(${table})`, fn);
    },
    
    /**
     * Log a database update operation
     * @param table Table name
     * @param fn Function to execute
     * @returns Result of the function
     */
    update: async <T>(table: string, fn: () => Promise<T>): Promise<T> => {
      return logDatabaseOperation(`${context}.update(${table})`, fn);
    },
    
    /**
     * Log a database delete operation
     * @param table Table name
     * @param fn Function to execute
     * @returns Result of the function
     */
    delete: async <T>(table: string, fn: () => Promise<T>): Promise<T> => {
      return logDatabaseOperation(`${context}.delete(${table})`, fn);
    },
    
    /**
     * Log a database transaction operation
     * @param operation Operation name
     * @param fn Function to execute
     * @returns Result of the function
     */
    transaction: async <T>(operation: string, fn: () => Promise<T>): Promise<T> => {
      return logDatabaseOperation(`${context}.transaction(${operation})`, fn);
    }
  };
}
