import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create a simple error image
const createErrorImage = () => {
  // Create the assets directory if it doesn't exist
  const assetsDir = path.join(__dirname, '..', 'assets');
  if (!fs.existsSync(assetsDir)) {
    fs.mkdirSync(assetsDir, { recursive: true });
  }

  // Create a simple 1024x1024 PNG with error message
  // This is a minimal valid PNG file with error text
  const errorImagePath = path.join(assetsDir, 'error-image.png');
  
  // Create a canvas and draw error message
  const { createCanvas } = require('canvas');
  const canvas = createCanvas(1024, 1024);
  const ctx = canvas.getContext('2d');
  
  // Fill background
  ctx.fillStyle = '#f8f9fa';
  ctx.fillRect(0, 0, 1024, 1024);
  
  // Draw error icon
  ctx.fillStyle = '#dc3545';
  ctx.beginPath();
  ctx.arc(512, 400, 100, 0, Math.PI * 2);
  ctx.fill();
  
  // Draw exclamation mark
  ctx.fillStyle = 'white';
  ctx.font = 'bold 120px Arial';
  ctx.textAlign = 'center';
  ctx.textBaseline = 'middle';
  ctx.fillText('!', 512, 400);
  
  // Draw error message
  ctx.fillStyle = '#343a40';
  ctx.font = 'bold 40px Arial';
  ctx.fillText('Image Generation Failed', 512, 600);
  
  ctx.font = '30px Arial';
  ctx.fillText('Please try again with a different description', 512, 650);
  
  // Save the canvas to a file
  const buffer = canvas.toBuffer('image/png');
  fs.writeFileSync(errorImagePath, buffer);
  
  console.log(`Created error image at: ${errorImagePath}`);
};

createErrorImage();
