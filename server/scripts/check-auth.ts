/**
 * Authentication Status Check Script
 *
 * This script checks the status of Clerk authentication configuration
 * and helps diagnose issues with authentication.
 */

import dotenv from 'dotenv';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import { createRequire } from 'module';

// Setup for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const require = createRequire(import.meta.url);

// Load environment variables
dotenv.config({ path: path.resolve(process.cwd(), '.env') });
dotenv.config({ path: path.resolve(process.cwd(), '.env.local') });

// Configure logging
const logger = {
  info: (message, ...args) => {
    console.info(`[AUTH-CHECK] ${message}`, ...args);
  },
  warn: (message, ...args) => {
    console.warn(`[AUTH-CHECK] ${message}`, ...args);
  },
  error: (message, ...args) => {
    console.error(`[AUTH-CHECK] ${message}`, ...args);
  },
  success: (message, ...args) => {
    console.log(`[AUTH-CHECK] ✅ ${message}`, ...args);
  }
};

// Check Clerk configuration
function checkClerkConfig() {
  logger.info('Checking Clerk configuration...');

  const publishableKey = process.env.VITE_CLERK_PUBLISHABLE_KEY;
  const secretKey = process.env.CLERK_SECRET_KEY;

  if (!publishableKey) {
    logger.error('VITE_CLERK_PUBLISHABLE_KEY is not set');
  } else {
    // Check if it's a placeholder or actual key
    if (publishableKey === '${CLERK_PUBLISHABLE_KEY}') {
      logger.warn('VITE_CLERK_PUBLISHABLE_KEY is a placeholder, not an actual key');
    } else {
      logger.success('VITE_CLERK_PUBLISHABLE_KEY is set');
    }
  }

  if (!secretKey) {
    logger.error('CLERK_SECRET_KEY is not set');
  } else {
    // Check if it's a placeholder or actual key
    if (secretKey === '${CLERK_SECRET_KEY}') {
      logger.warn('CLERK_SECRET_KEY is a placeholder, not an actual key');
    } else {
      logger.success('CLERK_SECRET_KEY is set');
    }
  }

  // Check for Clerk SDK
  try {
    // Using dynamic import for ESM compatibility
    import('@clerk/express')
      .then(() => {
        logger.success('Clerk Express SDK is installed');
      })
      .catch(() => {
        logger.error('Error importing Clerk Express SDK');
      });
  } catch (error) {
    logger.error('Clerk Express SDK is not installed or has issues');
  }
}

// Check environment files
function checkEnvFiles() {
  logger.info('Checking environment files...');

  const envPath = path.resolve(process.cwd(), '.env');
  const envLocalPath = path.resolve(process.cwd(), '.env.local');

  if (fs.existsSync(envPath)) {
    logger.success('.env file exists');

    // Check for duplicate keys
    const envContent = fs.readFileSync(envPath, 'utf8');
    const clerkKeyCount = (envContent.match(/CLERK_/g) || []).length;
    if (clerkKeyCount > 2) {
      logger.warn('Possible duplicate Clerk keys in .env file');
    }
  } else {
    logger.warn('.env file does not exist');
  }

  if (fs.existsSync(envLocalPath)) {
    logger.success('.env.local file exists');

    // Check for duplicate keys
    const envLocalContent = fs.readFileSync(envLocalPath, 'utf8');
    const clerkKeyCount = (envLocalContent.match(/CLERK_/g) || []).length;
    if (clerkKeyCount > 2) {
      logger.warn('Possible duplicate Clerk keys in .env.local file');
    }
  } else {
    logger.warn('.env.local file does not exist');
  }
}

// Check API endpoints
function checkApiEndpoints() {
  logger.info('Checking API endpoints...');

  // Check if the auth endpoints are defined in the server code
  try {
    const serverIndexPath = path.resolve(process.cwd(), 'server/index.ts');
    if (fs.existsSync(serverIndexPath)) {
      const serverContent = fs.readFileSync(serverIndexPath, 'utf8');

      if (serverContent.includes('setupClerkRoutes')) {
        logger.success('Clerk routes are set up in server/index.ts');
      } else {
        logger.warn('Clerk routes may not be properly set up in server/index.ts');
      }
    }
  } catch (error) {
    logger.error('Error checking server files:', error);
  }
}

// Main function
async function main() {
  logger.info('Starting authentication check...');

  checkEnvFiles();
  checkClerkConfig();
  checkApiEndpoints();

  logger.info('Authentication check completed');
}

// Run the script
main().catch(error => {
  logger.error('Error running auth check:', error);
  process.exit(1);
});
