/**
 * Test script for Clerk authentication
 * 
 * This script tests the Clerk authentication by making a request to the
 * authentication endpoint with a test token.
 */

import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import fetch from 'node-fetch';

// Setup for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
dotenv.config({ path: path.resolve(process.cwd(), '.env') });
dotenv.config({ path: path.resolve(process.cwd(), '.env.local') });

// Configure logging
const logger = {
  info: (message, ...args) => {
    console.info(`[AUTH-TEST] ${message}`, ...args);
  },
  warn: (message, ...args) => {
    console.warn(`[AUTH-TEST] ${message}`, ...args);
  },
  error: (message, ...args) => {
    console.error(`[AUTH-TEST] ${message}`, ...args);
  },
  success: (message, ...args) => {
    console.log(`[AUTH-TEST] ✅ ${message}`, ...args);
  }
};

// Test the authentication endpoint
async function testAuthEndpoint() {
  logger.info('Testing authentication endpoint...');
  
  try {
    // Make a request to the authentication status endpoint
    const response = await fetch('http://localhost:5000/api/auth/status');
    
    if (response.ok) {
      const data = await response.json();
      logger.success('Authentication endpoint is working');
      logger.info('Response:', data);
    } else {
      logger.error('Authentication endpoint returned an error:', response.status);
      const errorText = await response.text();
      logger.error('Error details:', errorText);
    }
  } catch (error) {
    logger.error('Error testing authentication endpoint:', error);
    logger.info('Make sure the server is running on port 5000');
  }
}

// Main function
async function main() {
  logger.info('Starting authentication test...');
  
  await testAuthEndpoint();
  
  logger.info('Authentication test completed');
}

// Run the script
main().catch(error => {
  logger.error('Error running auth test:', error);
  process.exit(1);
});
