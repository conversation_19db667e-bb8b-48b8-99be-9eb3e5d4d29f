# Demo Accounts Master Guide - Complete Setup & Maintenance

## 🎯 **Overview**

This is the definitive guide for creating, maintaining, and troubleshooting demo accounts in Renovision Studio. Follow this guide to set up a complete demo environment with 10 functional demo accounts, including one with a professional subscription.

## 🚀 **Quick Start - Complete Setup**

Run these commands in order for a complete demo account setup:

```bash
# 1. Create all 10 demo accounts
npm run create-demo-accounts

# 2. Populate with realistic sample data
npm run populate-demo-data

# 3. Upgrade acquireuser7 to professional plan
npm run upgrade-demo-subscription

# 4. Verify everything is working correctly
npm run test-demo-auth
npm run verify-demo-subscription
npm run test-subscription-fix
```

**Result**: 10 fully functional demo accounts with realistic data, including one professional account.

## 📋 **Demo Account Details**

### **Standard Demo Accounts (Free Tier)**
- `acquireuser8` through `acquireuser16` (9 accounts)
- Free plan with basic limits
- Realistic professional personas
- Sample projects and data

### **Premium Demo Account (Professional Tier)**
- `acquireuser7` - <PERSON>
- Professional plan (10 projects, 5 images each)
- Active subscription for 1 month
- Perfect for showcasing premium features

## 🔐 **Authentication**

### **Universal Login Process**
1. Go to sign-in page
2. Enter username: `acquireuser7` (or any number 7-16)
3. Enter verification code: `424242`
4. ✅ Logged in!

### **Technical Details**
- Uses Clerk's test mode with `+clerk_test` email format
- No real emails sent
- Works only in development environments
- Production-safe implementation

## 🛠️ **Management Commands Reference**

### **Initial Setup**
```bash
npm run create-demo-accounts      # Create all 10 accounts
npm run populate-demo-data        # Add sample projects/data
npm run upgrade-demo-subscription # Upgrade acquireuser7 to professional
```

### **Verification & Testing**
```bash
npm run check-users               # View all accounts and usage stats
npm run test-demo-auth           # Test authentication system
npm run verify-demo-subscription # Check subscription details
npm run test-subscription-fix    # Test subscription system
```

### **Maintenance & Troubleshooting**
```bash
npm run fix-demo-subscription    # Fix subscription status issues
npm run create-demo-accounts     # Recreate accounts if needed
npm run populate-demo-data       # Repopulate sample data
```

## 🔧 **Troubleshooting Guide**

### **Common Issues & Solutions**

#### **"Syncing" Message on Subscription Page**
**Problem**: Subscription page shows "syncing" instead of active status
**Solution**: 
```bash
npm run fix-demo-subscription
```

#### **Demo Accounts Not Found**
**Problem**: Accounts don't exist or can't authenticate
**Solution**:
```bash
npm run create-demo-accounts
npm run test-demo-auth
```

#### **Missing Sample Data**
**Problem**: Accounts exist but have no projects/data
**Solution**:
```bash
npm run populate-demo-data
```

#### **Authentication Fails**
**Problem**: Can't log in with demo accounts
**Solution**:
1. Verify accounts exist: `npm run check-users`
2. Test auth system: `npm run test-demo-auth`
3. Recreate if needed: `npm run create-demo-accounts`

#### **Subscription Issues**
**Problem**: Professional account not working correctly
**Solution**:
```bash
npm run verify-demo-subscription
npm run fix-demo-subscription
npm run test-subscription-fix
```

## 📊 **Verification Checklist**

Use this checklist to verify your demo account setup:

### **✅ Account Creation**
- [ ] 10 demo accounts created (`acquireuser7-16`)
- [ ] All accounts have realistic names and bios
- [ ] All accounts use test email format
- [ ] Authentication works for all accounts

### **✅ Sample Data**
- [ ] Projects created across all accounts
- [ ] Reference categories populated
- [ ] Usage records with realistic data
- [ ] Varied data distribution

### **✅ Professional Account**
- [ ] `acquireuser7` has professional subscription
- [ ] Subscription status shows "active" (not "syncing")
- [ ] Professional limits applied (10 projects, 5 images)
- [ ] Premium features accessible

### **✅ System Integration**
- [ ] Authentication system working
- [ ] Subscription system working
- [ ] No Stripe API errors for demo accounts
- [ ] All management scripts functional

## 🎯 **Best Practices**

### **For Demonstrations**
- Use `acquireuser7` for premium feature demos
- Use other accounts to show free tier limitations
- Demonstrate the upgrade value proposition
- Show realistic project data and workflows

### **For Development**
- Test features across different account types
- Verify subscription-gated functionality
- Test multi-user scenarios
- Use for integration testing

### **For Maintenance**
- Run verification scripts regularly
- Fix issues immediately when detected
- Keep documentation updated
- Monitor for any system changes

## 🔒 **Security & Production Safety**

### **Built-in Safety Features**
- Demo accounts only work in development/test environments
- Test email format prevents real communications
- Fake Stripe IDs clearly identified as demo
- Metadata flags mark accounts as test accounts

### **Production Deployment**
- Demo accounts automatically disabled in production
- No risk of demo data in production environment
- Subscription system handles demo accounts safely
- No security vulnerabilities introduced

## 📁 **Related Documentation**

- `DEMO_ACCOUNTS.md` - Comprehensive demo account documentation
- `DEMO_ACCOUNTS_QUICK_REFERENCE.md` - Quick reference guide
- `DEMO_PROFESSIONAL_UPGRADE_SUMMARY.md` - Professional plan details
- `SUBSCRIPTION_FIX_SUMMARY.md` - Subscription system fix details
- `DEMO_ACCOUNTS_IMPLEMENTATION_SUMMARY.md` - Technical implementation

## 🎉 **Success Criteria**

Your demo account setup is complete when:

1. ✅ All 10 accounts authenticate successfully
2. ✅ `acquireuser7` shows active professional subscription
3. ✅ Sample data populated across all accounts
4. ✅ No "syncing" messages on subscription pages
5. ✅ All management scripts run without errors
6. ✅ Premium features accessible on professional account

## 📞 **Support**

If you encounter issues not covered in this guide:

1. Check the troubleshooting section above
2. Run the verification scripts to identify the problem
3. Review the related documentation files
4. Use the management scripts to fix common issues

---

**This guide ensures you have a complete, functional demo environment ready for professional demonstrations and development work.**
