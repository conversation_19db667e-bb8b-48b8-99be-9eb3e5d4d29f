#!/bin/bash

echo "Fixing Clerk SDK issues..."

# Clean npm cache and remove node_modules
echo "Cleaning npm cache and removing node_modules..."
npm cache clean --force
rm -rf node_modules package-lock.json

# Install specific versions of Clerk packages
echo "Installing specific versions of Clerk packages..."
npm install @clerk/clerk-react@5.30.2 --save
npm install @clerk/express@1.0.0 --save

# Fix imports in source files
echo "Fixing Clerk imports..."
find ./client/src -type f -name "*.ts" -o -name "*.tsx" | while read -r file; do
  if grep -q "@clerk/clerk-sdk-node" "$file"; then
    echo "Fixing imports in $file..."
    sed -i '' 's/@clerk\/clerk-sdk-node/@clerk\/express/g' "$file"
  fi
done

find ./server -type f -name "*.ts" -o -name "*.js" | while read -r file; do
  if grep -q "@clerk/clerk-sdk-node" "$file"; then
    echo "Fixing imports in $file..."
    sed -i '' 's/@clerk\/clerk-sdk-node/@clerk\/express/g' "$file"
  fi
done

# Clean install dependencies
echo "Performing clean install..."
npm install --legacy-peer-deps

echo "Clerk SDK fixes completed!" 