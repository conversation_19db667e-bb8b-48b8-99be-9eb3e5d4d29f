{"name": "rest-express", "version": "1.0.0", "type": "module", "license": "MIT", "scripts": {"preinstall": "node -e \"if(process.env.npm_execpath.indexOf('npm') === -1) throw new Error('Please use npm instead of yarn or pnpm')\"", "postinstall": "echo 'Dependencies installed successfully with legacy peer deps'", "prepare": "command -v nvm && nvm use || echo 'nvm not found, using system Node.js'", "fix-permissions": "sudo chown -R $(whoami) ~/.npm", "clean-cache": "npm cache clean --force", "fix-path-to-regexp": "./fix-path-to-regexp-combined.sh", "fix-clerk": "./fix-clerk-combined.sh", "fix-tailwind": "./fix-tailwind-setup.sh", "fix-jest-deps": "./scripts/fix-jest-dependencies.sh", "setup": "./setup-env.sh", "setup:safe": "./setup-env.sh --safe", "setup:latest": "./setup-env.sh --latest", "setup:dev": "./setup-env.sh --dev", "dev": "NODE_ENV=development tsx server/index.ts", "dev:prod": "NODE_ENV=production tsx server/index.ts", "build": "vite build && esbuild server/index.ts --platform=node --packages=external --bundle --format=esm --outdir=dist", "deploy:replit": "./deploy-replit.sh", "deploy:secure": "./deploy-secure.sh", "start": "NODE_ENV=production node dist/index.js", "check": "tsc", "check:auth": "./scripts/check-auth.sh", "test:auth": "./scripts/test-auth.sh", "test:e2e": "playwright test", "test:api": "jest --config=jest.config.cjs", "test": "bash ./run-tests.sh", "test:openai": "node server/test-openai.js", "test:setup": "node tests/setup-test-fixtures.js", "test:setup-fixtures": "node tests/setup-test-fixtures.js", "pretest": "npm run test:setup-fixtures", "test:subscription:api": "jest --config=jest.subscription.config.cjs", "test:subscription:e2e": "playwright test tests/e2e/subscription-comprehensive.spec.js", "test:subscription": "npm run test:subscription:api && npm run test:subscription:e2e", "test:stripe-webhooks": "jest --config=jest.subscription.config.cjs tests/api/stripe-webhooks.test.js", "test:visual": "playwright test --project=visual-regression", "test:visual:update": "playwright test --project=visual-regression --update-snapshots", "test:subscription:visual": "playwright test tests/e2e/visual-regression/subscription-ui.spec.js --project=visual-regression", "test:coupon": "jest --config=jest.config.cjs tests/api/coupon.test.ts", "test:coupon:e2e": "playwright test tests/e2e/coupon.spec.ts", "test:navigation": "playwright test tests/e2e/navigation.spec.ts", "test:component": "jest --config=jest.component.config.cjs", "test:visual:coupon": "playwright test tests/e2e/visual-regression/coupon-ui.spec.js --project=visual-regression", "test:visual:navigation": "playwright test tests/e2e/visual-regression/navigation.spec.js --project=visual-regression", "create-demo-accounts": "tsx scripts/create-demo-accounts.ts", "check-users": "tsx scripts/check-users.ts", "test-demo-auth": "tsx scripts/test-demo-auth.ts", "populate-demo-data": "tsx scripts/populate-demo-data.ts", "upgrade-demo-subscription": "tsx scripts/upgrade-demo-subscription.ts", "verify-demo-subscription": "tsx scripts/verify-demo-subscription.ts", "fix-demo-subscription": "tsx scripts/fix-demo-subscription.ts", "test-subscription-fix": "tsx scripts/test-subscription-fix.ts", "delete-demo-projects": "tsx scripts/delete-demo-projects.ts"}, "dependencies": {"@clerk/clerk-react": "^5.30.2", "@clerk/express": "^1.0.0", "@hookform/resolvers": "^5.0.1", "@jridgewell/trace-mapping": "^0.3.25", "@radix-ui/react-accordion": "^1.2.8", "@radix-ui/react-alert-dialog": "^1.1.11", "@radix-ui/react-aspect-ratio": "^1.1.4", "@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-collapsible": "^1.1.8", "@radix-ui/react-context-menu": "^2.2.12", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-hover-card": "^1.1.11", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-menubar": "^1.1.12", "@radix-ui/react-navigation-menu": "^1.2.10", "@radix-ui/react-popover": "^1.1.11", "@radix-ui/react-progress": "^1.1.4", "@radix-ui/react-radio-group": "^1.3.4", "@radix-ui/react-scroll-area": "^1.2.6", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slider": "^1.3.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-toast": "^1.2.11", "@radix-ui/react-toggle": "^1.1.6", "@radix-ui/react-toggle-group": "^1.1.7", "@radix-ui/react-tooltip": "^1.2.4", "@replit/vite-plugin-shadcn-theme-json": "^0.0.4", "@stripe/react-stripe-js": "^3.6.0", "@stripe/stripe-js": "^7.2.0", "@supabase/supabase-js": "^2.49.4", "@tanstack/react-query": "^5.74.11", "@types/multer": "^1.4.12", "@types/uuid": "^10.0.0", "axios": "^1.9.0", "browser-image-compression": "^2.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "connect-pg-simple": "^10.0.0", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "express": "^4.18.2", "express-session": "^1.18.1", "form-data": "^4.0.2", "formidable": "^3.5.4", "framer-motion": "^12.9.2", "glob": "^10.4.5", "input-otp": "^1.4.2", "jose": "^6.0.10", "lodash": "^4.17.21", "lru-cache": "^10.4.3", "lucide-react": "^0.503.0", "memoizee": "^0.4.17", "memorystore": "^1.6.7", "multer": "^1.4.5-lts.2", "next-themes": "^0.4.6", "node-fetch": "^3.3.2", "nodemailer": "^6.10.1", "openai": "^4.96.2", "openid-client": "^6.4.2", "passport": "^0.7.0", "passport-apple": "^2.0.2", "passport-facebook": "^3.0.0", "passport-google-oauth20": "^2.0.0", "passport-local": "^1.0.0", "path-to-regexp": "^6.2.1", "react": "^18.3.1", "react-day-picker": "^9.6.7", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-hook-form": "^7.56.1", "react-icons": "^5.5.0", "react-resizable-panels": "^2.1.9", "recharts": "^2.15.3", "stripe": "^18.1.0", "superagent": "^10.2.0", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.2.8", "uuid": "^11.1.0", "vaul": "^1.1.2", "wouter": "^3.7.0", "ws": "^8.18.1", "zod": "^3.24.3", "zod-validation-error": "^3.4.0"}, "devDependencies": {"@babel/preset-env": "^7.27.2", "@babel/preset-typescript": "^7.27.1", "@playwright/test": "^1.52.0", "@replit/vite-plugin-cartographer": "^0.1.2", "@replit/vite-plugin-runtime-error-modal": "^0.0.3", "@tailwindcss/typography": "^0.5.16", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/express": "5.0.1", "@types/express-session": "^1.18.1", "@types/jest": "^29.5.14", "@types/node": "22.15.3", "@types/nodemailer": "^6.4.17", "@types/passport": "^1.0.17", "@types/passport-local": "^1.0.38", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@types/ws": "^8.18.1", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "babel-jest": "^29.7.0", "esbuild": "^0.25.3", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jsdom": "^26.1.0", "playwright": "^1.52.0", "postcss": "^8.5.3", "supertest": "^7.1.0", "tailwindcss": "^3.4.17", "ts-jest": "^29.3.4", "tsx": "^4.19.4", "typescript": "5.8.3", "vite": "^6.3.4"}, "optionalDependencies": {"bufferutil": "^4.0.9", "canvas": "^3.1.0"}}