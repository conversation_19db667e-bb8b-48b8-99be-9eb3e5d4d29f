#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const SUPABASE_URL = process.env.SUPABASE_URL;
const SUPABASE_DB_KEY = process.env.SUPABASE_DB_KEY;

console.log('🧪 Final test after manual database fix...');

if (!SUPABASE_URL || !SUPABASE_DB_KEY) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(SUPABASE_URL, SUPABASE_DB_KEY, {
  auth: {
    persistSession: false,
    autoRefreshToken: false,
    detectSessionInUrl: false
  }
});

async function testCompleteWorkflow() {
  try {
    console.log('📋 Step 1: Testing complete draft creation workflow...');
    
    const testUserId = 'user_2wGQABv0jGkkg4Sr5V9BhdKEiPA'; // Real Clerk user ID
    
    // Ensure user exists
    const { data: user, error: userError } = await supabase
      .from('users')
      .upsert({
        id: testUserId,
        username: 'Test User',
        email: '<EMAIL>',
        first_name: 'Test',
        last_name: 'User'
      })
      .select()
      .single();

    if (userError) {
      console.error('❌ User creation failed:', userError);
      return false;
    }
    console.log('✅ User ready');

    // Test creating a complete draft project with all fields
    const draftProjectData = {
      user_id: testUserId,
      title: 'Complete Draft Test',
      description: 'Testing all draft functionality',
      status: 'draft',
      modification_description: 'Replace the kitchen floor with hardwood',
      before_images: [
        { url: 'before1.jpg', name: 'Kitchen Before' },
        { url: 'before2.jpg', name: 'Kitchen Before 2' }
      ],
      reference_images: [
        { url: 'ref1.jpg', name: 'Hardwood Reference' },
        { url: 'ref2.jpg', name: 'Color Reference' }
      ],
      step: 2,
      modification_type: 'replace_floor',
      modification_options: {
        floorType: 'hardwood',
        color: 'oak',
        finish: 'matte'
      }
    };

    console.log('Creating complete draft project...');
    const { data: draftProject, error: draftError } = await supabase
      .from('projects')
      .insert(draftProjectData)
      .select()
      .single();

    if (draftError) {
      console.error('❌ Draft project creation failed:', draftError);
      return false;
    }

    console.log('✅ Complete draft project created successfully:', {
      id: draftProject.id,
      title: draftProject.title,
      status: draftProject.status,
      modification_type: draftProject.modification_type,
      step: draftProject.step
    });

    // Test updating the draft
    console.log('📋 Step 2: Testing draft updates...');
    
    const { data: updatedDraft, error: updateError } = await supabase
      .from('projects')
      .update({
        title: 'Updated Complete Draft Test',
        step: 3,
        modification_options: {
          ...draftProject.modification_options,
          updated: true,
          timestamp: new Date().toISOString()
        }
      })
      .eq('id', draftProject.id)
      .select()
      .single();

    if (updateError) {
      console.error('❌ Draft update failed:', updateError);
      return false;
    }

    console.log('✅ Draft updated successfully');

    // Test retrieving drafts for user
    console.log('📋 Step 3: Testing draft retrieval...');
    
    const { data: userDrafts, error: retrieveError } = await supabase
      .from('projects')
      .select('*')
      .eq('user_id', testUserId)
      .eq('status', 'draft');

    if (retrieveError) {
      console.error('❌ Draft retrieval failed:', retrieveError);
      return false;
    }

    console.log(`✅ Retrieved ${userDrafts.length} drafts for user`);

    // Test converting draft to processing
    console.log('📋 Step 4: Testing draft to processing conversion...');
    
    const { data: processingProject, error: processError } = await supabase
      .from('projects')
      .update({ status: 'processing' })
      .eq('id', draftProject.id)
      .select()
      .single();

    if (processError) {
      console.error('❌ Draft to processing conversion failed:', processError);
      return false;
    }

    console.log('✅ Draft converted to processing status');

    // Clean up
    console.log('📋 Step 5: Cleaning up test data...');
    
    await supabase.from('projects').delete().eq('id', draftProject.id);
    console.log('✅ Test data cleaned up');

    return true;

  } catch (error) {
    console.error('❌ Unexpected error:', error);
    return false;
  }
}

async function testAPIEndpoints() {
  try {
    console.log('📋 Step 6: Testing API endpoints...');
    
    // Test health endpoint
    const healthResponse = await fetch('http://localhost:3000/api/health');
    if (!healthResponse.ok) {
      console.log('⚠️  API server not responding');
      return false;
    }
    
    const healthData = await healthResponse.json();
    console.log('Health status:', healthData.status);
    console.log('Database status:', healthData.services.database);
    
    if (healthData.services.database !== 'connected') {
      console.log('⚠️  Database not connected via API');
      return false;
    }

    console.log('✅ API endpoints are healthy');
    return true;

  } catch (error) {
    console.log('⚠️  API server not available (this is OK if server is not running)');
    return true; // Don't fail the test if server is not running
  }
}

async function main() {
  console.log('🚀 Starting final comprehensive test...');
  
  const dbTest = await testCompleteWorkflow();
  const apiTest = await testAPIEndpoints();
  
  console.log('\n📊 Test Results:');
  console.log(`Database functionality: ${dbTest ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`API endpoints: ${apiTest ? '✅ PASS' : '⚠️  SKIP'}`);
  
  if (dbTest) {
    console.log('\n🎉 SUCCESS! All database functionality is working correctly!');
    console.log('✅ Clerk user IDs are supported');
    console.log('✅ Draft projects can be created with all fields');
    console.log('✅ Draft projects can be updated');
    console.log('✅ Draft projects can be retrieved');
    console.log('✅ Draft projects can be converted to processing');
    console.log('\n🚀 Your application should now work without the "Failed to create project" error!');
  } else {
    console.log('\n❌ Database functionality test failed');
    console.log('📋 Please ensure you have run the manual database fix from MANUAL_DATABASE_FIX.md');
  }
  
  process.exit(dbTest ? 0 : 1);
}

main();
