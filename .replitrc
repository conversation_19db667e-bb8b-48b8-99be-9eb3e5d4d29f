# Replit configuration for Renovision Studio
# Handles canvas dependency conflicts during deployment

[deployment]
run = "npm start"
build = "./deploy-replit.sh"
deploymentTarget = "cloudrun"

[packager]
language = "nodejs"

[packager.features]
packageSearch = true
guessImports = true

[languages.javascript]
pattern = "**/{*.js,*.jsx,*.ts,*.tsx}"

[languages.javascript.languageServer]
start = "typescript-language-server --stdio"

[env]
NODE_ENV = "production"
