#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const SUPABASE_URL = process.env.SUPABASE_URL;
const SUPABASE_DB_KEY = process.env.SUPABASE_DB_KEY;

console.log('🔧 Applying schema fix via direct database connection...');

if (!SUPABASE_URL || !SUPABASE_DB_KEY) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

// Extract project reference from URL
const projectRef = SUPABASE_URL.match(/https:\/\/([^.]+)\.supabase\.co/)?.[1];
if (!projectRef) {
  console.error('❌ Could not extract project reference from URL');
  process.exit(1);
}

console.log(`Project reference: ${projectRef}`);

const supabase = createClient(SUPABASE_URL, SUPABASE_DB_KEY, {
  auth: {
    persistSession: false,
    autoRefreshToken: false,
    detectSessionInUrl: false
  }
});

async function applySchemaFix() {
  try {
    console.log('📋 Attempting to apply schema fix using SQL execution...');
    
    // Try to execute the SQL using the database URL directly
    const sqlCommands = [
      "ALTER TABLE public.projects ADD COLUMN IF NOT EXISTS modification_description TEXT;",
      "ALTER TABLE public.projects ADD COLUMN IF NOT EXISTS before_images JSONB;",
      "ALTER TABLE public.projects ADD COLUMN IF NOT EXISTS reference_images JSONB;",
      "ALTER TABLE public.projects ADD COLUMN IF NOT EXISTS step INTEGER;",
      "ALTER TABLE public.projects ADD COLUMN IF NOT EXISTS modification_type TEXT;",
      "ALTER TABLE public.projects ADD COLUMN IF NOT EXISTS modification_options JSONB;",
      "ALTER TABLE public.projects ADD COLUMN IF NOT EXISTS updated_at TIMESTAMPTZ DEFAULT NOW();"
    ];

    // Try using the REST API to execute SQL
    for (const sql of sqlCommands) {
      console.log(`Executing: ${sql}`);
      
      try {
        // Method 1: Try using fetch to the SQL endpoint
        const response = await fetch(`${SUPABASE_URL}/rest/v1/rpc/exec_sql`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${SUPABASE_DB_KEY}`,
            'apikey': SUPABASE_DB_KEY
          },
          body: JSON.stringify({ sql })
        });

        if (response.ok) {
          console.log(`✅ Executed: ${sql}`);
        } else {
          console.log(`⚠️  Response: ${response.status} - ${await response.text()}`);
        }
      } catch (err) {
        console.log(`⚠️  Failed to execute: ${err.message}`);
      }
    }

    // Try to refresh the schema cache
    console.log('🔄 Attempting to refresh schema cache...');
    try {
      const refreshResponse = await fetch(`${SUPABASE_URL}/rest/v1/rpc/notify_schema_reload`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${SUPABASE_DB_KEY}`,
          'apikey': SUPABASE_DB_KEY
        },
        body: JSON.stringify({})
      });

      if (refreshResponse.ok) {
        console.log('✅ Schema cache refresh attempted');
      } else {
        console.log('⚠️  Schema cache refresh failed');
      }
    } catch (err) {
      console.log('⚠️  Could not refresh schema cache');
    }

    // Wait a moment for changes to propagate
    console.log('⏳ Waiting for schema changes to propagate...');
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Test if the changes worked
    console.log('🧪 Testing if schema changes were applied...');
    
    const testUserId = 'user_schema_test_123';
    
    // Create test user
    const { data: testUser, error: userError } = await supabase
      .from('users')
      .upsert({
        id: testUserId,
        username: 'Schema Test User',
        email: '<EMAIL>'
      })
      .select()
      .single();

    if (userError) {
      console.error('❌ Test user creation failed:', userError);
      return false;
    }

    // Test creating project with draft fields
    const { data: testProject, error: projectError } = await supabase
      .from('projects')
      .insert({
        user_id: testUserId,
        title: 'Schema Test Project',
        description: 'Testing schema changes',
        status: 'draft',
        modification_description: 'Test modification',
        before_images: [{ url: 'test.jpg' }],
        reference_images: [{ url: 'ref.jpg' }],
        step: 1,
        modification_type: 'custom',
        modification_options: { test: true }
      })
      .select()
      .single();

    // Clean up test data
    if (testProject) {
      await supabase.from('projects').delete().eq('id', testProject.id);
    }
    await supabase.from('users').delete().eq('id', testUserId);

    if (projectError) {
      console.error('❌ Schema changes not applied:', projectError.message);
      return false;
    }

    console.log('✅ Schema changes successfully applied!');
    return true;

  } catch (error) {
    console.error('❌ Error applying schema fix:', error);
    return false;
  }
}

async function runFinalTest() {
  try {
    console.log('🧪 Running final comprehensive test...');
    
    const realUserId = 'user_2wGQABv0jGkkg4Sr5V9BhdKEiPA';
    
    // Ensure user exists
    const { data: user, error: userError } = await supabase
      .from('users')
      .upsert({
        id: realUserId,
        username: 'Final Test User',
        email: '<EMAIL>',
        first_name: 'Final',
        last_name: 'Test'
      })
      .select()
      .single();

    if (userError) {
      console.error('❌ User setup failed:', userError);
      return false;
    }

    // Test complete draft creation workflow
    const draftData = {
      user_id: realUserId,
      title: 'Final Test Draft',
      description: 'Complete draft functionality test',
      status: 'draft',
      modification_description: 'Replace kitchen floor with hardwood',
      before_images: [
        { url: 'before1.jpg', name: 'Kitchen Before' },
        { url: 'before2.jpg', name: 'Kitchen Before 2' }
      ],
      reference_images: [
        { url: 'ref1.jpg', name: 'Hardwood Reference' },
        { url: 'ref2.jpg', name: 'Color Reference' }
      ],
      step: 2,
      modification_type: 'replace_floor',
      modification_options: {
        floorType: 'hardwood',
        color: 'oak',
        finish: 'matte',
        timestamp: new Date().toISOString()
      }
    };

    console.log('Creating complete draft project...');
    const { data: draft, error: draftError } = await supabase
      .from('projects')
      .insert(draftData)
      .select()
      .single();

    if (draftError) {
      console.error('❌ Draft creation failed:', draftError);
      return false;
    }

    console.log('✅ Draft created successfully:', {
      id: draft.id,
      title: draft.title,
      modification_type: draft.modification_type,
      step: draft.step,
      before_images_count: draft.before_images?.length || 0,
      reference_images_count: draft.reference_images?.length || 0
    });

    // Test draft update
    const { data: updatedDraft, error: updateError } = await supabase
      .from('projects')
      .update({
        step: 3,
        modification_description: 'Updated: Replace kitchen floor with premium hardwood',
        modification_options: {
          ...draft.modification_options,
          updated: true,
          updateTimestamp: new Date().toISOString()
        }
      })
      .eq('id', draft.id)
      .select()
      .single();

    if (updateError) {
      console.error('❌ Draft update failed:', updateError);
      return false;
    }

    console.log('✅ Draft updated successfully');

    // Test status conversion
    const { data: processingProject, error: statusError } = await supabase
      .from('projects')
      .update({ status: 'processing' })
      .eq('id', draft.id)
      .select()
      .single();

    if (statusError) {
      console.error('❌ Status change failed:', statusError);
      return false;
    }

    console.log('✅ Status changed to processing');

    // Test retrieval
    const { data: userProjects, error: retrieveError } = await supabase
      .from('projects')
      .select('*')
      .eq('user_id', realUserId);

    if (retrieveError) {
      console.error('❌ Project retrieval failed:', retrieveError);
      return false;
    }

    console.log(`✅ Retrieved ${userProjects.length} projects for user`);

    // Clean up
    await supabase.from('projects').delete().eq('id', draft.id);
    console.log('✅ Test data cleaned up');

    return true;

  } catch (error) {
    console.error('❌ Final test failed:', error);
    return false;
  }
}

async function main() {
  console.log('🚀 Starting complete schema fix and test...');
  
  const schemaFixed = await applySchemaFix();
  
  if (schemaFixed) {
    console.log('🎉 Schema fix applied successfully!');
    
    const testPassed = await runFinalTest();
    
    if (testPassed) {
      console.log('\n🎉 COMPLETE SUCCESS!');
      console.log('✅ Database schema is fully compatible with Clerk');
      console.log('✅ All draft fields are working correctly');
      console.log('✅ Draft creation, update, and status changes work');
      console.log('✅ Project retrieval works');
      console.log('\n🚀 Your application should now work perfectly!');
      console.log('🔄 Please restart your server with: npm run dev');
    } else {
      console.log('\n⚠️  Final test failed - manual intervention may be needed');
    }
    
    process.exit(testPassed ? 0 : 1);
  } else {
    console.log('\n📋 Schema fix could not be applied automatically');
    console.log('Please run the SQL commands manually in Supabase dashboard');
    process.exit(1);
  }
}

main();
