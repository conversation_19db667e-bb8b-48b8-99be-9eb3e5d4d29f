#!/bin/bash

# Run API tests for Renovision.Studio

echo "Running Renovision.Studio API Tests"
echo "============================"
echo

# Check for required environment variables
if [ -z "$CLERK_SECRET_KEY" ]; then
  echo "⚠️  Warning: CLERK_SECRET_KEY is not set. Authentication tests may fail."
fi

if [ -z "$SUPABASE_URL" ] || [ -z "$SUPABASE_DB_KEY" ]; then
  echo "⚠️  Warning: SUPABASE_URL or SUPABASE_DB_KEY is not set. Database tests may fail."
fi

if [ -z "$OPENAI_API_KEY" ]; then
  echo "⚠️  Warning: OPENAI_API_KEY is not set. Image generation tests may fail."
fi

echo
echo "Running API Tests"
echo "-------------------"

# Run the specific API test file
npx jest $1

API_RESULT=$?

echo
echo "Test Results Summary"
echo "===================="
echo "API Tests: $([ $API_RESULT -eq 0 ] && echo '✅ PASSED' || echo '❌ FAILED')"
echo

# Return overall success/failure
if [ $API_RESULT -eq 0 ]; then
  echo "✅ All tests passed!"
  exit 0
else
  echo "❌ Some tests failed. See logs above for details."
  exit 1
fi
