#!/bin/bash

# Run all tests for Renovision.Studio

echo "Running Renovision.Studio Test Suite"
echo "============================"
echo

# Check for required environment variables
if [ -z "$CLERK_SECRET_KEY" ]; then
  echo "⚠️  Warning: CLERK_SECRET_KEY is not set. Authentication tests may fail."
fi

if [ -z "$SUPABASE_URL" ] || [ -z "$SUPABASE_DB_KEY" ]; then
  echo "⚠️  Warning: SUPABASE_URL or SUPABASE_DB_KEY is not set. Database tests may fail."
fi

if [ -z "$OPENAI_API_KEY" ]; then
  echo "⚠️  Warning: OPENAI_API_KEY is not set. Image generation tests may fail."
fi

echo
echo "1. Running API Tests"
echo "-------------------"
npm run test:api

API_RESULT=$?

echo
echo "2. Running Subscription API Tests"
echo "--------------------------------"
npm run test:subscription:api
SUBSCRIPTION_API_RESULT=$?

echo
echo "3. Running Stripe Webhook Tests"
echo "------------------------------"
npm run test:stripe-webhooks
WEBHOOK_RESULT=$?

echo
echo "4. Running E2E Tests"
echo "-------------------"
npm run test:e2e

E2E_RESULT=$?

echo
echo "5. Running Subscription E2E Tests"
echo "--------------------------------"
npm run test:subscription:e2e

SUBSCRIPTION_E2E_RESULT=$?

echo
echo "6. Running Visual Regression Tests"
echo "--------------------------------"
npm run test:visual

VISUAL_REGRESSION_RESULT=$?

echo
echo "Test Results Summary"
echo "===================="
echo "API Tests: $([ $API_RESULT -eq 0 ] && echo '✅ PASSED' || echo '❌ FAILED')"
echo "Subscription API Tests: $([ $SUBSCRIPTION_API_RESULT -eq 0 ] && echo '✅ PASSED' || echo '❌ FAILED')"
echo "Stripe Webhook Tests: $([ $WEBHOOK_RESULT -eq 0 ] && echo '✅ PASSED' || echo '❌ FAILED')"
echo "E2E Tests: $([ $E2E_RESULT -eq 0 ] && echo '✅ PASSED' || echo '❌ FAILED')"
echo "Subscription E2E Tests: $([ $SUBSCRIPTION_E2E_RESULT -eq 0 ] && echo '✅ PASSED' || echo '❌ FAILED')"
echo "Visual Regression Tests: $([ $VISUAL_REGRESSION_RESULT -eq 0 ] && echo '✅ PASSED' || echo '❌ FAILED')"
echo

# Return overall success/failure
if [ $API_RESULT -eq 0 ] && [ $E2E_RESULT -eq 0 ] && [ $SUBSCRIPTION_API_RESULT -eq 0 ] && [ $WEBHOOK_RESULT -eq 0 ] && [ $SUBSCRIPTION_E2E_RESULT -eq 0 ] && [ $VISUAL_REGRESSION_RESULT -eq 0 ]; then
  echo "✅ All tests passed!"
  exit 0
else
  echo "❌ Some tests failed. See logs above for details."
  exit 1
fi
