/** @type {import('ts-jest').JestConfigWithTsJest} */
module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  transform: {
    '^.+\\.tsx?$': [
      'ts-jest',
      {
        tsconfig: 'tsconfig.test.json',
      },
    ],
  },
  moduleNameMapper: {
    '@/(.*)': '<rootDir>/client/src/$1',
    '@shared/(.*)': '<rootDir>/shared/$1',
  },
  testMatch: ['**/tests/api/subscription.test.js', '**/tests/api/simple-subscription.test.js', '**/tests/api/stripe-webhooks.test.js'],
  setupFilesAfterEnv: ['<rootDir>/tests/setup-subscription.js'],
  verbose: true,
};
