# Manual Database Schema Fix for Clerk User IDs

## Problem
Your database schema is using UUID type for user IDs, but Clerk authentication uses string-based user IDs like `user_2wGQABv0jGkkg4Sr5V9BhdKEiPA`. This causes the error:

```
Error creating draft: Error: Failed to create project
```

## Solution
You need to manually update your Supabase database schema to use TEXT instead of UUID for user ID fields.

## Steps to Fix

### 1. Open Supabase Dashboard
1. Go to your Supabase project dashboard
2. Navigate to the **SQL Editor** tab

### 2. Execute the Following SQL (Part 1: User ID Fix)
Copy and paste this SQL into the SQL Editor and run it:

```sql
-- Step 1: Drop foreign key constraints temporarily
ALTER TABLE projects DROP CONSTRAINT IF EXISTS projects_user_id_fkey;
ALTER TABLE reference_categories DROP CONSTRAINT IF EXISTS reference_categories_user_id_fkey;
ALTER TABLE reference_items DROP CONSTRAINT IF EXISTS reference_items_user_id_fkey;
ALTER TABLE renovation_presets DROP CONSTRAINT IF EXISTS renovation_presets_created_by_fkey;
ALTER TABLE subscriptions DROP CONSTRAINT IF EXISTS subscriptions_user_id_fkey;
ALTER TABLE usage DROP CONSTRAINT IF EXISTS usage_user_id_fkey;

-- Step 2: Update column types from UUID to TEXT
ALTER TABLE users ALTER COLUMN id TYPE text;
ALTER TABLE projects ALTER COLUMN user_id TYPE text;
ALTER TABLE reference_categories ALTER COLUMN user_id TYPE text;
ALTER TABLE reference_items ALTER COLUMN user_id TYPE text;
ALTER TABLE renovation_presets ALTER COLUMN created_by TYPE text;
ALTER TABLE subscriptions ALTER COLUMN user_id TYPE text;
ALTER TABLE usage ALTER COLUMN user_id TYPE text;

-- Step 3: Recreate foreign key constraints
ALTER TABLE projects ADD CONSTRAINT projects_user_id_fkey
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

ALTER TABLE reference_categories ADD CONSTRAINT reference_categories_user_id_fkey
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

ALTER TABLE reference_items ADD CONSTRAINT reference_items_user_id_fkey
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

ALTER TABLE renovation_presets ADD CONSTRAINT renovation_presets_created_by_fkey
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL;

ALTER TABLE subscriptions ADD CONSTRAINT subscriptions_user_id_fkey
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

ALTER TABLE usage ADD CONSTRAINT usage_user_id_fkey
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;
```

### 3. Execute the Following SQL (Part 2: Draft Fields)
Run this second SQL block to add the missing draft-related columns:

```sql
-- Add fields from drafts table to projects table
ALTER TABLE public.projects
ADD COLUMN IF NOT EXISTS modification_description TEXT,
ADD COLUMN IF NOT EXISTS before_images JSONB,
ADD COLUMN IF NOT EXISTS reference_images JSONB,
ADD COLUMN IF NOT EXISTS step INTEGER,
ADD COLUMN IF NOT EXISTS modification_type TEXT,
ADD COLUMN IF NOT EXISTS modification_options JSONB,
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMPTZ DEFAULT NOW();
```

### 4. Refresh the Schema Cache
After running both SQL blocks, refresh the PostgREST schema cache by running:

```sql
-- Refresh the PostgREST schema cache
NOTIFY pgrst, 'reload schema';
```

### 5. Verify the Fix
After running all SQL blocks, you can verify it worked by running:

```sql
-- Check user_id type
SELECT column_name, data_type
FROM information_schema.columns
WHERE table_name = 'projects' AND column_name = 'user_id';

-- Check that all draft fields exist
SELECT column_name, data_type
FROM information_schema.columns
WHERE table_name = 'projects'
AND column_name IN ('modification_description', 'before_images', 'reference_images', 'step', 'modification_type', 'modification_options');
```

This should return:
- `user_id` with data_type `text`
- All 6 draft-related columns

### 6. Restart Your Server
After applying the database changes:
1. Stop your development server (Ctrl+C)
2. Restart it with `npm run dev`
3. Try creating a draft project again

## What This Fixes
- ✅ Allows Clerk user IDs (strings) to be stored in the database
- ✅ Fixes the "Failed to create project" error
- ✅ Maintains all foreign key relationships
- ✅ Preserves existing data (if any)

## Verification
Once fixed, you should be able to:
- Create new draft projects
- Save projects with Clerk authentication
- Use all project-related features without database errors

## If You Encounter Issues
1. Check the Supabase logs in the dashboard
2. Ensure all SQL commands executed successfully
3. Verify that your `.env` file has the correct Supabase credentials
4. Contact support if you need help with the database migration
