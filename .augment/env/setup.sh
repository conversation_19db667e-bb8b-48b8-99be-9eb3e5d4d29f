#!/bin/bash

# Renovision Studio Development Environment Setup
echo "🚀 Setting up Renovision Studio development environment..."

# Update system packages
echo "📦 Updating system packages..."
sudo apt-get update -y

# Install system dependencies for canvas and other native modules
echo "🎨 Installing system dependencies for canvas..."
sudo apt-get install -y \
    build-essential \
    libcairo2-dev \
    libpango1.0-dev \
    libjpeg-dev \
    libgif-dev \
    librsvg2-dev \
    libpixman-1-dev \
    pkg-config \
    python3 \
    make \
    g++

# Install nvm if not already installed
if [ ! -d "$HOME/.nvm" ]; then
    echo "📥 Installing nvm..."
    curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.40.0/install.sh | bash
    
    # Add nvm to PATH for current session
    export NVM_DIR="$HOME/.nvm"
    [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
    [ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"
    
    # Add nvm to user's profile for future sessions
    echo 'export NVM_DIR="$HOME/.nvm"' >> $HOME/.profile
    echo '[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"' >> $HOME/.profile
    echo '[ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"' >> $HOME/.profile
else
    echo "✅ nvm is already installed"
    export NVM_DIR="$HOME/.nvm"
    [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
fi

# Install and use Node.js version from .nvmrc
echo "🟢 Installing Node.js..."
if [ -f ".nvmrc" ]; then
    NODE_VERSION=$(cat .nvmrc)
    echo "Installing Node.js v$NODE_VERSION from .nvmrc..."
    nvm install $NODE_VERSION
    nvm use $NODE_VERSION
else
    echo "Installing Node.js v18 LTS..."
    nvm install 18
    nvm use 18
fi

# Add node and npm to PATH in profile
echo "🔧 Adding Node.js to PATH..."
NODE_PATH=$(which node)
NPM_PATH=$(which npm)
if [ -n "$NODE_PATH" ]; then
    NODE_DIR=$(dirname "$NODE_PATH")
    echo "export PATH=\"$NODE_DIR:\$PATH\"" >> $HOME/.profile
fi

# Fix npm cache permissions
echo "🔐 Fixing npm permissions..."
if [ -d "$HOME/.npm" ]; then
    sudo chown -R $(whoami) ~/.npm
fi

# Clean npm cache
echo "🧹 Cleaning npm cache..."
npm cache clean --force

# Remove existing node_modules and package-lock.json for clean install
echo "🗑️ Removing existing dependencies..."
rm -rf node_modules package-lock.json

# Install dependencies with legacy peer deps (required for canvas compatibility)
echo "📦 Installing project dependencies..."
npm install --legacy-peer-deps

# Run fix scripts if they exist
echo "🔧 Running fix scripts..."
if [ -f "./fix-path-to-regexp-combined.sh" ]; then
    chmod +x ./fix-path-to-regexp-combined.sh
    ./fix-path-to-regexp-combined.sh
fi

if [ -f "./fix-clerk-combined.sh" ]; then
    chmod +x ./fix-clerk-combined.sh
    ./fix-clerk-combined.sh
fi

if [ -f "./fix-tailwind-setup.sh" ]; then
    chmod +x ./fix-tailwind-setup.sh
    ./fix-tailwind-setup.sh
fi

# Install Playwright browsers
echo "🎭 Installing Playwright browsers..."
npx playwright install --with-deps

# Set up test fixtures
echo "🧪 Setting up test fixtures..."
if [ -f "tests/setup-test-fixtures.js" ]; then
    node tests/setup-test-fixtures.js
fi

# Build the project to ensure everything works
echo "🏗️ Building the project..."
npm run build

echo "✅ Setup complete!"
echo "Node.js version: $(node -v)"
echo "npm version: $(npm -v)"
echo ""
echo "🧪 Ready to run tests!"