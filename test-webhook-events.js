// Simple test script to verify the saveProcessedWebhookEvent method
import { storage } from './server/storage.ts';

async function testSaveProcessedWebhookEvent() {
  try {
    console.log('Testing saveProcessedWebhookEvent method...');

    // Call the method
    await storage.saveProcessedWebhookEvent('test_event_id', 'test_event_type');

    console.log('Successfully saved processed webhook event!');
    console.log('Test passed!');
  } catch (error) {
    console.error('Error testing saveProcessedWebhookEvent:', error);
  }
}

testSaveProcessedWebhookEvent();
