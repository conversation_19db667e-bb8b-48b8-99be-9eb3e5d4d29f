# NPM configuration for Renovision Studio
# Resolves canvas@3.1.0 vs jest-environment-jsdom dependency conflicts
# Handles Rollup native binary issues in Replit deployment (npm CLI bug #4828)
# Forces installation of platform-specific optional dependencies

legacy-peer-deps=true
fund=false
audit=false
prefer-offline=false
cache-max=0
# CRITICAL: Set to true to fix Rollup native binary issues
# This ensures @rollup/rollup-linux-x64-gnu gets installed on Linux platforms
optional=true
target_platform=linux
target_arch=x64
# Force npm to include optional dependencies in package-lock.json
include=optional
