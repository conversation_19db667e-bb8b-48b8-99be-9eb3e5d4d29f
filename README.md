# Renovision.Studio

Renovision.Studio is an AI-powered renovation visualization platform that helps users visualize home renovation projects before they start.

## Features

- AI-powered renovation visualization
- Reference library for inspiration
- Project gallery
- User authentication with Clerk
- Database storage with Supabase
- Image generation with OpenAI

## Getting Started

### Prerequisites

- Node.js (v22.14.0 recommended, managed via nvm)
- npm (v10.9.2 or higher)
- Supabase account
- Clerk account
- OpenAI API key

### Installation

1. Clone the repository
```bash
git clone https://github.com/yourusername/Renovision.Studio.git
cd Renovision.Studio
```

2. Choose your setup mode:

```bash
# Default (safe) setup with compatible versions
npm run setup

# Or choose a specific mode:
npm run setup:safe     # Safe setup with compatible versions
npm run setup:latest   # Setup with latest versions
npm run setup:dev      # Development setup with specific versions
```

The setup script will:
- Install nvm if not already installed
- Install and configure the appropriate Node.js version
- Fix npm cache permissions
- Install dependencies
- Run necessary fixes for:
  - path-to-regexp compatibility
  - Clerk SDK setup
  - Tailwind CSS configuration

Individual fix scripts are also available if needed:
```bash
# Fix npm permissions
npm run fix-permissions

# Clean npm cache
npm run clean-cache

# Fix path-to-regexp issues
npm run fix-path-to-regexp

# Fix Clerk SDK issues
npm run fix-clerk

# Fix Tailwind CSS setup
npm run fix-tailwind
```

3. Set up demo accounts for testing and demonstrations:

```bash
# Complete demo account setup
npm run create-demo-accounts      # Create 10 demo accounts
npm run populate-demo-data        # Add sample projects/data
npm run upgrade-demo-subscription # Upgrade acquireuser7 to professional
npm run test-demo-auth           # Verify authentication works
```

**Quick Demo Login:**
- Username: `acquireuser7` (professional) or `acquireuser8-16` (free)
- Verification Code: `424242`

📖 **See [DEMO_ACCOUNTS_MASTER_GUIDE.md](DEMO_ACCOUNTS_MASTER_GUIDE.md) for complete demo account documentation.**

4. Create a `.env` file in the root directory with the following variables:
```