# Renovision Studio Development Guidelines

This document outlines the consistent standards and best practices for the Renovision Studio project.

## Project Architecture Overview

### Monorepo Structure
- **Client**: React frontend (`client/`) with TypeScript, Vite, Tailwind CSS
- **Server**: Express backend (`server/`) with TypeScript, Node.js
- **Shared**: Common types and schemas (`shared/`) used across client and server
- **Tests**: Comprehensive testing suite (`tests/`) with API, E2E, and visual regression tests

### Technology Stack
- **Frontend**: React 18, TypeScript, Vite, Tailwind CSS, Radix UI, TanStack React Query, Wouter (routing)
- **Backend**: Node.js, Express, TypeScript, Supabase (database only), Clerk (authentication)
- **Database**: PostgreSQL via Supabase with custom indexes and optimizations
- **AI Integration**: OpenAI API for image generation with fallback error handling
- **Payments**: Stripe with webhooks and subscription management
- **Analytics**: Google Analytics 4 (GA4) with React integration and automatic tracking
- **File Storage**: Local uploads directory with multer, image optimization
- **Validation**: Zod schemas for all data validation across client and server
- **Icons**: Lucide React and Material Design icons
- **Animations**: Framer Motion for page transitions and UI animations

## Authentication Standards

### Clerk Integration
- **Authentication Provider**: Clerk handles all user authentication, session management, and user profiles
- **Database Role**: Supabase used ONLY as a database - authentication completely disabled
- **Token-Based Auth**: All API requests use Bearer tokens from Clerk
- **Middleware Pattern**: `clerkAuth` middleware validates tokens on all protected routes
- **User ID Format**: Clerk user IDs (string format like `user_2wGQABv0jGkkg4Sr5V9BhdKEiPA`) used throughout system
- **Session Management**: Clerk handles session persistence, refresh tokens, and logout

### API Authentication Patterns
- **Authorization Header**: All authenticated requests include `Authorization: Bearer <token>`
- **Token Validation**: Server-side middleware validates tokens and extracts user information
- **Error Handling**: 401 responses for invalid/expired tokens, 403 for insufficient permissions
- **Retry Logic**: Client automatically retries failed auth requests once before showing error
- **Debug Logging**: Comprehensive auth logging with `[CLERK-AUTH]` and `[DEBUG-CLERK]` tags

### Protected Routes
- **Client-Side**: Use `useAuth()` hook to check authentication state
- **Server-Side**: Apply `requireAuth` middleware to protected endpoints
- **Conditional Rendering**: Show different UI based on `isSignedIn` state
- **Redirect Patterns**: Redirect unauthenticated users to sign-in page

## API and Data Management Standards

### API Client Architecture
- **Centralized Client**: Use `apiClient` function from `lib/api-client.ts` for all API requests
- **Authentication Integration**: Automatic token injection via `getAuthToken()` helper
- **Retry Logic**: Built-in retry mechanism for failed requests (configurable retries)
- **Error Handling**: Structured error responses with proper HTTP status codes
- **Request Logging**: Performance timing and debug logging for all requests
- **Type Safety**: All API functions return typed responses using TypeScript generics

### API Endpoint Patterns
- **RESTful Design**: Follow REST conventions for all endpoints
- **Consistent Naming**: Use plural nouns for resources (`/api/projects`, `/api/drafts`)
- **HTTP Methods**: GET (read), POST (create), PUT/PATCH (update), DELETE (remove)
- **Status Codes**: 200 (success), 201 (created), 400 (bad request), 401 (unauthorized), 404 (not found), 500 (server error)
- **Response Format**: Consistent JSON structure with data and error fields
- **Validation**: All inputs validated using Zod schemas before processing

### Database Interaction Standards
- **ORM Pattern**: Use Supabase client for all database operations
- **Type Safety**: All database operations use typed schemas from `shared/schema.ts`
- **Error Handling**: Wrap all database operations in try-catch blocks
- **Logging**: Log all database operations with context and performance metrics
- **Transactions**: Use database transactions for multi-table operations
- **Indexes**: Optimize queries with proper database indexes for performance

### Server-Side Middleware Patterns
- **Authentication Middleware**: `clerkAuth` and `requireAuth` for token validation
- **Subscription Middleware**: `requireSubscription`, `checkProjectLimit`, `checkImageLimit` for usage enforcement
- **Error Handling Middleware**: Centralized `errorHandler` for consistent error responses
- **Async Handler**: `asyncHandler` wrapper for automatic error catching in async routes
- **Request Helpers**: `addAuthHelpers` adds authentication methods to request objects
- **Validation Middleware**: Zod schema validation before route processing

### Route Organization Patterns
- **Modular Routes**: Separate route modules for different features (`contact.ts`, `newsletter.ts`)
- **Route Registration**: Central `registerRoutes` function in `routes.ts`
- **Middleware Application**: Apply middleware in correct order (auth, validation, business logic)
- **Error Boundaries**: Wrap route handlers with error handling middleware
- **Response Patterns**: Consistent JSON response structure with data/error fields

### Database Operation Standards
- **Connection Management**: Single Supabase client instance with auth disabled
- **Query Logging**: Use `logDatabaseOperation` wrapper for performance monitoring
- **Error Classification**: Distinguish between validation, database, and business logic errors
- **Transaction Patterns**: Use database transactions for multi-step operations
- **Performance Monitoring**: Log slow queries (>1000ms) and medium queries (>500ms)

### React Query (TanStack Query) Standards
- **Query Keys**: Use descriptive array-based keys like `['/api/projects']`, `['/api/drafts']`
- **Mutations**: Use `useMutation` for all data modifications (POST, PUT, DELETE)
- **Cache Invalidation**: Invalidate relevant queries after successful mutations
- **Error Handling**: Handle errors in mutation callbacks and show user-friendly messages
- **Loading States**: Use `isLoading`, `isPending` states for UI feedback
- **Optimistic Updates**: Implement optimistic updates for better UX where appropriate
- **Stale Time**: Configure appropriate stale times for different data types
- **Background Refetch**: Enable background refetching for real-time data updates

## Type Safety and Validation Standards

### Shared Schema Architecture
- **Central Schema**: All types and validation schemas defined in `shared/schema.ts`
- **Database Types**: Auto-generated types from Supabase schema in `shared/types/supabase.ts`
- **Zod Validation**: All API inputs validated using Zod schemas before processing
- **Type Exports**: Export both database types and insert types for all tables
- **Schema Consistency**: Keep Zod schemas in sync with database schema definitions

### TypeScript Configuration Standards
- **Strict Mode**: Use strict TypeScript configuration across all projects
- **Module System**: ESNext modules with bundler resolution for modern build tools
- **Path Mapping**: Configure path aliases (`@/*` for client, `@shared/*` for shared)
- **Incremental Compilation**: Enable incremental builds with build info caching
- **Library Support**: Include DOM, DOM.iterable, and ESNext libraries
- **JSX Handling**: Use `preserve` mode for Vite processing
- **Type Checking**: Enable `skipLibCheck` for faster builds, `allowImportingTsExtensions` for TypeScript imports

### TypeScript Development Standards
- **Type Imports**: Use `type` imports for type-only imports to improve build performance
- **Generic Types**: Use generics for reusable components and API functions
- **Union Types**: Use union types for controlled values (status, modification types)
- **Interface vs Type**: Use interfaces for object shapes, types for unions and computed types
- **Null Safety**: Handle null/undefined values explicitly with optional chaining and nullish coalescing
- **Component Props**: Extend HTML element props for better TypeScript integration
- **Forward Refs**: Use proper typing with `React.forwardRef` for DOM access components

### Validation Patterns
- **Server-Side**: Validate all inputs using Zod schemas in API endpoints
- **Client-Side**: Use react-hook-form with Zod resolvers for form validation
- **Error Messages**: Provide clear, user-friendly validation error messages
- **Schema Reuse**: Reuse validation schemas between client and server
- **Runtime Validation**: Validate external API responses and user uploads

## UI/UX Design Standards

### Design System Architecture
- **Component Library**: Radix UI primitives with custom styling using Tailwind CSS
- **Design Tokens**: CSS custom properties for colors, spacing, and typography
- **Theme System**: Light/dark theme support with system preference detection
- **Responsive Design**: Mobile-first approach with consistent breakpoints
- **Accessibility**: WCAG 2.1 AA compliance through Radix UI primitives

### Component Standards
- **Composition Pattern**: Build complex components from smaller, reusable primitives
- **Variant System**: Use `class-variance-authority` (cva) for component variants
- **Prop Interfaces**: Extend HTML element props for better TypeScript integration
- **Forward Refs**: Use `forwardRef` for components that need DOM access
- **Display Names**: Set `displayName` for all components for better debugging

### Styling Conventions
- **Tailwind Classes**: Use Tailwind utility classes for all styling
- **Custom CSS**: Minimize custom CSS - prefer Tailwind utilities
- **Class Composition**: Use `cn()` utility for conditional class composition
- **Responsive Utilities**: Use Tailwind responsive prefixes (`sm:`, `md:`, `lg:`)
- **State Variants**: Use data attributes and Tailwind modifiers for state styling

### User Experience Patterns
- **Loading States**: Show loading spinners, skeletons, or progress indicators for all async operations
- **Error States**: Display clear, actionable error messages with retry options
- **Empty States**: Provide helpful empty states with clear next actions
- **Toast Notifications**: Use toast notifications for success/error feedback
- **Form Validation**: Real-time validation with clear error messages
- **Responsive Navigation**: Mobile-first navigation with appropriate breakpoints

## Error Handling and Logging Standards

### Error Handling Architecture
- **Centralized Errors**: Use `errorHandler.ts` for consistent error processing
- **Error Types**: Define specific error types (validation, authentication, database, etc.)
- **Error Boundaries**: Implement React error boundaries for component error handling
- **Graceful Degradation**: Provide fallback UI when features fail
- **User-Friendly Messages**: Convert technical errors to user-friendly messages
- **Retry Mechanisms**: Implement retry logic for transient failures

### Logging Standards
- **Structured Logging**: Use consistent log format with timestamps, categories, and context
- **Log Categories**: Categorize logs by system area (`api`, `auth`, `db`, `ui`, `system`)
- **Log Levels**: Use appropriate levels (debug, info, warn, error, trace) based on severity
- **Performance Logging**: Log request timing and database operation performance
- **Debug Mode**: Respect `DEBUG_MODE` environment variable for verbose logging
- **Client Logging**: Use browser console with structured format for client-side logging

### Debug Configuration Standards
- **Environment Variable**: Use `DEBUG_MODE` to control debug logging across the application
- **Category-Based Debugging**: Enable debug logging for specific categories (e.g., `DEBUG_MODE=auth,db`)
- **Wildcard Support**: Use `*` to enable all debug categories (`DEBUG_MODE=*`)
- **Client-Server Sync**: Both client and server respect the same debug configuration
- **Performance Impact**: Debug logging only enabled when explicitly configured
- **Log Format**: Consistent format with `[TIMESTAMP] [LEVEL] [CATEGORY] MESSAGE`
- **Metadata Support**: Include structured metadata objects with log messages

### Infrastructure Logging Patterns
- **Database Operations**: Use `logDatabaseOperation` wrapper for performance monitoring
- **API Requests**: Log request timing, authentication status, and response codes
- **External Services**: Log all external API calls with timing and error details
- **Authentication**: Comprehensive auth logging with `[CLERK-AUTH]` and `[DEBUG-CLERK]` prefixes
- **Error Context**: Include error codes, types, and stack traces in error logs
- **Performance Metrics**: Log slow operations (>500ms medium, >1000ms slow)

## Testing Standards

### Testing Architecture
- **Test Types**: Unit tests (Jest), API tests (Jest), E2E tests (Playwright), Visual regression tests
- **Test Organization**: Separate directories for different test types (`tests/api/`, `tests/e2e/`, `client/src/__tests__/`)
- **Test Configuration**: Multiple Jest configs for different test suites
- **Test Utilities**: Shared utilities for authentication, database setup, and fixtures
- **CI/CD Integration**: Automated testing in continuous integration pipeline

### Unit Testing Standards
- **Framework**: Jest with TypeScript support
- **Test Files**: Co-locate component tests in `__tests__` directories
- **Naming Convention**: `*.test.ts` or `*.test.tsx` for test files
- **Mock Strategy**: Mock external dependencies and API calls
- **Coverage**: Aim for high test coverage on critical business logic
- **Assertions**: Use descriptive test names and clear assertions

### API Testing Standards
- **Test Environment**: Separate test database and environment variables
- **Authentication**: Use test authentication tokens for protected endpoints
- **Data Cleanup**: Clean up test data after each test run
- **Error Testing**: Test both success and error scenarios
- **Schema Validation**: Validate API responses against expected schemas
- **Performance**: Include basic performance assertions for critical endpoints

### E2E Testing Standards
- **Framework**: Playwright with TypeScript
- **Browser Coverage**: Test across Chrome, Firefox, and Safari
- **Page Objects**: Use page object pattern for complex interactions
- **Test Data**: Use fixtures and test data for consistent testing
- **Visual Testing**: Include visual regression tests for UI components
- **Mobile Testing**: Test responsive design on mobile viewports

### Testing Configuration Standards
- **Jest Configuration**: Multiple Jest configs for different test suites (`jest.config.cjs`, `jest.component.config.cjs`, `jest.subscription.config.cjs`)
- **TypeScript Integration**: Use `ts-jest` with ESM support and proper module mapping
- **Path Mapping**: Configure module name mapping for `@/` and `@shared/` aliases
- **Test Environment**: Node.js environment for API tests, jsdom for component tests, separate test database
- **Setup Files**: Use `tests/setup.ts` for API tests, `tests/setup-component.js` for component tests
- **Test Scripts**: Comprehensive npm scripts for different test types and combinations
- **ESM Module Handling**: Configure `transformIgnorePatterns` for node-fetch and related ESM modules
- **Component Testing**: Use `@testing-library/react`, `@testing-library/jest-dom`, and `jest-environment-jsdom`

### Playwright Configuration Standards
- **Test Directory**: Organize tests in `tests/e2e/` with subdirectories for different test types
- **Timeout Configuration**: 30-second test timeout, 5-second expect timeout
- **Visual Regression**: Dedicated project with consistent viewport (1280x720) and light mode
- **Screenshot Settings**: 5% pixel difference tolerance, disabled animations for consistency
- **Browser Matrix**: Test across Chromium, Firefox, and WebKit
- **CI/CD Integration**: Retry configuration for CI environments, HTML reporting

### Test Organization Patterns
- **API Tests**: Located in `tests/api/` with `.test.ts` extension
- **E2E Tests**: Located in `tests/e2e/` with `.spec.ts` extension
- **Visual Tests**: Located in `tests/e2e/visual-regression/` with dedicated snapshots directory
- **Component Tests**: Located in `client/src/__tests__/` with `.test.tsx` extension
- **Test Utilities**: Shared utilities in `tests/` directory for authentication and fixtures
- **Test Data**: Fixtures and test data management with setup/teardown scripts

### Test Execution Standards
- **Test Pipeline Order**: Setup fixtures → Component tests → API tests → Subscription tests → E2E tests → Visual regression
- **Parallel Execution**: Component and API tests can run in parallel, E2E tests run sequentially
- **Test Isolation**: Each test suite runs in isolation with proper setup/teardown
- **Mock Management**: Use mocks for external APIs (OpenAI, Stripe) in unit tests, real APIs in integration tests
- **Database Testing**: Use separate test database with automatic cleanup between test suites
- **Performance Testing**: Monitor test execution time and optimize slow tests

### Test Coverage Standards
- **Component Coverage**: Minimum 90% coverage for all React components (✅ ACHIEVED: 100% - 11/11 tests passing)
- **API Coverage**: 100% coverage for all API endpoints and middleware (⚠️ IN PROGRESS: 25% - 22/87 tests passing)
- **Integration Coverage**: Cover all critical user flows end-to-end
- **Error Handling**: Test all error scenarios and edge cases
- **Mock Validation**: Ensure mocks accurately represent real service behavior
- **Regression Prevention**: Add tests for all bug fixes to prevent regressions

### Test Infrastructure Status
- **Component Tests**: ✅ FULLY WORKING - All 11 tests passing (100% success rate)
- **API Tests**: ⚠️ PARTIALLY WORKING - 3/13 test suites passing, 22/87 individual tests passing (25% success rate)
- **Test Setup**: ✅ COMPLETE - All Jest configurations, mocks, and fixtures working
- **ESM Module Handling**: ✅ RESOLVED - Node-fetch and OpenAI mocks working properly
- **Database Testing**: ✅ MOCKED - Database utilities and fallback mocks implemented
- **External API Mocking**: ✅ WORKING - OpenAI, Stripe, and node-fetch mocks fully implemented

### Test Execution Performance
- **Component Tests**: Fast execution (~1.2s for 11 tests)
- **API Tests**: Moderate execution (~3.8s for 87 tests)
- **Mock Reliability**: 100% - All external API calls properly mocked
- **Test Isolation**: ✅ Complete - Tests run independently with proper cleanup
- **CI/CD Ready**: ✅ All configurations compatible with continuous integration

### Remaining Test Issues (API Tests Only)
1. **Response Structure Mismatches** (60% of failures) - Tests expecting different API response formats
2. **Mock Behavior Gaps** (25% of failures) - Some Stripe mock scenarios need dynamic responses
3. **Database Operation Edge Cases** (15% of failures) - Minor database mocking improvements needed

### Test Execution Commands
```bash
# Run all component tests (100% working)
npm run test:component

# Run all API tests (25% working, 75% infrastructure complete)
npm run test:api

# Run specific test suites
npm run test:api -- --testNamePattern="OpenAI"
npm run test:component -- --testNamePattern="ScrollToTop"

# Run tests with coverage
npm run test:component -- --coverage
npm run test:api -- --coverage

# Run tests in watch mode for development
npm run test:component -- --watch
npm run test:api -- --watch

# Run all tests (when ready)
npm test
```

### Test Development Best Practices
- **Component Tests**: Use React Testing Library with proper accessibility queries
- **API Tests**: Mock external services, use real database operations where possible
- **Test Data**: Use consistent test fixtures and cleanup between tests
- **Error Testing**: Always test both success and failure scenarios
- **Mock Validation**: Ensure mocks accurately represent real service behavior
- **Test Isolation**: Each test should be independent and not rely on other tests

### Deployment and Dependency Management
- **Jest Version Compatibility**: Use stable Jest 29.3.4 with compatible babel-jest and jest-environment-jsdom (ts-jest@29.7.0 does not exist)
- **Dependency Resolution**: All Jest packages must be on the same major version to avoid peer dependency conflicts
- **Replit Deployment**: Fixed Jest dependency conflicts that were causing deployment failures
- **Package Management**: Use `npm install --legacy-peer-deps` if peer dependency conflicts arise
- **Version Pinning**: Critical testing dependencies are pinned to stable versions to ensure deployment reliability
- **ts-jest Version Fix**: Updated from non-existent 29.7.0 to stable 29.3.4 to resolve deployment issues

### Deployment Fix Commands
```bash
# For Replit deployment (handles canvas@3.1.0 vs jest-environment-jsdom conflict):
npm run deploy:replit

# Or manually fix dependencies:
npm install --legacy-peer-deps

# If you encounter Jest dependency conflicts during deployment:
npm run fix-jest-deps

# Verify Jest configuration works:
npm run test:component
```

### Common Deployment Issues and Solutions

#### ts-jest Version Not Found Error
**Issue**: `npm error notarget No matching version found for ts-jest@^29.7.0`
**Root Cause**: ts-jest version 29.7.0 does not exist in the npm registry
**Solution**:
1. Update package.json to use ts-jest@^29.3.4 (latest stable version in 29.x series)
2. Run `npm install --legacy-peer-deps` to install dependencies
3. Verify build works with `npm run build`

**Prevention**: Always check package versions exist before updating dependencies

#### Rollup Native Binary Missing Error
**Issue**: `Error: Cannot find module @rollup/rollup-linux-x64-gnu`
**Root Cause**: npm has a bug with optional dependencies causing corrupted Rollup native binaries
**Solution**:
1. Remove node_modules and package-lock.json
2. Clear npm cache with `npm cache clean --force`
3. Install dependencies with `npm install --legacy-peer-deps`
4. Manually install Rollup binary: `npm install @rollup/rollup-linux-x64-gnu --legacy-peer-deps`
5. Run build to verify: `npm run build`

**Prevention**: Use the updated deployment script which handles this automatically

#### Platform-Specific Rollup Dependencies Error
**Issue**: `npm install failed due to unsupported platform @rollup/rollup-darwin-arm64@4.44.0 requiring macOS ARM64 but Replit runs on Linux x64`
**Root Cause**: macOS-specific Rollup package explicitly listed in dependencies instead of being handled as optional
**Solution**:
1. Remove `@rollup/rollup-darwin-arm64` from package.json dependencies
2. Update .npmrc with platform targeting: `target_platform=linux` and `target_arch=x64`
3. Use `--no-optional` flag during installation to prevent platform-specific dependencies
4. Install correct Linux binary: `npm install @rollup/rollup-linux-x64-gnu --save-optional`

**Prevention**: Never add platform-specific packages as regular dependencies; use optionalDependencies or let package managers handle platform detection

#### Node.js Version Compatibility Issues
**Issue**: Packages requiring Node.js 20+ but Replit uses Node.js 18.20.5
**Root Cause**: Some packages (glob@11.x, lru-cache@11.x) require newer Node.js versions
**Solution**:
1. Downgrade glob to ^10.4.5 (compatible with Node.js 18)
2. Downgrade lru-cache to ^10.4.3 (compatible with Node.js 18)
3. These versions provide the same functionality with Node.js 18 compatibility

**Prevention**: Check Node.js engine requirements before updating dependencies

### Canvas Dependency Conflict Resolution
- **Issue**: `canvas@3.1.0` (needed for server-side image processing) conflicts with `jest-environment-jsdom@29.7.0`
- **Solution**: Multiple approaches implemented for maximum compatibility
  - Moved canvas to `optionalDependencies`
  - Created `.npmrc` with `legacy-peer-deps=true`
  - Added `replit.nix` for Nix-based deployment
  - Created `Dockerfile` for containerized deployment
- **Deployment**: Multiple deployment options available
- **Testing**: Component tests work perfectly despite the dependency conflict

### Replit Deployment Solutions
1. **Automatic via .npmrc** (RECOMMENDED): `.npmrc` file forces `--legacy-peer-deps` globally
2. **Manual deployment**: Use `npm run deploy:replit` script
3. **Nix-based**: `replit.nix` provides system dependencies for canvas
4. **Docker deployment**: `Dockerfile` handles all dependencies in container

### Deployment Status
- ✅ **Jest Dependencies**: All packages on compatible stable versions (29.3.4 for ts-jest, 29.7.0 for others)
- ✅ **Component Tests**: 100% working (11/11 tests passing)
- ✅ **Build Process**: No dependency conflicts preventing deployment
- ✅ **TypeScript Configuration**: Optimized for both development and testing
- ✅ **Mock System**: All external APIs properly mocked for reliable testing
- ✅ **Replit Deployment**: Fixed ts-jest version issue that was preventing deployment
- ✅ **Rollup Native Binary**: Fixed missing @rollup/rollup-linux-x64-gnu issue
- ✅ **Node.js Compatibility**: Downgraded packages to work with Node.js 18.20.5
- ✅ **Complete Deployment**: All deployment issues resolved and tested

## Performance and Security Standards

### Performance Optimization
- **Code Splitting**: Use dynamic imports and lazy loading for route-based code splitting
- **Image Optimization**: Optimize images with appropriate formats and sizes
- **Bundle Analysis**: Monitor bundle size and optimize dependencies
- **Caching Strategy**: Implement appropriate caching for API responses and static assets
- **Database Optimization**: Use indexes, query optimization, and connection pooling
- **CDN Usage**: Serve static assets through CDN when possible

### Security Standards
- **Input Validation**: Validate and sanitize all user inputs on both client and server
- **SQL Injection Prevention**: Use parameterized queries and ORM patterns
- **XSS Prevention**: Sanitize user-generated content and use CSP headers
- **Authentication Security**: Secure token storage and transmission
- **HTTPS Enforcement**: Use HTTPS in production environments
- **Environment Variables**: Store sensitive data in environment variables, never in code
- **Dependency Security**: Regularly audit and update dependencies to fix security vulnerabilities
- **Credential Management**: Use Replit Secrets for production deployment, never commit credentials to repository
- **Unused Dependencies**: Remove unused packages to reduce attack surface
- **Security Scanning**: Run `npm audit` regularly and address vulnerabilities promptly

### File Upload Security
- **File Type Validation**: Restrict file types to allowed formats (JPEG, PNG, WEBP)
- **File Size Limits**: Enforce maximum file size limits (10MB)
- **Virus Scanning**: Consider virus scanning for uploaded files in production
- **Storage Security**: Store uploaded files outside web root when possible
- **Access Control**: Implement proper access controls for uploaded files

## External Service Integration Standards

### Google Analytics 4 (GA4) Integration Standards
- **Environment Configuration**: Use `VITE_GA4_MEASUREMENT_ID` for tracking ID and `VITE_GA4_DEBUG_MODE` for debug mode
- **Production Only**: GA4 tracking only enabled in production environment with valid measurement ID
- **React Integration**: Use custom `useGA4` hook for automatic page view tracking with Wouter routing
- **Authentication Integration**: Automatic user ID tracking with Clerk authentication state changes
- **Event Tracking**: Structured event tracking for authentication, projects, payments, and errors
- **Privacy Compliance**: Proper consent management and GDPR compliance considerations
- **Performance Optimization**: Async loading to prevent impact on Core Web Vitals
- **Debug Support**: Debug mode for development testing with comprehensive logging
- **Type Safety**: Full TypeScript support with typed event parameters and configuration
- **Context Provider**: GA4Provider component for application-wide analytics access
- **Manual Tracking**: Support for manual event tracking through context hooks
- **Error Handling**: Graceful fallback when GA4 is not available or fails to load

### GA4 Event Categories and Standards
- **Page Views**: Automatic tracking for all route changes with user context
- **Authentication Events**: `sign_up`, `sign_in`, `sign_out` with Clerk integration
- **Project Events**: `project_created`, `project_completed`, `image_generated`, `image_generation_failed`
- **Payment Events**: `checkout_started`, `checkout_completed`, `subscription_created`, `subscription_cancelled`
- **Error Events**: `error_occurred` with error type, message, and context
- **Form Events**: `form_start`, `form_submit`, `form_error`, `form_field_focus/blur/change`
- **Custom Events**: Flexible custom event tracking with category, label, value, and custom parameters

### GA4 Implementation Architecture
- **Core Library**: `client/src/lib/ga4.ts` - Core GA4 utility functions with environment configuration
- **React Hook**: `client/src/hooks/use-ga4.tsx` - React hook for automatic tracking and manual functions
- **Context Provider**: `client/src/components/GA4Provider.tsx` - Application-wide analytics context
- **HTML Integration**: GA4 script loaded in `client/index.html` with proper async configuration
- **Environment Variables**: Exposed through Vite configuration for client-side access
- **Logging Integration**: Uses existing logger utility with 'analytics' category for debug information

### Stripe Integration Patterns
- **Client-Side**: Use `@stripe/stripe-js` with lazy loading and publishable key from environment
- **Checkout Flow**: Implement `createCheckoutSession` with duplicate request prevention and cooldown
- **Session Management**: Track in-flight requests to prevent duplicate checkout attempts
- **Error Handling**: Comprehensive error handling with user-friendly messages
- **Webhook Processing**: Server-side webhook handling for subscription events
- **Security**: Validate webhook signatures and process events idempotently

### Webhook Processing Standards
- **Signature Verification**: Use `stripe.webhooks.constructEvent` with webhook secret for production
- **Development Mode**: Allow unsigned webhooks for development when `STRIPE_WEBHOOK_SECRET` is not set
- **Event Logging**: Log all webhook events with event ID, type, and object ID
- **Idempotency**: Track processed webhook events to prevent duplicate processing
- **Error Handling**: Return 200 status even on processing errors to prevent Stripe retries
- **Event Types**: Handle `checkout.session.completed`, `customer.subscription.created/updated/deleted`
- **Coupon Processing**: Extract and process coupon codes from checkout sessions
- **Database Updates**: Update subscription status and usage records based on webhook events

### OpenAI API Integration
- **Model Selection**: Use `gpt-image-1` model for all image generation requests
- **Image Processing**: Support multiple input images (before + reference images)
- **Error Handling**: Graceful fallback to text-only generation on image processing failures
- **Retry Logic**: Implement retry mechanisms for transient API failures
- **Cost Management**: Support mock mode for development to reduce API costs
- **Logging**: Comprehensive logging for debugging and monitoring API usage

### Clerk Authentication Integration
- **Client Configuration**: Configure ClerkProvider with custom sign-in/sign-up URLs
- **Token Management**: Automatic token refresh with localStorage persistence
- **Session Handling**: Global auth cache for performance across page navigations
- **Error Recovery**: Automatic retry logic for authentication failures
- **Debug Logging**: Structured logging with `[CLERK-AUTH]` prefixes for debugging
- **Custom Pages**: Always use custom authentication pages instead of Clerk's hosted pages

### Supabase Database Integration
- **Configuration**: Use Supabase client with authentication completely disabled
- **Connection Management**: Single client instance shared across the application
- **Type Safety**: Use auto-generated types from Supabase schema
- **Error Handling**: Distinguish between database errors and application errors
- **Performance**: Monitor query performance and log slow operations
- **Security**: Use service role key for server-side operations, never expose in client

### Email Service Integration
- **SMTP Configuration**: Use nodemailer with configurable SMTP settings (Gmail, custom SMTP)
- **Environment Variables**: `SMTP_HOST`, `SMTP_PORT`, `SMTP_USER`, `SMTP_PASS`, `EMAIL_FROM`, `CONTACT_EMAIL`
- **Development Mode**: Use Ethereal fake SMTP service for testing with preview URLs
- **Production Mode**: Use real SMTP credentials with security logging disabled
- **Email Templates**: Structured templates for contact notifications and auto-replies
- **Error Handling**: Continue processing even if email sending fails, with comprehensive logging
- **Security**: Never log actual passwords, use masked logging for credentials

### Retry and Resilience Patterns
- **Retry Logic**: Use `withRetry` utility for transient failures with exponential backoff
- **Retryable Errors**: Network errors, database connection errors, rate limits, Stripe connection errors
- **Non-Retryable Errors**: Validation errors (400), authentication errors (401/403), not found (404), conflicts (409)
- **Backoff Strategy**: Exponential backoff with jitter (0.85-1.15 multiplier) to prevent thundering herd
- **Max Retries**: Default 3 attempts with configurable retry counts
- **Error Classification**: Distinguish between retryable and non-retryable errors
- **Logging**: Comprehensive retry logging with attempt counts and delay information

## Development Environment Standards

### Node.js Version Management
- **Version Manager**: Use nvm for Node.js version isolation
- **Version File**: `.nvmrc` file specifies the project Node.js version
- **Setup Script**: `setup-env.sh` provides automated environment setup with multiple modes:
  - `--safe`: Node.js v18 LTS with compatible dependencies
  - `--latest`: Latest Node.js LTS with updated dependencies
  - `--dev`: Node.js v22.14.0 for development
- **Package Manager**: npm only (enforced via preinstall script)
- **Legacy Peer Deps**: Use `--legacy-peer-deps` flag for dependency resolution

### Environment Configuration
- **Environment Variables**: Store sensitive data in `.env` file (never commit)
- **Client Variables**: Use `VITE_` prefix for client-accessible environment variables
- **Variable Exposure**: Configure in `vite.config.ts` define section for client access
- **Debug Mode**: Support `DEBUG_MODE` environment variable with category-based logging
- **Required Variables**: `SUPABASE_URL`, `SUPABASE_DB_KEY`, `STRIPE_SECRET_KEY`, `CLERK_SECRET_KEY`
- **Optional Variables**: `OPENAI_API_KEY` (for AI features)

### Build and Deployment Standards
- **Build Tool**: Vite for frontend, esbuild for backend bundling
- **Development**: `npm run dev` starts development server with hot reload
- **Production Build**: `npm run build` creates optimized production bundle
- **Production Start**: `npm run start` runs production server
- **Platform Detection**: Automatic Replit detection with port configuration
- **Static Assets**: Served from `/uploads` directory with Express static middleware

### Configuration File Standards
- **Vite Configuration**: Use `vite.config.ts` with TypeScript support and path aliases
- **TypeScript Config**: Multiple configs (`tsconfig.json`, `tsconfig.test.json`) for different environments
- **Tailwind Config**: Use `tailwind.config.ts` with TypeScript for type safety
- **Jest Configuration**: Multiple Jest configs for different test suites with ESM support
- **Playwright Config**: Comprehensive E2E and visual regression testing configuration
- **Environment Variables**: Use `.env` files with proper variable prefixing (`VITE_` for client)
- **Path Aliases**: Configure consistent path mapping across all tools (`@/`, `@shared/`)

### Package.json Script Standards
- **Development Scripts**: `dev`, `build`, `start`, `test` for standard operations
- **Setup Scripts**: `setup`, `setup:dev`, `setup:latest` for environment configuration
- **Fix Scripts**: Dedicated scripts for common dependency issues
- **Test Scripts**: Comprehensive test scripts for different test types and combinations
- **Health Check Scripts**: `check:auth`, `test:auth` for system validation
- **Preinstall Hook**: Enforce npm usage and prevent other package managers

### Comprehensive Script Categories
- **Environment Management**: `prepare`, `fix-permissions`, `clean-cache` for environment setup
- **Development Tools**: `dev`, `dev:prod`, `build`, `start`, `check` for development workflow
- **Testing Suite**: `test`, `test:e2e`, `test:api`, `test:visual`, `test:component` for comprehensive testing
- **Specialized Testing**: `test:subscription`, `test:coupon`, `test:navigation` for feature-specific tests
- **Visual Regression**: `test:visual:update`, `test:visual:coupon`, `test:visual:navigation` for UI testing
- **Setup and Fixtures**: `test:setup`, `test:setup-fixtures`, `pretest` for test data management
- **External Service Testing**: `test:openai`, `test:stripe-webhooks` for integration testing

### Script Automation Standards
- **Utility Scripts**: Comprehensive scripts in `scripts/` directory for common operations
- **Coupon Management**: `create-coupons.js`, `create-test-coupon.js`, `import-stripe-coupon.js`
- **Usage Testing**: `test-usage-tracking.js`, `test-usage.ts` for usage system validation
- **Schema Management**: `update-schema.js`, `update-schema.ts`, `update-schema.sql`
- **Authentication Testing**: `check-auth.sh`, `test-auth.sh` for auth system validation
- **Migration Scripts**: `apply-migration.sh` for database migration management
- **Manual Testing**: `manual-test.js` for comprehensive system testing

### Test Fixture Standards
- **Fixture Organization**: Store test fixtures in `tests/fixtures/` directory
- **Coupon Fixtures**: `coupons.ts` with `createTestCoupon` and `createMockStripeCoupon` utilities
- **Subscription Fixtures**: `subscriptions.js` for subscription test data
- **Image Fixtures**: Test images (`test-image.jpg`, `test-room.jpg`, `reference-image.jpg`)
- **Mock Data Generation**: Utilities for generating realistic test data
- **Fixture Cleanup**: Automated cleanup of test data after test runs

### Development Scripts and Automation
- **Setup Scripts**: Automated environment setup with dependency fixing
- **Fix Scripts**: Dedicated scripts for common dependency issues:
  - `fix-path-to-regexp-combined.sh`: Fixes path-to-regexp compatibility
  - `fix-clerk-combined.sh`: Fixes Clerk integration issues
  - `fix-tailwind-setup.sh`: Fixes Tailwind CSS setup
- **Permission Scripts**: `fix-permissions` and `clean-cache` for npm issues
- **Health Checks**: `check:auth` and `test:auth` scripts for authentication validation

### nvm Usage
- **Preferred Tool**: Use nvm to isolate Node.js versions for the project
- **Version**: Node.js v22.14.0 recommended for development
- **Setup**: Run `npm run setup` or `npm run setup:dev` for proper environment configuration

## Dependency Management

### Package Manager Standards
- **Primary Tool**: Use npm (not yarn or pnpm) for consistency
- **Installation**: Always use package managers instead of manually editing package.json
- **Commands**: Use `npm install <package>` instead of direct file editing
- **Lock Files**: Maintain package-lock.json for consistent builds
- **Security Auditing**: Run `npm audit` before deployment and address vulnerabilities
- **Unused Dependencies**: Regularly review and remove unused packages to reduce attack surface

### Replit Deployment Security Standards

#### Environment Variable Security
- **Production Secrets**: Use Replit Secrets for all sensitive environment variables in production
- **Development Template**: Use `.env.example` as a template showing required variables without exposing values
- **Variable Substitution**: Use `${VARIABLE_NAME}` pattern in `.env` to reference Replit Secrets
- **Never Commit Credentials**: Never commit actual API keys, tokens, or secrets to the repository
- **Credential Rotation**: Regularly rotate API keys and update them in Replit Secrets

#### Required Replit Secrets
- `SUPABASE_DB_KEY`: Supabase service role key for database operations
- `SUPABASE_KEY`: Supabase service role key (backup/alias)
- `VITE_SUPABASE_ANON_KEY`: Supabase anonymous key for client-side operations
- `OPENAI_API_KEY`: OpenAI API key for AI image generation
- `STRIPE_SECRET_KEY`: Stripe secret key for payment processing
- `VITE_CLERK_PUBLISHABLE_KEY`: Clerk publishable key for authentication
- `CLERK_SECRET_KEY`: Clerk secret key for server-side authentication

#### Secure Deployment Process
1. **Use Secure Script**: Run `npm run deploy:secure` for production deployment
2. **Environment Check**: Script verifies all required environment variables are configured
3. **Security Audit**: Automatic security vulnerability scanning during deployment
4. **Dependency Updates**: Clean installation with latest secure versions
5. **Build Verification**: Ensures application builds successfully before deployment

#### Security Monitoring
- **Regular Audits**: Run security audits weekly or before major deployments
- **Vulnerability Tracking**: Monitor and address security advisories for dependencies
- **Access Control**: Limit access to Replit Secrets to authorized team members only
- **Audit Logging**: Monitor access to sensitive environment variables

## User Experience Standards

### Mobile Responsiveness
- **Design Approach**: Mobile-first responsive design for all components
- **Header/Footer**: Ensure proper mobile navigation and layout
- **Content Width**: Maintain consistent content width across pages (reference-library page width is the standard)

### Testimonial Section Standards
- **Realistic Presentation**: Mix of photo headshots and initial-based avatars for authenticity
- **Avatar Fallback**: When `imageUrl` is null, display user initials in a styled circular avatar
- **Visual Variety**: Not every testimonial should have a photo - use initials for some users to appear more realistic
- **Component Support**: `TestimonialCard` component handles both photo and initial-based avatars automatically

### Marketing Language Standards
- **Target Audience**: Use language that appeals to both businesses (contractors, designers) and consumers (homeowners)
- **AI Visualization Description**: "Our AI technology creates realistic visualizations of your renovation ideas, helping you see exactly how your space will look before making any changes"
- **Inclusive Messaging**: Avoid contractor-only language - use "your renovation ideas" instead of "client proposals"

### Navigation
- **Mobile Navigation**: Consolidate under user profile dropdown instead of hamburger menu
- **Footer Links**: All footer links should scroll to top of page, even when navigating to current page

### Progress Indicators
- **Create Page Flow**: Use single progress indicator showing "Choose Modification > Upload Images > Customize > Review"
- **UI Consistency**: Avoid duplicate progress indicators - remove numbered steppers (1, 2, 3) in favor of descriptive text-based progress
- **User Experience**: Clear, descriptive step names help users understand their current position in the flow

### Custom Sign-In Implementation
- **Custom Pages**: Always use custom sign-in pages (`/sign-in`) instead of Clerk's hosted pages
- **Redirect Handling**: Both SignInPage and SignUpPage should read `redirect_url` query parameter and redirect users back to their intended destination after authentication
- **Manual Redirects**: All authentication redirects should use `navigate("/sign-in")` instead of Clerk's redirect components
- **Consistent Experience**: Never allow Clerk's hosted pages to appear - always redirect to custom pages for consistent branding
- **ClerkProvider Configuration**: Configure with custom URLs:
  ```tsx
  <ClerkProvider
    publishableKey={publishableKey}
    signInUrl="/sign-in"
    signUpUrl="/sign-up"
    afterSignInUrl="/"
    afterSignUpUrl="/"
  >
  ```

### Protected Route Implementation
- **Custom Component**: Use custom `ProtectedRoute` component that redirects to custom sign-in page
- **Loading States**: Include loading states for better UX during authentication checks
- **Destination Preservation**: Preserve the intended destination URL for post-authentication redirect

## Testing Standards (Comprehensive)

### Test Coverage Requirements
- **Comprehensive Coverage**: Maintain automated test coverage across all critical functionality
- **Test Types**: Unit tests (Jest), API tests (Jest), E2E tests (Playwright), Visual regression tests
- **Post-Change Testing**: Always suggest writing/updating tests after significant changes
- **Critical Path Testing**: Ensure all user-critical flows are covered by E2E tests

### Mock and Testing Modes
- **OpenAI API Mocking**: Support toggling between real and mocked responses to reduce costs during testing
- **Database Flow Consistency**: Mock mode should use same database storage flow as production
- **Simple Implementations**: Prefer simpler implementations for mocking external APIs
- **Test Data Management**: Use fixtures and test data for consistent, repeatable testing

## Code Quality Standards

### ES Module Standards
- **Module Type**: Project uses ES modules (`type: 'module'` in package.json)
- **Import Syntax**: Use `import` syntax instead of `require` in scripts
- **Consistency**: Maintain ES module standards throughout codebase
- **Dynamic Imports**: Use dynamic imports for code splitting and lazy loading

### Naming Conventions and File Organization

#### File Naming Standards
- **Components**: PascalCase for React components (`CreateVisualizationFlow.tsx`, `ProjectCard.tsx`)
- **Hooks**: camelCase with `use` prefix (`use-auth.tsx`, `use-mobile.tsx`, `useErrorHandler.ts`)
- **Utilities**: camelCase for utility files (`api-client.ts`, `image-utils.ts`, `debug-config.ts`)
- **Pages**: PascalCase for page components (`HomePage.tsx`, `SettingsPage.tsx`)
- **Types**: camelCase for type definition files (`schema.ts`, `supabase.ts`)
- **Tests**: Match source file name with `.test.ts` or `.test.tsx` extension
- **Scripts**: kebab-case for shell scripts (`setup-env.sh`, `run-tests.sh`)
- **Configuration**: kebab-case for config files (`vite.config.ts`, `tailwind.config.ts`)

#### Directory Structure Standards
- **Feature-Based Organization**: Group related files by feature/domain rather than by type
- **UI Components**: Store in `client/src/components/ui/` for reusable UI primitives
- **Feature Components**: Store in `client/src/components/` for feature-specific components
- **Hooks**: Store in `client/src/hooks/` directory
- **Utilities**: Store in `client/src/lib/` and `client/src/utils/` directories
- **Types**: Store shared types in `shared/` directory
- **Tests**: Organize by test type (`tests/api/`, `tests/e2e/`, `client/src/__tests__/`)

#### Variable and Function Naming
- **React Components**: PascalCase (`CreateVisualizationFlow`, `ProjectCard`)
- **Functions**: camelCase (`generatePrompt`, `normalizeImagePath`, `createCheckoutSession`)
- **Variables**: camelCase (`authToken`, `isLoading`, `projectData`)
- **Constants**: SCREAMING_SNAKE_CASE (`MOBILE_BREAKPOINT`, `TOAST_LIMIT`, `API_BASE_URL`)
- **Type Names**: PascalCase (`ModificationType`, `ProjectWithImages`, `ClerkCompatibleUser`)
- **Interface Names**: PascalCase with descriptive names (`PromptParams`, `AuthContextType`)
- **Enum Values**: camelCase (`modificationType`, `planLimits`)

#### State Management Naming Patterns
- **State Variables**: Descriptive camelCase (`[modificationType][OptionType]` pattern)
- **State Setters**: `set` prefix with PascalCase (`setIsLoading`, `setAuthToken`)
- **Boolean States**: `is`, `has`, `can`, `should` prefixes (`isLoading`, `hasError`, `canSubmit`)
- **Query Keys**: Array format with descriptive strings (`['/api/projects']`, `['/api/drafts']`)
- **Event Handlers**: `handle` or `on` prefix (`handleSubmit`, `onError`, `handleClick`)

#### Import and Export Patterns
- **Default Exports**: Use for React components and main module exports
- **Named Exports**: Use for utilities, hooks, types, and multiple related functions
- **Type Imports**: Use `type` keyword for type-only imports (`import type { User } from '@shared/schema'`)
- **Barrel Exports**: Use index files for clean import paths in component directories
- **Path Aliases**: Use `@/` for client imports, `@shared/` for shared imports

### Custom Hook Patterns
- **Naming Convention**: Use `use` prefix with descriptive camelCase names (`useAuth`, `useErrorHandler`)
- **File Extensions**: Use `.tsx` for hooks that return JSX, `.ts` for pure logic hooks
- **State Management**: Use `useState` and `useEffect` with proper dependency arrays
- **Error Handling**: Include error states and error handling logic in custom hooks
- **Performance**: Use `useCallback` and `useMemo` for expensive operations
- **Cleanup**: Always return cleanup functions from `useEffect` when needed

### Utility Function Standards
- **Pure Functions**: Write pure functions that don't cause side effects
- **Type Safety**: Use proper TypeScript typing for all parameters and return values
- **Error Handling**: Include proper error handling and validation
- **Documentation**: Use JSDoc comments for complex utility functions
- **Testing**: Write unit tests for all utility functions
- **Naming**: Use descriptive function names that clearly indicate purpose

### Image Handling Patterns
- **Path Normalization**: Use `normalizeImagePath` utility for consistent image path handling
- **Thumbnail Generation**: Use `createThumbnailUrl` for file preview generation
- **Dimension Detection**: Use `getImageDimensions` for image size validation
- **Upload Validation**: Validate file types, sizes, and formats before processing
- **Error Handling**: Provide fallback images and error states for failed image operations

### Image Processing Standards
- **Path Resolution**: Handle both string paths and Image objects with consistent normalization
- **URL Handling**: Support both relative paths and absolute URLs for image sources
- **Generated Images**: Special handling for generated images in `/uploads/generated/` directory
- **Error Images**: Use canvas-generated error images for failed AI generations
- **File Type Support**: Support JPEG, PNG, WEBP formats with proper validation
- **Storage Organization**: Organize uploads in `/uploads/` with generated images in subdirectory

### Error Image Generation
- **Canvas Creation**: Use node-canvas to generate error images programmatically
- **Error Visualization**: Create 1024x1024 error images with clear error messaging
- **Fallback Assets**: Store error images in `assets/` directory for reuse
- **Error Scripts**: Use `create-error-image.mjs` for generating error image assets
- **Visual Design**: Consistent error image design with red error icon and descriptive text

### API Client Patterns
- **Centralized Client**: Use single `apiClient` function for all HTTP requests
- **Authentication**: Automatic token injection via `getAuthToken` helper
- **Retry Logic**: Built-in retry mechanism with configurable retry counts
- **Error Handling**: Structured error responses with proper HTTP status codes
- **Performance**: Request timing and performance logging
- **Type Safety**: Generic type support for typed API responses

## Feature Development Standards

### Create Page Flow Architecture
- **User Experience**: Streamlined 4-step flow where users first select action, then supply images
- **Step Flow**: Choose Modification → Upload Images → Customize → Review
- **Options Priority**: Prioritize pre-defined renovation options over free-form descriptions
- **Reference Images**: Support reference images in custom modification flows
- **Backend Prompts**: Maintain highly maintainable and easily modifiable prompt templates

#### Modification Types System
- **Definition Location**: Modification types defined in `CreateVisualizationFlow.tsx` with enabled/disabled flags
- **Current Types**:
  - `replace_floor` (enabled) - Full UI implementation with material/color selection and reference image support
  - `change_wall_color` (enabled) - Full UI implementation with color/finish selection and reference image support
  - `update_cabinets` (enabled) - Full UI implementation with material/color/style selection and reference image support
  - `kitchen_remodel` (enabled) - Full UI implementation with cabinet style/countertop/appliance/color scheme options and reference image support
  - `bathroom_remodel` (enabled) - Full UI implementation with fixture/tile/vanity/color scheme options and reference image support
  - `interior_painting` (enabled) - Full UI implementation with wall/ceiling/accent/finish options and reference image support
  - `lighting_upgrades` (enabled) - Full UI implementation with fixture style/type/ambiance/smart controls and reference image support
  - `exterior_repainting` (enabled) - Full UI implementation with main/trim/accent/door color options and reference image support
  - `custom` (enabled) - Full implementation for free-form descriptions
- **Enabling Process**: To enable a modification type:
  1. Set `enabled: true` in the modificationTypes array
  2. Add UI implementation in `renderModificationOptions()` function
  3. Update completion logic in `handleComplete()` function
  4. Ensure prompt template exists in `promptTemplates.ts`

#### Prompt Template System
- **Location**: All prompt templates stored in `server/promptTemplates.ts`
- **Architecture**: Each modification type has a dedicated template function
- **Template Function Signature**: `(params: PromptParams) => string`
- **PromptParams Interface**:
  ```typescript
  interface PromptParams {
    description?: string;
    options?: any;
    hasPrimaryReferenceImage: boolean;
    hasAdditionalReferenceImages: boolean;
    referenceImageCount: number;
  }
  ```
- **Template Registration**: Add new templates to `promptTemplates` object export
- **Fallback Behavior**: Unknown modification types fall back to custom template
- **Quality Instructions**: Each template includes specific quality instructions for that modification type

#### Data Flow Architecture
- **Frontend Structure**: Uses `StructuredModification` interface with `type`, `description`, `options`, `primaryReferenceImageId`
- **Backend Processing**: `processModification()` function handles complete flow from creation to image generation
- **Prompt Generation**: `generatePrompt()` function selects appropriate template based on modification type
- **OpenAI Integration**: Structured data passed to `generateRenovationImage()` with reference image support
- **Reference Image Handling**: Primary reference image prioritized, additional images used for inspiration

#### UI Component Patterns
- **Options Rendering**: Each modification type has dedicated UI in `renderModificationOptions()` function
- **Reference Image Integration**: Support for both reference image selection and traditional option selection
- **Custom Descriptions**: All modification types support additional custom description fields
- **Validation**: Each modification type validates required fields before allowing progression
- **Review Display**: Modification details shown in review step with type-specific information
- **Backend Prompt Privacy**: Generated prompts are hidden from users - only show "Additional Details (Optional)" field
- **User Input Separation**: User custom descriptions are combined with generated prompts only during backend processing

#### Reusable UI Components (Modular Architecture)
- **ReferenceImageToggle**: Standardized toggle component for switching between option selection and reference image mode
  - Props: `id`, `checked`, `onCheckedChange`, `title`, `description`
  - Consistent styling and behavior across all modification types
  - **Enhanced Descriptions**: Use specific language like "Apply the exact cabinet style, countertops, and layout from your reference image" instead of generic "Use reference image"
- **OptionSelector**: Reusable component for radio button option selection
  - Props: `title`, `options`, `value`, `onChange`, `columns` (default: 2)
  - Supports dynamic grid layouts and consistent option display
  - Options format: `Array<{ id: string; name: string; description: string }>`
- **ReferenceImageSelector**: Standardized reference image selection component
  - Props: `selectedImageId`, `onImageSelect`, `title`
  - Handles empty state with "Add Reference Images" button
  - Consistent grid layout and selection indicators
- **CustomDescriptionField**: Reusable textarea component for additional details
  - Props: `value`, `onChange`, `placeholder`
  - Consistent styling and placeholder text patterns

#### State Management Architecture
- **Consistent Naming**: All state variables follow pattern: `[modificationType][OptionType]` (e.g., `kitchenCabinetStyle`, `bathroomFixtureStyle`)
- **Reference Image State**: Each modification type has dedicated reference image state variables
- **Custom Description State**: Each modification type has its own custom description field
- **Toggle State**: Each modification type has its own reference image toggle state
- **State Dependencies**: All state variables included in useEffect dependency arrays for proper description generation

#### Implemented Modification Types Details

**Wall Color Changes (`change_wall_color`)**:
- **UI Options**: 12 predefined colors (white, off-white, beige, gray variants, blue, green, yellow, pink, black, custom)
- **Finish Options**: 5 finish types (matte, eggshell, satin, semi-gloss, gloss) with descriptions
- **Reference Image Support**: Toggle between color/finish selection and reference image inspiration
- **Validation**: Requires either wall color selection or reference image selection
- **Prompt Template**: Enhanced with specific wall color instructions, lighting considerations, and finish handling

**Cabinet Updates (`update_cabinets`)**:
- **Material Options**: 5 materials (wood, laminate, thermofoil, metal, glass) with descriptions
- **Color Options**: 10 colors (white, off-white, cream, gray, black, natural wood, dark wood, light wood, navy, green)
- **Style Options**: 6 styles (shaker, raised panel, flat panel, beadboard, glass front, louvered) with descriptions
- **Reference Image Support**: Toggle between option selection and reference image inspiration
- **Validation**: Requires at least one cabinet option (material, color, or style) or reference image selection
- **Prompt Template**: Enhanced with cabinet-specific instructions, hardware considerations, and realistic proportions

**Floor Replacement (`replace_floor`)**:
- **Material Options**: 6 materials (hardwood, laminate, tile, vinyl, carpet, concrete)
- **Color Options**: 7 colors (natural, light, medium, dark, white, gray, black)
- **Reference Image Support**: Toggle between material/color selection and reference image inspiration
- **Validation**: Requires both material and color selection or reference image selection
- **Icon**: `layers` (Material Design icon)

**Kitchen Remodel (`kitchen_remodel`)**:
- **Cabinet Style Options**: 5 styles (modern, traditional, transitional, farmhouse, industrial)
- **Countertop Material Options**: 5 materials (quartz, granite, marble, butcher-block, concrete)
- **Appliance Style Options**: 5 styles (stainless-steel, black-stainless, white, black, panel-ready)
- **Color Scheme Options**: 5 schemes (white-and-gray, navy-and-white, black-and-white, warm-wood, earth-tones)
- **Reference Image Support**: Toggle between option selection and reference image inspiration
- **Validation**: Requires at least one option (cabinet style, countertop, appliance, or color scheme) or reference image
- **Icon**: `countertops` (Material Design icon)

**Bathroom Remodel (`bathroom_remodel`)**:
- **Fixture Style Options**: 5 styles (modern, traditional, transitional, spa-like, industrial)
- **Tile Style Options**: 5 styles (subway, mosaic, large-format, natural-stone, geometric)
- **Vanity Style Options**: 5 styles (floating, freestanding, double-vanity, vessel-sink, integrated)
- **Color Scheme Options**: 5 schemes (white-and-gray, navy-and-white, earth-tones, black-and-white, soft-pastels)
- **Reference Image Support**: Toggle between option selection and reference image inspiration
- **Validation**: Requires at least one option (fixture, tile, vanity, or color scheme) or reference image
- **Icon**: `bathtub` (Material Design icon)

**Interior Painting (`interior_painting`)**:
- **Wall Color Options**: 5 colors (warm-white, cool-gray, sage-green, navy-blue, soft-beige)
- **Ceiling Color Options**: 4 colors (white, off-white, light-gray, same-as-walls)
- **Accent Wall Options**: 4 options (feature-wall, fireplace-wall, bedroom-headboard, dining-room)
- **Finish Options**: 4 finishes (matte, eggshell, satin, semi-gloss)
- **Reference Image Support**: Toggle between option selection and reference image inspiration
- **Validation**: Requires at least one option (wall color, ceiling color, accent wall, or finish) or reference image
- **Icon**: `brush` (Material Design icon)

**Lighting Upgrades (`lighting_upgrades`)**:
- **Fixture Style Options**: 5 styles (modern, traditional, industrial, farmhouse, mid-century)
- **Lighting Type Options**: 5 types (recessed, pendant, chandelier, track, under-cabinet)
- **Ambiance Options**: 4 options (bright-energizing, warm-cozy, dramatic, natural)
- **Smart Controls**: Boolean option for smart lighting controls
- **Reference Image Support**: Toggle between option selection and reference image inspiration
- **Validation**: Requires at least one option (fixture style, type, ambiance, or smart controls) or reference image
- **Icon**: `lightbulb` (Material Design icon)

**Exterior Repainting (`exterior_repainting`)**:
- **Main Color Options**: 5 colors (classic-white, charcoal-gray, navy-blue, sage-green, warm-beige)
- **Trim Color Options**: 4 colors (bright-white, cream, black, dark-gray)
- **Accent Color Options**: 4 colors (red, blue, green, yellow)
- **Door Color Options**: 5 colors (red, navy, black, green, natural-wood)
- **Reference Image Support**: Toggle between option selection and reference image inspiration
- **Validation**: Requires at least one option (main, trim, accent, or door color) or reference image
- **Icon**: `home` (Material Design icon)

#### Backend Processing Flow
- **API Endpoint**: `POST /api/projects/:id/modifications` creates modification and starts processing
- **Async Processing**: `processModification()` function runs asynchronously after modification creation
- **Status Updates**: Project status flows: `draft` → `processing` → `completed` (or `failed`)
- **Image Generation**: Uses `generateRenovationImage()` which calls OpenAI gpt-image-1 model
- **Reference Image Order**: Primary reference image placed first, additional images follow
- **Error Handling**: Failed generations create error images with helpful messages
- **Database Storage**: Generated images stored with proper relationships to projects and modifications

#### API Integration Standards
- **OpenAI Model**: Use `gpt-image-1` model for all image generation
- **Image Input**: Support multiple images (before image + reference images)
- **Prompt Structure**: Generated prompts include modification-specific instructions and quality guidelines
- **Fallback Behavior**: Text-only generation if image processing fails
- **Error Recovery**: Graceful degradation with informative error images
- **Logging**: Comprehensive logging for debugging prompt generation and API calls

#### Modular Implementation Benefits
- **Code Reusability**: Reusable components eliminate code duplication across modification types
- **Consistent UX**: All modification types follow identical interaction patterns
- **Easy Maintenance**: Changes to common patterns only need to be made in one place
- **Type Safety**: TypeScript ensures proper prop types for all reusable components
- **Scalability**: New modification types can be implemented quickly using existing components

#### Description Generation System
- **Dynamic Generation**: Descriptions automatically generated based on selected options
- **Reference Image Integration**: Different description patterns for reference image vs option selection modes
- **Smart Concatenation**: Multiple options intelligently combined into readable descriptions
- **Custom Description Support**: Additional custom descriptions appended to generated descriptions
- **Real-time Updates**: Descriptions update immediately as users change selections
- **Dependency Management**: Comprehensive useEffect dependency arrays ensure proper updates

#### Adding New Modification Types - Implementation Checklist
1. **Backend Prompt Template** (`server/promptTemplates.ts`):
   - Create new template function following `(params: PromptParams) => string` signature
   - Include reference image handling logic
   - Add modification-specific quality instructions
   - Register in `promptTemplates` object export

2. **Schema Updates** (`shared/schema.ts`):
   - Add new type to `modificationType` enum if not already present
   - Ensure type is included in `ModificationType` union type

3. **Frontend Modification Definition** (`client/src/components/CreateVisualizationFlow.tsx`):
   - Add entry to `modificationTypes` array with appropriate icon and description
   - Set `enabled: true` to activate the modification type

4. **State Variables** (`client/src/components/CreateVisualizationFlow.tsx`):
   - Add state variables following naming convention: `[modificationType][OptionType]`
   - Include reference image state: `use[ModificationType]ReferenceImage`, `selected[ModificationType]ReferenceImageId`
   - Add custom description state: `custom[ModificationType]Description`

5. **Option Data Definitions** (`client/src/components/CreateVisualizationFlow.tsx`):
   - Define option arrays with `{ id, name, description }` format
   - Include helper functions for option name resolution
   - Follow existing naming patterns for consistency

6. **UI Implementation** (`client/src/components/CreateVisualizationFlow.tsx`):
   - Add case in `renderModificationOptions()` function for the new type
   - Use reusable components: `ReferenceImageToggle`, `OptionSelector`, `ReferenceImageSelector`, `CustomDescriptionField`
   - Implement conditional rendering based on reference image toggle
   - Include all relevant option categories for the modification type

7. **Description Generation** (`client/src/components/CreateVisualizationFlow.tsx`):
   - Add case in description generation useEffect for the new modification type
   - Implement both reference image and option selection description patterns
   - Add all new state variables to useEffect dependency array

8. **Validation Logic** (`client/src/components/CreateVisualizationFlow.tsx`):
   - Update `canProceedToNextStep()` function to validate required fields
   - Implement appropriate validation rules (at least one option OR reference image)
   - Ensure proper error handling for incomplete selections

9. **Completion Logic** (`client/src/components/CreateVisualizationFlow.tsx`):
   - Add case in `handleComplete()` function to handle the new modification type
   - Structure options data appropriately for backend processing
   - Set primary reference image if applicable
   - Follow existing option structuring patterns

10. **Testing**:
    - Test complete flow from selection to image generation
    - Verify prompt generation produces expected results
    - Test with and without reference images
    - Validate error handling and edge cases
    - Ensure description generation works correctly
    - Test all option combinations

### Project Management
- **Draft Handling**: Unified approach - save new projects as 'draft' until explicitly submitted
- **Status Flow**: Projects flow from 'draft' → 'processing' → 'completed'
- **Database Design**: Eliminate separate draft tables in favor of unified project handling
- **Draft-to-Project Transition**: When continuing from a draft, show "Project ready" instead of "Project created" + "Draft deleted"
- **User Experience**: Seamless transition from draft to project without confusing "deletion" messaging
- **Silent Cleanup**: Remove draft records silently during project creation to avoid unnecessary notifications

### Draft Deletion Consistency Standards
- **User-Initiated Deletions**: Always show toast notifications when users explicitly delete drafts (DraftCard, manual delete buttons)
- **Automatic Cleanup**: Use silent deletion mutations for system cleanup (project creation, old draft cleanup, transition flows)
- **Mutation Pattern**: Implement both `deleteDraftMutation` (with toast) and `deleteDraftSilentMutation` (without toast) in components that need both
- **Toast Messages**:
  - User deletions: "Draft deleted - Your draft has been deleted"
  - Automatic cleanup: No toast notifications
  - Project transitions: Context-appropriate messages ("Project ready" vs "Project created")
- **Error Handling**: Always show error toasts for failed deletions, regardless of context

### Draft Auto-Save Standards
- **Progressive Updates**: Auto-save should update existing drafts instead of creating multiple drafts
- **Draft ID Tracking**: Use `currentDraftId` state to track which draft to update during auto-save
- **Update Logic**: Use `updateDraftMutation` with silent flag for auto-save operations to avoid showing toast notifications
- **Draft Creation**: Only create new drafts when no `currentDraftId` exists, then set the ID for future updates
- **ID Management**: Clear `currentDraftId` when starting new projects, deleting drafts, or creating projects
- **Frequency Control**: Prevent auto-save if last save was within 30 seconds to avoid excessive API calls
- **User Experience**: Auto-save should be invisible to users - no notifications or interruptions during typing

### Subscription Management Standards
- **Plan Limits**: Define clear limits for each subscription plan (projects, images, features)
- **Usage Enforcement**: Use middleware (`requireSubscription`, `checkProjectLimit`, `checkImageLimit`) to enforce limits
- **Stripe Integration**: Create customers and subscriptions through Stripe API with proper error handling
- **Database Sync**: Maintain subscription state in local database synchronized with Stripe
- **Period Management**: Track billing periods and handle subscription renewals
- **Cancellation Handling**: Support immediate and end-of-period cancellations
- **Upgrade/Downgrade**: Handle plan changes with proper proration and limit adjustments

### Coupon System Standards
- **Multi-Use Support**: Support multiple users using same coupon code (not one-time per code)
- **Coupon Types**: Support one-time discounts, ongoing discounts, and free month promotions
- **Stripe Validation**: Validate coupons through Stripe before local storage to minimize admin work
- **Database Sync**: Store coupon information locally after Stripe validation
- **Redemption Tracking**: Track usage counts and enforce maximum redemption limits
- **Expiration Handling**: Support time-based expiration and usage-based limits
- **Error Handling**: Graceful handling of invalid, expired, or exhausted coupons

### Contact and Newsletter Systems
- **Contact Forms**: Store submissions in database and send email notifications
- **Email Integration**: Send both admin notifications and user auto-replies
- **Newsletter Management**: Track subscription status and prevent duplicate subscriptions
- **Validation**: Use Zod schemas for all form inputs with proper error handling
- **Database Storage**: Store all contact submissions and newsletter subscriptions
- **Error Resilience**: Continue processing even if email sending fails

### Usage Tracking Standards
- **Project Counting**: Projects are counted only when they transition from 'draft' to 'processing' status, not when initially created as drafts
- **Image Counting**: Images are counted on submission to OpenAI API (to prevent abuse), then decremented if generation fails (to ensure fair billing)
- **Abuse Prevention**: Usage is incremented immediately when images are submitted for processing to prevent users from submitting multiple requests simultaneously
- **Fair Billing**: Failed OpenAI API calls result in usage decrement to ensure users only pay for successful generations
- **Error Resilience**: Usage tracking continues even if individual tracking operations fail, with comprehensive logging for debugging
- **Validation**: All usage tracking methods include input validation (non-empty user IDs, positive counts)
- **Database Safety**: Image count decrements cannot go below zero to prevent negative usage counts
- **Logging**: Comprehensive logging for all usage operations including increments, decrements, and failures
- **Reference Images**: Reference image uploads do not count toward usage - only generated images count
- **Upload vs Generation**: Before images and reference images uploaded to projects do not count toward usage until they are successfully processed by OpenAI

## Error Handling Standards

### Authentication Errors
- **Missing Headers**: Provide world-class error handling when authorization headers are missing
- **User Experience**: Clear, helpful error messages for authentication issues
- **Fallback Behavior**: Graceful degradation when authentication services are unavailable

### Database Connection Errors
- **Retry Logic**: Implement retry logic for database operations with exponential backoff (3 attempts max)
- **Network Errors**: Detect and handle "fetch failed" errors with appropriate user messaging
- **Health Checks**: Use `/api/health` endpoint to monitor database connectivity status
- **User Feedback**: Provide clear error messages when database is unavailable:
  - Status 503 for service unavailable
  - Helpful error messages explaining connectivity issues
  - Suggestions for users (check internet connection, try again later)
- **Graceful Degradation**: Continue serving static content when database is unavailable

### Health Monitoring and System Reliability
- **Health Endpoint**: Implement `/api/health` endpoint for system status monitoring
- **Database Connectivity**: Monitor database connection status and response times
- **External Service Health**: Check connectivity to Stripe, Clerk, and OpenAI APIs
- **Performance Metrics**: Track API response times and database query performance
- **Error Rate Monitoring**: Monitor error rates across different system components
- **Uptime Tracking**: Track system uptime and availability metrics
- **Alert Thresholds**: Define clear thresholds for performance and error rate alerts

### System Resilience Patterns
- **Circuit Breaker**: Implement circuit breaker pattern for external service calls
- **Graceful Degradation**: Provide fallback functionality when services are unavailable
- **Timeout Management**: Set appropriate timeouts for all external service calls
- **Resource Limits**: Implement proper resource limits and rate limiting
- **Memory Management**: Monitor memory usage and implement proper cleanup
- **Connection Pooling**: Use connection pooling for database and external services
- **Load Balancing**: Design for horizontal scaling and load distribution

### Database Schema Standards
- **User ID Types**: Always use TEXT type for user_id fields to support Clerk authentication
- **UUID vs TEXT**: Clerk user IDs are strings (e.g., `user_2wGQABv0jGkkg4Sr5V9BhdKEiPA`), not UUIDs
- **Migration Requirements**: When changing from UUID to TEXT:
  1. Drop foreign key constraints first
  2. Alter column types
  3. Recreate foreign key constraints
- **Schema Validation**: Always verify schema changes work with Clerk user IDs before deployment
- **Error Handling**: Capture and log actual Supabase errors, not just generic "Failed to create" messages

### Database Migration Standards
- **Migration Files**: Store migrations in `supabase/migrations/` with timestamp prefixes
- **Migration Scripts**: Use `scripts/apply-migration.sh` for applying migrations
- **Schema Updates**: Use `scripts/update-schema.ts` for programmatic schema updates
- **Rollback Strategy**: Always include rollback instructions in migration comments
- **Testing**: Test migrations on development database before production deployment
- **Documentation**: Document breaking changes and required application updates

### Database Logging and Monitoring
- **Performance Logging**: Use `logDatabaseOperation` wrapper for all database operations
- **Context Loggers**: Use `createDbLogger` for operation-specific logging contexts
- **Timing Thresholds**: Log slow queries (>1000ms), medium queries (>500ms), fast queries (debug only)
- **Error Details**: Include error codes, details, and context in database error logs
- **Operation Types**: Separate logging for query, insert, update, delete, and transaction operations
- **Performance Metrics**: Track database operation performance for optimization

## Documentation Standards

### Code Comments
- **Inline Documentation**: Include meaningful comments for complex logic
- **API Documentation**: Document all API endpoints and their expected inputs/outputs
- **Setup Instructions**: Maintain clear setup and running instructions in README.md

### Change Documentation
- **Guidelines Update**: Always update this file when establishing new patterns or standards
- **Consistency**: Ensure all team members follow the same standards for maintainability

---

## Complete Implementation Status (May 2025)

### Fully Implemented Modification Types (9 Total)
✅ **Replace Floor** - Material/color selection with reference image support
✅ **Change Wall Color** - Color/finish selection with reference image support
✅ **Update Cabinets** - Material/color/style selection with reference image support
✅ **Kitchen Remodel** - Cabinet/countertop/appliance/color scheme options with reference image support
✅ **Bathroom Remodel** - Fixture/tile/vanity/color scheme options with reference image support
✅ **Interior Painting** - Wall/ceiling/accent/finish options with reference image support
✅ **Lighting Upgrades** - Fixture/type/ambiance/smart controls with reference image support
✅ **Exterior Repainting** - Main/trim/accent/door color options with reference image support
✅ **Custom Modification** - Free-form description with optional reference images

### Implementation Completeness
- ✅ **Backend**: All 9 modification types have dedicated prompt templates
- ✅ **Frontend**: All 9 modification types have complete UI implementations
- ✅ **State Management**: 150+ state variables properly managed with consistent naming
- ✅ **Validation**: All modification types have appropriate validation rules
- ✅ **Description Generation**: Dynamic descriptions for all modification types
- ✅ **Reference Images**: Full reference image support for all modification types
- ✅ **Reusable Components**: 4 modular components eliminate code duplication
- ✅ **Icons**: All modification types have appropriate Material Design icons
- ✅ **Error Handling**: Comprehensive validation and error handling throughout

### Architecture Quality
- **World-Class Maintainability**: Modular components and consistent patterns
- **Type Safety**: Full TypeScript implementation with proper type definitions
- **Code Reusability**: Reusable components used across all modification types
- **Scalability**: Easy to add new modification types following established patterns
- **User Experience**: Consistent interaction patterns across all modification types
- **Performance**: Efficient state management and optimized re-rendering

---

## Create Page Architecture Summary

The Renovision Studio create page uses a sophisticated, modular architecture that separates concerns between UI components, prompt generation, and backend processing. This design allows for easy addition of new modification types while maintaining consistency and code reusability.

**Key Architecture Benefits:**
- **Modularity**: Each modification type is self-contained with its own prompt template and UI logic
- **Extensibility**: New modification types can be added by following the established patterns
- **Maintainability**: Centralized prompt templates make it easy to refine AI instructions
- **Consistency**: Unified data flow ensures all modification types work the same way
- **Reference Image Support**: Built-in support for reference images across all modification types
- **Reusable Components**: Modular UI components eliminate code duplication and ensure consistency

---

## Summary of Key Standards

### Architecture Principles
1. **Type Safety First**: TypeScript throughout, shared schemas, runtime validation with Zod
2. **API-First Design**: RESTful APIs with consistent patterns, middleware, and error handling
3. **Component Composition**: Reusable, accessible components built on Radix UI with cva variants
4. **Performance Optimization**: Code splitting, caching, query optimization, and bundle analysis
5. **Security by Design**: Input validation, secure authentication, proper error handling, and file upload security

### Development Workflow Standards
1. **Environment Setup**: Use nvm for Node.js isolation, automated setup scripts, and proper dependency management
2. **Code Organization**: Feature-based structure, consistent naming conventions, and proper import/export patterns
3. **Testing Strategy**: Comprehensive test coverage with Jest, Playwright, and visual regression testing
4. **Build Process**: Vite for frontend, esbuild for backend, with proper TypeScript configuration
5. **External Integrations**: Structured patterns for Stripe, OpenAI, Clerk, Supabase, and email services

### Infrastructure and Reliability Standards
1. **Logging and Debugging**: Structured logging with category-based debug configuration and performance monitoring
2. **Error Handling and Resilience**: Retry mechanisms, circuit breakers, and graceful degradation patterns
3. **Health Monitoring**: System health endpoints, performance metrics, and uptime tracking
4. **Email Integration**: SMTP configuration, template management, and error-resilient email processing
5. **Configuration Management**: Comprehensive configuration files with TypeScript support and environment variables

### Quality Assurance Standards
1. **Automated Testing**: Unit tests (Jest), API tests, E2E tests (Playwright), and visual regression tests
2. **Error Handling**: Centralized error handling, structured logging, and user-friendly error messages
3. **Performance Monitoring**: Database operation logging, request timing, and slow query detection
4. **Code Quality**: ESLint, TypeScript strict mode, and consistent code formatting
5. **Security Practices**: Input validation, authentication middleware, and secure file handling

### Domain-Specific Standards
1. **Renovation AI**: Comprehensive prompt template system with modification type support
2. **Image Processing**: Structured image handling with normalization, validation, error image generation
3. **User Experience**: Mobile-first design, consistent navigation, and accessibility compliance
4. **Business Logic**: Subscription management, usage tracking, coupon system, and webhook processing
5. **Data Management**: Shared schema architecture, type safety, database optimization, and migration management

### Infrastructure and Automation Standards
1. **Script Automation**: Comprehensive script suite for development, testing, and deployment operations
2. **Database Management**: Migration scripts, schema updates, performance logging, and monitoring
3. **Test Infrastructure**: Fixture management, mock data generation, and comprehensive test coverage
4. **Webhook Processing**: Stripe webhook handling with signature verification and idempotent processing
5. **Error Handling**: Canvas-generated error images, comprehensive logging, and graceful degradation

---

*Last Updated: January 2025*
*This document should be updated whenever new standards are established or existing ones are modified.*

*This comprehensive guide ensures consistent, maintainable, and scalable development practices across the entire Renovision Studio project.*
