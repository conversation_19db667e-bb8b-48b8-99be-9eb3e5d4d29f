/** @type {import('ts-jest').JestConfigWithTsJest} */
module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  transform: {
    '^.+\\.tsx?$': [
      'ts-jest',
      {
        tsconfig: 'tsconfig.test.json',
        useESM: false,
      },
    ],
  },
  moduleNameMapper: {
    '@/(.*)': '<rootDir>/client/src/$1',
    '@shared/(.*)': '<rootDir>/shared/$1',
    '^node-fetch$': '<rootDir>/tests/__mocks__/node-fetch.ts',
    '^openai$': '<rootDir>/tests/__mocks__/openai.ts',
    '^stripe$': '<rootDir>/tests/__mocks__/stripe.ts',
  },
  testMatch: ['**/tests/api/**/*.test.ts', '**/tests/api/**/*.test.js'],
  setupFilesAfterEnv: ['<rootDir>/tests/setup.ts'],
  verbose: true,
  transformIgnorePatterns: [
    'node_modules/(?!(node-fetch|fetch-blob|data-uri-to-buffer|formdata-polyfill)/)'
  ],
  // Remove deprecated globals configuration
  // extensionsToTreatAsEsm: ['.ts'],
};
