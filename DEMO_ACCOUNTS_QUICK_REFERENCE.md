# Demo Accounts - Quick Reference

## 🚀 Quick Login Instructions

1. **Go to sign-in page**
2. **Enter username**: `acquireuser7` (or any number 7-16)
3. **Verification code**: `424242`
4. **Done!** ✅

## 📋 Available Demo Accounts

| Username | Name | Role/Specialty | Plan |
|----------|------|----------------|------|
| `acquireuser7` | <PERSON> | Interior Design Enthusiast | **Professional** ⭐ |
| `acquireuser8` | <PERSON> | Sustainable Design Expert | Free |
| `acquireuser9` | <PERSON> | Architect & Designer | Free |
| `acquireuser10` | <PERSON> | DIY Blogger & Project Manager | Free |
| `acquireuser11` | <PERSON> | Real Estate Developer | Free |
| `acquireuser12` | <PERSON> | Kitchen & Bath Contractor | Free |
| `acquireuser13` | <PERSON> | Home Staging Consultant | Free |
| `acquireuser14` | <PERSON> | Interior Decorator | Free |
| `acquireuser15` | <PERSON> | Construction Project Manager | Free |
| `acquireuser16` | <PERSON> | Sustainable Design Advocate | Free |

## 🔑 Authentication Methods

### Option 1: Username
- Username: `acquireuser{7-16}`
- Code: `424242`

### Option 2: Email
- Email: `acquireuser{7-16}+<EMAIL>`
- Code: `424242`

## 🛠️ Management Commands

### Initial Setup
```bash
# Complete demo account setup (run in order)
npm run create-demo-accounts      # Create all 10 accounts
npm run populate-demo-data        # Add sample projects/data
npm run upgrade-demo-subscription # Upgrade acquireuser7 to professional
```

### Verification & Maintenance
```bash
# Check account status
npm run check-users               # View all accounts and usage
npm run test-demo-auth           # Test authentication system
npm run verify-demo-subscription # Check subscription details

# Fix issues if needed
npm run fix-demo-subscription    # Fix subscription status
npm run test-subscription-fix    # Test subscription system
```

## ⚠️ Important Notes

- **Test Mode Only**: Works in development environments
- **No Real Emails**: Uses Clerk's test email format
- **Universal Code**: `424242` works for all demo accounts
- **Realistic Profiles**: Each account has unique professional background

## ⭐ Premium Demo Account

**`acquireuser7`** has a **Professional Plan** subscription:
- ✅ 10 projects per month (vs 3 on free)
- ✅ 5 images per project (vs 3 on free)
- ✅ All premium features enabled
- ✅ Valid for 1 month from today
- 🎯 Perfect for demonstrating premium features!

## 🎯 Use Cases

- **Feature Testing**: Test user flows and functionality
- **Client Demos**: Professional personas for presentations
- **Premium Feature Demos**: Use acquireuser7 to showcase paid features
- **Subscription Testing**: Test free vs professional plan differences
- **Multi-user Testing**: Different user types and scenarios
- **Authentication Testing**: Verify sign-in/sign-up processes

## 🚨 Important Notes

- **No "Syncing" Issues**: Subscription system properly handles demo accounts
- **Production Safe**: Demo accounts only work in development/test environments
- **Automatic Setup**: Scripts handle all technical details automatically
- **Easy Maintenance**: Simple commands to fix any issues that arise
