import { QueryClient, QueryFunction } from "@tanstack/react-query";
import { getAuthToken, apiGet } from './api-client';

// Configure API logging
export const logger = {
  debug: (message: string, ...args: any[]) => {
    if (process.env.NODE_ENV === 'development') {
      console.debug(`[API-CLIENT] ${message}`, ...args);
    }
  },
  info: (message: string, ...args: any[]) => {
    console.info(`[API-CLIENT] ${message}`, ...args);
  },
  warn: (message: string, ...args: any[]) => {
    console.warn(`[API-CLIENT] ${message}`, ...args);
  },
  error: (message: string, ...args: any[]) => {
    console.error(`[API-CLIENT] ${message}`, ...args);
  }
};

async function throwIfResNotOk(res: Response) {
  if (!res.ok) {
    let errorData;
    try {
      // Try to parse as JSON first
      errorData = await res.json();

      // Add status code to the error object
      errorData.status = res.status;

      logger.error(`API error: ${res.status}`, {
        url: res.url,
        error: errorData
      });

      // Throw the structured error object
      throw errorData;
    } catch (e) {
      // If JSON parsing fails, try to get text
      try {
        const errorText = await res.text();
        logger.error(`API error: ${res.status}`, {
          url: res.url,
          message: errorText || res.statusText
        });

        // Throw a structured error object
        throw {
          error: {
            message: errorText || res.statusText,
            type: 'UNKNOWN_ERROR'
          },
          status: res.status
        };
      } catch (textError) {
        // If all else fails, throw a generic error
        logger.error(`API error: ${res.status}`, {
          url: res.url,
          statusText: res.statusText
        });

        throw {
          error: {
            message: res.statusText || 'Unknown error',
            type: 'UNKNOWN_ERROR'
          },
          status: res.status
        };
      }
    }
  }
}

// Export the apiRequest function for backward compatibility
export async function apiRequest(
  method: string,
  url: string,
  data?: unknown | undefined,
): Promise<Response> {
  // Import dynamically to avoid circular dependencies
  const { apiClient } = await import('./api-client');

  // Use our centralized API client
  const response = await apiClient(method, url, data, { retries: 1 });

  // Convert the JSON response back to a Response object for backward compatibility
  return new Response(JSON.stringify(response), {
    headers: { 'Content-Type': 'application/json' },
    status: 200
  });
}

type UnauthorizedBehavior = "returnNull" | "throw";
export const getQueryFn: <T>(options: {
  on401: UnauthorizedBehavior;
}) => QueryFunction<T> =
  ({ on401: unauthorizedBehavior }) =>
  async ({ queryKey }) => {
    const url = queryKey[0] as string;

    try {
      // Use our centralized API client
      return await apiGet<T>(url, {
        retries: 1 // Allow one retry for auth issues
      });
    } catch (error) {
      // Handle 401 errors according to the specified behavior
      if (error instanceof Error &&
          error.message.includes('Authentication failed') &&
          unauthorizedBehavior === "returnNull") {
        logger.warn(`Unauthorized access to ${url}, returning null as configured`);
        return null;
      }

      // Re-throw all other errors
      throw error;
    }
  };

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      queryFn: getQueryFn({ on401: "throw" }),
      refetchInterval: false,
      refetchOnWindowFocus: false,
      staleTime: 5 * 60 * 1000, // 5 minutes - keep data fresh but reduce loading screens
      cacheTime: 10 * 60 * 1000, // 10 minutes - keep data in cache longer
      retry: false,
      // Disable suspense mode to prevent synchronous suspension errors
      suspense: false,
      // Use cached data while revalidating
      keepPreviousData: true,
    },
    mutations: {
      retry: false,
    },
  },
});

// Add a global type definition for the Clerk getToken function
declare global {
  interface Window {
    __clerk_getToken?: (options?: { skipCache?: boolean }) => Promise<string | null>;
  }
}
