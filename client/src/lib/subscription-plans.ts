/**
 * Subscription plan limits and details
 * This file contains the client-side representation of the subscription plans
 * that match the server-side configuration.
 */

export const PLAN_LIMITS = {
  starter: {
    monthly: {
      price: 17.99,
      projects: 3,
      imagesPerProject: 3,
      displayName: "Starter",
      features: [
        "3 projects per month",
        "3 images per project",
        "Access to all basic renovation styles",
        "Standard quality image generation",
        "Email support"
      ]
    },
    annual: {
      price: 14.99,
      annualPrice: 179.88, // 14.99 * 12 = 179.88 (20% discount from monthly)
      projects: 3,
      imagesPerProject: 3,
      displayName: "Starter",
      features: [
        "3 projects per month",
        "3 images per project",
        "Access to all basic renovation styles",
        "Standard quality image generation",
        "Email support",
        "20% discount with annual billing"
      ]
    }
  },
  professional: {
    monthly: {
      price: 58.80,
      projects: 10,
      imagesPerProject: 5,
      displayName: "Professional",
      features: [
        "10 projects per month",
        "5 images per project",
        "Access to all renovation styles",
        "High quality image generation",
        "Priority email support",
        "Custom renovation presets"
      ]
    },
    annual: {
      price: 49,
      annualPrice: 588.00, // 49 * 12 = 588.00 (20% discount from monthly)
      projects: 10,
      imagesPerProject: 5,
      displayName: "Professional",
      features: [
        "10 projects per month",
        "5 images per project",
        "Access to all renovation styles",
        "High quality image generation",
        "Priority email support",
        "Custom renovation presets",
        "20% discount with annual billing"
      ]
    }
  }
};

/**
 * Get plan details by ID and billing cycle
 */
export function getPlanDetails(planId: string, billingCycle: 'monthly' | 'annual') {
  if (!planId || !billingCycle) return null;

  const plan = PLAN_LIMITS[planId as keyof typeof PLAN_LIMITS];
  if (!plan) return null;

  return plan[billingCycle];
}

/**
 * Format price for display
 */
export function formatPrice(price: number) {
  return new Intl.NumberFormat('en-AU', {
    style: 'currency',
    currency: 'AUD',
    minimumFractionDigits: 2
  }).format(price);
}

/**
 * Get formatted billing cycle text
 */
export function getBillingCycleText(billingCycle: string) {
  return billingCycle === 'annual' ? 'Annually' : 'Monthly';
}
