/**
 * Stripe client-side initialization
 * This file provides utilities for working with <PERSON><PERSON> on the client side
 */

// Initialize Stripe with the publishable key
let stripePromise: Promise<any> | null = null;

// Track in-flight checkout requests to prevent duplicates
let checkoutInProgress = false;
let lastCheckoutTimestamp = 0;
const CHECKOUT_COOLDOWN_MS = 2000; // 2 seconds cooldown between checkout attempts

/**
 * Get the Stripe instance (lazy-loaded)
 */
export const getStripe = async () => {
  if (!stripePromise) {
    // Dynamically import the Stripe.js library
    const { loadStripe } = await import('@stripe/stripe-js');

    // Get the publishable key from environment variables
    const publishableKey = import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY;

    if (!publishableKey) {
      console.error('Stripe publishable key is missing. Make sure it is set in your environment variables.');
      throw new Error('Stripe publishable key is missing');
    }

    // Initialize Stripe with the publishable key
    stripePromise = loadStripe(publishableKey as string);
  }

  return stripePromise;
};

/**
 * Create a checkout session and redirect to Stripe Checkout
 */
export const createCheckoutSession = async (
  planId: string,
  billingCycle: 'monthly' | 'annual',
  token: string,
  couponCode?: string
) => {
  try {
    // Prevent duplicate checkout requests
    const now = Date.now();
    if (checkoutInProgress) {
      console.warn('Checkout already in progress, ignoring duplicate request');
      return {
        success: false,
        error: 'A checkout is already in progress. Please wait...'
      };
    }

    // Check if we're within the cooldown period
    if (now - lastCheckoutTimestamp < CHECKOUT_COOLDOWN_MS) {
      console.warn('Checkout request too frequent, enforcing cooldown');
      return {
        success: false,
        error: 'Please wait a moment before trying again'
      };
    }

    // Set flags to prevent duplicate requests
    checkoutInProgress = true;
    lastCheckoutTimestamp = now;

    try {
      // Call the API to create a checkout session
      console.log(`Creating checkout session for plan: ${planId}, billing cycle: ${billingCycle}`);

      // Add a unique request ID to help identify duplicate requests on the server
      const requestId = `${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;

      const response = await fetch('/api/create-checkout-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
          'X-Request-ID': requestId
        },
        body: JSON.stringify({
          planId,
          billingCycle,
          requestId,
          couponCode: couponCode || undefined
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: 'Failed to create checkout session' }));

        // Check for rate limit errors
        if (response.status === 429) {
          console.warn('Stripe rate limit exceeded. Please try again in a few seconds.');
          throw new Error('Stripe rate limit exceeded. Please try again in a few seconds.');
        }

        throw new Error(errorData.message || `Failed to create checkout session: ${response.status}`);
      }

      const { url } = await response.json();

      if (!url) {
        throw new Error("No checkout URL returned from server");
      }

      // Redirect to Stripe Checkout
      window.location.href = url;

      return { success: true };
    } finally {
      // Reset the flag after a short delay to allow for page navigation
      setTimeout(() => {
        checkoutInProgress = false;
      }, 5000);
    }
  } catch (error: any) {
    console.error('Error creating checkout session:', error);
    // Reset the flag immediately on error
    checkoutInProgress = false;

    return {
      success: false,
      error: error.message || 'An unexpected error occurred'
    };
  }
};
