import { logger } from './queryClient';

// Cache for auth token to avoid excessive token fetching
interface TokenCache {
  token: string | null;
  timestamp: number;
}

let tokenCache: TokenCache = {
  token: null,
  timestamp: 0
};

// Check if a token is expired
function isTokenExpired(token: string | null): boolean {
  if (!token) return true;

  try {
    // Decode the JWT to check expiration
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
      return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
    }).join(''));

    const { exp } = JSON.parse(jsonPayload);
    if (!exp) return false;

    // Check if token is expired (with 30 second buffer)
    return Date.now() >= (exp * 1000) - 30000;
  } catch (e) {
    logger.error('Error checking token expiration:', e);
    return true; // If we can't decode the token, assume it's expired
  }
}

// Get the auth token with caching to reduce authentication overhead
export async function getAuthToken(): Promise<string | null> {
  const now = Date.now();

  // If we have a cached token that's less than 5 minutes old and not expired, use it
  if (tokenCache.token &&
      (now - tokenCache.timestamp < 5 * 60 * 1000) &&
      !isTokenExpired(tokenCache.token)) {
    logger.debug('Using cached token');
    return tokenCache.token;
  }

  // Try to get token from sessionStorage first (fastest)
  try {
    const authCache = sessionStorage.getItem('renovate-ai-auth-cache');
    if (authCache) {
      const parsed = JSON.parse(authCache);
      if (parsed.authToken &&
          parsed.lastUpdated &&
          (now - parsed.lastUpdated < 5 * 60 * 1000) &&
          !isTokenExpired(parsed.authToken)) {
        tokenCache = {
          token: parsed.authToken,
          timestamp: now
        };
        logger.debug('Got token from session storage');
        return parsed.authToken;
      }
    }
  } catch (e) {
    logger.warn('Failed to get token from sessionStorage', e);
  }

  // Try to get Clerk's getToken from the global window (set by ClerkProvider)
  if (window && typeof window.Clerk && window.Clerk.session) {
    try {
      const token = await window.Clerk.session.getToken();
      if (token) {
        logger.debug('Got fresh Clerk token from Clerk.session');
        // Update cache
        tokenCache = {
          token,
          timestamp: now
        };

        // Store in localStorage and sessionStorage for persistence
        localStorage.setItem('clerk-auth-token', token);

        try {
          const authCache = sessionStorage.getItem('renovate-ai-auth-cache') || '{}';
          const parsed = JSON.parse(authCache);
          parsed.authToken = token;
          parsed.lastUpdated = now;
          sessionStorage.setItem('renovate-ai-auth-cache', JSON.stringify(parsed));
        } catch (e) {
          logger.warn('Failed to update sessionStorage', e);
        }

        return token;
      }
    } catch (err) {
      logger.error('Error getting token from Clerk.session:', err);
    }
  } else if (window && typeof window.__clerk_getToken === 'function') {
    try {
      const token = await window.__clerk_getToken({ skipCache: true });
      if (token) {
        logger.debug('Got fresh Clerk token from __clerk_getToken');
        // Update cache
        tokenCache = {
          token,
          timestamp: now
        };

        // Store in localStorage and sessionStorage for persistence
        localStorage.setItem('clerk-auth-token', token);

        try {
          const authCache = sessionStorage.getItem('renovate-ai-auth-cache') || '{}';
          const parsed = JSON.parse(authCache);
          parsed.authToken = token;
          parsed.lastUpdated = now;
          sessionStorage.setItem('renovate-ai-auth-cache', JSON.stringify(parsed));
        } catch (e) {
          logger.warn('Failed to update sessionStorage', e);
        }

        return token;
      }
    } catch (err) {
      logger.error('Error getting token from __clerk_getToken:', err);
    }
  }

  // Fallback to localStorage
  const token = localStorage.getItem('clerk-auth-token');
  if (token && !isTokenExpired(token)) {
    logger.debug('Got Clerk token from localStorage');
    // Update cache
    tokenCache = {
      token,
      timestamp: now
    };
    return token;
  } else if (token && isTokenExpired(token)) {
    logger.warn('Token from localStorage is expired');
  }

  return null;
}

// Centralized API client with authentication
export async function apiClient<T = any>(
  method: string,
  url: string,
  data?: unknown,
  options: {
    retries?: number;
    headers?: Record<string, string>;
    skipAuth?: boolean;
  } = {}
): Promise<T> {
  const {
    retries = 1,
    headers: customHeaders = {},
    skipAuth = false
  } = options;

  // Start timing the request
  const startTime = performance.now();

  // Prepare headers
  const headers: Record<string, string> = {
    ...customHeaders
  };

  // Add content type for JSON requests
  if (data && !(data instanceof FormData)) {
    headers["Content-Type"] = "application/json";
  }

  // Add auth token if available and not skipped
  if (!skipAuth) {
    const token = await getAuthToken();
    if (token && typeof token === 'string' && token.trim() !== '') {
      headers["Authorization"] = `Bearer ${token}`;
      logger.debug(`Adding auth token to ${method} ${url}`);
    } else {
      logger.warn(`No valid auth token available for ${method} ${url}`);
    }
  }

  logger.debug(`Sending ${method} request to ${url}`);

  let lastError: Error | null = null;
  let currentRetry = 0;

  while (currentRetry <= retries) {
    try {
      const res = await fetch(url, {
        method,
        headers,
        body: data instanceof FormData ? data : (data ? JSON.stringify(data) : undefined),
        credentials: "include", // Keep this for backward compatibility
      });

      // Log request timing
      const endTime = performance.now();
      logger.debug(`${method} ${url} completed in ${Math.round(endTime - startTime)}ms with status ${res.status}`);

      // Handle authentication errors
      if (res.status === 401) {
        // Special handling for subscription endpoints - return default data instead of error
        if (url.includes('/api/subscription')) {
          logger.warn(`Authentication failed for ${method} ${url}, but this is a subscription endpoint - returning default data`);

          // For subscription endpoints, return default free tier data
          if (url.includes('/api/debug/subscription')) {
            return {
              rawSubscription: null,
              rawUsage: null,
              planLimits: null,
              authStatus: 'unauthenticated'
            } as T;
          } else {
            return {
              subscription: null,
              usage: null,
              limits: {
                projects: 1,
                imagesPerProject: 1,
                price: 0
              }
            } as T;
          }
        }

        // For other endpoints, try to refresh token and retry
        if (currentRetry < retries) {
          logger.warn(`Authentication failed for ${method} ${url}, refreshing token and retrying...`);

          // Force token refresh
          if (window && typeof window.__clerk_getToken === 'function') {
            try {
              const newToken = await window.__clerk_getToken({ skipCache: true });
              if (newToken) {
                // Update token cache
                tokenCache = {
                  token: newToken,
                  timestamp: Date.now()
                };
                localStorage.setItem('clerk-auth-token', newToken);

                // Update session storage
                try {
                  const authCache = sessionStorage.getItem('renovate-ai-auth-cache') || '{}';
                  const parsed = JSON.parse(authCache);
                  parsed.authToken = newToken;
                  parsed.lastUpdated = Date.now();
                  sessionStorage.setItem('renovate-ai-auth-cache', JSON.stringify(parsed));
                } catch (e) {
                  logger.warn('Failed to update sessionStorage', e);
                }

                logger.debug('Token refreshed, retrying request');
                currentRetry++;
                continue; // Retry with new token
              }
            } catch (err) {
              logger.error('Error refreshing token:', err);
            }
          }
        }

        // If we're out of retries or couldn't refresh token
        const errorText = await res.text();
        throw new Error(`Authentication failed: ${errorText}`);
      }

      // Handle other errors
      if (!res.ok) {
        const errorText = await res.text();
        throw new Error(`Request failed with status ${res.status}: ${errorText}`);
      }

      // Parse response
      if (res.headers.get('content-type')?.includes('application/json')) {
        return await res.json();
      } else {
        return (await res.text()) as unknown as T;
      }
    } catch (error) {
      lastError = error as Error;

      // If this is not an auth error or we're out of retries, don't retry
      if (!(error instanceof Error && error.message.includes('Authentication failed')) || currentRetry >= retries) {
        break;
      }

      currentRetry++;
      logger.warn(`Retry ${currentRetry}/${retries} for ${method} ${url}`);

      // Wait a bit before retrying
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  }

  // If we got here, all retries failed
  throw lastError || new Error(`Request to ${url} failed after ${retries} retries`);
}

// Convenience methods
export const apiGet = <T = any>(url: string, options?: Parameters<typeof apiClient>[3]) =>
  apiClient<T>('GET', url, undefined, options);

export const apiPost = <T = any>(url: string, data?: unknown, options?: Parameters<typeof apiClient>[3]) =>
  apiClient<T>('POST', url, data, options);

export const apiPut = <T = any>(url: string, data?: unknown, options?: Parameters<typeof apiClient>[3]) =>
  apiClient<T>('PUT', url, data, options);

export const apiPatch = <T = any>(url: string, data?: unknown, options?: Parameters<typeof apiClient>[3]) =>
  apiClient<T>('PATCH', url, data, options);

export const apiDelete = <T = any>(url: string, options?: Parameters<typeof apiClient>[3]) =>
  apiClient<T>('DELETE', url, undefined, options);
