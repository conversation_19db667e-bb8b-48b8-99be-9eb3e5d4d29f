/**
 * Google Analytics 4 (GA4) utility functions for Renovision Studio
 * 
 * This module provides type-safe GA4 integration with proper environment
 * configuration and debug support for the React application.
 */

import { logger } from '@/utils/logger';

// GA4 Event Types
export interface GA4Event {
  event_name: string;
  event_parameters?: Record<string, any>;
}

export interface GA4PageViewEvent {
  page_title?: string;
  page_location?: string;
  page_path?: string;
  user_id?: string;
  custom_parameters?: Record<string, any>;
}

export interface GA4CustomEvent {
  action: string;
  category?: string;
  label?: string;
  value?: number;
  user_id?: string;
  custom_parameters?: Record<string, any>;
}

// GA4 Configuration
interface GA4Config {
  measurementId: string;
  debugMode: boolean;
  isProduction: boolean;
  isEnabled: boolean;
}

// Get GA4 configuration from environment variables
const createGA4Config = (): GA4Config => {
  const measurementId = import.meta.env.VITE_GA4_MEASUREMENT_ID;
  const debugMode = import.meta.env.VITE_GA4_DEBUG_MODE === 'true';
  const isProduction = import.meta.env.NODE_ENV === 'production';

  return {
    measurementId: measurementId || '',
    debugMode,
    isProduction,
    isEnabled: !!measurementId && isProduction, // Only enable in production with valid ID
  };
};

const config = createGA4Config();

// Declare gtag function for TypeScript
declare global {
  interface Window {
    gtag: (...args: any[]) => void;
    dataLayer: any[];
  }
}

/**
 * Initialize GA4 with the measurement ID
 */
export const initializeGA4 = (): void => {
  if (!config.isEnabled) {
    logger.debug('GA4 initialization skipped', 'analytics', {
      reason: config.measurementId ? 'not in production' : 'no measurement ID',
      measurementId: config.measurementId,
      isProduction: config.isProduction,
    });
    return;
  }

  try {
    // Initialize gtag with measurement ID
    window.gtag('config', config.measurementId, {
      debug_mode: config.debugMode,
      send_page_view: false, // We'll handle page views manually
    });

    logger.info('GA4 initialized successfully', 'analytics', {
      measurementId: config.measurementId,
      debugMode: config.debugMode,
    });
  } catch (error) {
    logger.error('Failed to initialize GA4', 'analytics', { error });
  }
};

/**
 * Track a page view event
 */
export const trackPageView = (params: GA4PageViewEvent = {}): void => {
  if (!config.isEnabled) {
    logger.debug('GA4 page view tracking skipped', 'analytics', params);
    return;
  }

  try {
    const eventParams = {
      page_title: params.page_title || document.title,
      page_location: params.page_location || window.location.href,
      page_path: params.page_path || window.location.pathname,
      ...params.custom_parameters,
    };

    // Add user_id if provided
    if (params.user_id) {
      eventParams.user_id = params.user_id;
    }

    window.gtag('event', 'page_view', eventParams);

    logger.debug('GA4 page view tracked', 'analytics', eventParams);
  } catch (error) {
    logger.error('Failed to track GA4 page view', 'analytics', { error, params });
  }
};

/**
 * Track a custom event
 */
export const trackEvent = (params: GA4CustomEvent): void => {
  if (!config.isEnabled) {
    logger.debug('GA4 event tracking skipped', 'analytics', params);
    return;
  }

  try {
    const eventParams: Record<string, any> = {};

    // Add standard parameters
    if (params.category) eventParams.event_category = params.category;
    if (params.label) eventParams.event_label = params.label;
    if (params.value !== undefined) eventParams.value = params.value;
    if (params.user_id) eventParams.user_id = params.user_id;

    // Add custom parameters
    if (params.custom_parameters) {
      Object.assign(eventParams, params.custom_parameters);
    }

    window.gtag('event', params.action, eventParams);

    logger.debug('GA4 custom event tracked', 'analytics', { action: params.action, ...eventParams });
  } catch (error) {
    logger.error('Failed to track GA4 custom event', 'analytics', { error, params });
  }
};

/**
 * Set user ID for tracking
 */
export const setUserId = (userId: string | null): void => {
  if (!config.isEnabled) {
    logger.debug('GA4 user ID setting skipped', 'analytics', { userId });
    return;
  }

  try {
    window.gtag('config', config.measurementId, {
      user_id: userId,
    });

    logger.debug('GA4 user ID set', 'analytics', { userId });
  } catch (error) {
    logger.error('Failed to set GA4 user ID', 'analytics', { error, userId });
  }
};

/**
 * Track authentication events
 */
export const trackAuthEvent = (action: 'sign_up' | 'sign_in' | 'sign_out', userId?: string): void => {
  trackEvent({
    action,
    category: 'authentication',
    user_id: userId,
    custom_parameters: {
      method: 'clerk', // Since we use Clerk for authentication
    },
  });
};

/**
 * Track project-related events
 */
export const trackProjectEvent = (
  action: 'project_created' | 'project_completed' | 'image_generated' | 'image_generation_failed',
  projectId?: string,
  userId?: string,
  additionalParams?: Record<string, any>
): void => {
  trackEvent({
    action,
    category: 'project',
    label: projectId,
    user_id: userId,
    custom_parameters: {
      project_id: projectId,
      ...additionalParams,
    },
  });
};

/**
 * Track Stripe/payment events
 */
export const trackPaymentEvent = (
  action: 'checkout_started' | 'checkout_completed' | 'subscription_created' | 'subscription_cancelled',
  userId?: string,
  additionalParams?: Record<string, any>
): void => {
  trackEvent({
    action,
    category: 'payment',
    user_id: userId,
    custom_parameters: additionalParams,
  });
};

/**
 * Track error events
 */
export const trackErrorEvent = (
  errorType: string,
  errorMessage: string,
  userId?: string,
  additionalParams?: Record<string, any>
): void => {
  trackEvent({
    action: 'error_occurred',
    category: 'error',
    label: errorType,
    user_id: userId,
    custom_parameters: {
      error_type: errorType,
      error_message: errorMessage,
      ...additionalParams,
    },
  });
};

/**
 * Get current GA4 configuration (for debugging)
 */
export const getGA4Config = (): GA4Config => config;

/**
 * Check if GA4 is enabled and ready
 */
export const isGA4Ready = (): boolean => {
  return config.isEnabled && typeof window !== 'undefined' && typeof window.gtag === 'function';
};
