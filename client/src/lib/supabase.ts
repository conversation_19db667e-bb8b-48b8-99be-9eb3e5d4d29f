import { createClient } from '@supabase/supabase-js';

// Hardcoded for development only - in production these would be environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_DB_KEY || '';

// Create Supabase client with auth completely disabled
export const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    persistSession: false, // Disable auth session persistence
    autoRefreshToken: false, // Disable token refresh
    detectSessionInUrl: false // Disable detecting session in URL
  }
});