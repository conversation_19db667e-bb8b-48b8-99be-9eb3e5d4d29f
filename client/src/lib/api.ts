import type { Project, ProjectWithImages, Image } from "@shared/schema";
import { logger } from "./queryClient";
import { apiGet, apiPost, apiPut, apiDelete, getAuthToken } from './api-client';

// For backward compatibility
export function setAuthTokenGetter(getter: () => Promise<string | null>) {
  // This function is kept for backward compatibility but is no longer needed
  logger.debug('setAuthTokenGetter called - this is deprecated and no longer needed');
}

// Cache for projects to reduce API calls
let projectsCache: {
  data: ProjectWithImages[] | null;
  timestamp: number;
  userId: string | null;
} = {
  data: null,
  timestamp: 0,
  userId: null
};

export async function getProjects(): Promise<ProjectWithImages[]> {
  logger.debug('Starting to fetch projects');

  try {
    // Get the user ID from the token for caching
    const token = await getAuthToken();
    if (!token) {
      logger.warn('No auth token available for fetching projects - returning empty array');
      return [];
    }

    const userId = extractUserIdFromToken(token);

    // Check if we have cached data that's less than 10 seconds old and for the same user
    const now = Date.now();
    if (projectsCache.data &&
        now - projectsCache.timestamp < 10000 &&
        projectsCache.userId === userId) {
      logger.debug('Using cached projects data');
      return projectsCache.data;
    }

    // Use our centralized API client
    const data = await apiGet<ProjectWithImages[]>('/api/projects', {
      headers: {
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
      },
      retries: 1
    });

    logger.debug(`Successfully fetched ${data.length} projects`);

    // Update cache
    projectsCache = {
      data,
      timestamp: now,
      userId
    };

    return data;
  } catch (error) {
    // Handle errors gracefully
    logger.error('Error fetching projects:', error);
    return [];
  }
}

export async function getProject(id: number): Promise<ProjectWithImages> {
  logger.debug(`Fetching project details for ID: ${id}`);

  // Use our centralized API client
  return apiGet<ProjectWithImages>(`/api/projects/${id}`, { retries: 1 });
}

export async function createProject(data: { title: string; description?: string }): Promise<Project> {
  logger.debug('Creating new project');

  // Use our centralized API client
  return apiPost<Project>('/api/projects', data, { retries: 1 });
}

export async function uploadBeforeImages(projectId: number, files: File[]): Promise<Image[]> {
  logger.debug(`Uploading before images for project ${projectId}`);

  const formData = new FormData();
  files.forEach(file => {
    formData.append("images", file);
  });

  // Use our centralized API client
  return apiPost<Image[]>(`/api/projects/${projectId}/images/before`, formData, { retries: 1 });
}

export async function uploadReferenceImages(projectId: number, files: File[]): Promise<Image[]> {
  logger.debug(`Uploading reference images for project ${projectId}`);

  const formData = new FormData();
  files.forEach(file => {
    formData.append("images", file);
  });

  // Use our centralized API client
  return apiPost<Image[]>(`/api/projects/${projectId}/images/reference`, formData, { retries: 1 });
}

export type ModificationType = 'custom' | 'replace_floor' | 'change_wall_color' | 'update_cabinets' | 'change_countertops';

export interface StructuredModification {
  type: ModificationType;
  description?: string;
  referenceImageIds?: number[];
  primaryReferenceImageId?: number; // Specific reference image to use for the modification
  options?: {
    material?: string;
    color?: string;
    style?: string;
    useReferenceImage?: boolean;
    referenceImageId?: number;
    customDescription?: string;
    [key: string]: any;
  };
}

export async function createModification(
  projectId: number,
  data: StructuredModification | {
    description: string;
    referenceImageIds?: number[]
  }
): Promise<any> {
  logger.debug(`Creating modification for project ${projectId}`);

  // Handle legacy format (just description) by converting to structured format
  if (!('type' in data)) {
    const structuredData: any = {
      type: 'custom',
      description: data.description,
      reference_image_ids: data.referenceImageIds // Convert to snake_case
    };
    return apiPost(`/api/projects/${projectId}/modifications`, structuredData, { retries: 1 });
  }

  // Convert camelCase to snake_case for API
  const apiData: any = {
    ...data,
    reference_image_ids: data.referenceImageIds,
    primary_reference_image_id: data.primaryReferenceImageId
  };
  
  // Remove camelCase versions
  delete apiData.referenceImageIds;
  delete apiData.primaryReferenceImageId;

  // Use our centralized API client with structured data
  return apiPost(`/api/projects/${projectId}/modifications`, apiData, { retries: 1 });
}

export async function getReferenceCategoryItems(categoryId: number): Promise<any> {
  logger.debug(`Fetching reference category items for category ${categoryId}`);
  return apiGet(`/api/reference-categories/${categoryId}`, { retries: 1 });
}

export async function getReferenceItems(): Promise<any> {
  logger.debug('Fetching all reference items');
  return apiGet(`/api/reference-items`, { retries: 1 });
}

export async function getReferenceCategories(): Promise<any> {
  logger.debug('Fetching all reference categories');
  return apiGet(`/api/reference-categories`, { retries: 1 });
}

// Type for saved drafts
export type SavedDraft = {
  id: number;
  user_id: string;
  title: string;
  description?: string;
  modification_description?: string;
  project_id?: number;
  before_images?: any[];
  reference_images?: any[];
  step?: number;
  modification_type?: string;
  modification_options?: any;
  created_at: string;
  updated_at: string;
};

// Cache for drafts to reduce API calls
let draftsCache: {
  data: SavedDraft[] | null;
  timestamp: number;
  userId: string | null;
} = {
  data: null,
  timestamp: 0,
  userId: null
};

// Function to get drafts from the server
export async function getDrafts(): Promise<SavedDraft[]> {
  logger.debug('Starting to fetch drafts');

  try {
    // Get the user ID from the token for caching
    const token = await getAuthToken();
    if (!token) {
      logger.warn('No auth token available for fetching drafts - returning empty array');
      return [];
    }

    const userId = extractUserIdFromToken(token);

    // Check if we have cached data that's less than 10 seconds old and for the same user
    const now = Date.now();
    if (draftsCache.data &&
        now - draftsCache.timestamp < 10000 &&
        draftsCache.userId === userId) {
      logger.debug('Using cached drafts data');
      return draftsCache.data;
    }

    // Use our centralized API client
    const data = await apiGet<SavedDraft[]>('/api/drafts', {
      headers: {
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
      },
      retries: 1
    });

    logger.debug(`Successfully fetched ${data.length} drafts`);

    // Update cache
    draftsCache = {
      data,
      timestamp: now,
      userId
    };

    return data;
  } catch (error) {
    logger.error("Error getting drafts:", error);
    return [];
  }
}

// Function to get a specific draft
export async function getDraft(id: number): Promise<SavedDraft> {
  logger.debug(`Fetching draft with ID: ${id}`);
  return apiGet<SavedDraft>(`/api/drafts/${id}`, { retries: 1 });
}

// Function to create a draft
export async function createDraft(data: {
  title?: string;
  description?: string;
  modification_description?: string;
  project_id?: number;
  before_images?: any[];
  reference_images?: any[];
  step?: number;
  modification_type?: string;
  modification_options?: any;
}): Promise<SavedDraft> {
  logger.debug('Creating new draft');
  return apiPost<SavedDraft>("/api/drafts", data, { retries: 1 });
}

// Function to update a draft
export async function updateDraft(id: number, data: {
  title?: string;
  description?: string;
  modification_description?: string;
  project_id?: number;
  before_images?: any[];
  reference_images?: any[];
  step?: number;
  modification_type?: string;
  modification_options?: any;
}): Promise<SavedDraft> {
  logger.debug(`Updating draft with ID: ${id}`);
  return apiPut<SavedDraft>(`/api/drafts/${id}`, data, { retries: 1 });
}

// Function to delete a draft
export async function deleteDraft(id: number): Promise<void> {
  logger.debug(`Deleting draft with ID: ${id}`);
  await apiDelete(`/api/drafts/${id}`, { retries: 1 });

  // Invalidate drafts cache after deletion
  draftsCache.data = null;
  draftsCache.timestamp = 0;
}

// Utility function to extract user ID from JWT token
export function extractUserIdFromToken(token: string | null): string | null {
  if (!token) return null;

  try {
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
      return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
    }).join(''));
    const decoded = JSON.parse(jsonPayload);
    return decoded.sub || null;
  } catch (e) {
    console.error('[DEBUG] API - Error extracting user ID from token:', e);
    return null;
  }
}

// Function to invalidate caches - useful when auth state changes
export function invalidateCaches(): void {
  console.log('[DEBUG] API - Invalidating all data caches');

  // Reset projects cache
  projectsCache.data = null;
  projectsCache.timestamp = 0;
  projectsCache.userId = null;

  // Reset drafts cache
  draftsCache.data = null;
  draftsCache.timestamp = 0;
  draftsCache.userId = null;

  // Clear any browser cache for API requests
  if ('caches' in window) {
    try {
      // Try to clear API request caches
      caches.keys().then(cacheNames => {
        cacheNames.forEach(cacheName => {
          if (cacheName.includes('api')) {
            caches.delete(cacheName);
          }
        });
      });
    } catch (e) {
      console.error('[DEBUG] API - Error clearing browser caches:', e);
    }
  }
}
