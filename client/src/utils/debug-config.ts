/**
 * Debug Configuration Module for Client
 * 
 * This module provides a centralized configuration for debugging in the client application.
 * It reads the VITE_DEBUG_MODE environment variable and provides utilities for checking
 * if debugging is enabled for specific categories.
 */

// Parse the VITE_DEBUG_MODE environment variable
const parseDebugMode = (): { enabled: boolean; categories: Set<string> } => {
  // For client-side, we need to use import.meta.env
  const debugMode = import.meta.env.VITE_DEBUG_MODE || 'false';
  
  // If debug mode is simply 'true' or 'false'
  if (debugMode.toLowerCase() === 'true') {
    return { enabled: true, categories: new Set(['all']) };
  }
  
  if (debugMode.toLowerCase() === 'false') {
    return { enabled: false, categories: new Set() };
  }
  
  // If debug mode is a comma-separated list of categories
  const categories = new Set(
    debugMode.toLowerCase().split(',').map(cat => cat.trim())
  );
  
  return { 
    enabled: categories.size > 0, 
    categories 
  };
};

// Parse once at module load time
const { enabled: debugEnabled, categories: debugCategories } = parseDebugMode();

// Debug configuration object
export const debugConfig = {
  // Is debugging enabled at all?
  enabled: debugEnabled,
  
  // Check if a specific category is enabled
  isEnabled: (category: string): boolean => {
    if (!debugEnabled) return false;
    if (debugCategories.has('all')) return true;
    return debugCategories.has(category.toLowerCase());
  },
  
  // Get all enabled categories
  getEnabledCategories: (): string[] => {
    return Array.from(debugCategories);
  },
  
  // Log the current debug configuration (for internal use)
  logConfig: (): void => {
    console.info('[DEBUG-CONFIG] Debug mode:', debugEnabled ? 'ENABLED' : 'DISABLED');
    if (debugEnabled) {
      console.info('[DEBUG-CONFIG] Enabled categories:', Array.from(debugCategories).join(', '));
    }
  }
};

// Log the configuration when the module is first loaded in development
if (debugEnabled && import.meta.env.DEV) {
  debugConfig.logConfig();
}
