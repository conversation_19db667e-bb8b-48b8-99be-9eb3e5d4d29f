import type { Image as ImageType } from "@shared/schema";

/**
 * Normalizes an image path to ensure consistent format across the application
 *
 * @param image - The image path or Image object
 * @returns A normalized path string
 */
export function normalizeImagePath(image: string | ImageType): string {
  // If it's a string (direct path)
  if (typeof image === 'string') {
    // If it's already a URL, return as is
    if (image.startsWith('http')) {
      return image;
    }

    // If it's a relative path, ensure it has the correct format
    if (image.startsWith('/uploads/')) {
      return image;
    }

    // Extract filename if it's a full path
    const parts = image.split('/');
    const filename = parts[parts.length - 1];
    return `/uploads/${filename}`;
  }

  // If it's an Image object
  if (image && typeof image === 'object' && 'path' in image) {
    const path = image.path as string;

    // If it's already a URL, return as is
    if (path.startsWith('http')) {
      return path;
    }

    // If it's a relative path with /uploads/ prefix, return as is
    if (path.startsWith('/uploads/')) {
      return path;
    }

    // Special case for generated images (after images)
    // Check if this is an after image by looking for specific patterns or by context
    if (path.includes('generated') || path.includes('after') || path.includes('output')) {
      const parts = path.split('/');
      const filename = parts[parts.length - 1];

      // If the file is a text file (error case), return as is
      if (filename.endsWith('.txt')) {
        return `/uploads/${filename}`;
      }

      return `/uploads/generated/${filename}`;
    }

    // Extract filename if it's a full path
    const parts = path.split('/');
    const filename = parts[parts.length - 1];
    return `/uploads/${filename}`;
  }

  // Fallback
  console.error('Invalid image format:', image);
  return '';
}
