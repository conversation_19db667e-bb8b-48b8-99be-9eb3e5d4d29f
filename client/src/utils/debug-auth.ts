/**
 * Debug utilities for authentication
 *
 * This file contains utilities to help debug authentication issues.
 */

// Configure logging
const logger = {
  debug: (message: string, ...args: any[]) => {
    console.debug(`[AUTH-DEBUG] ${message}`, ...args);
  },
  info: (message: string, ...args: any[]) => {
    console.info(`[AUTH-DEBUG] ${message}`, ...args);
  },
  warn: (message: string, ...args: any[]) => {
    console.warn(`[AUTH-DEBUG] ${message}`, ...args);
  },
  error: (message: string, ...args: any[]) => {
    console.error(`[AUTH-DEBUG] ${message}`, ...args);
  }
};

/**
 * Debug the authentication token
 *
 * This function logs information about the authentication token
 * to help debug authentication issues.
 */
export function debugAuthToken() {
  logger.info('Debugging authentication token...');

  // Check if token exists in localStorage
  const token = localStorage.getItem('clerk-auth-token');

  if (!token) {
    logger.warn('No authentication token found in localStorage');
    return;
  }

  logger.info('Authentication token found in localStorage');

  // Parse the token (JWT)
  try {
    const parts = token.split('.');
    if (parts.length !== 3) {
      logger.error('Invalid token format - not a valid JWT');
      return;
    }

    // Decode the payload
    const payload = JSON.parse(atob(parts[1]));

    // Log token information
    logger.info('Token information:');
    logger.info('- Subject:', payload.sub);
    logger.info('- Issued at:', new Date(payload.iat * 1000).toISOString());
    logger.info('- Expires at:', new Date(payload.exp * 1000).toISOString());

    // Check if token is expired
    const now = Math.floor(Date.now() / 1000);
    if (payload.exp < now) {
      logger.error('Token is expired');
    } else {
      const timeLeft = payload.exp - now;
      const hoursLeft = Math.floor(timeLeft / 3600);
      const minutesLeft = Math.floor((timeLeft % 3600) / 60);
      logger.info(`Token expires in ${hoursLeft}h ${minutesLeft}m`);
    }
  } catch (error) {
    logger.error('Error parsing token:', error);
  }
}

/**
 * Test the authentication API
 *
 * This function makes a request to the authentication API
 * to test if the authentication is working properly.
 */
export async function testAuthApi() {
  logger.info('Testing authentication API...');

  try {
    // Make a request to the authentication status endpoint
    const response = await fetch('/api/auth/status');

    if (response.ok) {
      const data = await response.json();
      logger.info('Authentication API is working');
      logger.info('Response:', data);
    } else {
      logger.error('Authentication API returned an error:', response.status);
      const errorText = await response.text();
      logger.error('Error details:', errorText);
    }
  } catch (error) {
    logger.error('Error testing authentication API:', error);
  }

  try {
    // Make a request to the debug endpoint
    const token = localStorage.getItem('clerk-auth-token');
    const headers: Record<string, string> = {};

    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    const response = await fetch('/api/auth/debug', {
      headers
    });

    if (response.ok) {
      const data = await response.json();
      logger.info('Auth debug endpoint is working');
      logger.info('Debug data:', data);
      return data;
    } else {
      logger.error('Auth debug endpoint returned an error:', response.status);
      const errorText = await response.text();
      logger.error('Error details:', errorText);
    }
  } catch (error) {
    logger.error('Error testing auth debug endpoint:', error);
  }

  try {
    // Make a request to the user endpoint
    const token = localStorage.getItem('clerk-auth-token');
    const headers: Record<string, string> = {};

    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    const response = await fetch('/api/auth/user', {
      headers
    });

    if (response.ok) {
      const data = await response.json();
      logger.info('User API is working');
      logger.info('User data:', data);
    } else {
      logger.error('User API returned an error:', response.status);
      const errorText = await response.text();
      logger.error('Error details:', errorText);
    }
  } catch (error) {
    logger.error('Error testing user API:', error);
  }
}

/**
 * Debug authentication
 *
 * This function runs all authentication debug utilities.
 */
export function debugAuth() {
  logger.info('Starting authentication debug...');

  debugAuthToken();
  testAuthApi();

  logger.info('Authentication debug completed');
}
