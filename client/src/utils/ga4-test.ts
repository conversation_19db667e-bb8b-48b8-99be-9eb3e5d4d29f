/**
 * GA4 Implementation Test Utility
 * 
 * This utility helps test the GA4 implementation in development.
 * Run this in the browser console to verify GA4 is working correctly.
 */

import { getGA4Config, isGA4Ready, trackEvent, trackPageView } from '@/lib/ga4';
import { logger } from '@/utils/logger';

/**
 * Test GA4 configuration and readiness
 */
export const testGA4Config = (): void => {
  const config = getGA4Config();
  const isReady = isGA4Ready();

  logger.info('GA4 Configuration Test', 'analytics', {
    config,
    isReady,
    gtag: typeof window.gtag,
    dataLayer: Array.isArray(window.dataLayer),
  });

  console.group('🔍 GA4 Configuration Test');
  console.log('Configuration:', config);
  console.log('Is Ready:', isReady);
  console.log('gtag function available:', typeof window.gtag === 'function');
  console.log('dataLayer available:', Array.isArray(window.dataLayer));
  console.groupEnd();
};

/**
 * Test GA4 event tracking
 */
export const testGA4Events = (): void => {
  console.group('🎯 GA4 Event Tracking Test');

  // Test page view
  console.log('Testing page view tracking...');
  trackPageView({
    page_title: 'GA4 Test Page',
    page_path: '/test-ga4',
    custom_parameters: {
      test_mode: true,
    },
  });

  // Test custom event
  console.log('Testing custom event tracking...');
  trackEvent({
    action: 'test_event',
    category: 'testing',
    label: 'ga4_implementation_test',
    value: 1,
    custom_parameters: {
      test_timestamp: new Date().toISOString(),
      test_mode: true,
    },
  });

  console.log('Events sent! Check GA4 Real-time reports to verify.');
  console.groupEnd();
};

/**
 * Test GA4 in browser console
 * 
 * Usage:
 * 1. Open browser console
 * 2. Import this module: import('/src/utils/ga4-test.js')
 * 3. Run: testGA4Implementation()
 */
export const testGA4Implementation = (): void => {
  console.log('🚀 Starting GA4 Implementation Test...');
  
  testGA4Config();
  
  if (isGA4Ready()) {
    testGA4Events();
    console.log('✅ GA4 test completed successfully!');
  } else {
    console.warn('⚠️ GA4 is not ready. This is expected in development mode.');
    console.log('To test in production mode:');
    console.log('1. Set NODE_ENV=production');
    console.log('2. Ensure VITE_GA4_MEASUREMENT_ID is set');
    console.log('3. Reload the page');
  }
};

// Auto-run test if in development and debug mode
if (import.meta.env.NODE_ENV === 'development' && import.meta.env.VITE_DEBUG_MODE === 'true') {
  // Delay to ensure GA4 is initialized
  setTimeout(() => {
    console.log('🔧 Auto-running GA4 test in debug mode...');
    testGA4Implementation();
  }, 2000);
}
