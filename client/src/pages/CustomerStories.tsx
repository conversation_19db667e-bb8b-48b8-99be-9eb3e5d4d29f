import { Card, CardContent } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { ProjectImageComparison } from "@/components/ProjectImageComparison";

export default function CustomerStories() {
  // Sample customer stories data
  const stories = [
    {
      id: 1,
      name: "<PERSON>",
      role: "Interior Designer",
      company: "Thompson Designs",
      testimonial: "Renovision Studio has completely transformed how I present concepts to my clients. Being able to show them realistic visualizations of their space before any work begins has increased my project approval rate by 60%. The AI-generated images are incredibly realistic and save me countless hours of manual mockup work.",
      projectTitle: "Modern Apartment Renovation",
      projectDescription: "Complete transformation of a dated apartment into a modern, open-concept living space with natural materials and improved lighting.",
      beforeImage: "https://images.unsplash.com/photo-1493809842364-78817add7ffb?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      afterImage: "https://images.unsplash.com/photo-1600607687920-4e2a09cf159d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      avatar: "https://images.unsplash.com/photo-1568602471122-7832951cc4c5?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=100&q=80",
      industry: "Interior Design"
    },
    {
      id: 2,
      name: "Sarah Johnson",
      role: "Kitchen Renovation Specialist",
      company: "Johnson Kitchen & Bath",
      testimonial: "As a kitchen specialist, I need to help clients visualize major changes to their most important room. Renovision Studio allows me to show clients exactly how their new kitchen will look with different cabinet styles, countertops, and layouts. This has dramatically reduced revision requests and increased customer satisfaction.",
      projectTitle: "Luxury Kitchen Remodel",
      projectDescription: "Transformation of a traditional kitchen into a luxury cooking space with custom cabinetry, marble countertops, and professional-grade appliances.",
      beforeImage: "https://images.unsplash.com/photo-1556912172-45b7abe8b7e1?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      afterImage: "https://images.unsplash.com/photo-1556911220-bff31c812dba?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      avatar: "https://images.unsplash.com/photo-1544005313-94ddf0286df2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=100&q=80",
      industry: "Kitchen & Bath"
    },
    {
      id: 3,
      name: "David Wilson",
      role: "General Contractor",
      company: "Wilson Construction",
      testimonial: "Renovision Studio has become an essential tool for my construction business. I can now show homeowners exactly what their renovation will look like before we start demolition. This has helped me win more bids and build trust with clients who might otherwise be hesitant about major renovations.",
      projectTitle: "Historic Home Restoration",
      projectDescription: "Careful restoration of a 1920s craftsman home, preserving original details while updating systems and adding modern conveniences.",
      beforeImage: "https://images.unsplash.com/photo-1518780664697-55e3ad937233?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      afterImage: "https://images.unsplash.com/photo-1523217582562-09d0def993a6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      avatar: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=100&q=80",
      industry: "Construction"
    }
  ];

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Customer Success Stories</h1>
        <p className="text-gray-600">
          Discover how professionals in the renovation industry are using Renovision Studio to transform their businesses and delight their clients.
        </p>
      </div>

      <div className="space-y-12">
        {stories.map((story) => (
          <Card key={story.id} className="overflow-hidden">
            <CardContent className="p-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div>
                  <div className="flex items-center mb-4">
                    <Avatar className="h-12 w-12 mr-4">
                      <AvatarImage src={story.avatar} alt={story.name} />
                      <AvatarFallback>{story.name.charAt(0)}</AvatarFallback>
                    </Avatar>
                    <div>
                      <h3 className="font-bold text-lg">{story.name}</h3>
                      <p className="text-gray-600 text-sm">{story.role} at {story.company}</p>
                      <Badge variant="outline" className="mt-1">{story.industry}</Badge>
                    </div>
                  </div>
                  
                  <blockquote className="border-l-4 border-primary pl-4 italic text-gray-700 my-6">
                    "{story.testimonial}"
                  </blockquote>
                  
                  <div className="mt-6">
                    <h4 className="font-semibold text-lg mb-2">{story.projectTitle}</h4>
                    <p className="text-gray-600 text-sm">{story.projectDescription}</p>
                  </div>
                </div>
                
                <div className="h-80 relative">
                  <ProjectImageComparison
                    beforeImage={story.beforeImage}
                    afterImage={story.afterImage}
                    beforeAlt="Before renovation"
                    afterAlt="After renovation"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
