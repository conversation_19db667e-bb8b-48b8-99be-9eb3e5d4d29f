import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { But<PERSON> } from "@/components/ui/button";
import { Play, Clock, BookOpen } from "lucide-react";

export default function Tutorials() {
  // Sample tutorial data
  const tutorials = [
    {
      id: 1,
      title: "Getting Started with Renovision Studio",
      description: "Learn the basics of creating your first visualization project",
      thumbnail: "https://images.unsplash.com/photo-*************-191322ee64b0?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      duration: "5 min",
      level: "Beginner",
      category: "basics",
      videoUrl: "#",
      steps: [
        "Creating your account",
        "Navigating the dashboard",
        "Starting your first project",
        "Understanding the visualization process"
      ]
    },
    {
      id: 2,
      title: "Taking Effective Before Photos",
      description: "Tips for capturing the best images for successful visualizations",
      thumbnail: "https://images.unsplash.com/photo-*************-1ea8e935640e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      duration: "8 min",
      level: "Beginner",
      category: "photography",
      videoUrl: "#",
      steps: [
        "Choosing the right lighting conditions",
        "Finding the optimal camera angles",
        "Preparing the space for photography",
        "Camera settings for interior photography",
        "Common mistakes to avoid"
      ]
    },
    {
      id: 3,
      title: "Creating and Using Reference Libraries",
      description: "Organize your reference materials for efficient project creation",
      thumbnail: "https://images.unsplash.com/photo-1586282391129-76a6df230234?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      duration: "10 min",
      level: "Intermediate",
      category: "organization",
      videoUrl: "#",
      steps: [
        "Setting up your reference library",
        "Creating categories for different styles and materials",
        "Adding and organizing reference images",
        "Using your library in visualization projects",
        "Sharing libraries with team members (Professional and Enterprise plans)"
      ]
    },
    {
      id: 4,
      title: "Writing Effective Visualization Instructions",
      description: "Learn how to communicate your vision clearly for better results",
      thumbnail: "https://images.unsplash.com/photo-1517971129774-8a2b38fa128e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      duration: "7 min",
      level: "Intermediate",
      category: "basics",
      videoUrl: "#",
      steps: [
        "Understanding what the AI needs to know",
        "Being specific about materials and finishes",
        "Describing structural changes clearly",
        "Balancing detail with brevity",
        "Examples of effective instructions"
      ]
    },
    {
      id: 5,
      title: "Creating and Using Renovation Presets",
      description: "Save time by creating reusable renovation templates",
      thumbnail: "https://images.unsplash.com/photo-1600566753376-12c8ab7fb75b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      duration: "12 min",
      level: "Advanced",
      category: "advanced",
      videoUrl: "#",
      steps: [
        "Understanding renovation presets",
        "Creating your first preset",
        "Customizing presets for different room types",
        "Applying presets to new projects",
        "Managing and updating your preset library"
      ]
    },
    {
      id: 6,
      title: "Presenting Results to Clients",
      description: "Strategies for showcasing your visualizations effectively",
      thumbnail: "https://images.unsplash.com/photo-1557426272-fc759fdf7a8d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      duration: "15 min",
      level: "Advanced",
      category: "business",
      videoUrl: "#",
      steps: [
        "Preparing your visualization presentation",
        "Using the comparison slider effectively",
        "Explaining the visualization process to clients",
        "Handling client feedback and revision requests",
        "Converting visualizations into project approvals"
      ]
    }
  ];

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Video Tutorials</h1>
        <p className="text-gray-600">
          Learn how to use Renovision Studio with our step-by-step video tutorials.
        </p>
      </div>

      <Tabs defaultValue="all" className="mb-8">
        <TabsList>
          <TabsTrigger value="all">All Tutorials</TabsTrigger>
          <TabsTrigger value="basics">Basics</TabsTrigger>
          <TabsTrigger value="photography">Photography</TabsTrigger>
          <TabsTrigger value="organization">Organization</TabsTrigger>
          <TabsTrigger value="advanced">Advanced</TabsTrigger>
          <TabsTrigger value="business">Business</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="mt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {tutorials.map((tutorial) => (
              <TutorialCard key={tutorial.id} tutorial={tutorial} />
            ))}
          </div>
        </TabsContent>

        {["basics", "photography", "organization", "advanced", "business"].map((category) => (
          <TabsContent key={category} value={category} className="mt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {tutorials
                .filter((tutorial) => tutorial.category === category)
                .map((tutorial) => (
                  <TutorialCard key={tutorial.id} tutorial={tutorial} />
                ))}
            </div>
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
}

interface TutorialProps {
  tutorial: {
    id: number;
    title: string;
    description: string;
    thumbnail: string;
    duration: string;
    level: string;
    category: string;
    videoUrl: string;
    steps: string[];
  };
}

function TutorialCard({ tutorial }: TutorialProps) {
  return (
    <Card className="overflow-hidden flex flex-col h-full">
      <div className="relative">
        <img 
          src={tutorial.thumbnail} 
          alt={tutorial.title} 
          className="w-full h-48 object-cover"
        />
        <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity">
          <Button variant="secondary" size="sm" className="gap-2">
            <Play size={16} />
            Watch Tutorial
          </Button>
        </div>
      </div>
      <CardContent className="p-5 flex-grow flex flex-col">
        <div className="flex items-center gap-2 mb-2">
          <Badge variant="outline" className="text-xs font-normal">
            {tutorial.level}
          </Badge>
          <div className="flex items-center text-gray-500 text-xs">
            <Clock size={12} className="mr-1" />
            {tutorial.duration}
          </div>
        </div>
        
        <h3 className="font-semibold text-lg mb-2">{tutorial.title}</h3>
        <p className="text-gray-600 text-sm mb-4">{tutorial.description}</p>
        
        <div className="mt-auto">
          <h4 className="font-medium text-sm mb-2">What you'll learn:</h4>
          <ul className="text-sm text-gray-600 space-y-1">
            {tutorial.steps.slice(0, 3).map((step, index) => (
              <li key={index} className="flex items-start">
                <span className="text-primary mr-2">•</span>
                <span>{step}</span>
              </li>
            ))}
            {tutorial.steps.length > 3 && (
              <li className="text-primary text-xs font-medium">
                +{tutorial.steps.length - 3} more steps
              </li>
            )}
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}
