import { useState } from "react";
import { <PERSON>, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON>, Ta<PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search } from "lucide-react";

export default function Documentation() {
  const [searchQuery, setSearchQuery] = useState("");

  // Sample documentation sections
  const sections = [
    {
      id: "getting-started",
      title: "Getting Started",
      content: [
        {
          id: "account-setup",
          title: "Account Setup",
          text: "To get started with Renovision Studio, you'll need to create an account and choose a subscription plan that fits your needs. Follow these steps to set up your account:\n\n1. Click on the 'Sign Up' button in the top right corner of the homepage\n2. Enter your email address and create a password\n3. Verify your email address by clicking the link sent to your inbox\n4. Complete your profile information\n5. Choose a subscription plan from our Pricing page\n6. Add your payment information to activate your subscription"
        },
        {
          id: "first-project",
          title: "Creating Your First Project",
          text: "Once your account is set up, you can create your first visualization project:\n\n1. From your dashboard, click the 'Create New Visualization' button\n2. Enter a title and description for your project\n3. Select the room type (kitchen, bathroom, living room, etc.)\n4. Upload a clear 'before' image of the space you want to renovate\n5. Add reference images that show the style, materials, or design elements you want to incorporate\n6. Provide detailed instructions about the changes you want to visualize\n7. Submit your project for processing"
        }
      ]
    },
    {
      id: "uploading-images",
      title: "Uploading Images",
      content: [
        {
          id: "image-requirements",
          title: "Image Requirements",
          text: "For best results, follow these guidelines when uploading images:\n\n- Image format: JPG, PNG, or WEBP\n- Maximum file size: 10MB per image\n- Recommended resolution: At least 1920x1080 pixels\n- Lighting: Well-lit spaces produce better results\n- Angle: Take photos from a corner of the room to show more of the space\n- Clutter: Remove unnecessary items from the space before taking photos"
        },
        {
          id: "reference-images",
          title: "Using Reference Images",
          text: "Reference images help our AI understand your vision. You can upload images that show:\n\n- Design styles you like\n- Specific materials (flooring, countertops, etc.)\n- Color schemes\n- Furniture arrangements\n- Lighting fixtures\n\nYou can organize these references in your Reference Library for reuse across multiple projects."
        }
      ]
    },
    {
      id: "visualization-process",
      title: "Visualization Process",
      content: [
        {
          id: "how-it-works",
          title: "How It Works",
          text: "Renovision Studio uses advanced AI technology to transform your 'before' images into realistic visualizations of renovated spaces. Here's how the process works:\n\n1. Our AI analyzes your 'before' image to understand the space's dimensions, lighting, and features\n2. The system processes your reference images and text instructions\n3. The AI generates a realistic 'after' image showing your requested changes\n4. You can request revisions if needed to refine the results"
        },
        {
          id: "processing-time",
          title: "Processing Time",
          text: "Most visualizations are processed within minutes, depending on your subscription plan and current system load. You'll receive a notification when your visualization is ready to view."
        }
      ]
    },
    {
      id: "managing-projects",
      title: "Managing Projects",
      content: [
        {
          id: "project-organization",
          title: "Project Organization",
          text: "Keep your projects organized by:\n\n- Using descriptive titles and detailed descriptions\n- Tagging projects by room type or client name\n- Creating drafts for projects you're not ready to process\n- Archiving completed projects you no longer need active"
        },
        {
          id: "sharing-results",
          title: "Sharing Results",
          text: "Share your visualization results with clients or colleagues:\n\n- Download high-resolution before/after images\n- Generate shareable links (available on Professional and Enterprise plans)\n- Export project details as PDF reports (Enterprise plan only)\n- Use the comparison slider to showcase the transformation"
        }
      ]
    }
  ];

  // Filter sections based on search query
  const filteredSections = sections.map(section => ({
    ...section,
    content: section.content.filter(item => 
      item.title.toLowerCase().includes(searchQuery.toLowerCase()) || 
      item.text.toLowerCase().includes(searchQuery.toLowerCase())
    )
  })).filter(section => section.content.length > 0);

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Documentation</h1>
        <p className="text-gray-600 mb-6">
          Learn how to use Renovision Studio effectively with our comprehensive documentation.
        </p>
        
        <div className="relative max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
          <Input
            type="search"
            placeholder="Search documentation..."
            className="pl-10"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
        <Card className="md:col-span-1 h-fit">
          <CardContent className="p-4">
            <nav className="space-y-1">
              {sections.map((section) => (
                <Button
                  key={section.id}
                  variant="ghost"
                  className="w-full justify-start text-left font-normal"
                  asChild
                >
                  <a href={`#${section.id}`}>{section.title}</a>
                </Button>
              ))}
            </nav>
          </CardContent>
        </Card>

        <div className="md:col-span-3 space-y-8">
          {filteredSections.map((section) => (
            <div key={section.id} id={section.id}>
              <h2 className="text-2xl font-bold mb-4">{section.title}</h2>
              <div className="space-y-6">
                {section.content.map((item) => (
                  <Card key={item.id}>
                    <CardContent className="p-6">
                      <h3 className="text-xl font-semibold mb-3">{item.title}</h3>
                      <div className="text-gray-700 whitespace-pre-line">
                        {item.text}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
