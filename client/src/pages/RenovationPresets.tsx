import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { Plus, Search, Tag, Home, Edit, Trash } from "lucide-react";
import { useAuth } from "@/hooks/use-clerk-auth";
import { useDialog } from "@/hooks/use-dialog";
import { RenovationPresetDialog } from "@/components/RenovationPresetDialog";
import { DeleteConfirmDialog } from "@/components/DeleteConfirmDialog";
import { RenovationPreset } from "@shared/schema";

export default function RenovationPresets() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = useState("");
  const [activeRoomType, setActiveRoomType] = useState<string>("all");
  const [selectedPreset, setSelectedPreset] = useState<RenovationPreset | null>(null);
  const presetDialog = useDialog();
  const deleteDialog = useDialog();

  // Fetch renovation presets
  const { data: presets = [], isLoading } = useQuery<RenovationPreset[]>({
    queryKey: ["/api/renovation-presets"],
    staleTime: 60000, // 1 minute
  });

  // Unique room types for filtering
  const roomTypes = Array.from(new Set(presets.map((preset) => preset.roomType)));

  // Filter presets based on search query and active room type
  const filteredPresets = presets.filter((preset) => {
    const matchesSearch =
      searchQuery === "" ||
      preset.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (preset.description ? preset.description.toLowerCase().includes(searchQuery.toLowerCase()) : false) ||
      (preset.tags ? preset.tags.some((tag) => tag.toLowerCase().includes(searchQuery.toLowerCase())) : false);

    const matchesRoomType = activeRoomType === "all" || preset.roomType === activeRoomType;

    return matchesSearch && matchesRoomType;
  });

  // Handle creating a new preset
  const handleCreatePreset = () => {
    setSelectedPreset(null);
    presetDialog.open();
  };

  // Handle editing a preset
  const handleEditPreset = (preset: RenovationPreset) => {
    setSelectedPreset(preset);
    presetDialog.open();
  };

  // Handle deleting a preset
  const handleDeletePreset = (preset: RenovationPreset) => {
    setSelectedPreset(preset);
    deleteDialog.open();
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <header className="mb-8">
        <h1 className="text-3xl font-bold tracking-tight">Renovation Presets</h1>
        <p className="text-muted-foreground mt-2">
          Browse and manage renovation presets for different room types.
        </p>
      </header>

      <div className="flex flex-col md:flex-row gap-6 mb-8">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Search presets by name, description, or tags..."
            className="pl-10"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <div className="flex gap-4">
          <Select
            value={activeRoomType}
            onValueChange={(value) => setActiveRoomType(value)}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter by room" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Rooms</SelectItem>
              {roomTypes.map((roomType) => (
                <SelectItem key={roomType} value={roomType}>
                  {roomType.replace("_", " ").replace(/\b\w/g, (c) => c.toUpperCase())}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Button onClick={handleCreatePreset}>
            <Plus className="h-4 w-4 mr-2" />
            Create Preset
          </Button>
        </div>
      </div>

      <Tabs defaultValue="all" className="w-full">
        <TabsList className="mb-6">
          <TabsTrigger value="all">All Presets</TabsTrigger>
          <TabsTrigger value="default">Default Presets</TabsTrigger>
          <TabsTrigger value="custom">My Presets</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="mt-0 w-full">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 w-full">
            {isLoading ? (
              <p>Loading presets...</p>
            ) : filteredPresets.length === 0 ? (
              <div className="col-span-full text-center py-12">
                <h3 className="text-lg font-medium">No presets found</h3>
                <p className="text-muted-foreground mt-1">
                  Try adjusting your search or filter criteria.
                </p>
              </div>
            ) : (
              filteredPresets.map((preset) => (
                <PresetCard
                  key={preset.id}
                  preset={preset}
                  onEdit={handleEditPreset}
                  onDelete={handleDeletePreset}
                  isEditable={!!user && preset.createdBy === user.id}
                />
              ))
            )}
          </div>
        </TabsContent>

        <TabsContent value="default" className="mt-0 w-full">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 w-full">
            {isLoading ? (
              <p>Loading presets...</p>
            ) : filteredPresets.filter(p => p.isDefault).length === 0 ? (
              <div className="col-span-full text-center py-12">
                <h3 className="text-lg font-medium">No default presets found</h3>
                <p className="text-muted-foreground mt-1">
                  Try adjusting your search or filter criteria.
                </p>
              </div>
            ) : (
              filteredPresets
                .filter(p => p.isDefault)
                .map((preset) => (
                  <PresetCard
                    key={preset.id}
                    preset={preset}
                    onEdit={handleEditPreset}
                    onDelete={handleDeletePreset}
                    isEditable={false}
                  />
                ))
            )}
          </div>
        </TabsContent>

        <TabsContent value="custom" className="mt-0 w-full">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 w-full">
            {isLoading ? (
              <p>Loading presets...</p>
            ) : !user ? (
              <div className="col-span-full text-center py-12">
                <h3 className="text-lg font-medium">You need to be logged in</h3>
                <p className="text-muted-foreground mt-1">
                  Please log in to view your custom presets.
                </p>
              </div>
            ) : filteredPresets.filter(p => p.createdBy === user.id).length === 0 ? (
              <div className="col-span-full text-center py-12">
                <h3 className="text-lg font-medium">No custom presets found</h3>
                <p className="text-muted-foreground mt-1">
                  Create your first preset by clicking the "Create Preset" button.
                </p>
                <Button variant="outline" className="mt-4" onClick={handleCreatePreset}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Your First Preset
                </Button>
              </div>
            ) : (
              filteredPresets
                .filter(p => p.createdBy === user.id)
                .map((preset) => (
                  <PresetCard
                    key={preset.id}
                    preset={preset}
                    onEdit={handleEditPreset}
                    onDelete={handleDeletePreset}
                    isEditable={true}
                  />
                ))
            )}
          </div>
        </TabsContent>
      </Tabs>

      {/* Dialogs */}
      {presetDialog.isOpen && (
        <RenovationPresetDialog
          isOpen={presetDialog.isOpen}
          onClose={presetDialog.close}
          preset={selectedPreset}
        />
      )}

      {deleteDialog.isOpen && selectedPreset && (
        <DeleteConfirmDialog
          isOpen={deleteDialog.isOpen}
          onClose={deleteDialog.close}
          title="Delete Renovation Preset"
          description={`Are you sure you want to delete the "${selectedPreset.name}" preset? This action cannot be undone.`}
          apiEndpoint={`/api/renovation-presets/${selectedPreset.id}`}
          queryKey={["/api/renovation-presets"]}
        />
      )}
    </div>
  );
}

// Preset Card Component
type PresetCardProps = {
  preset: RenovationPreset;
  onEdit: (preset: RenovationPreset) => void;
  onDelete: (preset: RenovationPreset) => void;
  isEditable: boolean;
};

function PresetCard({ preset, onEdit, onDelete, isEditable }: PresetCardProps) {
  return (
    <Card className="overflow-hidden">
      {preset.imageUrl ? (
        <div className="relative h-48 w-full">
          <img
            src={preset.imageUrl}
            alt={preset.name}
            className="absolute inset-0 h-full w-full object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-background/80 to-transparent" />
          <div className="absolute bottom-4 left-4">
            <Badge variant={preset.isDefault ? "default" : "secondary"}>
              {preset.roomType.replace("_", " ").replace(/\b\w/g, (c) => c.toUpperCase())}
            </Badge>
          </div>
        </div>
      ) : (
        <div className="flex h-48 w-full items-center justify-center bg-muted">
          <Home className="h-12 w-12 text-muted-foreground/50" />
          <div className="absolute bottom-4 left-4">
            <Badge variant={preset.isDefault ? "default" : "secondary"}>
              {preset.roomType.replace("_", " ").replace(/\b\w/g, (c) => c.toUpperCase())}
            </Badge>
          </div>
        </div>
      )}

      <CardHeader>
        <div className="flex justify-between items-start">
          <div>
            <CardTitle>{preset.name}</CardTitle>
            <CardDescription className="mt-1">{preset.description}</CardDescription>
          </div>
          {preset.isDefault && (
            <Badge variant="outline" className="ml-2">Default</Badge>
          )}
        </div>
      </CardHeader>

      <CardContent>
        <div className="flex flex-wrap gap-2">
          {preset.tags?.map((tag) => (
            <Badge key={tag} variant="outline" className="flex items-center">
              <Tag className="h-3 w-3 mr-1" />
              {tag}
            </Badge>
          ))}
        </div>
      </CardContent>

      <Separator />

      <CardFooter className="flex justify-between pt-4">
        <Button variant="outline" className="w-full">
          Use Preset
        </Button>

        {isEditable && (
          <div className="flex gap-2 ml-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => onEdit(preset)}
            >
              <Edit className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => onDelete(preset)}
            >
              <Trash className="h-4 w-4" />
            </Button>
          </div>
        )}
      </CardFooter>
    </Card>
  );
}