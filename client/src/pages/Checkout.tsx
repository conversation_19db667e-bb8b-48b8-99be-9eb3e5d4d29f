import { useEffect, useState, useRef } from "react";
import { useToast } from "@/hooks/use-toast";
import { useLocation, useRoute } from "wouter";
import { useClerkAuth } from "@/hooks/use-clerk-auth";
import { createCheckoutSession } from "@/lib/stripe";
import { LoadingSpinnerWithText } from "@/components/ui/loading-spinner";
import { CouponInput } from "@/components/checkout/CouponInput";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { getPlanDetails, formatPrice } from "@/lib/subscription-plans";



export default function Checkout() {
  const { toast } = useToast();
  const [, navigate] = useLocation();
  const { isAuthenticated, getToken } = useClerkAuth();
  const [, params] = useRoute('/checkout/:plan');
  const [isProcessing, setIsProcessing] = useState(false);
  const [showSummary, setShowSummary] = useState(true);
  const [appliedCoupon, setAppliedCoupon] = useState<any>(null);
  const [planDetails, setPlanDetails] = useState<any>(null);
  const checkoutInitiated = useRef(false);
  const checkoutSessionCreated = useRef(false);

  // Load plan details and handle authentication
  useEffect(() => {
    if (!isAuthenticated) {
      toast({
        title: "Authentication Required",
        description: "Please sign in to complete your purchase",
      });
      navigate("/sign-in");
      return;
    }

    // Parse query parameters
    const queryParams = new URLSearchParams(window.location.search);

    // Get plan from either route params or query params
    let planParam = params?.plan || queryParams.get('plan');
    let billingParam = queryParams.get('billing');

    console.log("Checkout params:", { planParam, billingParam });

    if (!planParam || !billingParam) {
      toast({
        title: "Missing Information",
        description: "Plan and billing cycle are required",
        variant: "destructive",
      });
      navigate("/pricing");
      return;
    }

    // Load plan details
    const details = getPlanDetails(planParam, billingParam as 'monthly' | 'annual');
    if (!details) {
      toast({
        title: "Invalid Plan",
        description: "The selected plan is not available",
        variant: "destructive",
      });
      navigate("/pricing");
      return;
    }

    setPlanDetails({
      id: planParam,
      billingCycle: billingParam,
      ...details
    });

  }, [isAuthenticated, navigate, toast, params]);

  // Handle coupon application
  const handleApplyCoupon = (code: string, couponDetails: any) => {
    setAppliedCoupon({
      code,
      ...couponDetails
    });
  };

  // Handle coupon removal
  const handleRemoveCoupon = () => {
    setAppliedCoupon(null);
  };

  // Handle checkout
  const handleCheckout = async () => {
    if (isProcessing || checkoutInitiated.current) {
      return;
    }

    if (!planDetails) {
      toast({
        title: "Missing Plan Details",
        description: "Please select a plan before proceeding",
        variant: "destructive",
      });
      return;
    }

    // Set flags to prevent duplicate requests
    checkoutInitiated.current = true;
    setIsProcessing(true);

    try {
      toast({
        title: "Redirecting to Checkout",
        description: "Please wait while we prepare your checkout...",
      });

      // Get the authentication token
      if (typeof getToken !== 'function') {
        throw new Error("Authentication token function not available");
      }

      const token = await getToken();

      if (!token) {
        throw new Error("Unable to get authentication token");
      }

      console.log("Creating checkout session with token");

      // Use the Stripe utility to create a checkout session
      const result = await createCheckoutSession(
        planDetails.id,
        planDetails.billingCycle as 'monthly' | 'annual',
        token,
        appliedCoupon?.code
      );

      if (!result.success) {
        throw new Error(result.error || "Failed to create checkout session");
      }

      // The redirect happens in the createCheckoutSession function
    } catch (error: any) {
      console.error("Checkout error:", error);
      toast({
        title: "Error",
        description: error.message || "Failed to initialize checkout",
        variant: "destructive",
      });

      // Reset flags to allow retry
      checkoutInitiated.current = false;
      setIsProcessing(false);
    }
  };

  return (
    <div className="flex items-center justify-center min-h-[80vh] w-full">
      <div className="max-w-md w-full px-4 py-8">
        {isProcessing ? (
          <div className="text-center">
            <LoadingSpinnerWithText
              size="lg"
              title="Redirecting to Checkout"
              description="Please wait while we prepare your secure checkout experience..."
            />
          </div>
        ) : (
          <Card>
            <CardHeader>
              <CardTitle>Complete Your Purchase</CardTitle>
              <CardDescription>
                Review your subscription details before proceeding to payment
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {planDetails && (
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <div>
                      <h3 className="font-medium">{planDetails.displayName} Plan</h3>
                      <p className="text-sm text-muted-foreground">
                        {planDetails.billingCycle === 'annual' ? 'Annual' : 'Monthly'} billing
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">
                        {formatPrice(planDetails.billingCycle === 'annual' ? planDetails.annualPrice : planDetails.price)}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {planDetails.billingCycle === 'annual' ? 'per year' : 'per month'}
                      </p>
                    </div>
                  </div>

                  <div className="border-t pt-4">
                    <CouponInput
                      onApplyCoupon={handleApplyCoupon}
                      onRemoveCoupon={handleRemoveCoupon}
                      disabled={isProcessing}
                    />
                  </div>

                  <div className="border-t pt-4">
                    <h3 className="font-medium mb-2">Plan Features</h3>
                    <ul className="space-y-1 text-sm">
                      {planDetails.features?.map((feature: string, index: number) => (
                        <li key={index} className="flex items-start">
                          <span className="mr-2">•</span>
                          <span>{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              )}
            </CardContent>
            <CardFooter>
              <Button
                className="w-full"
                onClick={handleCheckout}
                disabled={isProcessing || !planDetails}
              >
                {isProcessing ? "Processing..." : "Proceed to Payment"}
              </Button>
            </CardFooter>
          </Card>
        )}
      </div>
    </div>
  );
}