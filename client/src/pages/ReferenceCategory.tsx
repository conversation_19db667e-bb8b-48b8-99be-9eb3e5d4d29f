import { useState } from "react";
import { use<PERSON><PERSON><PERSON>, Link, useLocation } from "wouter";
import { useAuth } from "@/hooks/use-clerk-auth";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { PlusCircle, ChevronLeft, Trash, Pencil, Check, X, Tag, FolderOpen } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ReferenceCategoryWithItems, ReferenceItem } from "@shared/schema";
import { apiRequest } from "@/lib/queryClient";

export default function ReferenceCategory() {
  const { id } = useParams<{ id: string }>();
  const [_, navigate] = useLocation();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { user } = useAuth();
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploadForm, setUploadForm] = useState({
    name: "",
    description: "",
    tags: "",
  });
  const [isUploadDialogOpen, setIsUploadDialogOpen] = useState(false);

  // Query the category with its items
  const {
    data: category,
    isLoading,
    isError,
    error
  } = useQuery<ReferenceCategoryWithItems>({
    queryKey: [`/api/reference-categories/${id}`],
    enabled: !!id && !!user,
  });

  // Mutation to upload a new reference item
  const uploadItemMutation = useMutation({
    mutationFn: async (data: FormData) => {
      const res = await fetch("/api/reference-items", {
        method: "POST",
        body: data,
      });
      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.error || "Failed to upload reference item");
      }
      return await res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/reference-categories/${id}`] });
      setUploadForm({
        name: "",
        description: "",
        tags: "",
      });
      setSelectedFile(null);
      setIsUploadDialogOpen(false);
      toast({
        title: "Reference item uploaded",
        description: "Your reference item has been uploaded successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to upload reference item",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Mutation to delete a reference item
  const deleteItemMutation = useMutation({
    mutationFn: async (itemId: number) => {
      await apiRequest("DELETE", `/api/reference-items/${itemId}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/reference-categories/${id}`] });
      toast({
        title: "Reference item deleted",
        description: "Your reference item has been deleted successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to delete reference item",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setSelectedFile(e.target.files[0]);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setUploadForm((prev) => ({ ...prev, [name]: value }));
  };

  const handleUpload = () => {
    if (!selectedFile) {
      toast({
        title: "File required",
        description: "Please select an image file to upload",
        variant: "destructive",
      });
      return;
    }

    if (!uploadForm.name.trim()) {
      toast({
        title: "Name required",
        description: "Please provide a name for this reference item",
        variant: "destructive",
      });
      return;
    }

    const formData = new FormData();
    formData.append("image", selectedFile);

    // Convert form data to JSON and append as a single field
    const jsonData = {
      name: uploadForm.name,
      description: uploadForm.description || undefined,
      categoryId: parseInt(id),
      tags: uploadForm.tags ? uploadForm.tags.split(",").map(tag => tag.trim()) : undefined,
    };

    formData.append("data", JSON.stringify(jsonData));

    uploadItemMutation.mutate(formData);
  };

  const handleDeleteItem = (itemId: number) => {
    if (window.confirm("Are you sure you want to delete this reference item?")) {
      deleteItemMutation.mutate(itemId);
    }
  };

  if (!user) {
    return <div className="flex justify-center items-center h-[60vh]">You need to be logged in to view this page.</div>;
  }

  if (isLoading) {
    return <div className="container py-10 flex justify-center items-center h-[60vh]">Loading category...</div>;
  }

  if (isError) {
    return (
      <div className="container py-10">
        <div className="bg-red-50 border border-red-200 text-red-700 p-4 rounded-md mb-6">
          <h3 className="text-lg font-medium">Error Loading Category</h3>
          <p>{error instanceof Error ? error.message : "Failed to load category data"}</p>
        </div>
        <Button variant="outline" asChild>
          <Link href="/reference-library">
            <ChevronLeft className="mr-2 h-4 w-4" /> Back to Reference Library
          </Link>
        </Button>
      </div>
    );
  }

  if (!category) {
    return (
      <div className="container py-10">
        <div className="bg-amber-50 border border-amber-200 text-amber-700 p-4 rounded-md mb-6">
          <h3 className="text-lg font-medium">Category Not Found</h3>
          <p>The requested reference category could not be found.</p>
        </div>
        <Button variant="outline" asChild>
          <Link href="/reference-library">
            <ChevronLeft className="mr-2 h-4 w-4" /> Back to Reference Library
          </Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <Button variant="outline" asChild className="mb-4">
        <Link href="/reference-library">
          <ChevronLeft className="mr-2 h-4 w-4" /> Back to Reference Library
        </Link>
      </Button>

      <div className="flex flex-col space-y-2 mb-8">
        <h1 className="text-3xl font-bold">{category.name}</h1>
        {category.description && (
          <p className="text-muted-foreground">
            {category.description}
          </p>
        )}
      </div>

      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold">Reference Items</h2>
        <Dialog open={isUploadDialogOpen} onOpenChange={setIsUploadDialogOpen}>
          <DialogTrigger asChild>
            <Button variant="default">
              <PlusCircle className="mr-2 h-4 w-4" />
              Add Item to Category
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>Upload Reference Item</DialogTitle>
              <DialogDescription>
                Upload a new reference image to the "{category.name}" category.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="file">Image File</Label>
                <Input
                  id="file"
                  type="file"
                  accept="image/*"
                  onChange={handleFileChange}
                />
                {selectedFile && (
                  <p className="text-sm text-muted-foreground">
                    Selected: {selectedFile.name}
                  </p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="name">Name</Label>
                <Input
                  id="name"
                  name="name"
                  placeholder="Reference name"
                  value={uploadForm.name}
                  onChange={handleInputChange}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="description">Description (optional)</Label>
                <Textarea
                  id="description"
                  name="description"
                  placeholder="Description"
                  value={uploadForm.description}
                  onChange={handleInputChange}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="tags">Tags (comma separated, optional)</Label>
                <Input
                  id="tags"
                  name="tags"
                  placeholder="floor, black, polished"
                  value={uploadForm.tags}
                  onChange={handleInputChange}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsUploadDialogOpen(false)}>
                Cancel
              </Button>
              <Button
                onClick={handleUpload}
                disabled={uploadItemMutation.isPending || !selectedFile}
              >
                {uploadItemMutation.isPending ? "Uploading..." : "Upload Reference"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
        </div>

      <Separator className="my-4" />

      {category.items && category.items.length > 0 ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {category.items.map((item) => (
            <Card key={item.id} className="overflow-hidden">
              <div className="aspect-square overflow-hidden">
                <img
                  src={item.image_path ? `/uploads/${item.image_path.split("/").pop()}` : '/placeholder-image.svg'}
                  alt={item.name}
                  className="h-full w-full object-cover transition-all hover:scale-105"
                  onError={(e) => {
                    // Fallback if image fails to load
                    console.error(`Failed to load image: ${item.image_path}`);
                    e.currentTarget.src = '/placeholder-image.svg';
                  }}
                />
              </div>
              <CardHeader className="p-3">
                <CardTitle className="text-lg truncate">{item.name}</CardTitle>
                {item.description && (
                  <CardDescription className="truncate">
                    {item.description}
                  </CardDescription>
                )}
              </CardHeader>
              <CardFooter className="p-3 pt-0 flex justify-between">
                {item.tags && item.tags.length > 0 ? (
                  <div className="flex flex-wrap gap-1">
                    {item.tags.slice(0, 3).map((tag, i) => (
                      <Badge key={i} variant="secondary" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                    {item.tags.length > 3 && (
                      <Badge variant="outline" className="text-xs">
                        +{item.tags.length - 3}
                      </Badge>
                    )}
                  </div>
                ) : (
                  <span className="text-xs text-muted-foreground">No tags</span>
                )}
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => handleDeleteItem(item.id)}
                  title="Delete"
                >
                  <Trash className="h-4 w-4" />
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      ) : (
        <div className="bg-muted p-8 rounded-lg text-center">
          <h3 className="font-medium text-lg mb-2">No Items in This Category</h3>
          <p className="text-muted-foreground mb-4">
            Upload items to this category to build your reference library.
          </p>
          <Button onClick={() => setIsUploadDialogOpen(true)}>
            <PlusCircle className="mr-2 h-4 w-4" />
            Add Your First Item
          </Button>
        </div>
      )}
    </div>
  );
}