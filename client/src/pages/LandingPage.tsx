import { Link, useLocation } from "wouter";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ProjectImageComparison } from "@/components/ProjectImageComparison";
import FeatureCard from "@/components/FeatureCard";
import TestimonialCard from "@/components/TestimonialCard";
import { LinkWithScroll } from "@/components/ScrollToTop";

export default function LandingPage() {
  // Sample data for features
  const features = [
    {
      icon: "cloud_upload",
      title: "Upload Images",
      description: "Upload before images of the area you want to renovate. Add reference images to show preferred styles."
    },
    {
      icon: "description",
      title: "Describe Changes",
      description: "Explain the modifications you want or point to reference images. Be specific about materials, colors, and styles."
    },
    {
      icon: "auto_awesome",
      title: "AI Visualization",
      description: "Our AI technology creates realistic visualizations of your renovation ideas, helping you see exactly how your space will look before making any changes."
    }
  ];

  // Sample data for testimonials
  const testimonials = [
    {
      name: "<PERSON>",
      role: "Renovation Specialist",
      company: "Carpenter Renovations",
      location: "Sydney, Australia",
      testimonial: "This tool has transformed how I present ideas to clients. Being able to show them what their space could look like before we start work has increased my conversion rate by 40%. My clients are now more confident in their decisions, and I'm closing deals faster than ever.",
      rating: 5.0,
      imageUrl: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=200&q=80",
      companyLogo: "https://ui-avatars.com/api/?name=CR&background=0D8ABC&color=fff"
    },
    {
      name: "Sarah Johnson",
      role: "Interior Designer",
      company: "Johnson Interiors",
      location: "Melbourne, Australia",
      testimonial: "The AI visualizations are incredibly realistic. It helps my clients make decisions faster because they can actually see how different options would look in their space. I've reduced my design revision cycles by 60% and increased client satisfaction scores by 35%.",
      rating: 4.5,
      imageUrl: null, // Use initials instead of photo
      companyLogo: "https://ui-avatars.com/api/?name=JI&background=6B21A8&color=fff"
    },
    {
      name: "Michael Rodriguez",
      role: "Kitchen Renovation Specialist",
      company: "Elite Kitchens",
      location: "Brisbane, Australia",
      testimonial: "As a kitchen specialist, I need to help clients visualize major changes to their most important room. Renovision Studio allows me to show clients exactly how their new kitchen will look with different cabinet styles, countertops, and layouts. My business has grown 45% since I started using this tool.",
      rating: 5.0,
      imageUrl: null, // Use initials instead of photo
      companyLogo: "https://ui-avatars.com/api/?name=EK&background=047857&color=fff"
    },
    {
      name: "Emma Wilson",
      role: "Home Renovation Contractor",
      company: "Wilson Home Renovations",
      location: "Perth, Australia",
      testimonial: "Renovision Studio has become an essential tool for my construction business. I can now show homeowners exactly what their renovation will look like before we start demolition. This has helped me win 30% more bids and build trust with clients who might otherwise be hesitant about major renovations.",
      rating: 4.5,
      imageUrl: "https://images.unsplash.com/photo-1580489944761-15a19d654956?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=200&q=80",
      companyLogo: "https://ui-avatars.com/api/?name=WHR&background=B91C1C&color=fff"
    }
  ];

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Hero Section */}
      <section className="mb-10">
        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          <div className="md:flex">
            <div className="p-8 md:w-1/2">
              <h1 className="font-heading text-3xl font-bold text-gray-900 mb-4">Transform Your Projects With AI Visualization</h1>
              <p className="text-gray-600 mb-6">Upload before images, describe your changes or reference other images, and let our AI show your clients what's possible.</p>
              <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-3">
                <Link href="/sign-up">
                  <Button className="inline-flex items-center">
                    <span className="material-icons text-sm mr-1">person_add</span>
                    Sign Up
                  </Button>
                </Link>
                <Link href="/sign-in">
                  <Button variant="outline" className="inline-flex items-center">
                    <span className="material-icons text-sm mr-1">login</span>
                    Sign In
                  </Button>
                </Link>
                <Link href="/pricing">
                  <Button variant="outline" className="inline-flex items-center">
                    <span className="material-icons text-sm mr-1">payments</span>
                    View Pricing
                  </Button>
                </Link>
              </div>
            </div>
            <div className="md:w-1/2 bg-gray-100 flex items-center justify-center p-6">
              <div className="relative w-full h-64 md:h-80">
                <ProjectImageComparison
                  beforeImage="/uploads/landing_before.png"
                  afterImage="/uploads/landing_after.png"
                  beforeAlt="Before renovation"
                  afterAlt="After renovation"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="mb-10">
        <div className="mb-6 text-center">
          <h2 className="font-heading text-2xl font-bold text-gray-900">How It Works</h2>
          <p className="text-gray-600 max-w-2xl mx-auto">Our AI-powered visualization tool helps tradespeople showcase possibilities to clients</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <FeatureCard
              key={index}
              icon={feature.icon}
              title={feature.title}
              description={feature.description}
            />
          ))}
        </div>
      </section>

      {/* Testimonials */}
      <section className="mb-10 py-12 bg-gradient-to-r from-primary-50 to-gray-50 rounded-xl">
        <div className="max-w-6xl mx-auto px-6">
          <div className="mb-10 text-center">
            <h2 className="font-heading text-3xl font-bold text-gray-900 mb-4">What Our Users Say</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Don't just take our word for it. Hear from the professionals who are using Renovision Studio to transform their businesses and delight their clients.
            </p>
          </div>

          <div className="relative">
            {/* Desktop view - grid layout */}
            <div className="hidden md:grid md:grid-cols-2 lg:grid-cols-2 gap-6">
              {testimonials.slice(0, 4).map((testimonial, index) => (
                <TestimonialCard
                  key={index}
                  name={testimonial.name}
                  role={testimonial.role}
                  company={testimonial.company}
                  location={testimonial.location}
                  testimonial={testimonial.testimonial}
                  rating={testimonial.rating}
                  imageUrl={testimonial.imageUrl}
                  companyLogo={testimonial.companyLogo}
                />
              ))}
            </div>

            {/* Mobile view - show only first two testimonials */}
            <div className="md:hidden space-y-6">
              {testimonials.slice(0, 2).map((testimonial, index) => (
                <TestimonialCard
                  key={index}
                  name={testimonial.name}
                  role={testimonial.role}
                  company={testimonial.company}
                  location={testimonial.location}
                  testimonial={testimonial.testimonial}
                  rating={testimonial.rating}
                  imageUrl={testimonial.imageUrl}
                  companyLogo={testimonial.companyLogo}
                />
              ))}
            </div>
          </div>

          <div className="mt-10 text-center">
            <LinkWithScroll href="/customer-stories">
              <Button variant="outline" size="lg" className="inline-flex items-center">
                <span className="material-icons text-sm mr-1">people</span>
                View All Success Stories
              </Button>
            </LinkWithScroll>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section>
        <div className="bg-primary-50 rounded-lg p-8 text-center">
          <h2 className="font-heading text-2xl font-bold text-gray-900 mb-4">Ready to Transform Your Projects?</h2>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">Join thousands of professionals who are using Renovision.Studio to impress clients and win more business.</p>
          <div className="flex flex-col sm:flex-row justify-center space-y-3 sm:space-y-0 sm:space-x-3">
            <Link href="/sign-up">
              <Button size="lg" className="inline-flex items-center">
                <span className="material-icons text-sm mr-1">person_add</span>
                Sign Up Free
              </Button>
            </Link>
            <Link href="/sign-in">
              <Button variant="outline" size="lg" className="inline-flex items-center">
                <span className="material-icons text-sm mr-1">login</span>
                Sign In
              </Button>
            </Link>
            <Link href="/pricing">
              <Button variant="outline" size="lg" className="inline-flex items-center">
                <span className="material-icons text-sm mr-1">info</span>
                Learn More
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
