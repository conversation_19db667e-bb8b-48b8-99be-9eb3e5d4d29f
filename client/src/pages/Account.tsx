import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Link, useLocation } from "wouter";
import { useEffect, useState } from "react";
import { useClerkAuth } from "@/hooks/use-clerk-auth";
import { useToast } from "@/hooks/use-toast";
import { useSubscription } from "@/contexts/SubscriptionContext";
import { PLAN_LIMITS, formatPrice, getBillingCycleText } from "@/lib/subscription-plans";
import {
  CreditCard,
  Calendar,
  Zap,
  Image,
  RefreshCw,
  Shield,
  AlertTriangle,
  CheckCircle,
  XCircle,
  ArrowRight
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { PlanChangeDialog } from "@/components/PlanChangeDialog";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

export default function Account() {
  const [, navigate] = useLocation();
  const { isAuthenticated, getToken, user } = useClerkAuth();
  const { toast } = useToast();
  const { subscription, usage, limits, isLoading, refetch, clearCache } = useSubscription();
  const [activeTab, setActiveTab] = useState("subscription");
  const [isUpdating, setIsUpdating] = useState(false);
  const [isClearing, setIsClearing] = useState(false);
  const [showPlanChangeDialog, setShowPlanChangeDialog] = useState(false);

  // Redirect to sign-in if not authenticated
  useEffect(() => {
    // Only redirect if we're sure the user is not authenticated
    // This prevents redirecting during the initial loading state
    if (isAuthenticated === false) {
      console.log('[ACCOUNT] User not authenticated, redirecting to sign-in');
      toast({
        title: "Authentication Required",
        description: "Please sign in to view your account",
      });
      navigate("/sign-in");
    }
  }, [isAuthenticated, navigate, toast]);

  // Add a safety check to prevent infinite API request loops
  useEffect(() => {
    const handleApiErrors = () => {
      const errorCount = parseInt(sessionStorage.getItem('account-page-error-count') || '0');
      if (errorCount > 3) {
        console.error('[ACCOUNT] Too many API errors, redirecting to sign-in');
        toast({
          title: "Authentication Error",
          description: "Please sign in again to continue",
          variant: "destructive",
        });
        // Clear any potentially corrupted auth data
        localStorage.removeItem('clerk-auth-token');
        sessionStorage.removeItem('renovate-ai-auth-cache');
        navigate("/sign-in");
        return true;
      }
      sessionStorage.setItem('account-page-error-count', (errorCount + 1).toString());
      return false;
    };

    // Reset error count when component mounts
    if (isAuthenticated) {
      sessionStorage.setItem('account-page-error-count', '0');
    }

    // Set up global error handler for API requests
    const originalFetch = window.fetch;
    window.fetch = async function(input, init) {
      const response = await originalFetch(input, init);

      // Check if this is an API request that returned 401
      if (typeof input === 'string' &&
          input.includes('/api/') &&
          response.status === 401) {
        console.error('[ACCOUNT] API request returned 401:', input);
        if (handleApiErrors()) {
          // If we're redirecting, don't continue with the request
          return new Response(JSON.stringify({ error: 'Authentication error' }), {
            status: 401,
            headers: { 'Content-Type': 'application/json' }
          });
        }
      }

      return response;
    };

    // Restore original fetch when component unmounts
    return () => {
      window.fetch = originalFetch;
    };
  }, [isAuthenticated, navigate, toast]);

  // We don't need to refresh subscription data here as it's already handled by the SubscriptionContext
  // This prevents duplicate fetches and refresh loops

  // Log subscription data for debugging
  useEffect(() => {
    if (subscription) {
      console.log('Account page subscription data:', subscription);
      console.log('Account page subscription details:', {
        hasSubscription: !!subscription,
        subscriptionType: typeof subscription,
        nestedSubscription: subscription?.subscription ? 'exists' : 'missing',
        nestedSubscriptionType: subscription?.subscription ? typeof subscription.subscription : 'N/A',
        doubleNestedSubscription: subscription?.subscription?.subscription ? 'exists' : 'missing',
        doubleNestedSubscriptionType: subscription?.subscription?.subscription ? typeof subscription.subscription.subscription : 'N/A',
        planId: subscription?.subscription?.subscription?.plan_id || 'missing',
        status: subscription?.subscription?.subscription?.status || 'missing',
        limits: limits,
        usage: usage,
        isLoading: isLoading,
        hasActiveSubscription: !!subscription?.subscription?.subscription?.status === 'active'
      });
    }
  }, [subscription, limits, usage, isLoading]);

  // Function to handle cancellation
  const handleCancelSubscription = async () => {
    // Check for subscription ID in both nested and flat structures
    const subscriptionId = subscription?.subscription?.subscription?.stripe_subscription_id ||
                          subscription?.subscription?.stripe_subscription_id ||
                          subscription?.stripe_subscription_id;

    console.log('Cancel subscription - subscription ID:', subscriptionId);
    console.log('Cancel subscription - subscription data:', subscription);

    if (!subscriptionId) {
      toast({
        title: "Error",
        description: "No active subscription found",
        variant: "destructive",
      });
      return;
    }

    if (!confirm("Are you sure you want to cancel your subscription? You will lose access to premium features at the end of your billing period.")) {
      return;
    }

    setIsUpdating(true);
    try {
      const token = await getToken();

      if (!token) {
        throw new Error('Authentication token not available');
      }

      console.log('Sending cancel request with subscription ID:', subscriptionId);

      const response = await fetch(`/api/subscription/cancel`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          subscriptionId: subscriptionId
        }),
      });

      console.log('Cancel response status:', response.status);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `Failed to cancel subscription: ${response.status}`);
      }

      await refetch();
      toast({
        title: "Subscription Cancelled",
        description: "Your subscription will remain active until the end of the current billing period.",
      });
    } catch (error) {
      console.error('Error cancelling subscription:', error);
      toast({
        title: "Error",
        description: "Failed to cancel subscription. Please try again later.",
        variant: "destructive",
      });
    } finally {
      setIsUpdating(false);
    }
  };

  // Function to handle reactivation
  const handleReactivateSubscription = async () => {
    // Check for subscription ID in both nested and flat structures
    const subscriptionId = subscription?.subscription?.subscription?.stripe_subscription_id ||
                          subscription?.subscription?.stripe_subscription_id ||
                          subscription?.stripe_subscription_id;

    if (!subscriptionId) {
      toast({
        title: "Error",
        description: "No subscription found to reactivate",
        variant: "destructive",
      });
      return;
    }

    setIsUpdating(true);
    try {
      const token = await getToken();

      if (!token) {
        throw new Error('Authentication token not available');
      }

      const response = await fetch(`/api/subscription/reactivate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          subscriptionId: subscriptionId
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `Failed to reactivate subscription: ${response.status}`);
      }

      await refetch();
      toast({
        title: "Subscription Reactivated",
        description: "Your subscription has been successfully reactivated.",
      });
    } catch (error) {
      console.error('Error reactivating subscription:', error);
      toast({
        title: "Error",
        description: "Failed to reactivate subscription. Please try again later.",
        variant: "destructive",
      });
    } finally {
      setIsUpdating(false);
    }
  };

  // Function to handle upgrade
  const handleUpgradeSubscription = () => {
    navigate("/pricing");
  };

  // Function to handle clearing the cache
  const handleClearCache = async () => {
    setIsClearing(true);
    try {
      await clearCache('all');
      toast({
        title: "Cache Cleared",
        description: "Your subscription data has been refreshed.",
      });
    } catch (error) {
      console.error('Error clearing cache:', error);
      toast({
        title: "Error",
        description: "Failed to clear cache. Please try again later.",
        variant: "destructive",
      });
    } finally {
      setIsClearing(false);
    }
  };

  // Get plan details
  const getPlanDetails = (planId: string, billingCycle: string) => {
    if (!planId || !billingCycle) return null;
    return PLAN_LIMITS[planId as keyof typeof PLAN_LIMITS]?.[billingCycle as 'monthly' | 'annual'];
  };

  // Get subscription status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-500">Active</Badge>;
      case 'canceled':
        return <Badge className="bg-orange-500">Canceled</Badge>;
      case 'past_due':
        return <Badge className="bg-red-500">Past Due</Badge>;
      case 'unpaid':
        return <Badge className="bg-red-500">Unpaid</Badge>;
      case 'incomplete':
        return <Badge className="bg-gray-500">Incomplete</Badge>;
      case 'incomplete_expired':
        return <Badge className="bg-red-500">Expired</Badge>;
      case 'trialing':
        return <Badge className="bg-blue-500">Trial</Badge>;
      case 'error':
        return <Badge className="bg-yellow-500">Syncing</Badge>;
      default:
        return <Badge className="bg-gray-500">{status}</Badge>;
    }
  };

  // Calculate usage percentages
  const getProjectsUsagePercentage = () => {
    if (!usage?.projects_count || !limits?.projects) return 0;
    return Math.min(100, (usage.projects_count / limits.projects) * 100);
  };

  const getImagesUsagePercentage = () => {
    if (!usage?.images_count || !limits?.imagesPerProject) return 0;
    return Math.min(100, (usage.images_count / (limits.projects * limits.imagesPerProject)) * 100);
  };

  // Function to handle plan change dialog
  const handleShowPlanChange = () => {
    setShowPlanChangeDialog(true);
  };

  const handlePlanChangeComplete = async () => {
    await refetch();
    setShowPlanChangeDialog(false);
  };

  // Combined function to handle cancel or reactivate based on current state
  const handleCancelOrReactivate = () => {
    const isCanceled = subscription?.subscription?.subscription?.cancel_at_period_end ||
                      subscription?.cancel_at_period_end;

    if (isCanceled) {
      handleReactivateSubscription();
    } else {
      handleCancelSubscription();
    }
  };

  // Show a loading state while authentication is being checked
  if (!isAuthenticated) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex flex-col gap-6">
          <div>
            <h1 className="text-3xl font-bold">Account Settings</h1>
            <p className="text-muted-foreground">Manage your account and subscription</p>
          </div>

          <div className="flex justify-center items-center py-20">
            <div className="animate-spin w-10 h-10 border-4 border-primary border-t-transparent rounded-full" aria-label="Loading"/>
            <span className="ml-3 text-lg">Loading account information...</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="flex flex-col gap-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold">Account Settings</h1>
            <p className="text-muted-foreground">Manage your account and subscription</p>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={handleClearCache}
            disabled={isClearing}
          >
            {isClearing ? 'Refreshing...' : 'Refresh Data'}
          </Button>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="mb-6">
            <TabsTrigger value="subscription">Subscription</TabsTrigger>
            <TabsTrigger value="usage">Usage</TabsTrigger>
          </TabsList>

          <TabsContent value="subscription" className="w-full">
            <div className="grid gap-6 md:grid-cols-2 w-full">
              <Card className="w-full">
                <CardHeader>
                  <CardTitle>Subscription Details</CardTitle>
                  <CardDescription>Your current subscription plan and status</CardDescription>
                </CardHeader>
                <CardContent>
                  {isLoading ? (
                    <div className="flex justify-center p-6">
                      <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full" aria-label="Loading"/>
                    </div>
                  ) : (subscription?.subscription?.subscription || (subscription && subscription.plan_id)) ? (
                    <div className="space-y-4">
                      <div className="flex justify-between items-center">
                        <h3 className="text-lg font-medium capitalize">
                          {(subscription?.subscription?.subscription?.plan_id || subscription.plan_id)} Plan
                        </h3>
                        {getStatusBadge(subscription?.subscription?.subscription?.status || subscription.status)}
                      </div>

                      <div className="space-y-3 mt-4">
                        <div className="flex items-center">
                          <Calendar className="h-4 w-4 text-gray-500 mr-2" />
                          <div>
                            <p className="text-sm font-medium">Start Date</p>
                            <p className="text-sm text-gray-500">
                              {(subscription?.subscription?.subscription?.current_period_start || subscription.current_period_start) ?
                                new Date(subscription?.subscription?.subscription?.current_period_start || subscription.current_period_start).toLocaleDateString('en-US', {
                                  year: 'numeric', month: 'long', day: 'numeric'
                                }) :
                                'Not available'
                              }
                            </p>
                          </div>
                        </div>

                        <div className="flex items-center">
                          <CreditCard className="h-4 w-4 text-gray-500 mr-2" />
                          <div>
                            <p className="text-sm font-medium">Billing Cycle</p>
                            <p className="text-sm text-gray-500 capitalize">
                              {subscription?.subscription?.subscription?.billing_cycle || subscription.billing_cycle}
                              {(subscription?.subscription?.subscription?.billing_cycle === 'annual' || subscription.billing_cycle === 'annual') && ' (20% discount)'}
                            </p>
                          </div>
                        </div>

                        <div className="flex items-center">
                          <RefreshCw className="h-4 w-4 text-gray-500 mr-2" />
                          <div>
                            <p className="text-sm font-medium">Next Billing Date</p>
                            <p className="text-sm text-gray-500">
                              {(subscription?.subscription?.subscription?.current_period_end || subscription.current_period_end) ?
                                new Date(subscription?.subscription?.subscription?.current_period_end || subscription.current_period_end).toLocaleDateString('en-US', {
                                  year: 'numeric', month: 'long', day: 'numeric'
                                }) :
                                'Not available'
                              }
                            </p>
                          </div>
                        </div>

                        {(subscription?.subscription?.subscription?.cancel_at_period_end || subscription.cancel_at_period_end) && (
                          <div className="flex items-start mt-4 p-3 bg-amber-50 border border-amber-200 rounded-md">
                            <AlertTriangle className="h-5 w-5 text-amber-500 mr-2 flex-shrink-0 mt-0.5" />
                            <div>
                              <p className="text-sm font-medium text-amber-800">Subscription Ending</p>
                              <p className="text-sm text-amber-700">
                                Your subscription will end on {new Date(subscription?.subscription?.subscription?.current_period_end || subscription.current_period_end).toLocaleDateString('en-US', {
                                  year: 'numeric', month: 'long', day: 'numeric'
                                })}. You can reactivate it before this date to maintain access.
                              </p>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center py-6 text-center">
                      <Shield className="h-12 w-12 text-gray-300 mb-4" />
                      <h3 className="text-lg font-medium mb-2">Free Tier</h3>
                      <p className="text-sm text-gray-500 mb-4">
                        You're currently on the free tier with limited features. Subscribe to a plan to access premium features.
                      </p>
                      <div className="bg-gray-50 rounded-md p-4 mt-2 mb-4 text-left w-full">
                        <h4 className="text-sm font-medium mb-2">Free Tier Limits:</h4>
                        <ul className="pl-4 space-y-1">
                          <li className="text-sm flex items-center">
                            <CheckCircle className="h-3.5 w-3.5 text-green-500 mr-2 flex-shrink-0" />
                            1 project
                          </li>
                          <li className="text-sm flex items-center">
                            <CheckCircle className="h-3.5 w-3.5 text-green-500 mr-2 flex-shrink-0" />
                            1 image per project
                          </li>
                        </ul>
                      </div>
                      <Button asChild>
                        <Link to="/pricing">Upgrade Now</Link>
                      </Button>
                    </div>
                  )}
                </CardContent>
                {(subscription?.subscription?.subscription || (subscription && subscription.plan_id)) && (
                  <CardFooter className="flex flex-col space-y-2">
                    {/* Show Change Plan button for all subscription states except past_due */}
                    {subscription?.subscription?.subscription?.status !== 'past_due' && (
                      <Button
                        variant="default"
                        className="w-full"
                        onClick={handleShowPlanChange}
                        disabled={isUpdating}
                      >
                        {isUpdating ? 'Processing...' : 'Change Plan'}
                      </Button>
                    )}

                    {/* Show Cancel/Reactivate button for active subscriptions */}
                    {(subscription?.subscription?.subscription?.status === 'active' || subscription?.status === 'active') && (
                      <Button
                        variant="outline"
                        className="w-full"
                        onClick={handleCancelOrReactivate}
                        disabled={isUpdating}
                      >
                        {isUpdating ? (
                          'Processing...'
                        ) : subscription?.subscription?.subscription?.cancel_at_period_end ||
                          subscription?.cancel_at_period_end ? (
                          'Reactivate Subscription'
                        ) : (
                          'Cancel Subscription'
                        )}
                      </Button>
                    )}

                    {/* Show subscription status messages */}
                    {subscription?.subscription?.subscription?.status === 'past_due' && (
                      <Alert variant="destructive">
                        <AlertTriangle className="h-4 w-4" />
                        <AlertDescription>
                          Your subscription is past due. Please update your payment method.
                        </AlertDescription>
                      </Alert>
                    )}

                    {(subscription?.subscription?.subscription?.cancel_at_period_end ||
                      subscription?.cancel_at_period_end) && (
                      <Alert variant="warning">
                        <AlertTriangle className="h-4 w-4" />
                        <AlertDescription>
                          Your subscription will end on{' '}
                          {new Date(
                            subscription?.subscription?.subscription?.current_period_end ||
                            subscription?.current_period_end
                          ).toLocaleDateString()}
                        </AlertDescription>
                      </Alert>
                    )}
                  </CardFooter>
                )}
              </Card>

              <Card className="w-full">
                <CardHeader>
                  <CardTitle>Plan Features</CardTitle>
                  <CardDescription>What's included in your plan</CardDescription>
                </CardHeader>
                <CardContent>
                  {isLoading ? (
                    <div className="flex justify-center p-6">
                      <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full" aria-label="Loading"/>
                    </div>
                  ) : (subscription?.subscription?.subscription || (subscription && subscription.plan_id)) ? (
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <div className="flex items-center">
                          <Zap className="h-4 w-4 text-primary mr-2" />
                          <p className="text-sm font-medium">Plan Limits</p>
                        </div>
                        <ul className="pl-6 space-y-1">
                          <li className="text-sm flex items-center">
                            <CheckCircle className="h-3.5 w-3.5 text-green-500 mr-2" />
                            {limits?.projects} projects per month
                          </li>
                          <li className="text-sm flex items-center">
                            <CheckCircle className="h-3.5 w-3.5 text-green-500 mr-2" />
                            {limits?.imagesPerProject} images per project
                          </li>
                        </ul>
                      </div>

                      <div className="space-y-2">
                        <div className="flex items-center">
                          <Shield className="h-4 w-4 text-primary mr-2" />
                          <p className="text-sm font-medium">Features</p>
                        </div>
                        <ul className="pl-6 space-y-1">
                          {getPlanDetails(
                            subscription?.subscription?.subscription?.plan_id || subscription.plan_id,
                            subscription?.subscription?.subscription?.billing_cycle || subscription.billing_cycle
                          )?.features.map((feature, index) => (
                            <li key={index} className="text-sm flex items-center">
                              <CheckCircle className="h-3.5 w-3.5 text-green-500 mr-2" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </div>

                      {(subscription?.subscription?.subscription?.plan_id === 'starter' || subscription.plan_id === 'starter') && (
                        <div className="mt-6 pt-4 border-t border-gray-200">
                          <h4 className="text-sm font-medium mb-2">Upgrade to Professional for:</h4>
                          <ul className="pl-6 space-y-1">
                            {PLAN_LIMITS.professional.monthly.features
                              .filter(f => !PLAN_LIMITS.starter.monthly.features.includes(f))
                              .map((feature, index) => (
                                <li key={index} className="text-sm flex items-center text-gray-600">
                                  <ArrowRight className="h-3.5 w-3.5 text-primary mr-2" />
                                  {feature}
                                </li>
                              ))}
                          </ul>
                          <Button
                            variant="outline"
                            className="w-full mt-4"
                            onClick={handleUpgradeSubscription}
                          >
                            Upgrade Now
                          </Button>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <div className="flex items-center">
                          <Zap className="h-4 w-4 text-primary mr-2" />
                          <p className="text-sm font-medium">Free Plan Limits</p>
                        </div>
                        <ul className="pl-6 space-y-1">
                          <li className="text-sm flex items-center">
                            <XCircle className="h-3.5 w-3.5 text-red-500 mr-2" />
                            Limited to 1 project
                          </li>
                          <li className="text-sm flex items-center">
                            <XCircle className="h-3.5 w-3.5 text-red-500 mr-2" />
                            Limited to 2 images per project
                          </li>
                        </ul>
                      </div>

                      <div className="space-y-2">
                        <div className="flex items-center">
                          <Shield className="h-4 w-4 text-primary mr-2" />
                          <p className="text-sm font-medium">Available Plans</p>
                        </div>
                        <div className="space-y-4 mt-2">
                          <div className="p-3 border rounded-md">
                            <h4 className="font-medium">Starter Plan</h4>
                            <p className="text-sm text-gray-500 mb-2">
                              {formatPrice(PLAN_LIMITS.starter.monthly.price)}/month or {formatPrice(PLAN_LIMITS.starter.annual.price)}/month billed annually
                            </p>
                            <ul className="pl-6 space-y-1 mb-3">
                              {PLAN_LIMITS.starter.monthly.features.slice(0, 3).map((feature, index) => (
                                <li key={index} className="text-sm flex items-center">
                                  <CheckCircle className="h-3.5 w-3.5 text-green-500 mr-2" />
                                  {feature}
                                </li>
                              ))}
                            </ul>
                          </div>

                          <div className="p-3 border rounded-md bg-gray-50">
                            <h4 className="font-medium">Professional Plan</h4>
                            <p className="text-sm text-gray-500 mb-2">
                              {formatPrice(PLAN_LIMITS.professional.monthly.price)}/month or {formatPrice(PLAN_LIMITS.professional.annual.price)}/month billed annually
                            </p>
                            <ul className="pl-6 space-y-1 mb-3">
                              {PLAN_LIMITS.professional.monthly.features.slice(0, 3).map((feature, index) => (
                                <li key={index} className="text-sm flex items-center">
                                  <CheckCircle className="h-3.5 w-3.5 text-green-500 mr-2" />
                                  {feature}
                                </li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      </div>

                      <Button className="w-full" asChild>
                        <Link to="/pricing">View All Plans</Link>
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="usage" className="w-full">
            <div className="grid grid-cols-1 gap-6 w-full">
              <Card className="w-full">
                <CardHeader>
                  <CardTitle>Usage Statistics</CardTitle>
                  <CardDescription>Your current usage and limits</CardDescription>
                </CardHeader>
                <CardContent>
                  {isLoading ? (
                    <div className="flex justify-center p-6">
                      <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full" aria-label="Loading"/>
                    </div>
                  ) : (subscription?.subscription?.subscription || (subscription && subscription.plan_id)) ? (
                    <div className="space-y-6">
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <h3 className="text-sm font-medium">Projects</h3>
                          <p className="text-sm text-gray-500">
                            {usage?.projects_count || 0} / {limits?.projects || 0}
                          </p>
                        </div>
                        <Progress value={getProjectsUsagePercentage()} className="h-2" />
                        <p className="text-xs text-gray-500">
                          {limits?.projects && usage?.projects_count && limits.projects - usage.projects_count > 0 ?
                            `${limits.projects - usage.projects_count} projects remaining this billing period` :
                            'Project limit reached'
                          }
                        </p>
                      </div>

                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <h3 className="text-sm font-medium">Images</h3>
                        <p className="text-sm text-gray-500">
                          {usage?.images_count || 0} / {(limits?.projects || 0) * (limits?.imagesPerProject || 0)}
                        </p>
                      </div>
                      <Progress value={getImagesUsagePercentage()} className="h-2" />
                      <p className="text-xs text-gray-500">
                        {limits?.projects && limits?.imagesPerProject && usage?.images_count &&
                          (limits.projects * limits.imagesPerProject) - usage.images_count > 0 ?
                          `${(limits.projects * limits.imagesPerProject) - usage.images_count} images remaining this billing period` :
                          'Image limit reached'
                        }
                      </p>
                    </div>

                    <div className="pt-4 border-t border-gray-200">
                      <h3 className="text-sm font-medium mb-2">Usage Period</h3>
                      <div className="flex justify-between text-sm">
                        <p className="text-gray-500">
                          {(subscription?.subscription?.subscription?.current_period_start || subscription.current_period_start) ?
                            new Date(subscription?.subscription?.subscription?.current_period_start || subscription.current_period_start).toLocaleDateString('en-US', {
                              year: 'numeric', month: 'short', day: 'numeric'
                            }) : 'N/A'
                          }
                        </p>
                        <p className="text-gray-500">to</p>
                        <p className="text-gray-500">
                          {(subscription?.subscription?.subscription?.current_period_end || subscription.current_period_end) ?
                            new Date(subscription?.subscription?.subscription?.current_period_end || subscription.current_period_end).toLocaleDateString('en-US', {
                              year: 'numeric', month: 'short', day: 'numeric'
                            }) : 'N/A'
                          }
                        </p>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-6">
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <h3 className="text-sm font-medium">Projects</h3>
                        <p className="text-sm text-gray-500">
                          {usage?.projects_count || 0} / {limits?.projects || 1}
                        </p>
                      </div>
                      <Progress value={getProjectsUsagePercentage()} className="h-2" />
                      <p className="text-xs text-gray-500">
                        {limits?.projects && usage?.projects_count && limits.projects - usage.projects_count > 0 ?
                          `${limits.projects - usage.projects_count} projects remaining on free tier` :
                          'Project limit reached'
                        }
                      </p>
                    </div>

                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <h3 className="text-sm font-medium">Images</h3>
                        <p className="text-sm text-gray-500">
                          {usage?.images_count || 0} / {(limits?.projects || 1) * (limits?.imagesPerProject || 1)}
                        </p>
                      </div>
                      <Progress value={getImagesUsagePercentage()} className="h-2" />
                      <p className="text-xs text-gray-500">
                        {limits?.projects && limits?.imagesPerProject && usage?.images_count &&
                          (limits.projects * limits.imagesPerProject) - usage.images_count > 0 ?
                          `${(limits.projects * limits.imagesPerProject) - usage.images_count} images remaining on free tier` :
                          'Image limit reached'
                        }
                      </p>
                    </div>

                    <div className="pt-4 border-t border-gray-200 text-center">
                      <p className="text-sm text-gray-500 mb-4">
                        Upgrade to a paid plan for more projects and images.
                      </p>
                      <Button asChild>
                        <Link to="/pricing">Upgrade Now</Link>
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
          </TabsContent>
        </Tabs>
      </div>

      <PlanChangeDialog
        isOpen={showPlanChangeDialog}
        onClose={() => setShowPlanChangeDialog(false)}
        currentPlan={subscription?.subscription?.subscription?.items?.data[0]?.price?.lookup_key?.split('_')[0] || ''}
        currentBillingCycle={subscription?.subscription?.subscription?.items?.data[0]?.price?.lookup_key?.split('_')[1] || 'monthly'}
        subscriptionStatus={subscription?.subscription?.subscription?.status || ''}
        isCanceled={subscription?.subscription?.subscription?.cancel_at_period_end || false}
        onPlanChange={handlePlanChangeComplete}
      />
    </div>
  );
}
