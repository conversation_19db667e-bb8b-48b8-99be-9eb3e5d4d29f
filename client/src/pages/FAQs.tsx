import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Card, CardContent } from "@/components/ui/card";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search } from "lucide-react";
import { useState } from "react";

export default function FAQs() {
  const [searchQuery, setSearchQuery] = useState("");

  // Sample FAQ categories and questions
  const faqCategories = [
    {
      id: "general",
      name: "General",
      faqs: [
        {
          question: "What is Renovision Studio?",
          answer: "Renovision Studio is an AI-powered platform that helps tradespeople, interior designers, and renovation professionals create realistic visualizations of renovation projects. By uploading 'before' images and providing instructions, you can generate photorealistic 'after' images to show clients how their space could look after renovation."
        },
        {
          question: "Who is Renovision Studio for?",
          answer: "Renovision Studio is designed for renovation professionals including contractors, interior designers, kitchen and bath specialists, architects, and real estate professionals who want to help clients visualize potential changes to their spaces."
        },
        {
          question: "Do I need design experience to use Renovision Studio?",
          answer: "No, you don't need design experience. Our platform is designed to be user-friendly for all professionals in the renovation industry. You simply upload photos, provide instructions or reference images, and our AI handles the visualization."
        },
        {
          question: "How accurate are the visualizations?",
          answer: "Our AI generates highly realistic visualizations based on the information you provide. While they're not exact architectural renderings, they're detailed enough to give clients a clear understanding of how the renovated space will look and feel. The quality of your input images and the clarity of your instructions directly impact the results."
        }
      ]
    },
    {
      id: "subscription",
      name: "Subscription & Pricing",
      faqs: [
        {
          question: "What subscription plans do you offer?",
          answer: "We offer several subscription tiers to meet different needs: Basic, Professional, and Enterprise. Each plan varies in terms of the number of visualizations per month, additional features, and priority processing. Visit our Pricing page for detailed information about each plan."
        },
        {
          question: "Can I change or cancel my subscription?",
          answer: "Yes, you can upgrade, downgrade, or cancel your subscription at any time from your Account settings. Upgrades take effect immediately, while downgrades or cancellations take effect at the end of your current billing cycle."
        },
        {
          question: "Do you offer a free trial?",
          answer: "Yes, new users can try Renovision Studio with a limited number of visualizations before committing to a subscription. This allows you to test the platform and see the quality of results before purchasing."
        },
        {
          question: "What payment methods do you accept?",
          answer: "We accept all major credit cards (Visa, Mastercard, American Express) and PayPal. All payments are processed securely through Stripe."
        },
        {
          question: "Do you offer refunds?",
          answer: "We don't typically offer refunds for subscription payments, but we may consider exceptions in certain circumstances. Please contact our support team if you have concerns about your subscription."
        }
      ]
    },
    {
      id: "usage",
      name: "Using the Platform",
      faqs: [
        {
          question: "What types of spaces can I visualize?",
          answer: "Renovision Studio works best with interior spaces such as kitchens, bathrooms, living rooms, bedrooms, and offices. While it can handle some exterior visualizations, interior renovations typically produce the best results."
        },
        {
          question: "How long does it take to generate a visualization?",
          answer: "Most visualizations are processed within minutes, though processing times may vary based on system load and your subscription tier. Enterprise and Professional plans include priority processing."
        },
        {
          question: "What image formats are supported?",
          answer: "We support JPG, PNG, and WEBP image formats. For best results, use high-resolution images (at least 1920x1080 pixels) with good lighting."
        },
        {
          question: "Can I request revisions to a visualization?",
          answer: "Yes, you can request revisions by adjusting your instructions and resubmitting the project. Each revision counts as a new visualization against your monthly quota."
        },
        {
          question: "How do I share visualizations with clients?",
          answer: "You can download the before/after images to share via email or your preferred communication method. Professional and Enterprise plans also include shareable links that allow clients to view the comparison directly in their browser."
        }
      ]
    },
    {
      id: "technical",
      name: "Technical Questions",
      faqs: [
        {
          question: "What are the recommended image specifications?",
          answer: "For best results, use images with the following specifications:\n- Resolution: At least 1920x1080 pixels\n- Format: JPG, PNG, or WEBP\n- Size: Under 10MB\n- Lighting: Well-lit spaces with minimal shadows\n- Angle: Taken from a corner to show more of the room"
        },
        {
          question: "Can I use Renovision Studio on mobile devices?",
          answer: "Yes, our platform is fully responsive and works on smartphones and tablets. However, for the best experience when uploading images and creating detailed projects, we recommend using a desktop or laptop computer."
        },
        {
          question: "Is my data secure?",
          answer: "Yes, we take data security seriously. All images and project information are stored securely, and we use industry-standard encryption for all data transfers. We do not share your data with third parties without your consent."
        },
        {
          question: "What browsers are supported?",
          answer: "Renovision Studio works best on modern browsers including Chrome, Firefox, Safari, and Edge. We recommend keeping your browser updated to the latest version for optimal performance."
        }
      ]
    },
    {
      id: "account",
      name: "Account Management",
      faqs: [
        {
          question: "How do I reset my password?",
          answer: "To reset your password, click on the 'Forgot password' link on the sign-in page. You'll receive an email with instructions to create a new password."
        },
        {
          question: "Can I have multiple users on one account?",
          answer: "Enterprise plans support team accounts with multiple users. Each user gets their own login credentials while sharing the account's visualization quota and resources. Contact our sales team for more information about team accounts."
        },
        {
          question: "How do I update my billing information?",
          answer: "You can update your billing information in the Account section under Subscription settings. Any changes will apply to your next billing cycle."
        },
        {
          question: "Can I export my project data?",
          answer: "Yes, you can download your visualization images at any time. Enterprise plans also include the ability to export project details and analytics in various formats."
        }
      ]
    }
  ];

  // Filter FAQs based on search query
  const filteredCategories = faqCategories.map(category => ({
    ...category,
    faqs: category.faqs.filter(faq => 
      faq.question.toLowerCase().includes(searchQuery.toLowerCase()) || 
      faq.answer.toLowerCase().includes(searchQuery.toLowerCase())
    )
  })).filter(category => category.faqs.length > 0);

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Frequently Asked Questions</h1>
        <p className="text-gray-600 mb-6">
          Find answers to common questions about Renovision Studio.
        </p>
        
        <div className="relative max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
          <Input
            type="search"
            placeholder="Search FAQs..."
            className="pl-10"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </div>

      {searchQuery ? (
        // Search results view
        <div className="space-y-6">
          {filteredCategories.map((category) => (
            <div key={category.id}>
              <h2 className="text-xl font-semibold mb-4">{category.name}</h2>
              <Accordion type="single" collapsible className="space-y-4">
                {category.faqs.map((faq, index) => (
                  <AccordionItem key={index} value={`${category.id}-${index}`} className="border rounded-lg p-1">
                    <AccordionTrigger className="px-4 hover:no-underline">
                      <span className="text-left font-medium">{faq.question}</span>
                    </AccordionTrigger>
                    <AccordionContent className="px-4 pb-4 pt-2 text-gray-600">
                      {faq.answer}
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            </div>
          ))}
        </div>
      ) : (
        // Default tabbed view
        <Tabs defaultValue="general">
          <TabsList className="mb-6">
            {faqCategories.map((category) => (
              <TabsTrigger key={category.id} value={category.id}>
                {category.name}
              </TabsTrigger>
            ))}
          </TabsList>

          {faqCategories.map((category) => (
            <TabsContent key={category.id} value={category.id}>
              <Card>
                <CardContent className="pt-6">
                  <Accordion type="single" collapsible className="space-y-4">
                    {category.faqs.map((faq, index) => (
                      <AccordionItem key={index} value={`item-${index}`} className="border rounded-lg p-1">
                        <AccordionTrigger className="px-4 hover:no-underline">
                          <span className="text-left font-medium">{faq.question}</span>
                        </AccordionTrigger>
                        <AccordionContent className="px-4 pb-4 pt-2 text-gray-600">
                          {faq.answer}
                        </AccordionContent>
                      </AccordionItem>
                    ))}
                  </Accordion>
                </CardContent>
              </Card>
            </TabsContent>
          ))}
        </Tabs>
      )}

      <div className="mt-12 bg-gray-50 rounded-lg p-6 text-center">
        <h2 className="text-xl font-semibold mb-2">Still have questions?</h2>
        <p className="text-gray-600 mb-4">
          If you couldn't find the answer you were looking for, our support team is here to help.
        </p>
        <Button asChild>
          <a href="/contact">Contact Support</a>
        </Button>
      </div>
    </div>
  );
}
