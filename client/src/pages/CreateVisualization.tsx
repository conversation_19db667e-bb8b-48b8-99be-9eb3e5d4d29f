import { useState, useEffect, useCallback, useTransition } from "react";
import { useLocation, Link } from "wouter";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { ImageUploader } from "@/components/ui/image-uploader";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useToast } from "@/hooks/use-toast";
import { queryClient } from "@/lib/queryClient";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import {
  createProject,
  uploadBeforeImages,
  uploadReferenceImages,
  createModification,
  getDrafts,
  createDraft,
  updateDraft,
  deleteDraft,
  ModificationType,
  StructuredModification
} from "@/lib/api";
import { ReferenceItemSelector } from "@/components/ReferenceItemSelector";
import { CreateVisualizationFlow } from "@/components/CreateVisualizationFlow";
import { useClerkAuth } from "@/hooks/use-clerk-auth";
import { useSubscription } from "@/contexts/SubscriptionContext";
import { logger } from "@/lib/queryClient";
import { useDebounce } from "@/hooks/useDebounce";
import { AlertCircle, CreditCard } from "lucide-react";

// Define ReferenceItem type to match the one in ReferenceItemSelector
type ReferenceItem = {
  id: number;
  name: string;
  description?: string;
  imagePath: string;
  tags?: string[];
  categoryId?: number;
};

// Form schema for project creation
const projectFormSchema = z.object({
  title: z.string().min(3, { message: "Project title must be at least 3 characters" }),
  description: z.string().optional(),
  modificationDescription: z.string().optional(), // Make this optional for the first step
});

type ProjectFormValues = z.infer<typeof projectFormSchema>;

type UploadedImageType = {
  id: number;
  path: string;
  originalFilename?: string | null;
  type?: string;
  projectId?: number | null;
  uploadedAt?: Date;
};

// Type for saved drafts
type SavedDraft = {
  id: number;
  user_id: string;
  title: string;
  description?: string;
  modification_description?: string;
  project_id?: number;
  before_images?: UploadedImageType[];
  reference_images?: UploadedImageType[];
  step?: number;
  modification_type?: string;
  modification_options?: any;
  created_at: string;
  updated_at: string;
};

export default function CreateVisualization() {
  const [step, setStep] = useState(0); // Start at step 0 for draft selection
  const [projectId, setProjectId] = useState<number | null>(null);
  const [beforeImages, setBeforeImages] = useState<UploadedImageType[]>([]);
  const [referenceImages, setReferenceImages] = useState<UploadedImageType[]>([]);
  const [isReferenceLibraryOpen, setIsReferenceLibraryOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<string>("upload");
  const [savedDrafts, setSavedDrafts] = useState<SavedDraft[]>([]);
  const [subscription, setSubscription] = useState<any>(null);
  const [isLoadingSubscription, setIsLoadingSubscription] = useState(true);
  const [selectedModificationType, setSelectedModificationType] = useState<ModificationType | null>(null);
  const [modificationOptions, setModificationOptions] = useState<any>(null);
  const [location, navigate] = useLocation();
  const { toast } = useToast();
  const { authToken, isAuthenticated } = useClerkAuth();
  const { hasActiveSubscription } = useSubscription();
  const [autoSaveEnabled, setAutoSaveEnabled] = useState(true);
  const [lastAutoSaveTime, setLastAutoSaveTime] = useState<number | null>(null);
  const [isLoadingFromDraft, setIsLoadingFromDraft] = useState(false);
  const [currentDraftId, setCurrentDraftId] = useState<number | null>(null);
  // Generate unique session ID for draft deduplication
  // This ID is used by the server's advisory lock system to prevent duplicate drafts
  const [draftSessionId] = useState(() => `draft-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`);
  const [lastSavedContent, setLastSavedContent] = useState<string>('');

  // Parse URL parameters
  const urlParams = new URLSearchParams(location.search);
  const draftIdParam = urlParams.get('draftId');
  const projectIdParam = urlParams.get('projectId');

  // Store the auth token in localStorage whenever it changes
  useEffect(() => {
    if (authToken) {
      localStorage.setItem('clerk-auth-token', authToken);
      logger.debug('Auth token stored in localStorage');
    }
  }, [authToken]);

  // Form setup
  const form = useForm<ProjectFormValues>({
    resolver: zodResolver(projectFormSchema),
    defaultValues: {
      title: "",
      description: "",
      modificationDescription: "",
    },
  });

  // We'll move this useEffect after defining the functions it depends on

  // Load drafts using React Query, only if authenticated
  const { data: loadedDrafts, isLoading: isDraftsLoading } = useQuery({
    queryKey: ['/api/drafts'],
    queryFn: getDrafts,
    // Skip the query if the user is not authenticated
    enabled: isAuthenticated,
    // Handle errors gracefully
    onError: (error) => {
      console.error("Error loading drafts:", error);
      // Don't show error toast, just log it
    }
  });

  // Update drafts state when loadedDrafts changes
  useEffect(() => {
    if (loadedDrafts) {
      setSavedDrafts(loadedDrafts);

      // If a draft ID is provided in the URL, load that draft
      if (draftIdParam) {
        const draftId = parseInt(draftIdParam);
        const draft = loadedDrafts.find(d => d.id === draftId);

        if (draft) {
          // Set the current draft ID so auto-save updates this draft
          setCurrentDraftId(draft.id);

          // Load the draft data into the form
          form.setValue("title", draft.title || "");
          form.setValue("description", draft.description || "");

          // Update lastSavedContent to prevent immediate auto-save after loading
          const loadedContent = JSON.stringify({
            title: (draft.title || '').trim(),
            description: (draft.description || '').trim(),
            step: draft.step || 1,
            modification_description: (draft.modification_description || '').trim()
          });
          setLastSavedContent(loadedContent);

          if (draft.modification_description) {
            form.setValue("modificationDescription", draft.modification_description);
          }

          // Set project ID if available
          if (draft.project_id) {
            setProjectId(draft.project_id);
          }

          // Set images if available
          if (draft.before_images && draft.before_images.length > 0) {
            setBeforeImages(draft.before_images);
          }

          if (draft.reference_images && draft.reference_images.length > 0) {
            setReferenceImages(draft.reference_images);
          }

          // Set modification type and options if available
          if (draft.modification_type) {
            setSelectedModificationType(draft.modification_type as ModificationType);
          }

          if (draft.modification_options) {
            setModificationOptions(draft.modification_options);
          }

          // Set the step based on the draft's step or default to step 1
          setStep(draft.step || 1);

          toast({
            title: "Draft loaded",
            description: "Your draft has been loaded successfully",
          });
        } else {
          toast({
            title: "Draft not found",
            description: "The requested draft could not be found",
            variant: "destructive",
          });
        }
      }
    }
  }, [loadedDrafts, draftIdParam, form, toast]);

  // Load project if project ID is provided in the URL
  useEffect(() => {
    if (projectIdParam && isAuthenticated) {
      const loadProject = async () => {
        try {
          const projectId = parseInt(projectIdParam);
          setProjectId(projectId);

          // Fetch the project details
          const response = await fetch(`/api/projects/${projectId}`);
          if (response.ok) {
            const project = await response.json();

            // Load the project data into the form
            form.setValue("title", project.title || "");
            form.setValue("description", project.description || "");

            // Set images if available
            if (project.beforeImages && project.beforeImages.length > 0) {
              setBeforeImages(project.beforeImages);
            }

            if (project.referenceImages && project.referenceImages.length > 0) {
              setReferenceImages(project.referenceImages);
            }

            // Skip to step 2 since we're editing an existing project
            setStep(2);

            toast({
              title: "Project loaded",
              description: "You can continue editing your project",
            });
          } else {
            toast({
              title: "Project not found",
              description: "The requested project could not be found",
              variant: "destructive",
            });
          }
        } catch (error) {
          console.error("Error loading project:", error);
          toast({
            title: "Error loading project",
            description: "There was an error loading the project",
            variant: "destructive",
          });
        }
      };

      loadProject();
    }
  }, [projectIdParam, isAuthenticated, form, toast]);

  // Fetch subscription details
  useEffect(() => {
    if (isAuthenticated) {
      const fetchSubscription = async () => {
        try {
          setIsLoadingSubscription(true);
          const response = await fetch('/api/subscription');
          if (response.ok) {
            const data = await response.json();
            console.log('[DEBUG] CreateVisualization - Subscription data:', JSON.stringify(data, null, 2));

            // Log the nested structure to help with debugging
            console.log('[DEBUG] CreateVisualization - Subscription structure:', {
              hasSubscriptionObject: !!data?.subscription,
              nestedSubscription: !!data?.subscription?.subscription,
              subscriptionKeys: data?.subscription ? Object.keys(data.subscription) : [],
              subscriptionType: data?.subscription ? typeof data.subscription : 'null',
              subscriptionNull: data?.subscription === null,
              status: data?.subscription?.subscription?.status || 'missing',
            });

            // Check subscription status
            const hasActiveSubscription = data?.subscription?.subscription?.status === 'active';

            console.log('[DEBUG] CreateVisualization - Has active subscription:', hasActiveSubscription);

            setSubscription(data);
          } else if (response.status !== 404) {
            // 404 is expected if no subscription exists
            console.error('Error fetching subscription:', response.statusText);
          }
        } catch (error) {
          console.error('Error fetching subscription:', error);
        } finally {
          setIsLoadingSubscription(false);
        }
      };

      fetchSubscription();
    }
  }, [isAuthenticated]);

  // Create draft mutation with enhanced error handling for deduplication
  // Handles 200 responses when the server returns an existing draft instead of creating a new one
  const createDraftMutation = useMutation({
    mutationKey: ['createDraft', draftSessionId],
    mutationFn: ({ data, silent = false }: { data: any, silent?: boolean }) => createDraft(data),
    onSuccess: (result, variables) => {
      if (!variables.silent) {
        toast({
          title: "Draft saved",
          description: "Your project has been saved as a draft",
        });
      }
      // Update the drafts list
      queryClient.invalidateQueries({ queryKey: ['/api/drafts'] });
    },
    onError: (error: any, variables) => {
      // Handle duplicate draft responses gracefully - this is expected behavior
      // When the server uses advisory locks to prevent duplicates, it returns
      // existing drafts with 200 status instead of creating new ones
      if (error?.response?.status === 409 || error?.response?.status === 200) {
        // This indicates deduplication worked correctly - existing draft returned
        console.log('Draft already exists for session, using existing draft');
        if (!variables.silent) {
          toast({
            title: "Draft loaded",
            description: "Your existing draft has been loaded",
          });
        }
        // Still update the drafts list
        queryClient.invalidateQueries({ queryKey: ['/api/drafts'] });
        return;
      }
      
      if (!variables.silent) {
        toast({
          title: "Error saving draft",
          description: (error as Error).message,
          variant: "destructive",
        });
      }
    },
  });

  // Update draft mutation (supports both silent and non-silent updates)
  const updateDraftMutation = useMutation({
    mutationFn: ({ id, data, silent = false }: { id: number, data: any, silent?: boolean }) => updateDraft(id, data),
    onSuccess: (data, variables) => {
      if (!variables.silent) {
        toast({
          title: "Draft updated",
          description: "Your project has been updated",
        });
      }
      // Update the drafts list
      queryClient.invalidateQueries({ queryKey: ['/api/drafts'] });
    },
    onError: (error, variables) => {
      if (!variables.silent) {
        toast({
          title: "Error updating draft",
          description: (error as Error).message,
          variant: "destructive",
        });
      } else {
        console.error('Error updating draft:', error);
      }
    },
  });

  // Delete draft mutation (with toast notification)
  const deleteDraftMutation = useMutation({
    mutationFn: deleteDraft,
    onSuccess: () => {
      toast({
        title: "Draft deleted",
        description: "Your draft has been deleted",
      });
      // Update the drafts list
      queryClient.invalidateQueries({ queryKey: ['/api/drafts'] });
    },
    onError: (error) => {
      toast({
        title: "Error deleting draft",
        description: (error as Error).message,
        variant: "destructive",
      });
    },
  });

  // Silent delete draft mutation (no toast notification)
  const deleteDraftSilentMutation = useMutation({
    mutationFn: deleteDraft,
    onSuccess: () => {
      // Silently update the drafts list without showing toast
      queryClient.invalidateQueries({ queryKey: ['/api/drafts'] });
    },
    onError: () => {
      // Silently handle errors for draft cleanup
    },
  });



  // Helper function to save a draft
  const saveDraft = useCallback((draftData: {
    title?: string;
    description?: string;
    modification_description?: string;
    project_id?: number;
    before_images?: any[];
    reference_images?: any[];
    step?: number;
    modification_type?: string;
    modification_options?: any;
  }) => {
    // Prevent saving if mutation is already in progress
    if (createDraftMutation.isPending) return;
    
    // Add session ID to prevent duplicates
    const dataWithSession = {
      ...draftData,
      session_id: draftSessionId
    };
    
    createDraftMutation.mutate({ data: dataWithSession, silent: false });
    setLastAutoSaveTime(Date.now());
  }, [createDraftMutation, draftSessionId]);

  // Simplified auto-save function - ONLY updates existing drafts
  const autoSaveDraft = useCallback(() => {
    if (!autoSaveEnabled || !isAuthenticated) return;

    // Don't auto-save if we're on step 0 (draft selection)  
    if (step === 0) return;

    // Don't auto-save if we're currently loading from a draft
    if (isLoadingFromDraft) return;

    // CRITICAL: Only auto-save if we have a current draft ID
    // The draft should be created immediately when moving to step 1
    if (!currentDraftId) return;

    // Prevent auto-save if update mutation is already in progress
    if (updateDraftMutation.isPending) return;

    // Get current form values
    const formData = form.getValues();

    // Create a content hash to check if content has actually changed
    const currentContent = JSON.stringify({
      title: (formData.title || '').trim(),
      description: (formData.description || '').trim(),
      step: step,
      modification_description: (formData.modificationDescription || '').trim()
    });

    // Don't save if content hasn't changed
    if (currentContent === lastSavedContent) return;

    // Don't auto-save if we've saved in the last 2 seconds
    if (lastAutoSaveTime && Date.now() - lastAutoSaveTime < 2000) return;

    // Create draft data based on current step
    const draftData: any = {
      title: (formData.title || '').trim() || "Untitled Project",
      description: (formData.description || '').trim(),
      step: step,
      session_id: draftSessionId
    };

    // Add step-specific data
    if (step >= 2) {
      draftData.modification_description = (formData.modificationDescription || '').trim();
      draftData.project_id = projectId;
      draftData.before_images = beforeImages;
      draftData.reference_images = referenceImages;
      draftData.modification_type = selectedModificationType;
      draftData.modification_options = modificationOptions;
    }

    // Always update the existing draft (never create)
    updateDraftMutation.mutate({ id: currentDraftId, data: draftData, silent: true }, {
      onSuccess: () => {
        setLastAutoSaveTime(Date.now());
        setLastSavedContent(currentContent);
        queryClient.invalidateQueries({ queryKey: ['/api/drafts'] });
      },
      onError: (error) => {
        console.error('Draft update failed:', error);
      }
    });
  }, [
    autoSaveEnabled,
    isAuthenticated,
    lastAutoSaveTime,
    step,
    form,
    projectId,
    beforeImages,
    referenceImages,
    selectedModificationType,
    modificationOptions,
    updateDraftMutation,
    queryClient,
    currentDraftId,
    isLoadingFromDraft,
    draftSessionId,
    lastSavedContent
  ]);

  // Function to create initial draft when starting a new project
  const createInitialDraft = useCallback(() => {
    // Enhanced checks to prevent duplicate creation
    if (!isAuthenticated || currentDraftId || createDraftMutation.isPending) return;

    const initialDraftData = {
      title: "Untitled Project",
      description: "",
      step: 1,
      session_id: draftSessionId
    };

    createDraftMutation.mutate({ data: initialDraftData, silent: true }, {
      onSuccess: (newDraft) => {
        setCurrentDraftId(newDraft.id);
        setLastSavedContent(JSON.stringify({
          title: "Untitled Project",
          description: "",
          step: 1,
          modification_description: ""
        }));
        // Pre-fill the form with initial values
        form.setValue("title", "Untitled Project");
        form.setValue("description", "");
      },
      onError: (error) => {
        console.error('Initial draft creation failed:', error);
      }
    });
  }, [isAuthenticated, currentDraftId, draftSessionId, createDraftMutation, form]);

  // Debounced version of autoSaveDraft to prevent excessive calls
  const debouncedAutoSaveDraft = useDebounce(autoSaveDraft, 2000); // 2 second delay

  // Helper function to clear a specific draft (shows toast)
  const clearDraft = useCallback((draftId: number) => {
    // If we're deleting the current draft, clear the current draft ID
    if (draftId === currentDraftId) {
      setCurrentDraftId(null);
    }
    deleteDraftMutation.mutate(draftId);
  }, [deleteDraftMutation, currentDraftId]);

  // Helper function to silently clear a specific draft (no toast)
  const clearDraftSilently = useCallback((draftId: number) => {
    // If we're deleting the current draft, clear the current draft ID
    if (draftId === currentDraftId) {
      setCurrentDraftId(null);
    }
    deleteDraftSilentMutation.mutate(draftId);
  }, [deleteDraftSilentMutation, currentDraftId]);

  // Helper function to clear all drafts
  const clearAllDrafts = useCallback(() => {
    // Clear the current draft ID since we're deleting all drafts
    setCurrentDraftId(null);
    // Delete all drafts one by one
    savedDrafts.forEach(draft => {
      deleteDraftMutation.mutate(draft.id);
    });
  }, [savedDrafts, deleteDraftMutation]);

  // Clean up old drafts (older than 7 days) when component mounts
  useEffect(() => {
    // Clean up old localStorage drafts
    const allKeys = Object.keys(localStorage);
    const draftKeys = allKeys.filter(key => key.startsWith('project_draft_'));
    draftKeys.forEach(key => localStorage.removeItem(key));

    // Clean up old database drafts
    if (loadedDrafts) {
      loadedDrafts.forEach(draft => {
        const draftTime = new Date(draft.created_at).getTime();
        const currentTime = new Date().getTime();
        const daysDiff = (currentTime - draftTime) / (1000 * 60 * 60 * 24);

        if (daysDiff > 7) {
          clearDraftSilently(draft.id);
        }
      });
    }
  }, [loadedDrafts, clearDraftSilently]);

  // Set up auto-save on form changes
  useEffect(() => {
    // Watch for form changes
    const subscription = form.watch(() => {
      debouncedAutoSaveDraft();
    });

    // Clean up subscription
    return () => subscription.unsubscribe();
  }, [form, debouncedAutoSaveDraft]);

  // Set up auto-save on page unload
  useEffect(() => {
    // Only add event listener if we're on a step that has data to save
    if (step === 0) return;

    // Function to handle beforeunload event
    const handleBeforeUnload = () => {
      autoSaveDraft();
    };

    // Add event listener
    window.addEventListener('beforeunload', handleBeforeUnload);

    // Clean up
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [step, autoSaveDraft]);

  // Set up periodic auto-save
  useEffect(() => {
    // Only set up timer if we're on a step that has data to save
    if (step === 0) return;

    // Set up interval for auto-save (every 60 seconds)
    const intervalId = setInterval(() => {
      autoSaveDraft();
    }, 60000);

    // Clean up
    return () => {
      clearInterval(intervalId);
    };
  }, [step, autoSaveDraft]);

  // Create project mutation
  const createProjectMutation = useMutation({
    mutationFn: createProject,
    onSuccess: (data) => {
      setProjectId(data.id);

      // Show different messages based on whether we're continuing from a draft
      if (isLoadingFromDraft) {
        toast({
          title: "Project ready",
          description: "Continue with your visualization",
        });
        // Silently clear drafts without showing "Draft deleted" message
        if (loadedDrafts) {
          loadedDrafts.forEach(draft => {
            if (draft.title === form.getValues().title) {
              clearDraftSilently(draft.id);
            }
          });
        }
        // Clear the current draft ID since we've created a project
        setCurrentDraftId(null);
        setIsLoadingFromDraft(false);
      } else {
        toast({
          title: "Project created successfully",
          description: "Now you can upload your images",
        });
        // Clear the current draft ID since we've created a project
        setCurrentDraftId(null);
      }

      setStep(2);
    },
    onError: (error) => {
      toast({
        title: "Error creating project",
        description: error.message,
        variant: "destructive",
      });
      setIsLoadingFromDraft(false);
    },
  });

  // Upload before images mutation
  const uploadBeforeImagesMutation = useMutation({
    mutationFn: ({ projectId, files }: { projectId: number; files: File[] }) =>
      uploadBeforeImages(projectId, files),
    onSuccess: (data) => {
      setBeforeImages(prev => [...prev, ...data]);
      toast({
        title: "Images uploaded successfully",
        description: `${data.length} before ${data.length === 1 ? 'image' : 'images'} uploaded`,
      });
    },
    onError: (error) => {
      toast({
        title: "Error uploading images",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Upload reference images mutation
  const uploadReferenceImagesMutation = useMutation({
    mutationFn: ({ projectId, files }: { projectId: number; files: File[] }) =>
      uploadReferenceImages(projectId, files),
    onSuccess: (data) => {
      setReferenceImages(prev => [...prev, ...data]);
      toast({
        title: "Reference images uploaded successfully",
        description: `${data.length} reference ${data.length === 1 ? 'image' : 'images'} uploaded`,
      });
    },
    onError: (error) => {
      toast({
        title: "Error uploading reference images",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Create modification mutation
  const createModificationMutation = useMutation({
    mutationFn: ({
      projectId,
      type,
      description,
      referenceImageIds,
      options
    }: {
      projectId: number;
      type: ModificationType;
      description: string;
      referenceImageIds?: number[];
      options?: any;
    }) =>
      createModification(projectId, {
        type,
        description,
        referenceImageIds,
        options
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/projects'] });
      toast({
        title: "Modification request submitted",
        description: "Your images are being processed. We'll notify you when they're ready.",
      });
      // Clear all drafts related to this project silently
      if (loadedDrafts) {
        loadedDrafts.forEach(draft => {
          if (draft.project_id === projectId) {
            clearDraftSilently(draft.id);
          }
        });
      }
      navigate(`/projects/${projectId}`);
    },
    onError: (error) => {
      toast({
        title: "Error creating modification",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Handle form submission for step 1 (project creation)
  const handleCreateProject = (data: ProjectFormValues) => {
    logger.info("Creating project with data", data);

    // Prevent multiple submissions
    if (createProjectMutation.isPending) {
      return;
    }

    // Check if user has an active subscription using the subscription context
    console.log('[DEBUG] CreateVisualization - Create project check:', {
      hasActiveSubscription
    });

    if (!hasActiveSubscription) {
      toast({
        title: "Subscription Required",
        description: "You need an active subscription to create projects.",
        variant: "destructive",
      });
      return;
    }

    try {
      // Use startTransition to prevent synchronous suspension errors
      startTransition(() => {
        createProjectMutation.mutate({
          title: data.title,
          description: data.description,
        });
      });
    } catch (error) {
      logger.error("Error in handleCreateProject", error);
      toast({
        title: "Unexpected error",
        description: (error as Error).message || "An unexpected error occurred.",
        variant: "destructive",
      });
    }
  };

  // Handle before image uploads
  const handleBeforeImageUpload = (files: File[]) => {
    if (!projectId) return;
    logger.info("Uploading before images", { projectId, fileCount: files.length });
    try {
      uploadBeforeImagesMutation.mutate({ projectId, files });
    } catch (error) {
      logger.error("Error in handleBeforeImageUpload", error);
      toast({
        title: "Unexpected error",
        description: (error as Error).message || "An unexpected error occurred.",
        variant: "destructive",
      });
    }
  };

  // Handle reference image uploads
  const handleReferenceImageUpload = (files: File[]) => {
    if (!projectId) return;
    logger.info("Uploading reference images", { projectId, fileCount: files.length });
    try {
      uploadReferenceImagesMutation.mutate({ projectId, files });
    } catch (error) {
      logger.error("Error in handleReferenceImageUpload", error);
      toast({
        title: "Unexpected error",
        description: (error as Error).message || "An unexpected error occurred.",
        variant: "destructive",
      });
    }
  };

  // Handle selecting items from reference library
  const handleReferenceItemsSelected = async (items: ReferenceItem[]) => {
    if (!projectId || items.length === 0) return;

    try {
      toast({
        title: "Processing reference items",
        description: "Please wait while we process your selected items...",
      });

      console.log("Original selected items:", JSON.stringify(items, null, 2));

      // Create temporary reference objects directly from the selected items
      const tempReferenceImages = items.map(item => {
        const image: UploadedImageType = {
          id: item.id,
          path: item.imagePath,
          originalFilename: item.name,
          type: "reference",
          projectId: projectId
        };
        return image;
      });

      console.log("Mapped reference images:", JSON.stringify(tempReferenceImages, null, 2));

      // Update state with the newly mapped reference images
      setReferenceImages(tempReferenceImages);

      toast({
        title: "Reference items added",
        description: `${items.length} reference ${items.length === 1 ? 'item' : 'items'} added from your library`,
      });

      // Switch to the library tab
      setActiveTab("library");

      // Close the reference library dialog
      setIsReferenceLibraryOpen(false);
    } catch (error) {
      toast({
        title: "Error adding reference items",
        description: "Could not process the selected reference items.",
        variant: "destructive",
      });
      console.error("Error processing reference items:", error);
    }
  };

  // Add isPending state for transitions
  const [isPending, startTransition] = useTransition();

  // Function to load a specific draft
  const loadDraft = (draft: SavedDraft) => {
    // Use startTransition to prevent synchronous suspension errors
    startTransition(() => {
      // Set form values
      form.setValue('title', draft.title || '');
      form.setValue('description', draft.description || '');

      // Set the current draft ID so auto-save updates this draft
      setCurrentDraftId(draft.id);

      // Update lastSavedContent to prevent immediate auto-save after loading
      const loadedContent = JSON.stringify({
        title: (draft.title || '').trim(),
        description: (draft.description || '').trim(),
        step: draft.step || 1,
        modification_description: (draft.modification_description || '').trim()
      });
      setLastSavedContent(loadedContent);

      // Mark that we're continuing from a draft
      setIsLoadingFromDraft(true);

      // If we have step 2 data
      if (draft.step === 2) {
        form.setValue('modificationDescription', draft.modification_description || '');

        // Load structured modification data if available
        if (draft.modification_type) {
          setSelectedModificationType(draft.modification_type as ModificationType);
        }

        if (draft.modification_options) {
          setModificationOptions(draft.modification_options);
        }

        // If we have a project ID, set it and move to step 2
        if (draft.project_id) {
          setProjectId(draft.project_id);

          // If we have images, set them
          if (draft.before_images) setBeforeImages(draft.before_images);
          if (draft.reference_images) setReferenceImages(draft.reference_images);

          // Move to step 2
          setStep(2);
        } else {
          // If no project ID, move to step 1
          setStep(1);
        }
      } else {
        // Move to step 1
        setStep(1);
      }

      toast({
        title: "Draft loaded",
        description: "Your saved draft has been loaded",
      });
    });
  };

  // Handle selection of a pre-prepared modification option
  const handleSelectModificationOption = (type: ModificationType, options: any) => {
    setSelectedModificationType(type);
    setModificationOptions(options);

    // Auto-fill the description field with a formatted version of the selected options
    if (type === "replace_floor") {
      if (options.useReferenceImage && options.referenceImageId) {
        // Find the selected reference image
        const selectedImage = referenceImages.find(img => img.id === options.referenceImageId);
        const imageName = selectedImage?.originalFilename || "selected reference image";
        const customDesc = options.customDescription ? options.customDescription : "";

        const description = `Replace the floor using the reference image "${imageName}" as inspiration. ${customDesc}`.trim();
        form.setValue("modificationDescription", description);
      } else {
        // Standard material/color selection
        const material = options.material ? floorMaterialName(options.material) : "";
        const color = options.color ? options.color : "";
        const customDesc = options.customDescription ? options.customDescription : "";

        const description = `Replace the floor with ${color} ${material} flooring. ${customDesc}`.trim();
        form.setValue("modificationDescription", description);
      }
    }

    toast({
      title: "Modification selected",
      description: `${type.replace("_", " ").replace(/\b\w/g, c => c.toUpperCase())} option applied`,
    });
  };

  // Handle custom description from the ModificationOptions component
  const handleCustomDescription = (description: string) => {
    form.setValue("modificationDescription", description);
    setSelectedModificationType("custom");
    setModificationOptions(null);

    toast({
      title: "Custom description applied",
      description: "Your custom modification description has been set",
    });
  };

  // Handle submission of step 2 (modification description and processing)
  const handleSubmitModification = () => {
    if (!projectId) return;

    const modificationDescription = form.getValues("modificationDescription") || "";

    // Create the structured modification data
    let modificationData: StructuredModification = {
      type: selectedModificationType || "custom",
      description: modificationDescription,
      referenceImageIds: referenceImages.map(img => img.id),
    };

    // Add options if we have them
    if (selectedModificationType && selectedModificationType !== "custom" && modificationOptions) {
      modificationData.options = modificationOptions;

      // For floor replacement with reference image, ensure we're using the correct reference image
      if (selectedModificationType === "replace_floor" &&
          modificationOptions.useReferenceImage &&
          modificationOptions.referenceImageId) {

        // If using a specific reference image for the floor, prioritize it
        // This ensures the backend knows which reference image is specifically for the floor
        modificationData.primaryReferenceImageId = modificationOptions.referenceImageId;
      }
    }

    // Use startTransition to prevent synchronous suspension errors
    startTransition(() => {
      createModificationMutation.mutate({
        projectId,
        ...modificationData
      });

      setStep(3);
    });
  };

  // Helper function to get the display name of floor materials
  const floorMaterialName = (materialId: string): string => {
    const materials: Record<string, string> = {
      "hardwood": "hardwood",
      "laminate": "laminate",
      "tile": "tile",
      "vinyl": "vinyl",
      "carpet": "carpet",
      "concrete": "concrete"
    };
    return materials[materialId] || materialId;
  };

  // Check if we can proceed to step 3
  const canProceedToStep3 = beforeImages.length > 0 && (
    // Either have a valid description
    ((form.getValues("modificationDescription")?.length || 0) >= 10) ||
    // Or have a selected modification type with valid options
    (selectedModificationType === "replace_floor" && modificationOptions && (
      // Either have material and color selected
      (!modificationOptions.useReferenceImage && modificationOptions.material && modificationOptions.color) ||
      // Or have a reference image selected
      (modificationOptions.useReferenceImage && modificationOptions.referenceImageId)
    )) ||
    // Or have any other non-custom modification type
    (selectedModificationType && selectedModificationType !== "custom" && selectedModificationType !== "replace_floor")
  );

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-6">
        <h2 className="font-heading text-2xl font-bold text-gray-900">Create New Visualization</h2>
        <p className="text-gray-600">Upload images and describe the changes you want to visualize</p>
      </div>

      {/* Subscription Alert - Show when authenticated but no active subscription */}
      {isAuthenticated && !isLoadingSubscription && !hasActiveSubscription && (
        <div className="mb-6">
          <Alert variant="warning" className="bg-amber-50 border-amber-200">
            <AlertCircle className="h-5 w-5 text-amber-500" />
            <AlertTitle className="text-amber-800">Subscription Required</AlertTitle>
            <AlertDescription className="text-amber-700">
              <p className="mb-2">You need an active subscription to create projects and generate visualizations.</p>
              <Button asChild size="sm" className="mt-1">
                <Link href="/pricing">
                  <CreditCard className="h-4 w-4 mr-2" />
                  View Pricing Plans
                </Link>
              </Button>
            </AlertDescription>
          </Alert>
        </div>
      )}

      <div className="bg-white rounded-lg shadow-sm p-6">

        <Form {...form}>
          {/* Step 0: Draft Selection */}
          {step === 0 && (
            <div>
              <h3 className="text-xl font-medium text-gray-900 mb-4">Start a New Project</h3>

              {isDraftsLoading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
                  <p className="text-gray-600">Loading your drafts...</p>
                </div>
              ) : savedDrafts.length > 0 ? (
                <div className="mb-6">
                  <h4 className="text-lg font-medium text-gray-800 mb-2">Saved Drafts</h4>
                  <p className="text-gray-600 text-sm mb-4">Continue working on a previously saved draft</p>

                  <div className="grid grid-cols-1 gap-4 mb-6">
                    {savedDrafts.map(draft => (
                      <div key={draft.id} className="border rounded-lg p-4 hover:border-primary-300 transition-colors">
                        <div className="flex justify-between items-start">
                          <div>
                            <h5 className="font-medium text-gray-900">{draft.title || 'Untitled Project'}</h5>
                            <p className="text-sm text-gray-500 mt-1">
                              {draft.description ?
                                (draft.description.length > 100 ?
                                  draft.description.substring(0, 100) + '...' :
                                  draft.description) :
                                'No description'}
                            </p>
                            <p className="text-xs text-gray-400 mt-2">
                              Last saved: {new Date(draft.updated_at).toLocaleString()}
                            </p>
                          </div>
                          <div className="flex space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => loadDraft(draft)}
                            >
                              Continue
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                if (confirm('Are you sure you want to delete this draft?')) {
                                  clearDraft(draft.id);
                                }
                              }}
                            >
                              <span className="material-icons text-sm text-gray-500">delete</span>
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  <div className="flex justify-between items-center border-t pt-4">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        if (confirm('Are you sure you want to delete all drafts?')) {
                          clearAllDrafts();
                          toast({
                            title: "All drafts deleted",
                            description: "All your drafts have been deleted",
                          });
                        }
                      }}
                    >
                      Clear All Drafts
                    </Button>

                    <Button
                      onClick={() => startTransition(() => {
                        setCurrentDraftId(null);
                        setLastSavedContent('');
                        setStep(1);
                        // Create initial draft with a longer delay to ensure state is settled
                        setTimeout(() => createInitialDraft(), 200);
                      })}
                      disabled={isPending || createDraftMutation.isPending}
                    >
                      {isPending || createDraftMutation.isPending ? "Loading..." : "Start New Project"}
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <div className="rounded-full bg-gray-100 p-4 inline-flex mb-4">
                    <span className="material-icons text-gray-500 text-3xl">add_circle</span>
                  </div>
                  <h4 className="text-lg font-medium text-gray-800 mb-2">No Saved Drafts</h4>
                  <p className="text-gray-600 mb-6">Start creating your new visualization project</p>
                  <Button
                    onClick={() => startTransition(() => {
                      setCurrentDraftId(null);
                      setLastSavedContent('');
                      setStep(1);
                      // Create initial draft with a longer delay to ensure state is settled
                      setTimeout(() => createInitialDraft(), 200);
                    })}
                    disabled={isPending || createDraftMutation.isPending}
                  >
                    {isPending || createDraftMutation.isPending ? "Loading..." : "Create New Project"}
                  </Button>
                </div>
              )}
            </div>
          )}

          <form onSubmit={form.handleSubmit(handleCreateProject)}>
            {/* Step 1: Project details and before images */}
            {step === 1 && (
              <div>
                <div className="space-y-4 mb-6">
                  <FormField
                    control={form.control}
                    name="title"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Project Title</FormLabel>
                        <FormControl>
                          <Input placeholder="e.g. Kitchen Renovation" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Project Description (Optional)</FormLabel>
                        <FormControl>
                          <Textarea placeholder="Briefly describe your project" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="flex justify-between">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      // Save as draft functionality
                      const formData = form.getValues();
                      saveDraft({
                        title: formData.title,
                        description: formData.description,
                        step: 1
                      });
                    }}
                    disabled={createDraftMutation.isPending}
                  >
                    {createDraftMutation.isPending ? "Saving..." : "Save as Draft"}
                  </Button>
                  <Button
                    type="submit"
                    disabled={createProjectMutation.isPending || isPending}
                  >
                    {createProjectMutation.isPending || isPending ?
                      (isLoadingFromDraft ? "Continuing..." : "Creating...") :
                      (isLoadingFromDraft ? "Continue Project" : "Create Project")
                    }
                  </Button>
                </div>
              </div>
            )}
          </form>

          {/* Step 2: New Streamlined Flow */}
          {step === 2 && (
            <div>
              {/* New CreateVisualizationFlow component */}
              <CreateVisualizationFlow
                onComplete={(data) => {
                  // Set the form values and modification data
                  form.setValue("modificationDescription", data.description);
                  setSelectedModificationType(data.modificationType);
                  setModificationOptions(data.options || {});

                  // Handle submission
                  handleSubmitModification();
                }}
                onBeforeImagesUpload={handleBeforeImageUpload}
                onReferenceImagesUpload={handleReferenceImageUpload}
                onReferenceItemsSelected={handleReferenceItemsSelected}
                beforeImages={beforeImages}
                referenceImages={referenceImages}
                projectId={projectId}
              />

              <div className="flex justify-between mt-8 border-t pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    // Save as draft functionality for step 2
                    const formData = form.getValues();
                    saveDraft({
                      title: formData.title,
                      description: formData.description,
                      modification_description: formData.modificationDescription,
                      project_id: projectId || undefined,
                      before_images: beforeImages,
                      reference_images: referenceImages,
                      step: 2,
                      // Save the structured modification data as well
                      modification_type: selectedModificationType || undefined,
                      modification_options: modificationOptions || undefined
                    });
                  }}
                  disabled={createDraftMutation.isPending}
                >
                  {createDraftMutation.isPending ? "Saving..." : "Save as Draft"}
                </Button>
              </div>
            </div>
          )}

          {/* Step 3: Processing confirmation */}
          {step === 3 && (
            <div className="text-center py-12">
              <div className="rounded-full bg-primary-100 p-6 inline-flex mb-6">
                <span className="material-icons text-primary-600 text-5xl">auto_awesome</span>
              </div>
              <h3 className="text-2xl font-medium text-gray-800 mb-3">Your images are being processed</h3>
              <p className="text-gray-600 mb-8 max-w-md mx-auto">
                Our AI is working on your visualization. This may take several minutes depending on the complexity of your request.
              </p>

              <div className="mb-8 max-w-md mx-auto">
                <div className="w-full bg-gray-200 rounded-full h-3 mb-2">
                  <div className="bg-primary-600 h-3 rounded-full animate-pulse" style={{ width: '70%' }}></div>
                </div>
                <p className="text-sm text-gray-500">We'll notify you when your visualization is complete</p>
              </div>

              <Button
                onClick={() => navigate(`/projects/${projectId}`)}
              >
                View Project Status
              </Button>
            </div>
          )}
        </Form>
      </div>
    </div>
  );
}
