import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Check } from "lucide-react";
import { Link, useLocation } from "wouter";
import { SignedIn, SignedOut } from "@clerk/clerk-react";
import { useState, useEffect } from "react";
import { <PERSON><PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import { useClerkAuth } from "@/hooks/use-clerk-auth";
import { createCheckoutSession } from "@/lib/stripe";

export default function Pricing() {
  const [, navigate] = useLocation();
  const [billingPeriod, setBillingPeriod] = useState<"annual" | "monthly">("annual");
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  const { isAuthenticated, getToken } = useClerkAuth();
  const [stripePublishableKey, setStripePublishableKey] = useState<string | null>(null);

  // Get the Stripe publishable key from environment variables
  useEffect(() => {
    // Access the environment variable with VITE_ prefix
    const key = import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY;
    if (key) {
      setStripePublishableKey(key as string);
    } else {
      console.error("Stripe publishable key not found in environment variables");
    }
  }, []);

  // Function to handle checkout with Stripe Checkout
  const handleCheckout = async (planId: string, billingCycle: string) => {
    setIsLoading(true);
    try {
      // For authenticated users, redirect to checkout page
      if (isAuthenticated) {
        // Find the plan details to get the price
        const planDetails = basePlans.find(p => p.id === planId);
        if (!planDetails) {
          throw new Error("Invalid plan selected");
        }

        // Get the correct price based on billing cycle
        const price = billingCycle === 'annual' ? planDetails.annualPrice : planDetails.monthlyPrice;

        if (!price && planId !== 'enterprise') {
          throw new Error("Price information not available for selected plan");
        }

        // Log for debugging
        console.log("Redirecting to checkout for plan:", planId, "billing cycle:", billingCycle, "price:", price);

        // Redirect to the checkout page with query parameters
        navigate(`/checkout?plan=${planId}&billing=${billingCycle}&amount=${price || 0}`);
      } else {
        // For unauthenticated users, redirect to sign-in
        toast({
          title: "Authentication Required",
          description: "Please sign in to subscribe to a plan",
        });
        navigate("/sign-in");
        setIsLoading(false);
      }
    } catch (error: any) {
      console.error("Checkout error:", error);
      toast({
        title: "Error",
        description: error.message || "An unexpected error occurred",
        variant: "destructive",
      });
      setIsLoading(false);
    }
  };

  // Define base plans with annual pricing
  const basePlans = [
    {
      id: "starter",
      name: "Starter",
      annualPrice: 14.99,
      monthlyPrice: 17.99, // 20% more expensive for monthly
      description: "Perfect for individuals trying out renovation visualizations.",
      features: [
        "3 projects per month",
        "3 images per project (9 images total)",
        "Instant processing"
      ],
      button: "Get Started",
      highlight: false,
      availableMonthly: true
    },
    {
      id: "professional",
      name: "Professional",
      annualPrice: 49,
      monthlyPrice: 58.80, // 20% more expensive for monthly
      description: "Ideal for professionals who need regular, high-quality visualizations.",
      features: [
        "10 projects per month",
        "5 images per project (50 images total)",
        "Instant processing",
        "Downloadable high-res images"
      ],
      button: "Upgrade to Pro",
      highlight: true,
      availableMonthly: true
    },
    {
      id: "enterprise",
      name: "Enterprise",
      annualPrice: null, // Custom pricing
      monthlyPrice: null, // Custom pricing
      description: "Tailored for teams and businesses with higher volume and specialized needs.",
      features: [
        "Unlimited projects",
        "Unlimited images",
        "Instant priority processing",
        "White-label client portal",
        "API access",
        "Dedicated account manager"
      ],
      button: "Contact Us to Get Started",
      highlight: false,
      availableMonthly: false
    }
  ];

  // Format the plans based on the selected billing period
  const plans = basePlans.map(plan => {
    const price = billingPeriod === "annual"
      ? plan.annualPrice
      : plan.monthlyPrice;

    return {
      ...plan,
      price: price ? new Intl.NumberFormat('en-AU', {
        style: 'currency',
        currency: 'AUD',
        minimumFractionDigits: 2
      }).format(price) : "Custom Pricing",
      period: price ? `${billingPeriod === "annual" ? "per month, billed annually" : "per month"}` : "",
      available: billingPeriod === "annual" ? true : plan.availableMonthly
    };
  });

  const faqs = [
    {
      question: "What's the difference between monthly and annual billing?",
      answer: "Annual billing offers a 20% discount compared to monthly billing. With annual billing, you commit to a 12-month subscription but pay a lower monthly rate. Monthly billing provides more flexibility but at a higher monthly cost. All prices are in Australian Dollars (AUD)."
    },
    {
      question: "How long does it take to process a visualization?",
      answer: "All our plans offer instant processing. Your visualizations will be generated immediately after submission, allowing you to see results right away."
    },
    {
      question: "Can I cancel my subscription anytime?",
      answer: "Yes, you can cancel your subscription at any time. For monthly plans, you'll have access until the end of your current billing month. For annual plans, you'll continue to have access until the end of your 12-month commitment period."
    },
    {
      question: "What formats can I download my results in?",
      answer: "You can download your visualization results in JPG, PNG, and WEBP formats. Professional and Enterprise plans include the option for high-resolution downloads."
    },
    {
      question: "How accurate are the visualizations?",
      answer: "Our AI technology provides highly realistic visualizations, but they are meant for visualization purposes only. Final results may vary based on actual materials and construction."
    }
  ];

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="text-center mb-8">
        <h1 className="font-heading text-3xl font-bold text-gray-900 mb-4">Pricing Plans</h1>
        <p className="text-gray-600 max-w-2xl mx-auto">Choose the perfect plan for your renovation visualization needs. All plans include our powerful AI-powered visualization technology with instant processing. Plans differ by the number of projects and images you can create each month.</p>
      </div>

      <div className="flex justify-center mb-8">
        <Tabs
          defaultValue="annual"
          value={billingPeriod}
          onValueChange={(value) => setBillingPeriod(value as "annual" | "monthly")}
          className="w-full max-w-md"
        >
          <div className="flex flex-col items-center space-y-2">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="annual">Annual Billing</TabsTrigger>
              <TabsTrigger value="monthly">Monthly Billing</TabsTrigger>
            </TabsList>
            <p className="text-sm text-primary-600 font-medium">
              {billingPeriod === "annual" ? "Save 20% with annual billing" : "Switch to annual billing to save 20%"}
            </p>
          </div>
        </Tabs>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
        {plans.map((plan) => (
          <Card
            key={plan.id}
            className={`${plan.highlight ? 'border-primary-600 shadow-lg' : 'border-gray-200'} relative ${!plan.available ? 'opacity-75' : ''} flex flex-col h-full`}
          >
            {plan.highlight && (
              <Badge variant="default" className="absolute top-0 right-0 translate-x-2 -translate-y-2">
                Most Popular
              </Badge>
            )}
            <CardHeader>
              <CardTitle className="text-xl font-medium">{plan.name}</CardTitle>
              <div className="mt-2">
                <span className="text-3xl font-bold">{plan.price}</span>
                <span className="text-gray-500 ml-2 text-sm">{plan.period}</span>
              </div>
              <CardDescription className="pt-2">{plan.description}</CardDescription>
            </CardHeader>
            <CardContent className="flex flex-col h-full">
              <ul className="space-y-3 flex-grow">
                {plan.features.map((feature, index) => (
                  <li key={index} className="flex items-start">
                    <div className="mr-2 h-5 w-5 text-primary-600 flex-shrink-0 mt-0.5">
                      <Check size={18} />
                    </div>
                    <span className="text-gray-600 text-sm">{feature}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
            <CardFooter className="mt-auto">
              {plan.id === "enterprise" ? (
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={() => {
                    navigate("/contact");
                    // Ensure the page scrolls to the top after navigation
                    window.scrollTo(0, 0);
                  }}
                >
                  Contact Us to Get Started
                </Button>
              ) : (
                <>
                  <SignedIn>
                    <Button
                      variant={plan.highlight ? "default" : "outline"}
                      className="w-full"
                      onClick={() => handleCheckout(plan.id, billingPeriod)}
                      disabled={!plan.available || isLoading}
                    >
                      {isLoading ? "Loading..." : plan.button}
                    </Button>
                  </SignedIn>
                  <SignedOut>
                    <Button
                      variant={plan.highlight ? "default" : "outline"}
                      className="w-full"
                      onClick={() => navigate("/sign-in")}
                      disabled={!plan.available}
                    >
                      Sign In to Subscribe
                    </Button>
                  </SignedOut>
                </>
              )}
            </CardFooter>
          </Card>
        ))}
      </div>

      <div className="mb-16">
        <h2 className="font-heading text-2xl font-bold text-gray-900 mb-6 text-center">Frequently Asked Questions</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {faqs.map((faq, index) => (
            <div key={index} className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="font-medium text-gray-900 mb-2">{faq.question}</h3>
              <p className="text-gray-600 text-sm">{faq.answer}</p>
            </div>
          ))}
        </div>
      </div>

      <div className="bg-gradient-to-r from-primary-50 to-secondary-50 rounded-lg p-8 text-center">
        <h2 className="font-heading text-2xl font-bold text-gray-900 mb-4">Need a Custom Solution?</h2>
        <p className="text-gray-600 mb-6 max-w-2xl mx-auto">If your business has specific requirements or higher volume needs, we offer custom enterprise solutions tailored to your workflow.</p>
        <Button
          size="lg"
          onClick={() => {
            navigate("/contact");
            // Ensure the page scrolls to the top after navigation
            window.scrollTo(0, 0);
          }}
        >
          Contact Our Sales Team
        </Button>
      </div>
    </div>
  );
}
