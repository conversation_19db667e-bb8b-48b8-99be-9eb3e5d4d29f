import { useState, use<PERSON><PERSON>back, useEffect } from "react";
import { Link } from "wouter";
import { useAuth } from "@/hooks/use-clerk-auth";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { PlusCircle, FolderPlus, Trash, Pencil, Check, X, FolderOpen, Loader2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { Dialog, DialogContent, DialogDescription, DialogFooter, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, Dialog<PERSON>rigger } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { ReferenceCategory, ReferenceItem } from "@shared/schema";
import { apiGet, apiPost, apiDelete } from "@/lib/api-client";
import { OptimizedFileInput } from "@/components/ui/optimized-file-input";
import { debounce } from "lodash";

// Component to manage reference categories
function ReferenceCategories() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [newCategoryName, setNewCategoryName] = useState("");
  const [newCategoryDescription, setNewCategoryDescription] = useState("");
  const [editCategoryId, setEditCategoryId] = useState<number | null>(null);
  const [editCategoryName, setEditCategoryName] = useState("");
  const [editCategoryDescription, setEditCategoryDescription] = useState("");
  const [isAddCategoryOpen, setIsAddCategoryOpen] = useState(false);

  // Query to fetch all categories
  const { data: categories = [], isLoading, isError } = useQuery<ReferenceCategory[]>({
    queryKey: ["/api/reference-categories"],
    queryFn: async () => {
      try {
        return await apiGet<ReferenceCategory[]>("/api/reference-categories", { retries: 2 });
      } catch (error) {
        console.error("Error fetching reference categories:", error);
        throw error;
      }
    },
  });

  // Mutation to create a new category
  const createCategoryMutation = useMutation({
    mutationFn: async (data: { name: string; description?: string }) => {
      try {
        return await apiPost<ReferenceCategory>("/api/reference-categories", data, { retries: 1 });
      } catch (error) {
        console.error("Error creating reference category:", error);
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/reference-categories"] });
      setNewCategoryName("");
      setNewCategoryDescription("");
      setIsAddCategoryOpen(false);
      toast({
        title: "Category created",
        description: "Your reference category has been created successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to create category",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Mutation to update a category
  const updateCategoryMutation = useMutation({
    mutationFn: async ({ id, data }: { id: number; data: { name?: string; description?: string } }) => {
      try {
        return await apiPost<ReferenceCategory>(`/api/reference-categories/${id}`, data, { retries: 1 });
      } catch (error) {
        console.error("Error updating reference category:", error);
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/reference-categories"] });
      setEditCategoryId(null);
      toast({
        title: "Category updated",
        description: "Your reference category has been updated successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to update category",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Mutation to delete a category
  const deleteCategoryMutation = useMutation({
    mutationFn: async (id: number) => {
      try {
        await apiDelete(`/api/reference-categories/${id}`, { retries: 1 });
      } catch (error) {
        console.error("Error deleting reference category:", error);
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/reference-categories"] });
      toast({
        title: "Category deleted",
        description: "Your reference category has been deleted successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to delete category",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const handleCreateCategory = () => {
    if (!newCategoryName.trim()) {
      toast({
        title: "Validation error",
        description: "Category name is required",
        variant: "destructive",
      });
      return;
    }

    createCategoryMutation.mutate({
      name: newCategoryName,
      description: newCategoryDescription || undefined,
    });
  };

  const handleUpdateCategory = (id: number) => {
    if (!editCategoryName.trim()) {
      toast({
        title: "Validation error",
        description: "Category name is required",
        variant: "destructive",
      });
      return;
    }

    updateCategoryMutation.mutate({
      id,
      data: {
        name: editCategoryName,
        description: editCategoryDescription || undefined,
      },
    });
  };

  const handleDeleteCategory = (id: number) => {
    if (window.confirm("Are you sure you want to delete this category? This will delete all reference items within this category.")) {
      deleteCategoryMutation.mutate(id);
    }
  };

  const startEditCategory = (category: ReferenceCategory) => {
    setEditCategoryId(category.id);
    setEditCategoryName(category.name);
    setEditCategoryDescription(category.description || "");
  };

  const cancelEditCategory = () => {
    setEditCategoryId(null);
  };

  if (isLoading) {
    return <div className="text-center py-8">Loading categories...</div>;
  }

  if (isError) {
    return <div className="text-center py-8 text-red-500">Failed to load categories. Please try again later.</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Reference Categories</h2>
        <Dialog open={isAddCategoryOpen} onOpenChange={setIsAddCategoryOpen}>
          <DialogTrigger asChild>
            <Button variant="default">
              <FolderPlus className="mr-2 h-4 w-4" />
              Add Category
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create New Category</DialogTitle>
              <DialogDescription>
                Create a new category to organize your reference items.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="name">Name</Label>
                <Input
                  id="name"
                  placeholder="Category name"
                  value={newCategoryName}
                  onChange={(e) => setNewCategoryName(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="description">Description (optional)</Label>
                <Textarea
                  id="description"
                  placeholder="Description"
                  className="min-h-24"
                  value={newCategoryDescription}
                  onChange={(e) => setNewCategoryDescription(e.target.value)}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddCategoryOpen(false)}>Cancel</Button>
              <Button onClick={handleCreateCategory} disabled={createCategoryMutation.isPending}>
                {createCategoryMutation.isPending ? "Creating..." : "Create Category"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {categories.length === 0 ? (
        <div className="bg-muted p-8 rounded-lg text-center">
          <h3 className="font-medium text-lg mb-2">No Categories Yet</h3>
          <p className="text-muted-foreground mb-4">
            Create categories to organize your reference items.
          </p>
          <Button onClick={() => setIsAddCategoryOpen(true)}>
            <FolderPlus className="mr-2 h-4 w-4" />
            Create Your First Category
          </Button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {categories.map((category) => (
            <Card key={category.id} className={editCategoryId === category.id ? "border-primary" : ""}>
              <CardHeader className="pb-2">
                {editCategoryId === category.id ? (
                  <Input
                    value={editCategoryName}
                    onChange={(e) => setEditCategoryName(e.target.value)}
                    className="font-semibold text-lg"
                  />
                ) : (
                  <CardTitle className="flex justify-between items-center">
                    <span className="truncate">{category.name}</span>
                    <div className="flex items-center space-x-1">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => startEditCategory(category)}
                        title="Edit"
                      >
                        <Pencil className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleDeleteCategory(category.id)}
                        title="Delete"
                      >
                        <Trash className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardTitle>
                )}
                {editCategoryId === category.id ? (
                  <Textarea
                    value={editCategoryDescription}
                    onChange={(e) => setEditCategoryDescription(e.target.value)}
                    className="text-sm text-muted-foreground"
                    placeholder="Description (optional)"
                  />
                ) : (
                  <CardDescription className="truncate">
                    {category.description || "No description"}
                  </CardDescription>
                )}
              </CardHeader>
              <CardFooter className="pt-2 justify-between">
                {editCategoryId === category.id ? (
                  <div className="flex space-x-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={cancelEditCategory}
                    >
                      <X className="h-4 w-4 mr-1" /> Cancel
                    </Button>
                    <Button
                      size="sm"
                      onClick={() => handleUpdateCategory(category.id)}
                      disabled={updateCategoryMutation.isPending}
                    >
                      <Check className="h-4 w-4 mr-1" /> Save
                    </Button>
                  </div>
                ) : (
                  <>
                    <span className="text-sm text-muted-foreground">
                      Created {category.created_at ? new Date(category.created_at).toLocaleDateString() : 'Unknown date'}
                    </span>
                    <Button
                      variant="outline"
                      size="sm"
                      asChild
                    >
                      <Link href={`/reference-library/${category.id}`}>
                        <FolderOpen className="h-4 w-4 mr-1" /> View Items
                      </Link>
                    </Button>
                  </>
                )}
              </CardFooter>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}

// Component to manage reference items
function ReferenceItems() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploadForm, setUploadForm] = useState({
    name: "",
    description: "",
    categoryId: "",
    tags: "",
  });
  const [isUploadDialogOpen, setIsUploadDialogOpen] = useState(false);

  // Query to fetch all items with better caching
  const { data: items = [], isLoading, isError } = useQuery<ReferenceItem[]>({
    queryKey: ["/api/reference-items"],
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
    queryFn: async () => {
      try {
        return await apiGet<ReferenceItem[]>("/api/reference-items", { retries: 2 });
      } catch (error) {
        console.error("Error fetching reference items:", error);
        throw error;
      }
    },
    onSuccess: (data) => {
      // Log the first item for debugging
      if (data && data.length > 0) {
        console.log("Reference item received:", data[0]);
      }
    },
    onError: (error) => {
      console.error("Error fetching reference items:", error);
    }
  });

  // Query to fetch all categories for the dropdown with better caching
  const { data: categories = [] } = useQuery<ReferenceCategory[]>({
    queryKey: ["/api/reference-categories"],
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
    queryFn: async () => {
      try {
        return await apiGet<ReferenceCategory[]>("/api/reference-categories", { retries: 2 });
      } catch (error) {
        console.error("Error fetching reference categories:", error);
        throw error;
      }
    },
  });

  // Mutation to upload a new reference item
  const uploadItemMutation = useMutation({
    mutationFn: async (data: FormData) => {
      try {
        return await apiPost<ReferenceItem>("/api/reference-items", data, { retries: 1 });
      } catch (error) {
        console.error("Error uploading reference item:", error);
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/reference-items"] });
      setUploadForm({
        name: "",
        description: "",
        categoryId: "",
        tags: "",
      });
      setSelectedFile(null);
      setIsUploadDialogOpen(false);
      toast({
        title: "Reference item uploaded",
        description: "Your reference item has been uploaded successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to upload reference item",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Mutation to delete a reference item
  const deleteItemMutation = useMutation({
    mutationFn: async (id: number) => {
      try {
        await apiDelete(`/api/reference-items/${id}`, { retries: 1 });
      } catch (error) {
        console.error("Error deleting reference item:", error);
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/reference-items"] });
      toast({
        title: "Reference item deleted",
        description: "Your reference item has been deleted successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to delete reference item",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Handle file selection with the optimized component
  const handleFileSelect = useCallback((file: File | null) => {
    setSelectedFile(file);

    // Auto-populate name field with file name (without extension)
    if (file) {
      const fileName = file.name.split('.').slice(0, -1).join('.');
      setUploadForm(prev => ({
        ...prev,
        name: fileName || prev.name,
      }));
    }
  }, []);

  // Debounced input handler to prevent excessive re-renders
  const debouncedInputChange = useCallback(
    debounce((name: string, value: string) => {
      setUploadForm(prev => ({ ...prev, [name]: value }));
    }, 300),
    []
  );

  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    // Update the input value immediately for UI responsiveness
    e.target.value = value;
    // Debounce the actual state update
    debouncedInputChange(name, value);
  }, [debouncedInputChange]);

  // Clean up debounce on unmount
  useEffect(() => {
    return () => {
      debouncedInputChange.cancel();
    };
  }, [debouncedInputChange]);

  const handleUpload = useCallback(() => {
    if (!selectedFile) {
      toast({
        title: "File required",
        description: "Please select an image file to upload",
        variant: "destructive",
      });
      return;
    }

    if (!uploadForm.name.trim()) {
      toast({
        title: "Name required",
        description: "Please provide a name for this reference item",
        variant: "destructive",
      });
      return;
    }

    const formData = new FormData();
    formData.append("image", selectedFile);

    // Convert form data to JSON and append as a single field
    const jsonData = {
      name: uploadForm.name,
      description: uploadForm.description || undefined,
      categoryId: uploadForm.categoryId ? parseInt(uploadForm.categoryId) : undefined,
      tags: uploadForm.tags ? uploadForm.tags.split(",").map(tag => tag.trim()) : undefined,
    };

    formData.append("data", JSON.stringify(jsonData));

    uploadItemMutation.mutate(formData);
  }, [selectedFile, uploadForm, toast, uploadItemMutation]);

  const handleDeleteItem = useCallback((id: number) => {
    if (window.confirm("Are you sure you want to delete this reference item?")) {
      deleteItemMutation.mutate(id);
    }
  }, [deleteItemMutation]);

  if (isLoading) {
    return <div className="text-center py-8">Loading reference items...</div>;
  }

  if (isError) {
    return <div className="text-center py-8 text-red-500">Failed to load reference items. Please try again later.</div>;
  }

  // Group items by category
  const itemsByCategory: Record<string, ReferenceItem[]> = {
    "Uncategorized": [],
  };

  // Initialize all existing categories
  categories.forEach(category => {
    itemsByCategory[category.name] = [];
  });

  // Populate categories with items
  items.forEach(item => {
    if (item.categoryId) {
      const category = categories.find(c => c.id === item.categoryId);
      if (category) {
        itemsByCategory[category.name] = itemsByCategory[category.name] || [];
        itemsByCategory[category.name].push(item);
      } else {
        itemsByCategory["Uncategorized"].push(item);
      }
    } else {
      itemsByCategory["Uncategorized"].push(item);
    }
  });

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Reference Items</h2>
        <Dialog open={isUploadDialogOpen} onOpenChange={setIsUploadDialogOpen}>
          <DialogTrigger asChild>
            <Button variant="default">
              <PlusCircle className="mr-2 h-4 w-4" />
              Add Reference Item
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>Upload Reference Item</DialogTitle>
              <DialogDescription>
                Upload a new reference image to your library. These can be used as references when creating new visualizations.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="file">Image File</Label>
                <OptimizedFileInput
                  id="file"
                  selectedFile={selectedFile}
                  onFileSelect={handleFileSelect}
                  isUploading={uploadItemMutation.isPending}
                  accept="image/*"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="name">Name</Label>
                <Input
                  id="name"
                  name="name"
                  placeholder="Reference name"
                  defaultValue={uploadForm.name}
                  onChange={handleInputChange}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="description">Description (optional)</Label>
                <Textarea
                  id="description"
                  name="description"
                  placeholder="Description"
                  defaultValue={uploadForm.description}
                  onChange={handleInputChange}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="categoryId">Category (optional)</Label>
                <select
                  id="categoryId"
                  name="categoryId"
                  className="w-full p-2 border rounded-md"
                  defaultValue={uploadForm.categoryId}
                  onChange={handleInputChange}
                >
                  <option value="">-- No Category --</option>
                  {categories.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="tags">Tags (comma separated, optional)</Label>
                <Input
                  id="tags"
                  name="tags"
                  placeholder="floor, black, polished"
                  defaultValue={uploadForm.tags}
                  onChange={handleInputChange}
                />
              </div>
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setIsUploadDialogOpen(false)}
                disabled={uploadItemMutation.isPending}
              >
                Cancel
              </Button>
              <Button
                onClick={handleUpload}
                disabled={uploadItemMutation.isPending || !selectedFile}
              >
                {uploadItemMutation.isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Uploading...
                  </>
                ) : (
                  "Upload Reference"
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {items.length === 0 ? (
        <div className="bg-muted p-8 rounded-lg text-center">
          <h3 className="font-medium text-lg mb-2">No Reference Items Yet</h3>
          <p className="text-muted-foreground mb-4">
            Upload reference images to build your library and use them in your projects.
          </p>
          <Button onClick={() => setIsUploadDialogOpen(true)}>
            <PlusCircle className="mr-2 h-4 w-4" />
            Upload Your First Reference
          </Button>
        </div>
      ) : (
        <div className="space-y-8">
          {Object.entries(itemsByCategory)
            .filter(([_, items]) => items.length > 0) // Only show categories with items
            .map(([categoryName, categoryItems]) => (
              <div key={categoryName} className="space-y-4">
                <h3 className="text-xl font-semibold flex items-center">
                  <FolderOpen className="mr-2 h-5 w-5" />
                  {categoryName}
                  <Badge variant="outline" className="ml-2">
                    {categoryItems.length} {categoryItems.length === 1 ? "item" : "items"}
                  </Badge>
                </h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {categoryItems.map((item) => (
                    <Card key={item.id} className="overflow-hidden">
                      <div className="aspect-square overflow-hidden">
                        <img
                          src={item.image_path || '/placeholder-image.svg'}
                          alt={item.name}
                          className="h-full w-full object-cover transition-all hover:scale-105"
                          onError={(e) => {
                            // Fallback if image fails to load
                            console.error(`Failed to load image: ${item.image_path}`);
                            e.currentTarget.src = '/placeholder-image.svg';
                          }}
                        />
                      </div>
                      <CardHeader className="p-3">
                        <CardTitle className="text-lg truncate">{item.name}</CardTitle>
                        {item.description && (
                          <CardDescription className="truncate">
                            {item.description}
                          </CardDescription>
                        )}
                      </CardHeader>
                      <CardFooter className="p-3 pt-0 flex justify-between">
                        {item.tags && item.tags.length > 0 ? (
                          <div className="flex flex-wrap gap-1">
                            {item.tags.slice(0, 3).map((tag, i) => (
                              <Badge key={i} variant="secondary" className="text-xs">
                                {tag}
                              </Badge>
                            ))}
                            {item.tags.length > 3 && (
                              <Badge variant="outline" className="text-xs">
                                +{item.tags.length - 3}
                              </Badge>
                            )}
                          </div>
                        ) : (
                          <span className="text-xs text-muted-foreground">No tags</span>
                        )}
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleDeleteItem(item.id)}
                          title="Delete"
                        >
                          <Trash className="h-4 w-4" />
                        </Button>
                      </CardFooter>
                    </Card>
                  ))}
                </div>
                <Separator />
              </div>
            ))
          }
        </div>
      )}
    </div>
  );
}

export default function ReferenceLibrary() {
  const { user } = useAuth();

  if (!user) {
    return <div className="flex justify-center items-center h-[60vh]">Loading...</div>;
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="flex flex-col space-y-2 mb-8">
        <h1 className="text-3xl font-bold">Reference Library</h1>
        <p className="text-muted-foreground">
          Manage your reference items for quick access when creating new visualizations.
        </p>
      </div>

      <Tabs defaultValue="items" className="w-full">
        <TabsList className="mb-8">
          <TabsTrigger value="items">Reference Items</TabsTrigger>
          <TabsTrigger value="categories">Categories</TabsTrigger>
        </TabsList>
        <TabsContent value="items">
          <ReferenceItems />
        </TabsContent>
        <TabsContent value="categories">
          <ReferenceCategories />
        </TabsContent>
      </Tabs>
    </div>
  );
}