import { useQuery } from "@tanstack/react-query";
import { use<PERSON>ara<PERSON>, Link } from "wouter";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { ProjectImageComparison } from "@/components/ProjectImageComparison";
import { getProject } from "@/lib/api";
import { normalizeImagePath } from "@/utils/image-utils";
import { formatDistanceToNow, format } from "date-fns";
import { ErrorMessage, ErrorMessages } from "@/components/ui/error-message";
import { useErrorHandler } from "@/hooks/useErrorHandler";

export default function Project() {
  const { id } = useParams<{ id: string }>();
  const projectId = parseInt(id || '0');
  const { error: apiError, handleError, clearError } = useErrorHandler();

  const { data: project, isLoading, error, refetch } = useQuery({
    queryKey: ['/api/projects', projectId],
    queryFn: () => getProject(projectId),
    refetchInterval: 5000, // Poll every 5 seconds
    refetchIntervalInBackground: true,
    onError: handleError
  });

  if (isLoading) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading project details...</p>
        </div>
      </div>
    );
  }

  if (apiError || error || !project) {
    const errorMessage = apiError?.message || 'We couldn\'t load the project details.';
    const errorTitle = apiError?.type === 'NOT_FOUND_ERROR' ? 'Project Not Found' : 'Error Loading Project';

    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-6">
          <Link href="/">
            <a className="text-primary-600 hover:text-primary-700 inline-flex items-center">
              <span className="material-icons text-sm mr-1">arrow_back</span>
              Return to Dashboard
            </a>
          </Link>
        </div>

        <Card>
          <CardContent className="p-8 text-center">
            <span className="material-icons text-red-500 text-5xl mb-4">error_outline</span>
            <h2 className="text-2xl font-medium text-gray-900 mb-2">{errorTitle}</h2>
            <p className="text-gray-600 mb-6">{errorMessage}</p>
            <div className="flex justify-center space-x-4">
              <Button onClick={() => {
                clearError();
                refetch();
              }}>
                Try Again
              </Button>
              <Link href="/">
                <Button variant="outline">
                  Return to Dashboard
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  const isDraft = project.status === "draft";
  const isProcessing = project.status === "processing";
  const isCompleted = project.status === "completed";
  const isFailed = project.status === "failed";

  // Safely handle date formatting with error handling
  let timeAgo = '';
  let formattedDate = '';
  try {
    const createdAt = new Date(project.created_at);
    if (!isNaN(createdAt.getTime())) {
      timeAgo = formatDistanceToNow(createdAt, { addSuffix: true });
      formattedDate = format(createdAt, "MMMM d, yyyy 'at' h:mm a");
    } else {
      console.warn('Invalid date format:', project.created_at);
      timeAgo = 'recently';
      formattedDate = 'Unknown date';
    }
  } catch (error) {
    console.warn('Error formatting date:', error);
    timeAgo = 'recently';
    formattedDate = 'Unknown date';
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="flex flex-col md:flex-row md:items-center justify-between mb-6">
        <div>
          <div className="flex items-center mb-2">
            <Link href="/">
              <a className="text-primary-600 hover:text-primary-700 mr-2">
                <span className="material-icons text-sm">arrow_back</span>
              </a>
            </Link>
            <h1 className="font-heading text-2xl font-bold text-gray-900">{project.title}</h1>
            <Badge variant="secondary" className={`ml-4
              ${isDraft ? 'bg-blue-100 text-blue-800' : ''}
              ${isProcessing ? 'bg-yellow-100 text-yellow-800' : ''}
              ${isCompleted ? 'bg-green-100 text-green-800' : ''}
              ${isFailed ? 'bg-red-100 text-red-800' : ''}
            `}>
              {isDraft ? 'Draft' : isProcessing ? 'Processing' : isCompleted ? 'Completed' : 'Failed'}
            </Badge>
          </div>
          <p className="text-gray-600">Created {timeAgo} • {formattedDate}</p>
        </div>

        <div className="flex space-x-3 mt-4 md:mt-0">
          <Button variant="outline" className="inline-flex items-center">
            <span className="material-icons text-sm mr-1">share</span>
            Share
          </Button>
          {isCompleted && project.afterImages.length > 0 && (
            <Button variant="default" className="inline-flex items-center">
              <span className="material-icons text-sm mr-1">download</span>
              Download Results
            </Button>
          )}
        </div>
      </div>

      {project.description && (
        <Card className="mb-6">
          <CardContent className="p-6">
            <h3 className="font-medium text-gray-700 mb-2">Project Description</h3>
            <p className="text-gray-600">{project.description}</p>
          </CardContent>
        </Card>
      )}

      <Tabs defaultValue="results">
        <TabsList className="mb-6">
          <TabsTrigger value="results">Results</TabsTrigger>
          <TabsTrigger value="before-images">Before Images</TabsTrigger>
          <TabsTrigger value="reference-images">Reference Images</TabsTrigger>
          <TabsTrigger value="modifications">Modifications</TabsTrigger>
        </TabsList>

        <TabsContent value="results">
          {isDraft && (
            <Card>
              <CardContent className="p-8 text-center">
                <span className="material-icons text-blue-500 text-5xl mb-4">edit_note</span>
                <h3 className="text-lg font-medium text-gray-900 mb-2">Draft Project</h3>
                <p className="text-gray-600 mb-6">This project is currently in draft status. Continue editing to add images and modifications.</p>
                <Link href={`/create?projectId=${project.id}`}>
                  <Button>
                    <span className="material-icons text-sm mr-1">edit</span>
                    Continue Editing
                  </Button>
                </Link>
              </CardContent>
            </Card>
          )}

          {isProcessing && (
            <Card>
              <CardContent className="p-8 text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-6"></div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">Processing Your Visualization</h3>
                <p className="text-gray-600 mb-4">Our AI is working on generating the visualizations based on your request.</p>
                <div className="max-w-md mx-auto mb-4">
                  <div className="w-full bg-gray-200 rounded-full h-2 mb-1">
                    <div className="bg-primary-600 h-2 rounded-full animate-pulse" style={{ width: '65%' }}></div>
                  </div>
                  <div className="flex justify-between text-xs text-gray-500">
                    <span>Analyzing images</span>
                    <span>65%</span>
                  </div>
                </div>
                <p className="text-sm text-gray-500 mb-6">This may take several minutes depending on complexity</p>

                <Button
                  variant="outline"
                  onClick={() => refetch()}
                  className="inline-flex items-center"
                >
                  <span className="material-icons text-sm mr-1">refresh</span>
                  Check Status
                </Button>
              </CardContent>
            </Card>
          )}

          {isFailed && (
            <Card>
              <CardContent className="p-8 text-center">
                <span className="material-icons text-red-500 text-5xl mb-4">error_outline</span>
                <h3 className="text-lg font-medium text-gray-900 mb-2">Processing Failed</h3>
                <p className="text-gray-600 mb-6">We encountered an error while processing your visualization request. Please try again.</p>
                <Link href="/create">
                  <Button>
                    Try Again
                  </Button>
                </Link>
              </CardContent>
            </Card>
          )}

          {isCompleted && project.beforeImages.length > 0 && project.afterImages.length > 0 && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {project.beforeImages.map((beforeImage, index) => {
                const afterImage = project.afterImages[index];
                if (!afterImage) return null;

                return (
                  <Card key={beforeImage.id} className="overflow-hidden">
                    <div className="h-80 relative">
                      {afterImage.path.endsWith('.txt') ? (
                        <div className="h-full flex flex-col items-center justify-center bg-gray-100 p-4">
                          <span className="material-icons text-red-500 text-5xl mb-4">error_outline</span>
                          <h3 className="font-medium text-lg text-gray-900 mb-2">Generation Failed</h3>
                          <p className="text-sm text-red-600 text-center">
                            {(() => {
                              try {
                                // Try to read the error message from the file
                                const errorText = afterImage.path.split('/').pop()?.startsWith('error_')
                                  ? 'The AI service encountered an issue. Please try again later.'
                                  : 'The AI couldn\'t generate an image for this request. Please try again with a different description.';
                                return errorText;
                              } catch (e) {
                                return 'The AI couldn\'t generate an image for this request. Please try again with a different description.';
                              }
                            })()}
                          </p>
                        </div>
                      ) : (
                        // Check if the after image is a text file (error case)
                        afterImage.path.endsWith('.txt') ? (
                          <div className="relative h-48 bg-gray-100 flex items-center justify-center">
                            <div className="absolute inset-0">
                              {beforeImage && (
                                <img
                                  src={normalizeImagePath(beforeImage)}
                                  alt={`Before image ${index + 1}`}
                                  className="w-full h-full object-cover opacity-50"
                                />
                              )}
                            </div>
                            <div className="absolute inset-0 flex flex-col items-center justify-center bg-gray-900 bg-opacity-40">
                              <span className="material-icons text-white text-4xl mb-2">error_outline</span>
                              <p className="text-white font-medium">Image generation failed</p>
                              <p className="text-white text-sm opacity-80">Please try again with a different description</p>
                            </div>
                          </div>
                        ) : (
                          <ProjectImageComparison
                            beforeImage={beforeImage}
                            afterImage={afterImage}
                            beforeAlt={`Before image ${index + 1}`}
                            afterAlt={`After image ${index + 1}`}
                          />
                        )
                      )}
                    </div>
                    <CardContent className="p-4">
                      <h4 className="font-medium text-gray-800 mb-2">Visualization {index + 1}</h4>
                      <div className="flex space-x-2">
                        <Button variant="outline" size="sm" className="text-xs">
                          <span className="material-icons text-xs mr-1">fullscreen</span>
                          View Larger
                        </Button>
                        {!afterImage.path.endsWith('.txt') && (
                          <Button variant="default" size="sm" className="text-xs">
                            <span className="material-icons text-xs mr-1">download</span>
                            Download
                          </Button>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          )}
        </TabsContent>

        <TabsContent value="before-images">
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
            {project.beforeImages.length > 0 ? (
              project.beforeImages.map(image => (
                <div key={image.id} className="relative group">
                  <img
                    src={normalizeImagePath(image)}
                    alt={image.original_filename || `Before image ${image.id}`}
                    className="w-full h-40 object-cover rounded-lg shadow-sm"
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-opacity duration-200 rounded-lg flex items-center justify-center">
                    <Button variant="secondary" size="sm" className="opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                      <span className="material-icons text-xs">fullscreen</span>
                    </Button>
                  </div>
                  <Badge variant="secondary" className="bg-primary-600 text-white absolute top-2 left-2">
                    Before
                  </Badge>
                </div>
              ))
            ) : (
              <div className="col-span-full text-center py-12 text-gray-500">
                No before images have been uploaded
              </div>
            )}
          </div>
        </TabsContent>

        <TabsContent value="reference-images">
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
            {project.referenceImages.length > 0 ? (
              project.referenceImages.map(image => (
                <div key={image.id} className="relative group">
                  <img
                    src={normalizeImagePath(image)}
                    alt={image.original_filename || `Reference image ${image.id}`}
                    className="w-full h-40 object-cover rounded-lg shadow-sm"
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-opacity duration-200 rounded-lg flex items-center justify-center">
                    <Button variant="secondary" size="sm" className="opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                      <span className="material-icons text-xs">fullscreen</span>
                    </Button>
                  </div>
                  <Badge variant="secondary" className="bg-secondary-600 text-white absolute top-2 left-2">
                    Reference
                  </Badge>
                </div>
              ))
            ) : (
              <div className="col-span-full text-center py-12 text-gray-500">
                No reference images have been uploaded
              </div>
            )}
          </div>
        </TabsContent>

        <TabsContent value="modifications">
          <div className="space-y-4">
            {project.modifications.length > 0 ? (
              project.modifications.map(modification => (
                <Card key={modification.id}>
                  <CardContent className="p-6">
                    <div className="flex justify-between items-start">
                      <h3 className="font-medium text-gray-800">Modification Request</h3>
                      <Badge variant="outline" className="text-xs">
                        {modification.createdAt
                          ? format(new Date(modification.createdAt), "MMM d, yyyy")
                          : "Date not available"}
                      </Badge>
                    </div>
                    <p className="mt-2 text-gray-600">{modification.description}</p>

                    {modification.referenceImageIds && modification.referenceImageIds.length > 0 && (
                      <div className="mt-4">
                        <h4 className="text-sm font-medium text-gray-700 mb-2">Referenced Images:</h4>
                        <div className="flex flex-wrap gap-2">
                          {modification.referenceImageIds.map(refId => {
                            const refImage = project.referenceImages.find(img => img.id === refId);
                            if (!refImage) return null;

                            return (
                              <img
                                key={refId}
                                src={normalizeImagePath(refImage)}
                                alt={`Reference ${refId}`}
                                className="w-12 h-12 object-cover rounded"
                              />
                            );
                          })}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))
            ) : (
              <div className="text-center py-12 text-gray-500">
                No modification descriptions have been added
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
