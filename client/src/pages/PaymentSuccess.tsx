import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from "@/components/ui/card";
import { Link, useLocation } from "wouter";
import { CheckCircle, Calendar, Home, CreditCard, Zap, Image, RefreshCw } from "lucide-react";
import { useEffect, useState, useRef, useCallback } from "react";
import { useClerkAuth } from "@/hooks/use-clerk-auth";
import { useToast } from "@/hooks/use-toast";
import { useSubscription } from "@/contexts/SubscriptionContext";
import { PLAN_LIMITS } from "@/lib/subscription-plans";
import { LoadingSpinner } from "@/components/ui/loading-spinner";

export default function PaymentSuccess() {
  const [location] = useLocation();
  const [, navigate] = useLocation();
  const { isAuthenticated, getToken } = useClerkAuth();
  const { toast } = useToast();
  const { subscription, usage, limits, isLoading, refetch } = useSubscription();

  const [planName, setPlanName] = useState("");
  const [planId, setPlanId] = useState("");
  const [billingCycle, setBillingCycle] = useState<"monthly" | "annual">("monthly");
  const [paymentDate, setPaymentDate] = useState("");
  const [sessionData, setSessionData] = useState<any>(null);
  const [isLoadingSession, setIsLoadingSession] = useState(true);

  // Refs to prevent multiple API calls
  const sessionFetchedRef = useRef(false);
  const refetchTriggeredRef = useRef(false);
  const sessionIdRef = useRef<string | null>(null);

  // Redirect to sign-in if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      toast({
        title: "Authentication Required",
        description: "Please sign in to view subscription details",
      });
      navigate("/sign-in");
    }
  }, [isAuthenticated, navigate]);

  // Track if we've already tried to create a subscription
  const subscriptionCreationAttemptedRef = useRef(false);

  // Function to manually create subscription
  const createSubscriptionManually = async (sessionId: string) => {
    // Prevent multiple calls to this function
    if (subscriptionCreationAttemptedRef.current) {
      console.log('Subscription creation already attempted, skipping duplicate call');
      return true; // Return true to prevent showing error messages
    }

    subscriptionCreationAttemptedRef.current = true;

    try {
      console.log(`Manually creating subscription for session: ${sessionId}`);
      const token = await getToken();

      const response = await fetch('/api/manual-subscription-create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ sessionId })
      });

      const data = await response.json();
      console.log('Manual subscription creation response:', data);

      if (response.ok) {
        // Consider any 2xx response as success, even if data.success is not true
        // This handles the case where the subscription already exists
        toast({
          title: "Subscription Activated",
          description: "Your subscription has been successfully activated.",
        });

        // Refresh subscription data
        await refetch();
        return true;
      } else {
        console.error('Failed to manually create subscription:', data);
        toast({
          title: "Subscription Error",
          description: data.message || "Failed to activate subscription. Please contact support.",
          variant: "destructive",
        });
        return false;
      }
    } catch (error) {
      console.error('Error creating subscription manually:', error);
      toast({
        title: "Error",
        description: "Failed to activate subscription. Please try refreshing the page.",
        variant: "destructive",
      });
      return false;
    }
  };

  // Memoized function to fetch session details
  const fetchSessionDetails = useCallback(async (sessionId: string) => {
    if (!isAuthenticated || !sessionId || sessionFetchedRef.current) {
      return;
    }

    setIsLoadingSession(true);
    try {
      console.log(`Fetching session details for: ${sessionId}`);
      const token = await getToken();

      const response = await fetch(`/api/checkout-session/${sessionId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Cache-Control': 'no-cache'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch session details');
      }

      const data = await response.json();
      console.log('Session details received:', {
        success: data.success,
        hasSession: !!data.session,
        hasSubscription: data.session?.subscription ? true : false,
        hasMetadata: data.session?.metadata ? true : false
      });

      setSessionData(data);
      sessionFetchedRef.current = true;

      if (data.success && data.session) {
        // Extract plan details from session metadata
        const metadata = data.session.metadata;

        if (metadata) {
          if (metadata.planId) {
            setPlanId(metadata.planId);

            // Get plan name from our plan limits
            const planInfo = PLAN_LIMITS[metadata.planId as keyof typeof PLAN_LIMITS];
            if (planInfo) {
              setPlanName(planInfo.monthly.displayName);
            } else {
              setPlanName(
                metadata.planId === "starter" ? "Starter" :
                metadata.planId === "professional" ? "Professional" :
                metadata.planId === "enterprise" ? "Enterprise" : ""
              );
            }
          }

          if (metadata.billingCycle) {
            setBillingCycle(metadata.billingCycle as "monthly" | "annual");
          }
        }

        // Try to manually create the subscription with a delay to ensure the session is fully processed
        setTimeout(async () => {
          const success = await createSubscriptionManually(sessionId);

          if (success) {
            // Refresh subscription data only once
            if (!refetchTriggeredRef.current) {
              refetchTriggeredRef.current = true;
              console.log('Refreshing subscription data after manual creation');
              await refetch();
            }
          }
        }, 500); // 500ms delay to ensure the session is fully processed
      }
    } catch (error) {
      console.error('Error fetching session details:', error);
      toast({
        title: "Error",
        description: "Failed to load payment details. Your subscription may still be active.",
        variant: "destructive",
      });
    } finally {
      setIsLoadingSession(false);
    }
  }, [isAuthenticated, getToken, toast, refetch, createSubscriptionManually]);

  // Refresh subscription data when component mounts - but only once
  useEffect(() => {
    if (isAuthenticated && !refetchTriggeredRef.current) {
      refetchTriggeredRef.current = true;
      refetch();
    }
  }, [isAuthenticated, refetch]);

  useEffect(() => {
    // Only run this effect once
    const initializeComponent = async () => {
      // Create formatted date
      const now = new Date();
      setPaymentDate(now.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      }));

      // Try to get session ID from URL
      const params = new URLSearchParams(window.location.search);
      const sessionId = params.get('session_id');

      // Store the session ID in a ref to avoid re-renders
      if (sessionId) {
        sessionIdRef.current = sessionId;
      }

      if (sessionId && isAuthenticated) {
        // Use the memoized function to fetch session details
        await fetchSessionDetails(sessionId);
      } else if (!sessionId) {
        // Fallback to old URL parameters for backward compatibility
        const planParam = params.get('plan');
        const billingParam = params.get('billing');

        if (planParam) {
          setPlanId(planParam);

          // Get plan name from our plan limits
          const planInfo = PLAN_LIMITS[planParam as keyof typeof PLAN_LIMITS];
          if (planInfo) {
            setPlanName(planInfo.monthly.displayName);
          } else {
            setPlanName(
              planParam === "starter" ? "Starter" :
              planParam === "professional" ? "Professional" :
              planParam === "enterprise" ? "Enterprise" : ""
            );
          }
        }

        if (billingParam) {
          setBillingCycle(billingParam as "monthly" | "annual");
        }

        setIsLoadingSession(false);
      }
    };

    // Only run the initialization once
    if (!sessionFetchedRef.current) {
      initializeComponent();
    }
  }, [isAuthenticated, fetchSessionDetails]);
  // Get plan details from our plan limits
  const planDetails = planId && billingCycle ?
    PLAN_LIMITS[planId as keyof typeof PLAN_LIMITS]?.[billingCycle] : null;

  return (
    <div className="flex justify-center items-start w-full py-12 px-4">
      <Card className="text-center w-full max-w-md">
        <CardHeader>
          <div className="flex justify-center mb-4">
            <CheckCircle className="h-16 w-16 text-green-500" />
          </div>
          <CardTitle className="text-2xl">Payment Successful!</CardTitle>
          <CardDescription>
            Thank you for your subscription
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="mb-4">
            Your payment has been successfully processed. You now have access to all features of your subscription plan. You will receive an email confirmation shortly.
          </p>

          {/* Subscription details */}
          {isLoading || isLoadingSession ? (
            <div className="flex justify-center p-6">
              <LoadingSpinner size="md" />
            </div>
          ) : subscription ? (
            <div className="bg-gray-50 rounded-md p-4 mt-4 text-left">
              <h3 className="font-medium text-gray-800 mb-2">Subscription Details</h3>
              <div className="space-y-3">
                <div className="flex items-center">
                  <Calendar className="h-4 w-4 text-gray-500 mr-2" />
                  <div>
                    <p className="text-sm font-medium">Subscription Start Date</p>
                    <p className="text-sm text-gray-500">
                      {subscription.subscription?.current_period_start ?
                        new Date(subscription.subscription.current_period_start).toLocaleDateString('en-US', {
                          year: 'numeric', month: 'long', day: 'numeric'
                        }) :
                        paymentDate
                      }
                    </p>
                  </div>
                </div>
                <div className="flex items-center">
                  <Zap className="h-4 w-4 text-gray-500 mr-2" />
                  <div>
                    <p className="text-sm font-medium">Plan Type</p>
                    <p className="text-sm text-gray-500 capitalize">
                      {planName || subscription.subscription?.plan_id} Plan
                    </p>
                  </div>
                </div>
                <div className="flex items-center">
                  <CreditCard className="h-4 w-4 text-gray-500 mr-2" />
                  <div>
                    <p className="text-sm font-medium">Billing Cycle</p>
                    <p className="text-sm text-gray-500 capitalize">
                      {billingCycle || subscription.subscription?.billing_cycle}
                      {(billingCycle === 'annual' || subscription.subscription?.billing_cycle === 'annual') &&
                        ' (20% discount)'}
                    </p>
                  </div>
                </div>
                <div className="flex items-center">
                  <RefreshCw className="h-4 w-4 text-gray-500 mr-2" />
                  <div>
                    <p className="text-sm font-medium">Next Billing Date</p>
                    <p className="text-sm text-gray-500">
                      {subscription.subscription?.current_period_end ?
                        new Date(subscription.subscription.current_period_end).toLocaleDateString('en-US', {
                          year: 'numeric', month: 'long', day: 'numeric'
                        }) :
                        'Not available'
                      }
                    </p>
                  </div>
                </div>
                {subscription.limits && (
                  <div className="mt-2 pt-2 border-t border-gray-200">
                    <p className="text-sm font-medium mb-1">Plan Limits:</p>
                    <ul className="text-sm text-gray-500 list-disc pl-5 space-y-1">
                      <li>{subscription.limits.projects} projects per month</li>
                      <li>{subscription.limits.imagesPerProject} images per project</li>
                    </ul>
                  </div>
                )}
              </div>
            </div>
          ) : planDetails ? (
            <div className="bg-gray-50 rounded-md p-4 mt-4 text-left">
              <h3 className="font-medium text-gray-800 mb-2">Subscription Details</h3>
              <div className="space-y-3">
                <div className="flex items-center">
                  <Calendar className="h-4 w-4 text-gray-500 mr-2" />
                  <div>
                    <p className="text-sm font-medium">Subscription Start Date</p>
                    <p className="text-sm text-gray-500">{paymentDate}</p>
                  </div>
                </div>
                <div className="flex items-center">
                  <Zap className="h-4 w-4 text-gray-500 mr-2" />
                  <div>
                    <p className="text-sm font-medium">Plan Type</p>
                    <p className="text-sm text-gray-500">{planDetails.displayName} Plan</p>
                  </div>
                </div>
                <div className="flex items-center">
                  <CreditCard className="h-4 w-4 text-gray-500 mr-2" />
                  <div>
                    <p className="text-sm font-medium">Billing Cycle</p>
                    <p className="text-sm text-gray-500 capitalize">
                      {billingCycle}
                      {billingCycle === 'annual' && ' (20% discount)'}
                    </p>
                  </div>
                </div>
                <div className="flex items-center">
                  <Image className="h-4 w-4 text-gray-500 mr-2" />
                  <div>
                    <p className="text-sm font-medium">Plan Limits</p>
                    <p className="text-sm text-gray-500">
                      {planDetails.projects} projects, {planDetails.imagesPerProject} images per project
                    </p>
                  </div>
                </div>
                <div className="mt-2 pt-2 border-t border-gray-200">
                  <p className="text-sm font-medium mb-1">Plan Features:</p>
                  <ul className="text-sm text-gray-500 list-disc pl-5 space-y-1">
                    {planDetails.features.map((feature, index) => (
                      <li key={index}>{feature}</li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          ) : (
            <div className="bg-gray-50 rounded-md p-4 mt-4 text-left">
              <h3 className="font-medium text-gray-800 mb-2">Subscription Details</h3>
              <p className="text-sm text-gray-500">
                Your subscription has been activated. Please check your dashboard for details.
              </p>
            </div>
          )}
        </CardContent>
        <CardFooter className="flex justify-center gap-4">
          <Button asChild>
            <Link to="/dashboard">Go to Dashboard</Link>
          </Button>
          <Button variant="outline" asChild>
            <Link to="/account">Manage Subscription</Link>
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}