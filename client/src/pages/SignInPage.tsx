import { SignIn } from "@clerk/clerk-react";
import { useLocation, useParams } from "wouter";
import { useEffect } from "react";
import { useGA4Context } from "@/components/GA4Provider";

export default function SignInPage() {
  const [location, setLocation] = useLocation();
  const params = useParams();
  const { trackAuth } = useGA4Context();

  // Get redirect URL from query parameters
  const urlParams = new URLSearchParams(window.location.search);
  const redirectUrl = urlParams.get('redirect_url') || '/';

  // Track sign-in page view
  useEffect(() => {
    trackAuth('sign_in');
  }, [trackAuth]);

  return (
    <div className="min-h-screen flex flex-col justify-center items-center bg-gray-50 p-4">
      <div className="w-full max-w-md mx-auto">
        <div className="text-center mb-6">
          <h1 className="text-3xl font-bold text-gray-900">Welcome to Renovision.Studio</h1>
          <p className="text-gray-600 mt-2">Sign in to continue</p>
        </div>

        <SignIn
          path="/sign-in"
          routing="path"
          signUpUrl="/sign-up"
          redirectUrl={redirectUrl}
          initialStep={params?.stage}
          appearance={{
            elements: {
              rootBox: "mx-auto w-full flex flex-col items-center",
              card: "bg-white rounded-lg shadow-lg p-8 w-full max-w-md",
              form: "w-full max-w-md mx-auto",
              formFieldRow: "w-full",
              formFieldInput: "w-full min-w-[300px] border-gray-300 focus:border-primary focus:ring-primary text-base px-4 py-2",
              formFieldInputShowPasswordButton: "right-3",
              main: "mx-auto max-w-md",
              header: "hidden",
              headerTitle: "hidden",
              headerSubtitle: "hidden",
              formButtonPrimary: "bg-primary hover:bg-primary/90 text-white w-full min-w-[300px] py-2 text-base",
              footer: "hidden",
              socialButtonsIconButton: "h-12",
              socialButtonsBlockButton: "h-12 font-medium",
              socialButtonsBlockButtonText: "font-bold",
              socialButtonsProviderIcon: "w-5 h-5",
              socialButtonsIconButtonsContainer: "flex justify-center gap-4 mt-4 min-w-[300px]",
              dividerContainer: "flex items-center justify-center my-4 min-w-[300px]",
              dividerText: "mx-4 text-gray-500",
              dividerLine: "bg-gray-200 flex-grow h-px",

              formFieldLabel: "text-gray-700 text-base",
              formFieldLabelRow: "mb-2",
              formButtonReset: "text-primary hover:text-primary/90",
              formFieldAction: "text-primary hover:text-primary/90",
              otpCodeFieldInput: "border-gray-300",
              otpCodeFieldInputs: "gap-2",
              identityPreviewEditButton: "text-primary hover:text-primary/90",
              formResendCodeLink: "text-primary hover:text-primary/90",

            },
            layout: {
              socialButtonsVariant: "iconButton",
              socialButtonsPlacement: "bottom",
              showOptionalFields: false,
            }
          }}
        />
      </div>
    </div>
  );
}