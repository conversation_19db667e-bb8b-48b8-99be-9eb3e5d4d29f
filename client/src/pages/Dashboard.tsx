import { useState, useEffect } from "react";
import { Link, useLocation } from "wouter";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle, CreditCard } from "lucide-react";
import ProjectCard from "@/components/ProjectCard";
import DraftCard from "@/components/DraftCard";
import FeatureCard from "@/components/FeatureCard";
import TestimonialCard from "@/components/TestimonialCard";
import { ProjectImageComparison } from "@/components/ProjectImageComparison";
import { getProjects, getDrafts, SavedDraft, setAuthTokenGetter, invalidateCaches } from "@/lib/api";
import { useClerkAuth } from "@/hooks/use-clerk-auth";
import { useToast } from "@/hooks/use-toast";
import { useSubscription } from "@/contexts/SubscriptionContext";

export default function Dashboard() {
  const { isAuthenticated, authToken, userId, getAuthToken, refreshToken } = useClerkAuth();
  const { toast } = useToast();
  const { hasActiveSubscription } = useSubscription();
  const [, navigate] = useLocation();
  const [drafts, setDrafts] = useState<SavedDraft[]>([]);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [subscription, setSubscription] = useState<any>(null);
  const [isLoadingSubscription, setIsLoadingSubscription] = useState(true);
  // Track initial authentication to prevent refresh loops
  const [initialAuthComplete, setInitialAuthComplete] = useState(false);

  // Set the auth token getter for API calls - only once when component mounts
  useEffect(() => {
    if (getAuthToken) {
      setAuthTokenGetter(getAuthToken);
      console.log("[DEBUG] Dashboard - Set auth token getter for API calls");
    }
  }, []);

  // Get query client for manual invalidation
  const queryClient = useQueryClient();

  // Only fetch data if the user is authenticated and initial auth is complete
  const {
    data: projects,
    isLoading,
    error,
    refetch: refetchProjects
  } = useQuery({
    queryKey: ['/api/projects'],
    queryFn: getProjects,
    // Skip the query if the user is not authenticated or initial auth is not complete
    enabled: isAuthenticated === true && initialAuthComplete === true,
    // Retry failed queries with exponential backoff
    retry: 3,
    retryDelay: attemptIndex => Math.min(500 * 2 ** attemptIndex, 3000),
    // Don't refetch on window focus to avoid unnecessary requests
    refetchOnWindowFocus: false,
    // Keep data fresh for 10 seconds to prevent constant refetching but still be responsive
    staleTime: 10 * 1000,
    // Always refetch when component mounts to ensure fresh data
    refetchOnMount: true,
    // Add a longer timeout for slow connections
    gcTime: 5 * 60 * 1000,
  });

  // Load drafts using React Query, only if authenticated
  const {
    data: loadedDrafts,
    isLoading: isDraftsLoading,
    refetch: refetchDrafts
  } = useQuery({
    queryKey: ['/api/drafts'],
    queryFn: getDrafts,
    // Skip the query if the user is not authenticated or initial auth is not complete
    enabled: isAuthenticated === true && initialAuthComplete === true,
    // Retry failed queries with exponential backoff
    retry: 3,
    retryDelay: attemptIndex => Math.min(500 * 2 ** attemptIndex, 3000),
    // Don't refetch on window focus to avoid unnecessary requests
    refetchOnWindowFocus: false,
    // Keep data fresh for 10 seconds to prevent constant refetching but still be responsive
    staleTime: 10 * 1000,
    // Always refetch when component mounts to ensure fresh data
    refetchOnMount: true,
    // Add a longer timeout for slow connections
    gcTime: 5 * 60 * 1000,
  });

  // Handle manual refresh requested by the user
  const handleRefreshData = async () => {
    // Prevent multiple refreshes
    if (isRefreshing) return;

    setIsRefreshing(true);
    try {
      // Force refresh the token
      const token = await refreshToken(true);
      console.log("[DEBUG] Dashboard - Token refreshed for manual refresh:", token ? "Token available" : "No token");

      if (token) {
        // Invalidate all data caches
        invalidateCaches();

        // Invalidate queries to force refetch with new token
        queryClient.invalidateQueries({ queryKey: ['/api/projects'] });
        queryClient.invalidateQueries({ queryKey: ['/api/drafts'] });

        // Explicitly refetch data
        const results = await Promise.all([
          refetchProjects(),
          refetchDrafts()
        ]);

        console.log("[DEBUG] Dashboard - Data manually refreshed successfully", {
          projectsCount: results[0].data?.length || 0,
          draftsCount: results[1].data?.length || 0
        });
      } else {
        console.warn("[DEBUG] Dashboard - No token available for manual refresh");
      }
    } catch (err) {
      console.error("[DEBUG] Dashboard - Error during manual refresh:", err);
    } finally {
      setIsRefreshing(false);
    }
  };

  // Handle initial authentication only once
  useEffect(() => {
    const handleInitialAuth = async () => {
      // Only run this effect if authentication state is known and we haven't completed initial auth
      if (isAuthenticated === true && initialAuthComplete === false) {
        console.log("[DEBUG] Dashboard - Initial authentication detected, preparing data load");

        try {
          // Force refresh the token
          const token = await refreshToken(true);
          console.log("[DEBUG] Dashboard - Initial token refresh completed:", token ? "Token available" : "No token");

          if (token) {
            // Store token in localStorage for immediate availability
            localStorage.setItem('clerk-auth-token', token);

            // Invalidate all data caches
            invalidateCaches();

            // Invalidate queries to force a fresh fetch
            queryClient.invalidateQueries({ queryKey: ['/api/projects'] });
            queryClient.invalidateQueries({ queryKey: ['/api/drafts'] });

            // Add a small delay to ensure token propagation
            await new Promise(resolve => setTimeout(resolve, 300));

            // Explicitly refetch data
            await Promise.all([
              queryClient.refetchQueries({ queryKey: ['/api/projects'] }),
              queryClient.refetchQueries({ queryKey: ['/api/drafts'] })
            ]);

            console.log("[DEBUG] Dashboard - Initial data fetch completed");
          } else {
            console.warn("[DEBUG] Dashboard - No token available after refresh");
          }
        } catch (error) {
          console.error("[DEBUG] Dashboard - Error during initial auth:", error);
        } finally {
          // Mark initial auth as complete regardless of outcome
          setInitialAuthComplete(true);
        }
      } else if (isAuthenticated === false && initialAuthComplete === false) {
        // If not authenticated, still mark initial auth as complete
        // This prevents issues when the user is not logged in
        setInitialAuthComplete(true);
      }
    };

    handleInitialAuth();
  }, [isAuthenticated, initialAuthComplete, refreshToken, queryClient]);

  // Log authentication state in development mode only
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log("[DEBUG] Dashboard - Auth State:", {
        isAuthenticated,
        hasAuthToken: !!authToken,
        userId
      });
    }
  }, [isAuthenticated, authToken, userId]);

  // Update drafts state when loadedDrafts changes
  useEffect(() => {
    if (loadedDrafts) {
      setDrafts(loadedDrafts);
      console.log("[DEBUG] Dashboard - Drafts loaded:", loadedDrafts.length);
    }
  }, [loadedDrafts]);

  // Debug log for projects
  useEffect(() => {
    if (projects) {
      console.log("[DEBUG] Dashboard - Projects loaded:", projects.length);
    }
  }, [projects]);

  // Fetch subscription details
  useEffect(() => {
    if (isAuthenticated && initialAuthComplete) {
      const fetchSubscription = async () => {
        try {
          setIsLoadingSubscription(true);

          // First try the debug endpoint to see raw subscription data
          const headers: Record<string, string> = {
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache'
          };

          // Only add Authorization header if authToken exists
          if (authToken) {
            headers['Authorization'] = `Bearer ${authToken}`;
          }

          const debugResponse = await fetch('/api/debug/subscription', { headers });

          if (debugResponse.ok) {
            const debugData = await debugResponse.json();
            console.log('[DEBUG] Dashboard - Raw subscription data:', JSON.stringify(debugData, null, 2));
          }

          // Then fetch the regular subscription data
          // Reuse the same headers object from above
          const response = await fetch('/api/subscription', { headers });

          if (response.ok) {
            const data = await response.json();
            console.log('[DEBUG] Dashboard - Subscription data:', JSON.stringify(data, null, 2));

            // Log the nested structure to help with debugging
            console.log('[DEBUG] Dashboard - Subscription structure:', {
              hasSubscriptionObject: !!data?.subscription,
              nestedSubscription: !!data?.subscription?.subscription,
              subscriptionKeys: data?.subscription ? Object.keys(data.subscription) : [],
              subscriptionType: data?.subscription ? typeof data.subscription : 'null',
              subscriptionNull: data?.subscription === null,
              status: data?.subscription?.subscription?.status || 'missing',
            });

            // Check subscription status
            const hasActiveSubscription = data?.subscription?.subscription?.status === 'active';

            console.log('[DEBUG] Dashboard - Has active subscription:', hasActiveSubscription);

            setSubscription(data);
          } else if (response.status !== 404) {
            // 404 is expected if no subscription exists
            console.error('Error fetching subscription:', response.statusText);
          }
        } catch (error) {
          console.error('Error fetching subscription:', error);
        } finally {
          setIsLoadingSubscription(false);
        }
      };

      fetchSubscription();
    }
  }, [isAuthenticated, initialAuthComplete, authToken]);

  // Handle draft deletion
  const handleDraftDelete = (draftId: number) => {
    setDrafts(prevDrafts => prevDrafts.filter(draft => draft.id !== draftId));
  };

  // Sample data for features
  const features = [
    {
      icon: "cloud_upload",
      title: "Upload Images",
      description: "Upload before images of the area you want to renovate. Add reference images to show preferred styles."
    },
    {
      icon: "description",
      title: "Describe Changes",
      description: "Explain the modifications you want or point to reference images. Be specific about materials, colors, and styles."
    },
    {
      icon: "auto_awesome",
      title: "AI Visualization",
      description: "Our AI technology creates realistic visualizations of your renovation ideas, helping you see exactly how your space will look before making any changes."
    }
  ];

  // Sample data for testimonials
  const testimonials = [
    {
      name: "John Carpenter",
      role: "Renovation Specialist",
      testimonial: "This tool has transformed how I present ideas to clients. Being able to show them what their space could look like before we start work has increased my conversion rate by 40%.",
      rating: 5.0,
      imageUrl: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=100&q=80"
    },
    {
      name: "Sarah Johnson",
      role: "Interior Designer",
      testimonial: "The AI visualizations are incredibly realistic. It helps my clients make decisions faster because they can actually see how different options would look in their space.",
      rating: 4.5,
      imageUrl: "https://images.unsplash.com/photo-1573497019940-1c28c88b4f3e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=100&q=80"
    }
  ];

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Welcome Section - Customized for authenticated users */}
      <section className="mb-10">
        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          <div className="md:flex">
            <div className="p-8 md:w-1/2">
              <h1 className="font-heading text-3xl font-bold text-gray-900 mb-4">
                {isAuthenticated
                  ? "Welcome to Your Dashboard"
                  : "Transform Your Projects With AI Visualization"}
              </h1>
              <p className="text-gray-600 mb-6">
                {isAuthenticated
                  ? "Create new visualization projects, manage your drafts, and explore your completed renovations all in one place."
                  : "Upload before images, describe your changes or reference other images, and let our AI show your clients what's possible."}
              </p>
              <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-3">
                {isAuthenticated ? (
                  <>
                    <Link href="/create">
                      <Button className="inline-flex items-center">
                        <span className="material-icons text-sm mr-1">add</span>
                        Start New Project
                      </Button>
                    </Link>
                    <Link href="/gallery">
                      <Button variant="outline" className="inline-flex items-center">
                        <span className="material-icons text-sm mr-1">photo_library</span>
                        View Gallery
                      </Button>
                    </Link>
                  </>
                ) : (
                  <Link href="/sign-in">
                    <Button className="inline-flex items-center">
                      <span className="material-icons text-sm mr-1">login</span>
                      Sign In to Start
                    </Button>
                  </Link>
                )}
              </div>
            </div>
            <div className="md:w-1/2 bg-gray-100 flex items-center justify-center p-6">
              <div className="relative w-full h-64 md:h-80">
                <ProjectImageComparison
                  beforeImage="/uploads/landing_before.png"
                  afterImage="/uploads/landing_after.png"
                  beforeAlt="Before renovation"
                  afterAlt="After renovation"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Subscription Alert - Show when authenticated but no active subscription */}
      {isAuthenticated && !isLoadingSubscription && !hasActiveSubscription && (
        <section className="mb-6">
          <Alert variant="warning" className="bg-amber-50 border-amber-200">
            <AlertCircle className="h-5 w-5 text-amber-500" />
            <AlertTitle className="text-amber-800">Subscription Required</AlertTitle>
            <AlertDescription className="text-amber-700">
              <p className="mb-2">You need an active subscription to create projects and generate visualizations.</p>
              <Button asChild size="sm" className="mt-1">
                <Link href="/pricing">
                  <CreditCard className="h-4 w-4 mr-2" />
                  View Pricing Plans
                </Link>
              </Button>
            </AlertDescription>
          </Alert>
        </section>
      )}

      {/* Recent Projects Section - Only show when authenticated */}
      {isAuthenticated && (
        <section className="mb-10">
          <div className="flex justify-between items-center mb-6">
            <div>
              <h2 className="font-heading text-2xl font-bold text-gray-900">Recent Projects</h2>
              <p className="text-gray-600">Your recent visualization projects</p>
            </div>
            <div className="flex items-center space-x-3">
              {/* Refresh button */}
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefreshData}
                disabled={isRefreshing}
                className="flex items-center"
              >
                <span className="material-icons text-sm mr-1">
                  {isRefreshing ? 'sync' : 'refresh'}
                </span>
                {isRefreshing ? 'Refreshing...' : 'Refresh'}
              </Button>

              <Link href="/gallery">
                <span className="text-primary-600 hover:text-primary-700 font-medium flex items-center cursor-pointer">
                  View All
                  <span className="material-icons text-sm ml-1">arrow_forward</span>
                </span>
              </Link>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {isLoading || isRefreshing ? (
              <div className="col-span-3 text-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600 mx-auto mb-4"></div>
                <h3 className="text-xl font-medium text-gray-700 mb-2">
                  {isRefreshing ? 'Refreshing your projects...' : 'Loading your projects...'}
                </h3>
                <p className="text-gray-500">This will only take a moment</p>
              </div>
            ) : error ? (
              <div className="col-span-3 text-center py-12 bg-red-50 rounded-lg border border-red-200">
                <span className="material-icons text-red-500 text-5xl mb-4">error_outline</span>
                <h3 className="text-xl font-medium text-red-700 mb-2">Error loading projects</h3>
                <p className="text-red-600 mb-6">There was a problem loading your projects. Please try again.</p>
                <Button onClick={handleRefreshData} variant="outline" className="bg-white">
                  <span className="material-icons text-sm mr-1">refresh</span>
                  Retry
                </Button>
              </div>
            ) : (projects && projects.length > 0) || drafts.length > 0 ? (
              <>
                {/* Show drafts first */}
                {drafts.map(draft => (
                  <DraftCard key={draft.id} draft={draft} onDelete={handleDraftDelete} />
                ))}

                {/* Then show actual projects, but filter out those that have associated drafts */}
                {(() => {
                  // Get all valid draft project_ids (non-undefined, non-null) once
                  const draftProjectIds = drafts
                    .filter(draft => draft.project_id !== undefined && draft.project_id !== null)
                    .map(draft => draft.project_id);

                  // Filter and map projects
                  return projects?.filter(project =>
                    // Only show projects that don't have associated drafts
                    !draftProjectIds.includes(project.id)
                  ).map(project => (
                    <ProjectCard key={project.id} project={project} />
                  ));
                })()}
              </>
            ) : (
              <div className="col-span-3 text-center py-12">
                <span className="material-icons text-gray-400 text-5xl mb-4">folder_open</span>
                <h3 className="text-xl font-medium text-gray-700 mb-2">No projects yet</h3>
                <p className="text-gray-500 mb-6">Create your first visualization project</p>
                <Link href="/create">
                  <Button>
                    <span className="material-icons text-sm mr-1">add</span>
                    Create New Project
                  </Button>
                </Link>
              </div>
            )}
          </div>
        </section>
      )}

      {/* Features Section */}
      <section className="mb-10">
        <div className="mb-6 text-center">
          <h2 className="font-heading text-2xl font-bold text-gray-900">How It Works</h2>
          <p className="text-gray-600 max-w-2xl mx-auto">Our AI-powered visualization tool helps tradespeople showcase possibilities to clients</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <FeatureCard
              key={index}
              icon={feature.icon}
              title={feature.title}
              description={feature.description}
            />
          ))}
        </div>
      </section>

      {/* Testimonials */}
      <section className="mb-10">
        <div className="mb-6">
          <h2 className="font-heading text-2xl font-bold text-gray-900">What Our Users Say</h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {testimonials.map((testimonial, index) => (
            <TestimonialCard
              key={index}
              name={testimonial.name}
              role={testimonial.role}
              testimonial={testimonial.testimonial}
              rating={testimonial.rating}
              imageUrl={testimonial.imageUrl}
            />
          ))}
        </div>
      </section>
    </div>
  );
}
