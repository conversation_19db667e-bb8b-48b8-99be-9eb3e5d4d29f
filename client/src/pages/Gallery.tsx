import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { Link } from "wouter";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ProjectImageComparison } from "@/components/ProjectImageComparison";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import DraftCard from "@/components/DraftCard";
import { getProjects, getDrafts, SavedDraft } from "@/lib/api";

export default function Gallery() {
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [drafts, setDrafts] = useState<SavedDraft[]>([]);

  const { data: projects, isLoading, error } = useQuery({
    queryKey: ['/api/projects'],
    queryFn: getProjects,
  });

  // Load drafts using React Query
  const { data: loadedDrafts, isLoading: isDraftsLoading } = useQuery({
    queryKey: ['/api/drafts'],
    queryFn: getDrafts,
  });

  // Update drafts state when loadedDrafts changes
  useEffect(() => {
    if (loadedDrafts) {
      setDrafts(loadedDrafts);
    }
  }, [loadedDrafts]);

  // Handle draft deletion
  const handleDraftDelete = (draftId: number) => {
    setDrafts(prevDrafts => prevDrafts.filter(draft => draft.id !== draftId));
  };

  // Show completed projects with before and after images, and draft projects
  const completedProjects = projects?.filter(
    project =>
      (project.status === "completed" &&
       project.beforeImages.length > 0 &&
       project.afterImages.length > 0) ||
      project.status === "draft"
  ) || [];

  // Example categories
  const categories = [
    { id: "all", name: "All" },
    { id: "kitchen", name: "Kitchens" },
    { id: "bathroom", name: "Bathrooms" },
    { id: "living", name: "Living Rooms" },
    { id: "exterior", name: "Exteriors" },
  ];

  if (isLoading) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <h1 className="font-heading text-2xl font-bold text-gray-900 mb-6">Project Gallery</h1>
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading gallery...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <h1 className="font-heading text-2xl font-bold text-gray-900 mb-6">Project Gallery</h1>
        <div className="text-center py-12">
          <span className="material-icons text-red-500 text-5xl mb-4">error_outline</span>
          <p className="text-gray-600">Error loading gallery. Please try again.</p>
        </div>
      </div>
    );
  }

  // For demo purposes, let's use some example projects if none are available
  const exampleProjects = [
    {
      id: 101,
      title: "Modern Kitchen Remodel",
      description: "Complete kitchen transformation with white cabinets and wood accents",
      category: "kitchen",
      beforeImage: "https://images.unsplash.com/photo-1505691938895-1758d7feb511?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      afterImage: "https://images.unsplash.com/photo-1556912172-45b7abe8b7e1?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
    },
    {
      id: 102,
      title: "Contemporary Bathroom",
      description: "Bathroom renovation with marble accents and modern fixtures",
      category: "bathroom",
      beforeImage: "https://images.unsplash.com/photo-1554995207-c18c203602cb?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      afterImage: "https://images.unsplash.com/photo-1600566752355-35792bedcfea?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
    },
    {
      id: 103,
      title: "Living Room Makeover",
      description: "Bright and airy living room transformation",
      category: "living",
      beforeImage: "https://images.unsplash.com/photo-1583608205776-bfd35f0d9f83?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      afterImage: "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
    },
    {
      id: 104,
      title: "Home Exterior Update",
      description: "Refreshed facade with modern paint colors",
      category: "exterior",
      beforeImage: "https://images.unsplash.com/photo-1518780664697-55e3ad937233?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      afterImage: "https://images.unsplash.com/photo-1564013799919-ab600027ffc6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
    }
  ];

  // Filter projects by category
  const filteredExamples = selectedCategory === "all"
    ? exampleProjects
    : exampleProjects.filter(proj => proj.category === selectedCategory);

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="flex flex-col md:flex-row md:items-center justify-between mb-6">
        <div>
          <h1 className="font-heading text-2xl font-bold text-gray-900">Project Gallery</h1>
          <p className="text-gray-600">Browse completed renovation visualizations</p>
        </div>

        <Link href="/create">
          <Button className="mt-4 md:mt-0">
            <span className="material-icons text-sm mr-1">add</span>
            Create New Visualization
          </Button>
        </Link>
      </div>

      <Tabs defaultValue="your-projects" className="mb-8">
        <TabsList>
          <TabsTrigger value="your-projects">Your Projects</TabsTrigger>
          <TabsTrigger value="examples">Example Visualizations</TabsTrigger>
        </TabsList>

        <TabsContent value="examples">
          <div className="mb-6">
            <div className="flex flex-wrap gap-2">
              {categories.map(category => (
                <Badge
                  key={category.id}
                  variant={selectedCategory === category.id ? "default" : "outline"}
                  className="cursor-pointer"
                  onClick={() => setSelectedCategory(category.id)}
                >
                  {category.name}
                </Badge>
              ))}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredExamples.map(example => (
              <Card key={example.id} className="overflow-hidden">
                <div className="h-64 relative">
                  <ProjectImageComparison
                    beforeImage={example.beforeImage}
                    afterImage={example.afterImage}
                    beforeAlt={`${example.title} before`}
                    afterAlt={`${example.title} after`}
                  />
                </div>
                <CardContent className="p-4">
                  <h3 className="font-medium text-gray-900">{example.title}</h3>
                  <p className="text-gray-600 text-sm mt-1">{example.description}</p>
                  <div className="mt-4 flex items-center justify-end">
                    <Button variant="outline" size="sm" className="text-xs">
                      <span className="material-icons text-xs mr-1">fullscreen</span>
                      View Larger
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="your-projects">
          {(completedProjects && completedProjects.length > 0) || drafts.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* Show drafts first */}
              {drafts.map(draft => (
                <DraftCard key={draft.id} draft={draft} onDelete={handleDraftDelete} />
              ))}

              {/* Then show completed projects */}
              {completedProjects.map(project => (
                <Card key={project.id} className="overflow-hidden">
                  <div className="h-64 relative">
                    {project.status === 'draft' ? (
                      <div className="h-full w-full flex items-center justify-center bg-gray-100">
                        <div className="flex flex-col items-center justify-center">
                          <span className="material-icons text-gray-400 text-4xl mb-2">edit_note</span>
                          <p className="text-gray-600 font-medium">Draft Project</p>
                        </div>
                      </div>
                    ) : project.beforeImages[0] && project.afterImages[0] ? (
                      <ProjectImageComparison
                        beforeImage={project.beforeImages[0]}
                        afterImage={project.afterImages[0]}
                        beforeAlt={`${project.title} before`}
                        afterAlt={`${project.title} after`}
                      />
                    ) : (
                      <div className="h-full w-full flex items-center justify-center bg-gray-100">
                        <div className="flex flex-col items-center justify-center">
                          <span className="material-icons text-gray-400 text-4xl mb-2">image</span>
                          <p className="text-gray-600 font-medium">No Images Available</p>
                        </div>
                      </div>
                    )}
                  </div>
                  <CardContent className="p-4">
                    <h3 className="font-medium text-gray-900">{project.title}</h3>
                    <p className="text-gray-600 text-sm mt-1">{project.description}</p>
                    <div className="mt-4 flex items-center justify-between">
                      <Badge variant="outline" className={`
                        ${project.status === 'draft' ? 'bg-blue-100 text-blue-800' : ''}
                        ${project.status === 'processing' ? 'bg-yellow-100 text-yellow-800' : ''}
                        ${project.status === 'completed' ? 'bg-green-100 text-green-800' : ''}
                        ${project.status === 'failed' ? 'bg-red-100 text-red-800' : ''}
                      `}>
                        {project.status === 'draft' ? 'Draft' :
                         project.status === 'processing' ? 'Processing' :
                         project.status === 'completed' ? 'Completed' :
                         'Failed'}
                      </Badge>
                      <Link href={`/projects/${project.id}`}>
                        <Button variant="outline" size="sm" className="text-xs">
                          <span className="material-icons text-xs mr-1">{project.status === 'draft' ? 'edit' : 'visibility'}</span>
                          {project.status === 'draft' ? 'Continue Editing' : 'View Details'}
                        </Button>
                      </Link>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="text-center py-12 bg-gray-50 rounded-lg border border-gray-200">
              <span className="material-icons text-gray-400 text-5xl mb-4">folder_open</span>
              <h3 className="text-xl font-medium text-gray-700 mb-2">No projects or drafts yet</h3>
              <p className="text-gray-500 mb-6">Create a visualization project to see it here</p>
              <Link href="/create">
                <Button>
                  <span className="material-icons text-sm mr-1">add</span>
                  Create New Project
                </Button>
              </Link>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
