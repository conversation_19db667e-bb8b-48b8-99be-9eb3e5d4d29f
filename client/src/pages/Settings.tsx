import { useClerkA<PERSON> } from "@/hooks/use-clerk-auth";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { useState, useEffect } from "react";
import { Loader2, Calendar, CreditCard, CheckCircle, AlertCircle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { Link } from "wouter";

export default function Settings() {
  const { user, isLoading, error, isAuthenticated } = useClerkAuth();
  const { toast } = useToast();
  const [isSaving, setIsSaving] = useState(false);
  const [subscription, setSubscription] = useState<any>(null);
  const [isLoadingSubscription, setIsLoadingSubscription] = useState(true);

  // Add logging for debugging
  useEffect(() => {
    // eslint-disable-next-line no-console
    console.info("[Settings] useAuth state", { user, isLoading, error });
  }, [user, isLoading, error]);

  // Fetch subscription details
  useEffect(() => {
    if (isAuthenticated) {
      const fetchSubscription = async () => {
        try {
          setIsLoadingSubscription(true);
          const response = await fetch('/api/subscription');
          if (response.ok) {
            const data = await response.json();
            console.log('Subscription data:', data); // Add logging to debug
            setSubscription(data);
          } else if (response.status !== 404) {
            // 404 is expected if no subscription exists
            console.error('Error fetching subscription:', response.statusText);
          }
        } catch (error) {
          console.error('Error fetching subscription:', error);
        } finally {
          setIsLoadingSubscription(false);
        }
      };

      fetchSubscription();
    }
  }, [isAuthenticated]);

  if (isLoading) {
    return <div className="flex justify-center items-center h-[60vh]">Loading user...</div>;
  }

  if (error) {
    return <div className="flex justify-center items-center h-[60vh] text-red-600">Error: {error.toString()}</div>;
  }

  if (!user) {
    return <div className="flex justify-center items-center h-[60vh]">No user found. Please sign in again.</div>;
  }

  const handleSaveAccount = (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);

    // Simulate API call
    setTimeout(() => {
      setIsSaving(false);
      toast({
        title: "Settings saved",
        description: "Your account settings have been updated successfully.",
      });
    }, 1000);
  };

  const handleSaveNotifications = (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);

    // Simulate API call
    setTimeout(() => {
      setIsSaving(false);
      toast({
        title: "Notification preferences saved",
        description: "Your notification settings have been updated successfully.",
      });
    }, 1000);
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <h1 className="text-3xl font-bold mb-6">Settings</h1>

      <Tabs defaultValue="account" className="w-full">
        <TabsList className="mb-6">
          <TabsTrigger value="account">Account</TabsTrigger>
          <TabsTrigger value="notifications">Notifications</TabsTrigger>
          <TabsTrigger value="subscription">Subscription</TabsTrigger>
        </TabsList>

        <TabsContent value="account" className="w-full">
          <div className="grid grid-cols-1 gap-6">
            <Card className="w-full">
              <CardHeader>
                <CardTitle>Account Settings</CardTitle>
                <CardDescription>
                  Manage your account details and preferences
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSaveAccount} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="username">Username</Label>
                      <Input
                        id="username"
                        defaultValue={user.username}
                        placeholder="Username"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="email">Email</Label>
                      <Input
                        id="email"
                        type="email"
                        defaultValue="<EMAIL>"
                        placeholder="Email"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="current-password">Current Password</Label>
                      <Input
                        id="current-password"
                        type="password"
                        placeholder="Current Password"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="new-password">New Password</Label>
                      <Input
                        id="new-password"
                        type="password"
                        placeholder="New Password"
                      />
                    </div>
                  </div>

                  <Button type="submit" disabled={isSaving}>
                    {isSaving ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      "Save Changes"
                    )}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="notifications" className="w-full">
          <div className="grid grid-cols-1 gap-6">
            <Card className="w-full">
              <CardHeader>
                <CardTitle>Notification Settings</CardTitle>
                <CardDescription>
                  Manage how you receive notifications and updates
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSaveNotifications} className="space-y-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="email-notifications">Email Notifications</Label>
                        <p className="text-sm text-muted-foreground">
                          Receive email notifications when your projects are updated
                        </p>
                      </div>
                      <Switch id="email-notifications" defaultChecked />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="marketing-emails">Marketing Emails</Label>
                        <p className="text-sm text-muted-foreground">
                          Receive emails about new features and promotions
                        </p>
                      </div>
                      <Switch id="marketing-emails" />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="project-updates">Project Updates</Label>
                        <p className="text-sm text-muted-foreground">
                          Receive notifications when your projects are processed
                        </p>
                      </div>
                      <Switch id="project-updates" defaultChecked />
                    </div>
                  </div>

                  <Button type="submit" disabled={isSaving}>
                    {isSaving ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      "Save Preferences"
                    )}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="subscription" className="w-full">
          <div className="grid grid-cols-1 gap-6">
            <Card className="w-full">
              <CardHeader>
                <CardTitle>Subscription Plan</CardTitle>
                <CardDescription>
                  Manage your subscription and billing details
                </CardDescription>
              </CardHeader>
              <CardContent>
                {isLoadingSubscription ? (
                  <div className="flex justify-center items-center py-8">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  </div>
                ) : subscription && subscription.subscription ? (
                  <div className="space-y-6">
                    <div className="bg-primary-50 border border-primary-100 p-6 rounded-lg">
                      <div className="flex items-start justify-between">
                        <div>
                          <h3 className="text-lg font-semibold mb-1">
                            Current Plan: {subscription.subscription?.plan_id === 'starter' ? 'Starter' :
                                          subscription.subscription?.plan_id === 'professional' ? 'Professional' :
                                          subscription.subscription?.plan_id}
                          </h3>
                          <div className="flex items-center text-sm text-primary-700 mb-1">
                            <CheckCircle className="h-4 w-4 mr-1" />
                            <span>Active subscription</span>
                          </div>
                          <p className="text-sm text-muted-foreground mb-4">
                            Billing cycle: {subscription.subscription?.billing_cycle === 'annual' ? 'Annual (paid monthly)' : 'Monthly'}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="text-lg font-bold">
                            ${subscription.subscription?.plan_id === 'starter' ?
                              (subscription.subscription?.billing_cycle === 'annual' ? '14.99' : '17.99') :
                              (subscription.subscription?.billing_cycle === 'annual' ? '49.00' : '58.80')}
                          </p>
                          <p className="text-xs text-muted-foreground">per month</p>
                        </div>
                      </div>

                      <div className="mt-4 pt-4 border-t border-primary-100">
                        <div className="flex items-center mb-2">
                          <Calendar className="h-4 w-4 text-primary-700 mr-2" />
                          <div>
                            <p className="text-sm font-medium">Current Period</p>
                            <p className="text-xs text-muted-foreground">
                              {subscription.subscription?.current_period_start ?
                                new Date(subscription.subscription?.current_period_start).toLocaleDateString('en-US', {
                                  year: 'numeric', month: 'long', day: 'numeric'
                                }) : 'N/A'} - {subscription.subscription?.current_period_end ?
                                new Date(subscription.subscription?.current_period_end).toLocaleDateString('en-US', {
                                  year: 'numeric', month: 'long', day: 'numeric'
                                }) : 'N/A'}
                            </p>
                          </div>
                        </div>

                        <div className="flex items-center">
                          <CreditCard className="h-4 w-4 text-primary-700 mr-2" />
                          <div>
                            <p className="text-sm font-medium">Payment Method</p>
                            <p className="text-xs text-muted-foreground">
                              Credit Card (Stripe)
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold">Usage This Period</h3>
                      {subscription.usage ? (
                        <div className="bg-muted p-4 rounded-lg">
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <p className="text-sm font-medium">Projects</p>
                              <div className="flex items-center mt-1">
                                <span className="text-2xl font-bold">{subscription.usage.projects_count}</span>
                                <span className="text-sm text-muted-foreground ml-2">/ {subscription.limits.projects}</span>
                              </div>
                              <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                                <div
                                  className="bg-primary h-2 rounded-full"
                                  style={{
                                    width: `${Math.min(100, (subscription.usage.projects_count / subscription.limits.projects) * 100)}%`
                                  }}
                                ></div>
                              </div>
                            </div>
                            <div>
                              <p className="text-sm font-medium">Images</p>
                              <div className="flex items-center mt-1">
                                <span className="text-2xl font-bold">{subscription.usage.images_count}</span>
                                <span className="text-sm text-muted-foreground ml-2">
                                  / {subscription.limits.projects * subscription.limits.imagesPerProject}
                                </span>
                              </div>
                              <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                                <div
                                  className="bg-primary h-2 rounded-full"
                                  style={{
                                    width: `${Math.min(100, (subscription.usage.images_count / (subscription.limits.projects * subscription.limits.imagesPerProject)) * 100)}%`
                                  }}
                                ></div>
                              </div>
                            </div>
                          </div>
                        </div>
                      ) : (
                        <p className="text-sm text-muted-foreground">
                          No usage data available for the current period.
                        </p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <h3 className="text-lg font-semibold">Plan Features</h3>
                      <div className="bg-muted p-4 rounded-lg">
                        <h4 className="font-medium mb-2">
                          {subscription.subscription?.plan_id === 'starter' ? 'Starter' : 'Professional'} Plan Includes:
                        </h4>
                        <ul className="text-sm space-y-1">
                          <li>• {subscription.limits.projects} projects per month</li>
                          <li>• {subscription.limits.imagesPerProject} images per project</li>
                          <li>• High-quality AI visualizations</li>
                          {subscription.subscription?.plan_id === 'professional' && (
                            <>
                              <li>• Priority processing</li>
                              <li>• Advanced customization options</li>
                            </>
                          )}
                        </ul>
                      </div>
                    </div>

                    <div className="flex justify-between items-center">
                      <Button variant="outline" asChild>
                        <Link href="/pricing">View All Plans</Link>
                      </Button>
                      <Button variant="destructive" disabled>Cancel Subscription</Button>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-6">
                    <div className="bg-muted p-6 rounded-lg">
                      <div className="flex items-start">
                        <AlertCircle className="h-5 w-5 text-amber-500 mr-2 mt-0.5" />
                        <div>
                          <h3 className="text-lg font-semibold mb-1">No Active Subscription</h3>
                          <p className="text-sm text-muted-foreground mb-4">
                            You don't have an active subscription. Subscribe to a plan to access premium features.
                          </p>
                          <Button asChild>
                            <Link href="/pricing">View Pricing Plans</Link>
                          </Button>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold">Available Plans</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="bg-muted p-4 rounded-lg">
                          <h4 className="font-medium mb-1">Starter Plan</h4>
                          <p className="text-sm font-bold mb-2">$14.99/month (annual billing)</p>
                          <ul className="text-sm space-y-1">
                            <li>• 3 projects per month</li>
                            <li>• 3 images per project</li>
                            <li>• Instant processing</li>
                          </ul>
                        </div>
                        <div className="bg-muted p-4 rounded-lg">
                          <h4 className="font-medium mb-1">Professional Plan</h4>
                          <p className="text-sm font-bold mb-2">$49/month (annual billing)</p>
                          <ul className="text-sm space-y-1">
                            <li>• 10 projects per month</li>
                            <li>• 5 images per project</li>
                            <li>• Priority processing</li>
                            <li>• Advanced customization options</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}