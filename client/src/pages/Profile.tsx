import { useAuth } from "@/hooks/use-clerk-auth";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";

export default function Profile() {
  const { user } = useAuth();

  // Get initials from username for avatar
  const getInitials = (name: string) => {
    return name.substring(0, 2).toUpperCase();
  };

  if (!user) {
    return <div className="flex justify-center items-center h-[60vh]">Loading...</div>;
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <h1 className="text-3xl font-bold mb-6">Your Profile</h1>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 w-full">
        <div className="col-span-1">
          <Card className="w-full">
            <CardHeader className="flex flex-col items-center">
              <Avatar className="h-24 w-24 mb-2">
                <AvatarFallback className="bg-primary-100 text-primary-800 text-2xl">
                  {getInitials(user.username)}
                </AvatarFallback>
              </Avatar>
              <CardTitle className="mt-2">{user.username}</CardTitle>
              <CardDescription>Member</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Username</p>
                  <p>{user.username}</p>
                </div>
                <Separator />
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Account Type</p>
                  <p>Standard</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="col-span-1 md:col-span-2">
          <Card className="w-full">
            <CardHeader>
              <CardTitle>Account Activity</CardTitle>
              <CardDescription>Your recent activity and account statistics</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="bg-muted rounded-lg p-4">
                    <p className="text-sm font-medium text-muted-foreground">Total Projects</p>
                    <p className="text-3xl font-bold">12</p>
                  </div>
                  <div className="bg-muted rounded-lg p-4">
                    <p className="text-sm font-medium text-muted-foreground">AI Visualizations</p>
                    <p className="text-3xl font-bold">23</p>
                  </div>
                </div>

                <Separator />

                <div>
                  <h3 className="text-lg font-semibold mb-2">Recent Activity</h3>
                  <ul className="space-y-2">
                    <li className="text-sm">Created project "Kitchen Renovation" - 2 days ago</li>
                    <li className="text-sm">Generated 3 visualizations for "Bathroom Remodel" - 5 days ago</li>
                    <li className="text-sm">Updated project description for "Garage Floor" - 1 week ago</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}