import { useState, useRef, useEffect } from "react";

// Add global styles for the image comparison component
if (typeof document !== 'undefined') {
  const style = document.createElement('style');
  style.innerHTML = `
    .image-comparison-container {
      position: relative;
      overflow: hidden;
      user-select: none;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      max-height: 100%;
    }

    .image-comparison-slider {
      position: absolute;
      top: 0;
      bottom: 0;
      width: 2px;
      background: white;
      z-index: 10;
      cursor: ew-resize;
    }

    .image-comparison-handle {
      position: absolute;
      top: 50%;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: white;
      transform: translate(-50%, -50%);
      z-index: 20;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
      cursor: ew-resize;
    }

    .image-comparison-before,
    .image-comparison-after {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }

    .image-comparison-before {
      clip-path: polygon(0% 0%, var(--position) 0%, var(--position) 100%, 0% 100%);
      -webkit-clip-path: polygon(0% 0%, var(--position) 0%, var(--position) 100%, 0% 100%);
    }

    .image-comparison-img {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: cover;
      display: block;
    }

    .image-comparison-label {
      position: absolute;
      top: 10px;
      background: rgba(0, 0, 0, 0.5);
      color: white;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 14px;
      font-weight: 500;
      z-index: 5;
    }

    .image-comparison-label-before {
      left: 10px;
    }

    .image-comparison-label-after {
      right: 10px;
    }
  `;
  document.head.appendChild(style);
}

interface ImageComparisonProps {
  beforeImage: string;
  afterImage: string;
  beforeAlt?: string;
  afterAlt?: string;
  className?: string;
}

export function ImageComparison({
  beforeImage,
  afterImage,
  beforeAlt = "Before image",
  afterAlt = "After image",
  className = "",
}: ImageComparisonProps) {
  const [position, setPosition] = useState(50);
  const [isDragging, setIsDragging] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const beforeImageRef = useRef<HTMLImageElement>(null);
  const afterImageRef = useRef<HTMLImageElement>(null);

  // Log image paths for debugging
  console.log('ImageComparison - beforeImage:', beforeImage);
  console.log('ImageComparison - afterImage:', afterImage);

  // Handle mouse down event
  const handleMouseDown = (e: React.MouseEvent | React.TouchEvent) => {
    e.preventDefault();
    setIsDragging(true);

    // Update position immediately on click/touch
    updatePosition(e);
  };

  // Handle mouse up event
  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // Handle mouse move event
  const handleMouseMove = (e: MouseEvent | TouchEvent) => {
    if (!isDragging) return;
    e.preventDefault();
    updatePosition(e);
  };

  // Update slider position based on mouse/touch position
  const updatePosition = (e: MouseEvent | TouchEvent | React.MouseEvent | React.TouchEvent) => {
    if (!containerRef.current) return;

    const containerRect = containerRef.current.getBoundingClientRect();
    let clientX: number;

    // Handle both mouse and touch events
    if ('touches' in e) {
      clientX = e.touches[0].clientX;
    } else {
      clientX = (e as MouseEvent).clientX;
    }

    // Calculate position as percentage
    const newPosition = ((clientX - containerRect.left) / containerRect.width) * 100;

    // Clamp position between 0 and 100
    setPosition(Math.max(0, Math.min(newPosition, 100)));
  };

  // Set up event listeners
  useEffect(() => {
    const handleMouseMoveEvent = (e: MouseEvent) => handleMouseMove(e);
    const handleTouchMoveEvent = (e: TouchEvent) => handleMouseMove(e);
    const handleMouseUpEvent = () => handleMouseUp();

    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMoveEvent);
      document.addEventListener('touchmove', handleTouchMoveEvent, { passive: false });
      document.addEventListener('mouseup', handleMouseUpEvent);
      document.addEventListener('touchend', handleMouseUpEvent);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMoveEvent);
      document.removeEventListener('touchmove', handleTouchMoveEvent);
      document.removeEventListener('mouseup', handleMouseUpEvent);
      document.removeEventListener('touchend', handleMouseUpEvent);
    };
  }, [isDragging]);

  // Track image loading and errors
  const [beforeLoaded, setBeforeLoaded] = useState(false);
  const [afterLoaded, setAfterLoaded] = useState(false);
  const [beforeError, setBeforeError] = useState(false);
  const [afterError, setAfterError] = useState(false);

  return (
    <div
      ref={containerRef}
      className={`image-comparison-container ${className}`}
      style={{
        height: '100%',
        width: '100%',
        '--position': `${position}%`
      } as React.CSSProperties}
      onMouseDown={handleMouseDown}
      onTouchStart={handleMouseDown}
    >
      {/* After image (bottom layer) */}
      <div className="image-comparison-after">
        <img
          ref={afterImageRef}
          src={afterImage}
          alt={afterAlt}
          className="image-comparison-img"
          onLoad={() => setAfterLoaded(true)}
          onError={() => setAfterError(true)}
          draggable="false"
        />
        {afterError && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
            <div className="text-center p-4">
              <span className="material-icons text-red-500 text-4xl">error_outline</span>
              <p className="mt-2 text-red-600">Failed to load after image</p>
            </div>
          </div>
        )}
      </div>

      {/* Before image (top layer, clipped) */}
      <div className="image-comparison-before">
        <img
          ref={beforeImageRef}
          src={beforeImage}
          alt={beforeAlt}
          className="image-comparison-img"
          onLoad={() => setBeforeLoaded(true)}
          onError={() => setBeforeError(true)}
          draggable="false"
        />
        {beforeError && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
            <div className="text-center p-4">
              <span className="material-icons text-red-500 text-4xl">error_outline</span>
              <p className="mt-2 text-red-600">Failed to load before image</p>
            </div>
          </div>
        )}
      </div>

      {/* Slider line */}
      <div
        className="image-comparison-slider"
        style={{ left: `${position}%` }}
      ></div>

      {/* Slider handle */}
      <div
        className="image-comparison-handle"
        style={{ left: `${position}%` }}
        onMouseDown={(e) => {
          e.stopPropagation();
          handleMouseDown(e);
        }}
        onTouchStart={(e) => {
          e.stopPropagation();
          handleMouseDown(e);
        }}
      >
        <span className="material-icons text-gray-700">compare</span>
      </div>

      {/* Labels */}
      <div className="image-comparison-label image-comparison-label-before">Before</div>
      <div className="image-comparison-label image-comparison-label-after">After</div>
    </div>
  );
}
