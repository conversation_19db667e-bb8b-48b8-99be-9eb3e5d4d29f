import { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { normalizeImagePath } from '@/utils/image-utils';

interface ImageUploaderProps {
  onFilesSelected: (files: File[]) => void;
  maxFiles?: number;
  accept?: string[];
  title: string;
  description: string;
  imageType?: string;
  uploadedFiles?: Array<{
    id: number;
    path: string;
    original_filename?: string | null;
    originalFilename?: string | null; // For backward compatibility
    type?: string;
    projectId?: number | null;
    uploadedAt?: Date;
  }>;
  onRemoveFile?: (id: number) => void;
}

export function ImageUploader({
  onFilesSelected,
  maxFiles = 10,
  accept = ['image/jpeg', 'image/png', 'image/webp'],
  title,
  description,
  imageType = "Before",
  uploadedFiles = [],
  onRemoveFile
}: ImageUploaderProps) {
  const [isDragging, setIsDragging] = useState(false);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (acceptedFiles.length > 0) {
      onFilesSelected(acceptedFiles);
    }
  }, [onFilesSelected]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.webp']
    },
    maxFiles,
    maxSize: 10 * 1024 * 1024, // 10MB
    onDragEnter: () => setIsDragging(true),
    onDragLeave: () => setIsDragging(false),
    onDropAccepted: () => setIsDragging(false),
    onDropRejected: () => setIsDragging(false),
  });

  const hasFiles = uploadedFiles.length > 0;

  return (
    <div className="w-full">
      <div
        {...getRootProps()}
        className={`border-2 border-dashed rounded-lg p-6 flex flex-col items-center justify-center bg-gray-50 cursor-pointer transition-colors duration-150 ${
          isDragging || isDragActive ? 'border-primary-500 bg-primary-50' : 'border-gray-300 hover:bg-gray-100'
        }`}
      >
        <span className="material-icons text-4xl text-gray-400 mb-2">
          {imageType === "Reference" ? "image" : "cloud_upload"}
        </span>
        <p className="text-gray-700 font-medium">{title}</p>
        <p className="text-gray-500 text-sm mt-1">{description}</p>
        <input {...getInputProps()} />
      </div>

      {hasFiles && (
        <div className="mt-4">
          <h4 className="text-sm font-medium text-gray-700 mb-3">Uploaded Images</h4>
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
            {uploadedFiles.map((file) => (
              <div key={file.id} className="relative group">
                <img
                  src={normalizeImagePath(file)}
                  alt={file.original_filename || file.originalFilename || `Uploaded image ${file.id}`}
                  className="w-full h-32 object-cover rounded-lg shadow-sm"
                />
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-opacity duration-200 rounded-lg flex items-center justify-center">
                  {onRemoveFile && (
                    <Button
                      variant="secondary"
                      size="icon"
                      className="p-1 bg-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200 shadow-sm"
                      onClick={() => onRemoveFile(file.id)}
                    >
                      <span className="material-icons text-red-500 text-sm">delete</span>
                    </Button>
                  )}
                </div>
                <Badge variant="secondary" className="bg-primary-600 text-white absolute top-2 left-2">
                  {imageType}
                </Badge>
              </div>
            ))}
          </div>
        </div>
      )}

      {!hasFiles && (
        <div className="mt-4 flex items-center justify-center h-20 border border-gray-200 rounded-lg bg-gray-50">
          <p className="text-gray-500 text-sm">No {imageType.toLowerCase()} images uploaded yet</p>
        </div>
      )}
    </div>
  );
}
