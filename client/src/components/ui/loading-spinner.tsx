import React from 'react';
import { cn } from '@/lib/utils';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  withCenter?: boolean;
  label?: string;
}

export function LoadingSpinner({
  size = 'md',
  className,
  withCenter = true,
  label = 'Loading'
}: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: {
      spinner: 'w-8 h-8 border-3',
      center: 'h-4 w-4'
    },
    md: {
      spinner: 'w-12 h-12 border-4',
      center: 'h-6 w-6'
    },
    lg: {
      spinner: 'w-16 h-16 border-4',
      center: 'h-8 w-8'
    }
  };

  const spinner = (
    <div className="relative">
      <div 
        className={cn(
          "animate-spin border-primary border-t-transparent rounded-full",
          sizeClasses[size].spinner,
          className
        )} 
        aria-label={label}
      />
      {withCenter && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className={cn("bg-background rounded-full", sizeClasses[size].center)}></div>
        </div>
      )}
    </div>
  );

  return spinner;
}

export function LoadingSpinnerWithText({
  size = 'md',
  title = 'Loading',
  description,
  className
}: {
  size?: 'sm' | 'md' | 'lg';
  title?: string;
  description?: string;
  className?: string;
}) {
  return (
    <div className={cn("flex flex-col items-center justify-center space-y-6", className)}>
      <LoadingSpinner size={size} />
      <div className="text-center">
        <h2 className="text-2xl font-bold mt-4">{title}</h2>
        {description && (
          <p className="text-muted-foreground max-w-xs mx-auto">{description}</p>
        )}
      </div>
    </div>
  );
}
