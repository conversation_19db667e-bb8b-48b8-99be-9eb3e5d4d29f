import React, { useRef, useCallback } from 'react';
import { Input } from './input';
import { Button } from './button';
import { Loader2, Upload, Check, X } from 'lucide-react';

interface OptimizedFileInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  onFileSelect: (file: File | null) => void;
  selectedFile: File | null;
  isUploading?: boolean;
  accept?: string;
}

export function OptimizedFileInput({
  onFileSelect,
  selectedFile,
  isUploading = false,
  accept = "image/*",
  ...props
}: OptimizedFileInputProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      onFileSelect(file);
    } else {
      onFileSelect(null);
    }
  }, [onFileSelect]);

  const clearSelection = () => {
    onFileSelect(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const triggerFileInput = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  return (
    <div className="space-y-2">
      <div className="flex flex-col gap-2">
        <div className="relative">
          <Input
            type="file"
            ref={fileInputRef}
            onChange={handleFileChange}
            className="hidden"
            accept={accept}
            disabled={isUploading}
            {...props}
          />

          <div className="flex items-center gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={triggerFileInput}
              disabled={isUploading}
              className="flex-1"
            >
              {isUploading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Uploading...
                </>
              ) : selectedFile ? (
                <>
                  <Check className="mr-2 h-4 w-4 text-green-500" />
                  Change File
                </>
              ) : (
                <>
                  <Upload className="mr-2 h-4 w-4" />
                  Select File
                </>
              )}
            </Button>

            {selectedFile && (
              <Button
                type="button"
                variant="ghost"
                size="icon"
                onClick={clearSelection}
                disabled={isUploading}
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>

        {selectedFile && (
          <div className="text-sm text-muted-foreground flex items-center">
            <span className="truncate max-w-[250px]">{selectedFile.name}</span>
            <span className="ml-2">({(selectedFile.size / (1024 * 1024)).toFixed(2)} MB)</span>
          </div>
        )}
      </div>
    </div>
  );
}
