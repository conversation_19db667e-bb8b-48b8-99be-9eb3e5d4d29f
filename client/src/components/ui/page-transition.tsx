import { useEffect, useState } from 'react';
import { useLocation } from 'wouter';

interface PageTransitionProps {
  children: React.ReactNode;
}

export function PageTransition({ children }: PageTransitionProps) {
  const [location] = useLocation();
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [displayLocation, setDisplayLocation] = useState(location);
  const [transitionStage, setTransitionStage] = useState('fadeIn');

  useEffect(() => {
    if (location !== displayLocation) {
      setIsTransitioning(true);
      setTransitionStage('fadeOut');

      // Short timeout to allow the fade out animation to complete
      const timeout = setTimeout(() => {
        setDisplayLocation(location);
        setTransitionStage('fadeIn');

        // After fade in, mark as no longer transitioning
        const fadeInTimeout = setTimeout(() => {
          setIsTransitioning(false);
        }, 300);

        return () => clearTimeout(fadeInTimeout);
      }, 100);

      return () => clearTimeout(timeout);
    }
  }, [location, displayLocation]);

  return (
    <div
      className={`transition-opacity duration-200 ease-in-out w-full ${
        transitionStage === 'fadeIn' ? 'opacity-100' : 'opacity-90'
      }`}
      style={{
        pointerEvents: isTransitioning ? 'none' : 'auto'
      }}
    >
      {children}
    </div>
  );
}
