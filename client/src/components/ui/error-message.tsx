import React from 'react';
import { <PERSON><PERSON>, AlertTitle, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";

interface ErrorMessageProps {
  title?: string;
  message: string;
  retry?: () => void;
  dismiss?: () => void;
  variant?: 'default' | 'destructive' | 'outline';
}

export function ErrorMessage({
  title = "An error occurred",
  message,
  retry,
  dismiss,
  variant = "destructive"
}: ErrorMessageProps) {
  return (
    <Alert variant={variant} className="mb-4">
      <AlertTitle className="font-medium">{title}</AlertTitle>
      <AlertDescription className="mt-2">
        <p className="text-sm">{message}</p>
        
        {(retry || dismiss) && (
          <div className="mt-4 flex space-x-3">
            {retry && (
              <Button 
                variant="outline" 
                size="sm" 
                onClick={retry}
                className="inline-flex items-center"
              >
                <span className="material-icons text-xs mr-1">refresh</span>
                Try Again
              </Button>
            )}
            
            {dismiss && (
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={dismiss}
                className="inline-flex items-center"
              >
                <span className="material-icons text-xs mr-1">close</span>
                Dismiss
              </Button>
            )}
          </div>
        )}
      </AlertDescription>
    </Alert>
  );
}

// Predefined error messages for common scenarios
export const ErrorMessages = {
  NETWORK_ERROR: "We couldn't connect to our servers. Please check your internet connection and try again.",
  SERVER_ERROR: "Something went wrong on our end. Our team has been notified and we're working to fix it.",
  AUTHENTICATION_ERROR: "You need to be signed in to access this feature. Please sign in and try again.",
  AUTHORIZATION_ERROR: "You don't have permission to access this resource.",
  VALIDATION_ERROR: "Please check the information you provided and try again.",
  NOT_FOUND_ERROR: "The resource you're looking for couldn't be found.",
  IMAGE_UPLOAD_ERROR: "There was a problem uploading your image. Please try again with a different image or format.",
  IMAGE_PROCESSING_ERROR: "We couldn't process your image. Our team has been notified of this issue.",
  AI_SERVICE_ERROR: "Our AI service is temporarily unavailable. Please try again later."
};
