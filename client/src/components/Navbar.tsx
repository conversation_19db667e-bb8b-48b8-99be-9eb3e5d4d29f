import { Link, useLocation } from "wouter";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuGroup,
  DropdownMenuLabel
} from "@/components/ui/dropdown-menu";
import { LogOut, Settings, User, Library, PaintBucket, CreditCard, Home, Image, Plus, Menu } from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useClerk } from "@clerk/clerk-react";
import { useClerkAuth } from "@/hooks/use-clerk-auth";
import { useEffect } from "react";
import { useIsMobile } from "@/hooks/use-mobile";
import { useGA4Context } from "@/components/GA4Provider";

const Navbar = () => {
  const [location] = useLocation();
  const { signOut } = useClerk();
  const { user, isAuthenticated, isInitialized } = useClerkAuth();
  const isMobile = useIsMobile();
  const { trackAuth } = useGA4Context();

  // Debug auth state
  useEffect(() => {
    console.log('[NAVBAR] Auth state:', { isAuthenticated, isInitialized });
  }, [isAuthenticated, isInitialized]);

  // Get initials from username or email for avatar fallback
  const getInitials = (name?: string) => {
    return name ? name.substring(0, 2).toUpperCase() : 'AU';
  };

  const handleLogout = () => {
    // Track sign-out event before actually signing out
    trackAuth('sign_out');
    signOut();
  };

  return (
    <nav className="bg-white shadow-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16 md:h-24">
          {/* Logo */}
          <div className="flex-shrink-0 flex items-center">
            <Link href="/" className="flex items-center cursor-pointer">
              <img
                src="/images/logo/Renovision_transparent.png"
                alt="Renovision.Studio"
                className="h-auto w-32 sm:w-40 md:w-48"
              />
            </Link>
          </div>

          {/* Desktop Navigation - Hidden on mobile */}
          <div className="hidden sm:ml-6 sm:flex sm:space-x-8">
            {isAuthenticated ? (
              /* Navigation for authenticated users */
              <>
                <Link href="/gallery">
                  <span className={`cursor-pointer ${location === '/gallery' ? 'border-primary-600 text-gray-800' : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'} inline-flex items-center border-b-2 px-1 pt-1 text-sm font-medium`}>
                    Gallery
                  </span>
                </Link>
                <Link href="/reference-library">
                  <span className={`cursor-pointer ${location === '/reference-library' ? 'border-primary-600 text-gray-800' : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'} inline-flex items-center border-b-2 px-1 pt-1 text-sm font-medium`}>
                    References
                  </span>
                </Link>
              </>
            ) : (
              /* Navigation for unauthenticated users */
              <>
                <Link href="/pricing">
                  <span className={`cursor-pointer ${location === '/pricing' ? 'border-primary-600 text-gray-800' : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'} inline-flex items-center border-b-2 px-1 pt-1 text-sm font-medium`}>
                    Pricing
                  </span>
                </Link>
                <Link href="/contact">
                  <span className={`cursor-pointer ${location === '/contact' ? 'border-primary-600 text-gray-800' : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'} inline-flex items-center border-b-2 px-1 pt-1 text-sm font-medium`}>
                    Contact Us
                  </span>
                </Link>
              </>
            )}
          </div>

          {/* Right side: User menu or auth buttons */}
          <div className="flex items-center">
            {isAuthenticated ? (
              /* Authenticated user UI */
              <>
                <Link href="/create" className="mr-2 md:mr-4">
                  <Button className="hidden md:inline-flex items-center">
                    <Plus className="mr-1 h-4 w-4" />
                    New Project
                  </Button>
                </Link>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                      <Avatar className="h-8 w-8">
                        <AvatarFallback className="bg-primary-100 text-primary-800">
                          {user ? getInitials(user.primaryEmailAddress?.emailAddress) : 'AU'}
                        </AvatarFallback>
                      </Avatar>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="w-56" align="end" forceMount>
                    <DropdownMenuLabel className="font-normal">
                      <div className="flex flex-col space-y-1">
                        <p className="text-sm font-medium leading-none">
                          {user?.fullName || user?.primaryEmailAddress?.emailAddress || 'User'}
                        </p>
                      </div>
                    </DropdownMenuLabel>
                    <DropdownMenuSeparator />

                    {/* Navigation Links - Visible on mobile and desktop */}
                    <DropdownMenuGroup>
                      {isMobile && (
                        <>
                          <Link href="/">
                            <DropdownMenuItem>
                              <Home className="mr-2 h-4 w-4" />
                              <span>Home</span>
                            </DropdownMenuItem>
                          </Link>
                          <Link href="/gallery">
                            <DropdownMenuItem>
                              <Image className="mr-2 h-4 w-4" />
                              <span>Gallery</span>
                            </DropdownMenuItem>
                          </Link>
                          <Link href="/create">
                            <DropdownMenuItem>
                              <Plus className="mr-2 h-4 w-4" />
                              <span>Create New Project</span>
                            </DropdownMenuItem>
                          </Link>
                        </>
                      )}

                      <Link href="/profile">
                        <DropdownMenuItem>
                          <User className="mr-2 h-4 w-4" />
                          <span>Profile</span>
                        </DropdownMenuItem>
                      </Link>
                      <Link href="/account">
                        <DropdownMenuItem>
                          <CreditCard className="mr-2 h-4 w-4" />
                          <span>Subscription</span>
                        </DropdownMenuItem>
                      </Link>
                      <Link href="/settings">
                        <DropdownMenuItem>
                          <Settings className="mr-2 h-4 w-4" />
                          <span>Settings</span>
                        </DropdownMenuItem>
                      </Link>
                      <Link href="/reference-library">
                        <DropdownMenuItem>
                          <Library className="mr-2 h-4 w-4" />
                          <span>Reference Library</span>
                        </DropdownMenuItem>
                      </Link>
                      <Link href="/renovation-presets">
                        <DropdownMenuItem>
                          <PaintBucket className="mr-2 h-4 w-4" />
                          <span>Renovation Presets</span>
                        </DropdownMenuItem>
                      </Link>
                    </DropdownMenuGroup>

                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={handleLogout}>
                      <LogOut className="mr-2 h-4 w-4" />
                      <span>Log out</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </>
            ) : (
              /* Unauthenticated user UI - Show buttons on all screen sizes */
              <div className="flex items-center">
                {isMobile && (
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" className="mr-2">
                        <Menu className="h-5 w-5" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <Link href="/">
                        <DropdownMenuItem>
                          <Home className="mr-2 h-4 w-4" />
                          <span>Home</span>
                        </DropdownMenuItem>
                      </Link>
                      <Link href="/pricing">
                        <DropdownMenuItem>
                          <CreditCard className="mr-2 h-4 w-4" />
                          <span>Pricing</span>
                        </DropdownMenuItem>
                      </Link>
                      <Link href="/contact">
                        <DropdownMenuItem>
                          <User className="mr-2 h-4 w-4" />
                          <span>Contact Us</span>
                        </DropdownMenuItem>
                      </Link>
                    </DropdownMenuContent>
                  </DropdownMenu>
                )}
                <div className="flex space-x-2">
                  <Link href="/sign-up">
                    <Button variant="outline" size={isMobile ? "sm" : "default"}>Sign Up</Button>
                  </Link>
                  <Link href="/sign-in">
                    <Button variant="default" size={isMobile ? "sm" : "default"}>Login</Button>
                  </Link>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
