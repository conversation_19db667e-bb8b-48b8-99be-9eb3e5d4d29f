import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { debugAuth, debugAuthToken, testAuthApi } from '@/utils/debug-auth';
import { useClerkAuth } from '@/hooks/use-clerk-auth';

/**
 * Authentication Debugger Component
 *
 * This component provides debugging tools for authentication.
 * It should only be used in development and when explicitly enabled.
 */
export function AuthDebugger() {
  const [logs, setLogs] = useState<string[]>([]);
  const [isVisible, setIsVisible] = useState(false);
  const [isEnabled, setIsEnabled] = useState(false);
  const { isAuthenticated, user, authToken } = useClerkAuth();

  // Check if auth debugging is enabled via environment variable
  useEffect(() => {
    const enableAuthDebug = import.meta.env.VITE_ENABLE_AUTH_DEBUG === 'true';
    setIsEnabled(enableAuthDebug);
  }, []);

  // Override console methods to capture logs
  const setupLogCapture = () => {
    const originalConsoleDebug = console.debug;
    const originalConsoleInfo = console.info;
    const originalConsoleWarn = console.warn;
    const originalConsoleError = console.error;

    console.debug = (...args) => {
      if (args[0] && typeof args[0] === 'string' && args[0].includes('[AUTH-DEBUG]')) {
        setLogs(prev => [...prev, args.join(' ')]);
      }
      originalConsoleDebug(...args);
    };

    console.info = (...args) => {
      if (args[0] && typeof args[0] === 'string' && args[0].includes('[AUTH-DEBUG]')) {
        setLogs(prev => [...prev, args.join(' ')]);
      }
      originalConsoleInfo(...args);
    };

    console.warn = (...args) => {
      if (args[0] && typeof args[0] === 'string' && args[0].includes('[AUTH-DEBUG]')) {
        setLogs(prev => [...prev, `⚠️ ${args.join(' ')}`]);
      }
      originalConsoleWarn(...args);
    };

    console.error = (...args) => {
      if (args[0] && typeof args[0] === 'string' && args[0].includes('[AUTH-DEBUG]')) {
        setLogs(prev => [...prev, `❌ ${args.join(' ')}`]);
      }
      originalConsoleError(...args);
    };

    return () => {
      console.debug = originalConsoleDebug;
      console.info = originalConsoleInfo;
      console.warn = originalConsoleWarn;
      console.error = originalConsoleError;
    };
  };

  // Run debug functions
  const handleDebugToken = () => {
    setLogs([]);
    const cleanup = setupLogCapture();
    debugAuthToken();
    cleanup();
  };

  const handleTestApi = async () => {
    setLogs([]);
    const cleanup = setupLogCapture();
    await testAuthApi();
    cleanup();
  };

  const handleDebugAll = async () => {
    setLogs([]);
    const cleanup = setupLogCapture();
    debugAuth();
    cleanup();
  };

  const handleClearLogs = () => {
    setLogs([]);
  };

  // Don't render anything if debugging is not enabled
  if (!isEnabled) {
    return null;
  }

  if (!isVisible) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <Button
          variant="outline"
          size="sm"
          onClick={() => setIsVisible(true)}
        >
          Debug Auth
        </Button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 w-96">
      <Card>
        <CardHeader>
          <CardTitle>Auth Debugger</CardTitle>
          <CardDescription>
            Debug authentication issues
            {isAuthenticated ? ' (Authenticated)' : ' (Not Authenticated)'}
            {authToken ? ' (Token Available)' : ' (No Token)'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex space-x-2">
              <Button size="sm" onClick={handleDebugToken}>Debug Token</Button>
              <Button size="sm" onClick={handleTestApi}>Test API</Button>
              <Button size="sm" onClick={handleDebugAll}>Debug All</Button>
            </div>

            {logs.length > 0 && (
              <div className="mt-4 bg-muted p-2 rounded-md max-h-60 overflow-y-auto text-xs font-mono">
                {logs.map((log, index) => (
                  <div key={index} className="whitespace-pre-wrap">{log}</div>
                ))}
              </div>
            )}
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button variant="outline" size="sm" onClick={handleClearLogs}>Clear Logs</Button>
          <Button variant="outline" size="sm" onClick={() => setIsVisible(false)}>Close</Button>
        </CardFooter>
      </Card>
    </div>
  );
}
