import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useClerkAuth } from '@/hooks/use-clerk-auth';
import { debugAuthToken, testAuthApi } from '@/utils/debug-auth';

export function AuthDebugPanel() {
  const { isAuthenticated, authToken, userId, refreshToken } = useClerkAuth();
  const [apiResponse, setApiResponse] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Function to test the API connection
  const testApi = async () => {
    setLoading(true);
    setError(null);
    try {
      // Call the debug endpoint
      const response = await fetch('/api/auth/debug', {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });
      
      if (!response.ok) {
        throw new Error(`API returned ${response.status}: ${await response.text()}`);
      }
      
      const data = await response.json();
      setApiResponse(data);
    } catch (err) {
      console.error('API test failed:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  // Function to refresh the auth token
  const handleRefreshToken = async () => {
    try {
      const newToken = await refreshToken(true);
      console.log('Token refreshed:', newToken ? 'success' : 'failed');
    } catch (err) {
      console.error('Token refresh failed:', err);
      setError(err instanceof Error ? err.message : 'Token refresh failed');
    }
  };

  return (
    <Card className="mb-6 border-red-300">
      <CardHeader className="bg-red-50">
        <CardTitle className="text-red-700">Authentication Debug Panel</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4 pt-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <h3 className="font-medium">Authentication Status</h3>
            <p>Authenticated: {isAuthenticated ? 'Yes' : 'No'}</p>
            <p>User ID: {userId || 'None'}</p>
            <p>Token: {authToken ? `${authToken.substring(0, 15)}...` : 'None'}</p>
          </div>
          <div className="space-y-2">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => debugAuthToken()}
              className="w-full"
            >
              Debug Token
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={handleRefreshToken}
              className="w-full"
            >
              Refresh Token
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={testApi}
              className="w-full"
              disabled={loading}
            >
              Test API Connection
            </Button>
          </div>
        </div>
        
        {error && (
          <div className="p-3 bg-red-50 text-red-700 rounded border border-red-200">
            <p className="font-medium">Error:</p>
            <p>{error}</p>
          </div>
        )}
        
        {apiResponse && (
          <div className="p-3 bg-gray-50 rounded border">
            <p className="font-medium mb-2">API Response:</p>
            <pre className="text-xs overflow-auto max-h-40 p-2 bg-gray-100 rounded">
              {JSON.stringify(apiResponse, null, 2)}
            </pre>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
