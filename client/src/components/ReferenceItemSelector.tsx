import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { 
  <PERSON><PERSON>, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogDescription,
  DialogFooter
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Search, Image, Tag as TagIcon, CheckSquare } from "lucide-react";
import { getReferenceCategories, getReferenceCategoryItems } from "@/lib/api";
import { Separator } from "@/components/ui/separator";

type ReferenceItem = {
  id: number;
  name: string;
  description?: string;
  imagePath: string;
  tags?: string[];
  categoryId?: number;
};

type ReferenceCategory = {
  id: number;
  name: string;
  description?: string;
  items?: ReferenceItem[];
};

interface ReferenceItemSelectorProps {
  isOpen: boolean;
  onClose: () => void;
  onItemsSelected: (items: ReferenceItem[]) => void;
  multiSelect?: boolean;
}

export function ReferenceItemSelector({
  isOpen,
  onClose,
  onItemsSelected,
  multiSelect = true
}: ReferenceItemSelectorProps) {
  const [selectedItems, setSelectedItems] = useState<ReferenceItem[]>([]);
  const [activeCategory, setActiveCategory] = useState<number | null>(null);
  const [searchQuery, setSearchQuery] = useState("");

  // Fetch all categories
  const { 
    data: categories = [], 
    isLoading: categoriesLoading 
  } = useQuery<ReferenceCategory[]>({
    queryKey: ["/api/reference-categories"],
    queryFn: getReferenceCategories,
    enabled: isOpen,
  });

  // Fetch items for the active category
  const {
    data: categoryWithItems,
    isLoading: itemsLoading
  } = useQuery<ReferenceCategory & { items: ReferenceItem[] }>({
    queryKey: ["/api/reference-categories", activeCategory],
    queryFn: () => getReferenceCategoryItems(activeCategory as number),
    enabled: isOpen && activeCategory !== null,
  });

  // Set the first category as active when categories are loaded
  useEffect(() => {
    if (categories.length > 0 && !activeCategory) {
      setActiveCategory(categories[0].id);
    }
  }, [categories]);

  // Filter items based on search query
  const filteredItems = categoryWithItems?.items?.filter(item => {
    if (!searchQuery) return true;
    
    const query = searchQuery.toLowerCase();
    return (
      item.name.toLowerCase().includes(query) ||
      (item.description && item.description.toLowerCase().includes(query)) ||
      (item.tags && item.tags.some(tag => tag.toLowerCase().includes(query)))
    );
  }) || [];

  // Handle item selection
  const toggleItemSelection = (item: ReferenceItem) => {
    if (multiSelect) {
      // For multi-select, toggle the item in the array
      const isSelected = selectedItems.some(i => i.id === item.id);
      if (isSelected) {
        setSelectedItems(selectedItems.filter(i => i.id !== item.id));
      } else {
        setSelectedItems([...selectedItems, item]);
      }
    } else {
      // For single-select, replace the entire selection
      setSelectedItems([item]);
    }
  };

  // Check if an item is selected
  const isItemSelected = (item: ReferenceItem) => {
    return selectedItems.some(i => i.id === item.id);
  };

  // Handle confirm selection
  const handleConfirm = () => {
    onItemsSelected(selectedItems);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={open => !open && onClose()}>
      <DialogContent className="max-w-4xl max-h-[80vh] flex flex-col">
        <DialogHeader>
          <DialogTitle className="text-xl">Select Reference Items</DialogTitle>
          <DialogDescription>
            Choose from your reference library to use as inspiration for this project.
          </DialogDescription>
        </DialogHeader>
        
        <div className="flex-1 overflow-hidden">
          {categoriesLoading ? (
            <div className="flex items-center justify-center h-64">
              <p className="text-muted-foreground">Loading categories...</p>
            </div>
          ) : categories.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-64">
              <p className="text-muted-foreground mb-2">Your reference library is empty.</p>
              <Button variant="outline" size="sm" asChild>
                <a href="/reference-library">Add References</a>
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 h-full max-h-[60vh]">
              {/* Category sidebar */}
              <div className="border rounded-md p-2 overflow-y-auto">
                <div className="font-medium mb-2">Categories</div>
                <ul className="space-y-1">
                  {categories.map(category => (
                    <li key={category.id}>
                      <Button
                        variant={activeCategory === category.id ? "default" : "ghost"}
                        className="w-full justify-start text-left"
                        onClick={() => setActiveCategory(category.id)}
                        size="sm"
                      >
                        {category.name}
                      </Button>
                    </li>
                  ))}
                </ul>
              </div>
              
              {/* Items grid */}
              <div className="col-span-3 border rounded-md overflow-hidden flex flex-col">
                <div className="p-2 border-b">
                  <div className="relative">
                    <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search by name, description, or tags..."
                      className="pl-8"
                      value={searchQuery}
                      onChange={e => setSearchQuery(e.target.value)}
                    />
                  </div>
                </div>
                
                <ScrollArea className="flex-1 p-3">
                  {itemsLoading ? (
                    <div className="flex items-center justify-center h-40">
                      <p className="text-muted-foreground">Loading items...</p>
                    </div>
                  ) : !categoryWithItems?.items || categoryWithItems.items.length === 0 ? (
                    <div className="flex items-center justify-center h-40">
                      <p className="text-muted-foreground">No items in this category</p>
                    </div>
                  ) : filteredItems.length === 0 ? (
                    <div className="flex items-center justify-center h-40">
                      <p className="text-muted-foreground">No items match your search</p>
                    </div>
                  ) : (
                    <div className="grid grid-cols-2 sm:grid-cols-3 gap-4">
                      {filteredItems.map(item => (
                        <div
                          key={item.id}
                          className={`border rounded-md overflow-hidden cursor-pointer transition-all ${
                            isItemSelected(item) 
                              ? "ring-2 ring-primary border-primary" 
                              : "hover:border-muted-foreground"
                          }`}
                          onClick={() => toggleItemSelection(item)}
                        >
                          <div className="relative h-36">
                            <img
                              src={item.imagePath}
                              alt={item.name}
                              className="w-full h-full object-cover"
                            />
                            {isItemSelected(item) && (
                              <div className="absolute top-2 right-2 bg-primary text-white rounded-full p-1">
                                <CheckSquare className="h-4 w-4" />
                              </div>
                            )}
                          </div>
                          <div className="p-2">
                            <h4 className="font-medium text-sm truncate">{item.name}</h4>
                            {item.tags && item.tags.length > 0 && (
                              <div className="flex flex-wrap gap-1 mt-1">
                                {item.tags.slice(0, 2).map(tag => (
                                  <Badge key={tag} variant="outline" className="text-xs">
                                    {tag}
                                  </Badge>
                                ))}
                                {item.tags.length > 2 && (
                                  <Badge variant="outline" className="text-xs">
                                    +{item.tags.length - 2}
                                  </Badge>
                                )}
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </ScrollArea>
              </div>
            </div>
          )}
        </div>
        
        <Separator className="my-2" />
        
        <div className="flex items-center justify-between">
          <div className="text-sm">
            {selectedItems.length} item{selectedItems.length !== 1 && 's'} selected
          </div>
          <DialogFooter className="sm:justify-end">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button
              onClick={handleConfirm}
              disabled={selectedItems.length === 0}
            >
              Use Selected
            </Button>
          </DialogFooter>
        </div>
      </DialogContent>
    </Dialog>
  );
}