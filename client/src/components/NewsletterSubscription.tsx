import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { Loader2 } from "lucide-react";

// Define the form schema with validation
const newsletterFormSchema = z.object({
  email: z.string().email({
    message: "Please enter a valid email address.",
  }),
});

// Define the form values type
type NewsletterFormValues = z.infer<typeof newsletterFormSchema>;

interface NewsletterSubscriptionProps {
  className?: string;
  variant?: "default" | "card" | "inline";
  title?: string;
  description?: string;
}

export function NewsletterSubscription({
  className = "",
  variant = "default",
  title = "Subscribe to our newsletter",
  description = "Stay updated with the latest news, features, and updates.",
}: NewsletterSubscriptionProps) {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubscribed, setIsSubscribed] = useState(false);

  // Initialize the form with react-hook-form and zod validation
  const form = useForm<NewsletterFormValues>({
    resolver: zodResolver(newsletterFormSchema),
    defaultValues: {
      email: "",
    },
  });

  // Handle form submission
  async function onSubmit(data: NewsletterFormValues) {
    setIsSubmitting(true);
    
    try {
      const response = await fetch("/api/newsletter/subscribe", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });
      
      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.message || "Failed to subscribe");
      }
      
      // Show success message
      toast({
        title: "Newsletter Subscription",
        description: result.message || "Thank you for subscribing to our newsletter!",
      });
      
      // Reset the form and update state
      form.reset();
      setIsSubscribed(true);
    } catch (error) {
      // Show error message
      toast({
        title: "Subscription Failed",
        description: error instanceof Error ? error.message : "Please try again later.",
        variant: "destructive",
      });
      
      // Log the error for debugging
      console.error("Newsletter subscription error:", error);
    } finally {
      setIsSubmitting(false);
    }
  }

  // Render different variants
  if (variant === "card") {
    return (
      <div className={`bg-muted p-6 rounded-lg ${className}`}>
        <div className="mb-4">
          <h3 className="text-lg font-semibold mb-2">{title}</h3>
          <p className="text-muted-foreground text-sm">{description}</p>
        </div>
        
        {isSubscribed ? (
          <div className="bg-primary/10 text-primary p-4 rounded-md">
            <p className="font-medium">Thank you for subscribing!</p>
            <p className="text-sm mt-1">You'll receive our next newsletter in your inbox.</p>
          </div>
        ) : (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input placeholder="Your email address" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <Button type="submit" className="w-full" disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Subscribing...
                  </>
                ) : (
                  "Subscribe"
                )}
              </Button>
            </form>
          </Form>
        )}
      </div>
    );
  }

  if (variant === "inline") {
    return (
      <div className={`${className}`}>
        {isSubscribed ? (
          <div className="text-primary">
            <p className="font-medium">Thank you for subscribing!</p>
          </div>
        ) : (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="flex flex-col sm:flex-row gap-2">
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem className="flex-1">
                    <FormControl>
                      <Input placeholder="Your email address" className="bg-white text-gray-900" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Subscribing...
                  </>
                ) : (
                  "Subscribe"
                )}
              </Button>
            </form>
          </Form>
        )}
      </div>
    );
  }

  // Default variant
  return (
    <div className={`${className}`}>
      <div className="mb-4">
        <h3 className="text-xl font-semibold mb-2">{title}</h3>
        <p className="text-muted-foreground">{description}</p>
      </div>
      
      {isSubscribed ? (
        <div className="bg-primary/10 text-primary p-4 rounded-md">
          <p className="font-medium">Thank you for subscribing!</p>
          <p className="text-sm mt-1">You'll receive our next newsletter in your inbox.</p>
        </div>
      ) : (
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input placeholder="Your email address" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Subscribing...
                </>
              ) : (
                "Subscribe"
              )}
            </Button>
          </form>
        </Form>
      )}
    </div>
  );
}
