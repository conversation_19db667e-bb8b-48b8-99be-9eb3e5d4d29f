import { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Textarea } from "@/components/ui/textarea";
import { ModificationType } from "@/lib/api";
import { CheckCircle2, Image as ImageIcon } from "lucide-react";
import { Switch } from "@/components/ui/switch";

// Define the available floor materials
const floorMaterials = [
  { id: "hardwood", name: "Hardwood", description: "Classic, warm, and timeless" },
  { id: "laminate", name: "Laminate", description: "Affordable and durable" },
  { id: "tile", name: "Tile", description: "Great for kitchens and bathrooms" },
  { id: "vinyl", name: "Vinyl", description: "Water-resistant and low maintenance" },
  { id: "carpet", name: "Carpet", description: "Soft and comfortable" },
  { id: "concrete", name: "Concrete", description: "Modern and industrial" },
];

// Define the available floor colors
const floorColors = [
  { id: "natural", name: "Natural" },
  { id: "light", name: "Light" },
  { id: "medium", name: "Medium" },
  { id: "dark", name: "Dark" },
  { id: "white", name: "White" },
  { id: "gray", name: "Gray" },
  { id: "black", name: "Black" },
];

// Define the image type for reference images
export interface ReferenceImageType {
  id: number;
  path: string;
  originalFilename?: string;
  type?: string;
  projectId?: number;
}

interface ModificationOptionsProps {
  onSelectOption: (type: ModificationType, options: any) => void;
  onCustomDescription: (description: string) => void;
  referenceImages: ReferenceImageType[];
  onOpenReferenceLibrary: () => void;
}

export function ModificationOptions({
  onSelectOption,
  onCustomDescription,
  referenceImages,
  onOpenReferenceLibrary
}: ModificationOptionsProps) {
  const [activeTab, setActiveTab] = useState<string>("presets");
  const [selectedModification, setSelectedModification] = useState<ModificationType | null>(null);
  const [floorMaterial, setFloorMaterial] = useState<string>("");
  const [floorColor, setFloorColor] = useState<string>("");
  const [customDescription, setCustomDescription] = useState<string>("");
  const [customFloorDescription, setCustomFloorDescription] = useState<string>("");
  const [useReferenceImage, setUseReferenceImage] = useState<boolean>(false);
  const [selectedReferenceImageId, setSelectedReferenceImageId] = useState<number | null>(null);

  // Reset reference image selection when toggling the switch
  useEffect(() => {
    if (!useReferenceImage) {
      setSelectedReferenceImageId(null);
    }
  }, [useReferenceImage]);

  // Handle selecting a modification type
  const handleSelectModification = (type: ModificationType) => {
    setSelectedModification(type);
  };

  // Handle applying the selected modification
  const handleApplyModification = () => {
    if (selectedModification === "replace_floor") {
      // For floor replacement, gather the options
      const options = {
        material: useReferenceImage ? null : floorMaterial,
        color: useReferenceImage ? null : floorColor,
        customDescription: customFloorDescription,
        useReferenceImage: useReferenceImage,
        referenceImageId: selectedReferenceImageId
      };
      onSelectOption("replace_floor", options);
    } else if (selectedModification) {
      // For other modification types
      onSelectOption(selectedModification, {});
    }
  };

  // Handle selecting a reference image
  const handleSelectReferenceImage = (imageId: number) => {
    setSelectedReferenceImageId(imageId === selectedReferenceImageId ? null : imageId);
  };

  // Handle custom description submission
  const handleCustomDescriptionSubmit = () => {
    onCustomDescription(customDescription);
  };

  // Render the floor replacement options
  const renderFloorOptions = () => {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between mb-4 border-b pb-4">
          <div className="flex items-center space-x-2">
            <Label htmlFor="use-reference-image" className="font-medium text-lg">
              Use Reference Image
            </Label>
            <p className="text-sm text-muted-foreground">
              Replace floor with a reference image
            </p>
          </div>
          <Switch
            id="use-reference-image"
            checked={useReferenceImage}
            onCheckedChange={setUseReferenceImage}
          />
        </div>

        {useReferenceImage ? (
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-medium">Select Reference Image</h3>
              {referenceImages.length === 0 && (
                <Button variant="outline" size="sm" onClick={onOpenReferenceLibrary}>
                  <ImageIcon className="h-4 w-4 mr-2" />
                  Add Reference Images
                </Button>
              )}
            </div>

            {referenceImages.length === 0 ? (
              <div className="border border-dashed rounded-lg p-8 text-center">
                <ImageIcon className="h-10 w-10 mx-auto mb-2 text-muted-foreground" />
                <p className="text-muted-foreground">No reference images available</p>
                <p className="text-sm text-muted-foreground mt-1">
                  Add reference images to use as floor inspiration
                </p>
                <Button variant="outline" size="sm" className="mt-4" onClick={onOpenReferenceLibrary}>
                  Open Reference Library
                </Button>
              </div>
            ) : (
              <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
                {referenceImages.map((image) => (
                  <div
                    key={image.id}
                    className={`relative border rounded-md overflow-hidden cursor-pointer transition-all ${
                      selectedReferenceImageId === image.id
                        ? "ring-2 ring-primary border-primary"
                        : "hover:border-muted-foreground"
                    }`}
                    onClick={() => handleSelectReferenceImage(image.id)}
                  >
                    <div className="aspect-square">
                      <img
                        src={image.path.startsWith('http') ? image.path : `/uploads/${image.path.split('/').pop()}`}
                        alt={image.originalFilename || "Reference image"}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    {selectedReferenceImageId === image.id && (
                      <div className="absolute top-2 right-2 bg-primary text-white rounded-full p-1">
                        <CheckCircle2 className="h-4 w-4" />
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}

            <div>
              <h3 className="text-lg font-medium mb-2">Additional Details (Optional)</h3>
              <Textarea
                placeholder="Add any specific details about how to use the reference image (e.g., 'Use the wood pattern but make it slightly darker')"
                value={customFloorDescription}
                onChange={(e) => setCustomFloorDescription(e.target.value)}
                className="min-h-20"
              />
            </div>

            <Button
              onClick={handleApplyModification}
              disabled={!selectedReferenceImageId}
              className="w-full"
            >
              Apply Floor Replacement with Reference Image
            </Button>
          </div>
        ) : (
          <>
            <div>
              <h3 className="text-lg font-medium mb-4">Select Floor Material</h3>
              <RadioGroup value={floorMaterial} onValueChange={setFloorMaterial} className="grid grid-cols-2 gap-4">
                {floorMaterials.map((material) => (
                  <div key={material.id} className="flex items-start space-x-2">
                    <RadioGroupItem value={material.id} id={`material-${material.id}`} />
                    <div className="grid gap-1.5">
                      <Label htmlFor={`material-${material.id}`} className="font-medium">
                        {material.name}
                      </Label>
                      <p className="text-sm text-muted-foreground">{material.description}</p>
                    </div>
                  </div>
                ))}
              </RadioGroup>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-4">Select Floor Color</h3>
              <RadioGroup value={floorColor} onValueChange={setFloorColor} className="grid grid-cols-3 gap-4">
                {floorColors.map((color) => (
                  <div key={color.id} className="flex items-center space-x-2">
                    <RadioGroupItem value={color.id} id={`color-${color.id}`} />
                    <Label htmlFor={`color-${color.id}`}>{color.name}</Label>
                  </div>
                ))}
              </RadioGroup>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-2">Additional Details (Optional)</h3>
              <Textarea
                placeholder="Add any specific details about the floor you want (e.g., wide planks, herringbone pattern, etc.)"
                value={customFloorDescription}
                onChange={(e) => setCustomFloorDescription(e.target.value)}
                className="min-h-20"
              />
            </div>

            <Button
              onClick={handleApplyModification}
              disabled={!floorMaterial || !floorColor}
              className="w-full"
            >
              Apply Floor Replacement
            </Button>
          </>
        )}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      <Tabs defaultValue="presets" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-2">
          <TabsTrigger value="presets">Common Modifications</TabsTrigger>
          <TabsTrigger value="custom">Custom Description</TabsTrigger>
        </TabsList>

        <TabsContent value="presets" className="space-y-6 pt-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card
              className={`cursor-pointer hover:border-primary-300 transition-colors ${selectedModification === "replace_floor" ? "border-primary-500 ring-2 ring-primary-200" : ""}`}
              onClick={() => handleSelectModification("replace_floor")}
            >
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center">
                    <span className="material-icons text-primary-600">floor</span>
                  </div>
                  <div>
                    <h3 className="font-medium text-lg">Replace Floor</h3>
                    <p className="text-sm text-gray-600">Change the flooring material and color</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Placeholder for future modification types */}
            <Card className="opacity-50 cursor-not-allowed">
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center">
                    <span className="material-icons text-gray-400">format_paint</span>
                  </div>
                  <div>
                    <h3 className="font-medium text-lg">Change Wall Color</h3>
                    <p className="text-sm text-gray-600">Coming soon</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {selectedModification === "replace_floor" && (
            <div className="mt-6 border rounded-lg p-6 bg-gray-50">
              {renderFloorOptions()}
            </div>
          )}
        </TabsContent>

        <TabsContent value="custom" className="space-y-4 pt-4">
          <div>
            <h3 className="text-lg font-medium mb-2">Describe Your Changes</h3>
            <p className="text-sm text-gray-600 mb-4">
              Provide a detailed description of all the changes you want to make to your image.
            </p>
            <Textarea
              placeholder="Describe in detail the modifications you want (e.g., 'Change the kitchen cabinets to white, add a wooden countertop, and replace the floor with dark hardwood')"
              className="min-h-32"
              value={customDescription}
              onChange={(e) => setCustomDescription(e.target.value)}
            />
          </div>
          <Button
            onClick={handleCustomDescriptionSubmit}
            disabled={customDescription.length < 10}
            className="w-full"
          >
            Apply Custom Description
          </Button>
        </TabsContent>
      </Tabs>
    </div>
  );
}
