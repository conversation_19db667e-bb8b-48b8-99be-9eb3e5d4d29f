import { useState, useEffect } from "react";
import { useClerkAuth } from "@/hooks/use-clerk-auth";
import { useToast } from "@/hooks/use-toast";
import { PLAN_LIMITS, formatPrice } from "@/lib/subscription-plans";
import { Loader2, Check<PERSON><PERSON>cle, ArrowRight, AlertTriangle } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";

type BillingCycle = 'monthly' | 'annual';
type PlanType = keyof typeof PLAN_LIMITS;

interface PlanChangeDialogProps {
  isOpen: boolean;
  onClose: () => void;
  currentPlan: string;
  currentBillingCycle: string;
  subscriptionStatus: string;
  isCanceled: boolean;
  onPlanChange: () => void;
}

export function PlanChangeDialog({
  isOpen,
  onClose,
  currentPlan,
  currentBillingCycle,
  subscriptionStatus,
  isCanceled,
  onPlanChange,
}: PlanChangeDialogProps) {
  const { getToken } = useClerkAuth();
  const { toast } = useToast();
  const [isChanging, setIsChanging] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<PlanType | null>(null);
  const [selectedBillingCycle, setSelectedBillingCycle] = useState<BillingCycle>(currentBillingCycle as BillingCycle);

  // Reset selected plan when dialog opens
  useEffect(() => {
    if (isOpen) {
      setSelectedPlan(null);
      setSelectedBillingCycle(currentBillingCycle as BillingCycle);
    }
  }, [isOpen, currentBillingCycle]);

  const handlePlanChange = async (plan: PlanType) => {
    try {
      setIsChanging(true);
      const token = await getToken();
      
      const response = await fetch('/api/subscription/change-plan', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          newPlan: plan,
          billingCycle: selectedBillingCycle,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to change plan');
      }

      toast({
        title: "Success",
        description: "Your subscription has been updated successfully.",
      });

      onPlanChange();
      onClose();
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to change plan",
        variant: "destructive",
      });
    } finally {
      setIsChanging(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Change Subscription Plan</DialogTitle>
          <DialogDescription>
            Choose the plan that best fits your needs. Changes will be applied immediately.
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          {Object.entries(PLAN_LIMITS).map(([plan, limits]) => (
            <div
              key={plan}
              className={`p-4 rounded-lg border ${
                selectedPlan === plan ? 'border-primary' : 'border-border'
              }`}
            >
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h3 className="font-semibold text-lg">{plan.charAt(0).toUpperCase() + plan.slice(1)}</h3>
                  <p className="text-sm text-muted-foreground">{limits.description}</p>
                </div>
                <div className="flex items-center gap-2">
                  <span className="font-semibold">
                    {formatPrice(PLAN_LIMITS[plan as PlanType][selectedBillingCycle].price)}
                  </span>
                  <span className="text-muted-foreground">
                    /{selectedBillingCycle}
                  </span>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex gap-2">
                  <Button
                    variant={selectedBillingCycle === 'monthly' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedBillingCycle('monthly')}
                    className="flex-1"
                  >
                    Monthly
                  </Button>
                  <Button
                    variant={selectedBillingCycle === 'annual' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedBillingCycle('annual')}
                    className="flex-1"
                  >
                    Annual
                  </Button>
                </div>

                <Button
                  variant="outline"
                  className="w-full"
                  onClick={() => handlePlanChange(plan as PlanType)}
                  disabled={isChanging || plan === currentPlan}
                >
                  {isChanging ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Processing...
                    </>
                  ) : plan === currentPlan ? (
                    <>
                      <CheckCircle className="mr-2 h-4 w-4" />
                      Current Plan
                    </>
                  ) : (
                    <>
                      <ArrowRight className="mr-2 h-4 w-4" />
                      Select Plan
                    </>
                  )}
                </Button>
              </div>

              {plan === currentPlan && isCanceled && (
                <Alert className="mt-2">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    Your subscription is scheduled to cancel at the end of the billing period. 
                    Selecting this plan again will reactivate your subscription.
                  </AlertDescription>
                </Alert>
              )}
            </div>
          ))}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 