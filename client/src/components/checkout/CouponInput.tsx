import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { useClerkAuth } from "@/hooks/use-clerk-auth";
import { Loader2, CheckCircle, XCircle } from "lucide-react";

interface CouponInputProps {
  onApplyCoupon: (couponCode: string, couponDetails: any) => void;
  onRemoveCoupon: () => void;
  disabled?: boolean;
}

export function CouponInput({ onApplyCoupon, onRemoveCoupon, disabled = false }: CouponInputProps) {
  const [couponCode, setCouponCode] = useState("");
  const [isValidating, setIsValidating] = useState(false);
  const [appliedCoupon, setAppliedCoupon] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();
  const { getToken } = useClerkAuth();

  const validateCoupon = async () => {
    if (!couponCode.trim()) {
      setError("Please enter a coupon code");
      return;
    }

    setIsValidating(true);
    setError(null);

    try {
      const token = await getToken();
      
      const response = await fetch("/api/validate-coupon", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${token}`
        },
        body: JSON.stringify({ code: couponCode.trim() })
      });

      const data = await response.json();

      if (!response.ok || !data.success) {
        setError(data.error || "Invalid coupon code");
        setAppliedCoupon(null);
        onRemoveCoupon();
        return;
      }

      // Coupon is valid
      setAppliedCoupon(data.coupon);
      onApplyCoupon(couponCode.trim(), data.coupon);
      
      toast({
        title: "Coupon applied",
        description: getDiscountDescription(data.coupon),
      });
    } catch (error) {
      console.error("Error validating coupon:", error);
      setError("An error occurred while validating the coupon");
      setAppliedCoupon(null);
      onRemoveCoupon();
    } finally {
      setIsValidating(false);
    }
  };

  const removeCoupon = () => {
    setCouponCode("");
    setAppliedCoupon(null);
    setError(null);
    onRemoveCoupon();
  };

  const getDiscountDescription = (coupon: any) => {
    if (!coupon) return "";

    switch (coupon.type) {
      case "percentage":
        return `${coupon.amount}% discount`;
      case "fixed_amount":
        return `$${coupon.amount.toFixed(2)} discount`;
      case "free_months":
        return `${coupon.amount} ${coupon.amount === 1 ? 'month' : 'months'} free`;
      default:
        return "Discount applied";
    }
  };

  return (
    <div className="space-y-2">
      <div className="text-sm font-medium">Have a coupon code?</div>
      
      {!appliedCoupon ? (
        <div className="flex space-x-2">
          <Input
            placeholder="Enter coupon code"
            value={couponCode}
            onChange={(e) => setCouponCode(e.target.value)}
            disabled={disabled || isValidating}
            className="flex-1"
          />
          <Button 
            onClick={validateCoupon} 
            disabled={disabled || isValidating || !couponCode.trim()}
            variant="outline"
          >
            {isValidating ? <Loader2 className="h-4 w-4 animate-spin" /> : "Apply"}
          </Button>
        </div>
      ) : (
        <div className="flex items-center justify-between p-2 border rounded-md bg-muted/50">
          <div className="flex items-center space-x-2">
            <CheckCircle className="h-5 w-5 text-green-500" />
            <div>
              <div className="font-medium">{couponCode}</div>
              <div className="text-xs text-muted-foreground">{getDiscountDescription(appliedCoupon)}</div>
            </div>
          </div>
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={removeCoupon}
            disabled={disabled}
          >
            <XCircle className="h-4 w-4" />
          </Button>
        </div>
      )}
      
      {error && (
        <div className="flex items-center space-x-1 text-sm text-destructive">
          <XCircle className="h-4 w-4" />
          <span>{error}</span>
        </div>
      )}
    </div>
  );
}
