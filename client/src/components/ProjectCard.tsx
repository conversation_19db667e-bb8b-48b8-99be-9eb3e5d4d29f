import { <PERSON> } from "wouter";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { ProjectImageComparison } from "@/components/ProjectImageComparison";
import type { ProjectWithImages } from "@shared/schema";
import { formatDistanceToNow } from "date-fns";
import { normalizeImagePath } from "@/utils/image-utils";

interface ProjectCardProps {
  project: ProjectWithImages;
}

export default function ProjectCard({ project }: ProjectCardProps) {
  const isDraft = project.status === "draft";
  const isProcessing = project.status === "processing";
  const isCompleted = project.status === "completed";



  // Get the first before and after image if available
  const beforeImage = project.beforeImages.length > 0 ? project.beforeImages[0] : null;
  const afterImage = project.afterImages.length > 0 ? project.afterImages[0] : null;

  // Format the creation date with error handling
  let timeAgo = '';
  try {
    const createdAt = new Date(project.created_at);
    if (!isNaN(createdAt.getTime())) {
      timeAgo = formatDistanceToNow(createdAt, { addSuffix: true });
    } else {
      console.warn('Invalid date format:', project.created_at);
      timeAgo = 'recently';
    }
  } catch (error) {
    console.warn('Error formatting date:', error);
    timeAgo = 'recently';
  }

  return (
    <Card className="overflow-hidden">
      <div className="relative h-48">
        {isDraft || isProcessing ? (
          <div className="relative h-48 bg-gray-100 flex items-center justify-center select-none">
            <div className="absolute inset-0 select-none">
              {beforeImage && (
                <img
                  src={beforeImage.path.startsWith('http') ? beforeImage.path : `/uploads/${beforeImage.path.split('/').pop()}`}
                  alt={project.title}
                  className="w-full h-full object-cover opacity-50 select-none"
                  draggable="false"
                  style={{ userSelect: 'none', WebkitUserSelect: 'none', pointerEvents: 'none' }}
                />
              )}
            </div>
            <div className="absolute inset-0 flex flex-col items-center justify-center bg-gray-900 bg-opacity-40 select-none">
              <div className="relative select-none">
                <svg className="animate-spin h-10 w-10 text-white select-none" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25 select-none" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75 select-none" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              </div>
              <p className="mt-3 text-white font-medium select-none">Processing with AI...</p>
              <p className="text-white text-sm opacity-80 select-none">This may take a few minutes</p>
            </div>
          </div>
        ) : isCompleted && beforeImage && afterImage ? (
          // Check if the after image is a text file (error case)
          afterImage.path.endsWith('.txt') ? (
            <div className="relative h-48 bg-gray-100 flex items-center justify-center select-none">
              <div className="absolute inset-0 select-none">
                {beforeImage && (
                  <img
                    src={normalizeImagePath(beforeImage)}
                    alt={`${project.title} before`}
                    className="w-full h-full object-cover opacity-50 select-none"
                    draggable="false"
                    style={{ userSelect: 'none', WebkitUserSelect: 'none', pointerEvents: 'none' }}
                  />
                )}
              </div>
              <div className="absolute inset-0 flex flex-col items-center justify-center bg-gray-900 bg-opacity-40 select-none">
                <span className="material-icons text-white text-4xl mb-2 select-none">error_outline</span>
                <p className="text-white font-medium select-none">Generation failed</p>
              </div>
            </div>
          ) : (
            <ProjectImageComparison
              beforeImage={beforeImage}
              afterImage={afterImage}
              beforeAlt={`${project.title} before`}
              afterAlt={`${project.title} after`}
            />
          )
        ) : (
          <div className="h-full bg-gray-200 flex items-center justify-center select-none">
            <span className="material-icons text-gray-400 text-4xl select-none">image</span>
          </div>
        )}

        {isCompleted && (
          <div className="absolute top-2 right-2 bg-white rounded-full p-1 shadow-md">
            <span className="material-icons text-gray-700 text-sm">fullscreen</span>
          </div>
        )}
      </div>

      <CardContent className="p-4">
        <div className="flex justify-between items-start">
          <div>
            <h3 className="font-medium text-gray-900">{project.title}</h3>
            <p className="text-gray-500 text-sm">Created {timeAgo}</p>
          </div>
          <Badge variant="secondary" className={`
            ${isDraft ? 'bg-blue-100 text-blue-800' : ''}
            ${isProcessing ? 'bg-yellow-100 text-yellow-800' : ''}
            ${isCompleted ? 'bg-green-100 text-green-800' : ''}
            ${project.status === 'failed' ? 'bg-red-100 text-red-800' : ''}
          `}>
            {project.status === 'draft' ? 'Draft' :
             project.status === 'processing' ? 'Processing' :
             project.status === 'completed' ? 'Completed' :
             'Failed'}
          </Badge>
        </div>

        <p className="text-gray-600 text-sm mt-2">{project.description}</p>

        {isDraft ? (
          <div className="mt-4 flex items-center">
            <Link href={`/projects/${project.id}`}>
              <Button variant="outline" size="sm" className="text-xs">
                <span className="material-icons text-xs mr-1">edit</span>
                Continue Editing
              </Button>
            </Link>
          </div>
        ) : isProcessing ? (
          <div className="mt-4 flex items-center">
            <Progress value={65} className="w-full h-2" />
            <span className="ml-2 text-xs font-medium text-gray-500">65%</span>
          </div>
        ) : (
          <div className="mt-4 flex items-center space-x-2">
            <Button variant="outline" size="sm" className="text-xs">
              <span className="material-icons text-xs mr-1">share</span>
              Share
            </Button>

            <Link href={`/projects/${project.id}`}>
              <Button variant="outline" size="sm" className="text-xs">
                <span className="material-icons text-xs mr-1">edit</span>
                Edit
              </Button>
            </Link>

            {isCompleted && afterImage && (
              <Button variant="default" size="sm" className="text-xs">
                <span className="material-icons text-xs mr-1">download</span>
                Download
              </Button>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
