import { Card, CardContent } from "@/components/ui/card";
import { motion } from "framer-motion";

interface TestimonialCardProps {
  name: string;
  role: string;
  testimonial: string;
  rating: number;
  imageUrl: string | null;
  company?: string;
  location?: string;
  companyLogo?: string;
}

export default function TestimonialCard({
  name,
  role,
  testimonial,
  rating,
  imageUrl,
  company,
  location,
  companyLogo
}: TestimonialCardProps) {
  // Create an array of stars based on the rating
  const stars = Array.from({ length: 5 }, (_, i) => {
    if (i < Math.floor(rating)) return "star"; // Full star
    if (i < rating) return "star_half"; // Half star
    return "star_border"; // Empty star
  });

  return (
    <motion.div
      whileHover={{ y: -5 }}
      transition={{ duration: 0.3 }}
    >
      <Card className="shadow-md hover:shadow-lg transition-shadow duration-300 h-full">
        <CardContent className="p-6 relative">
          {/* Quote icon */}
          <div className="absolute top-6 right-6 text-primary-100">
            <span className="material-icons text-4xl">format_quote</span>
          </div>

          <div className="flex items-center mb-5">
            <div className="relative">
              {imageUrl ? (
                <img
                  className="h-16 w-16 rounded-full object-cover border-2 border-primary-100"
                  src={imageUrl}
                  alt={name}
                />
              ) : (
                <div className="h-16 w-16 rounded-full bg-primary-100 border-2 border-primary-200 flex items-center justify-center">
                  <span className="text-primary-600 font-semibold text-lg">
                    {name.split(' ').map(n => n[0]).join('').toUpperCase()}
                  </span>
                </div>
              )}
              {companyLogo && (
                <div className="absolute -bottom-2 -right-2 bg-white rounded-full p-1 shadow-sm">
                  <img
                    className="h-6 w-6 rounded-full"
                    src={companyLogo}
                    alt={company || role}
                  />
                </div>
              )}
            </div>
            <div className="ml-4">
              <h3 className="text-lg font-semibold text-gray-900">{name}</h3>
              <p className="text-gray-600 text-sm">{role}{company ? ` at ${company}` : ''}</p>
              {location && (
                <p className="text-gray-500 text-xs flex items-center mt-1">
                  <span className="material-icons text-xs mr-1">location_on</span>
                  {location}
                </p>
              )}
            </div>
          </div>

          <blockquote className="relative text-gray-700 italic mb-5 pl-4 border-l-2 border-primary-200">
            "{testimonial}"
          </blockquote>

          <div className="mt-4 flex items-center">
            <div className="flex text-yellow-400">
              {stars.map((star, index) => (
                <span key={index} className="material-icons">{star}</span>
              ))}
            </div>
            <span className="ml-2 text-gray-600 text-sm font-medium">{rating.toFixed(1)}</span>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
