import { useEffect } from 'react';
import { useLocation } from 'wouter';

/**
 * ScrollToTop component that scrolls the window to the top when the location changes
 */
export function ScrollToTop() {
  const [location] = useLocation();

  useEffect(() => {
    window.scrollTo(0, 0);
  }, [location]);

  return null;
}

/**
 * Custom Link component that scrolls to top on navigation
 * Works even when navigating to the current page
 */
export function LinkWithScroll({ href, children, ...props }: React.ComponentProps<'a'> & { href: string }) {
  const [location, navigate] = useLocation();

  const handleClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
    e.preventDefault();

    // Always scroll to top
    window.scrollTo(0, 0);

    // If we're already on the same page, don't navigate (which would cause a re-render)
    // Just let the scroll to top happen
    if (location !== href) {
      navigate(href);
    }
  };

  return (
    <a href={href} onClick={handleClick} {...props}>
      {children}
    </a>
  );
}
