import { useState, useEffect } from "react";
import { useMutation } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/use-clerk-auth";
import { RenovationPreset } from "@shared/schema";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { X } from "lucide-react";
import { FormDescription } from "@/components/ui/form";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";

// Form schema
const presetFormSchema = z.object({
  name: z.string().min(3, "Name must be at least 3 characters"),
  roomType: z.string().min(1, "Room type is required"),
  promptTemplate: z.string().min(10, "Prompt template must be at least 10 characters"),
  tags: z.array(z.string()).min(1, "At least one tag is required"),
  description: z.string().optional().nullable(),
  isDefault: z.boolean().optional().nullable(),
  createdBy: z.number().optional().nullable(),
  isPublic: z.boolean().optional().nullable(),
  imageUrl: z.string().optional().nullable(),
});

type PresetFormValues = z.infer<typeof presetFormSchema>;

interface RenovationPresetDialogProps {
  isOpen: boolean;
  onClose: () => void;
  preset: RenovationPreset | null;
}

// List of common room types
const roomTypes = [
  "kitchen",
  "bathroom",
  "living_room",
  "bedroom",
  "dining_room",
  "office",
  "outdoor",
  "garage",
  "basement",
  "hallway",
  "other",
];

export function RenovationPresetDialog({
  isOpen,
  onClose,
  preset,
}: RenovationPresetDialogProps) {
  const { toast } = useToast();
  const { user } = useAuth();
  const [tagInput, setTagInput] = useState("");

  const isEditing = !!preset;
  const defaultValues: PresetFormValues = {
    name: preset?.name || "",
    roomType: preset?.roomType || "",
    promptTemplate: preset?.promptTemplate || "",
    tags: preset?.tags || [],
    description: preset?.description || "",
    isDefault: preset?.isDefault || false,
    createdBy: preset?.createdBy || (user ? user.id : null),
    isPublic: preset?.isPublic || false,
    imageUrl: preset?.imageUrl || "",
  };

  const form = useForm<PresetFormValues>({
    resolver: zodResolver(presetFormSchema),
    defaultValues,
  });

  // Reset form when preset changes
  useEffect(() => {
    if (isOpen) {
      form.reset(defaultValues);
    }
  }, [isOpen, preset]);

  // Create or update preset
  const mutation = useMutation({
    mutationFn: async (data: PresetFormValues) => {
      const endpoint = isEditing
        ? `/api/renovation-presets/${preset.id}`
        : "/api/renovation-presets";
      const method = isEditing ? "PATCH" : "POST";
      
      // Add current user ID as creator if creating new preset
      if (!isEditing && user) {
        data.createdBy = user.id;
      }

      const response = await apiRequest(method, endpoint, data);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: "Failed to save preset" }));
        throw new Error(errorData.error || "Failed to save preset");
      }
      
      return await response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/renovation-presets"] });
      toast({
        title: isEditing ? "Preset updated" : "Preset created",
        description: isEditing 
          ? "Your preset has been updated successfully." 
          : "Your new preset has been created successfully.",
      });
      onClose();
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Something went wrong",
        variant: "destructive",
      });
    },
  });

  // Handle form submission
  const onSubmit = (data: PresetFormValues) => {
    mutation.mutate(data);
  };

  // Handle adding a tag
  const handleAddTag = () => {
    if (tagInput.trim() === "") return;
    
    const currentTags = form.getValues("tags") || [];
    
    if (!currentTags.includes(tagInput.trim().toLowerCase())) {
      form.setValue("tags", [...currentTags, tagInput.trim().toLowerCase()]);
    }
    
    setTagInput("");
  };

  // Handle tag input keydown
  const handleTagInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      e.preventDefault();
      handleAddTag();
    }
  };

  // Handle removing a tag
  const handleRemoveTag = (tagToRemove: string) => {
    const currentTags = form.getValues("tags") || [];
    form.setValue(
      "tags",
      currentTags.filter((tag) => tag !== tagToRemove)
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? "Edit Renovation Preset" : "Create Renovation Preset"}
          </DialogTitle>
          <DialogDescription>
            {isEditing
              ? "Update the details of your renovation preset."
              : "Create a new preset to apply to future renovation projects."}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Modern Kitchen Design" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="roomType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Room Type</FormLabel>
                  <Select 
                    onValueChange={field.onChange} 
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a room type" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {roomTypes.map((type) => (
                        <SelectItem key={type} value={type}>
                          {type.replace("_", " ").replace(/\b\w/g, (c) => c.toUpperCase())}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="A modern kitchen design with clean lines and minimalist features"
                      {...field}
                      value={field.value || ""}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="promptTemplate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>AI Prompt Template</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Transform this [room type] with [style] elements, featuring [material] countertops and [color] cabinetry."
                      className="min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="tags"
              render={() => (
                <FormItem>
                  <FormLabel>Tags</FormLabel>
                  <div className="flex flex-wrap gap-2 mb-2">
                    {form.getValues("tags")?.map((tag) => (
                      <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                        {tag}
                        <X
                          className="h-3 w-3 cursor-pointer"
                          onClick={() => handleRemoveTag(tag)}
                        />
                      </Badge>
                    ))}
                  </div>
                  <div className="flex gap-2">
                    <Input
                      placeholder="Add a tag (e.g., modern, minimalist)"
                      value={tagInput}
                      onChange={(e) => setTagInput(e.target.value)}
                      onKeyDown={handleTagInputKeyDown}
                      className="flex-1"
                    />
                    <Button type="button" variant="outline" onClick={handleAddTag}>
                      Add
                    </Button>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="imageUrl"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Image URL (optional)</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="https://example.com/image.jpg"
                      {...field}
                      value={field.value || ""}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="isPublic"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">Make Public</FormLabel>
                    <FormDescription>
                      Allow other users to view and use this preset
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value || false}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <div className="flex justify-end gap-2 pt-4">
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit" disabled={mutation.isPending}>
                {mutation.isPending ? "Saving..." : isEditing ? "Update Preset" : "Create Preset"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}