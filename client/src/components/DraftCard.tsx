import { <PERSON> } from "wouter";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { formatDistanceToNow } from "date-fns";
import { SavedDraft, deleteDraft } from "@/lib/api";
import { useToast } from "@/hooks/use-toast";
import { useMutation, useQueryClient } from "@tanstack/react-query";

interface DraftCardProps {
  draft: SavedDraft;
  onDelete?: (draftId: number) => void;
}

export default function DraftCard({ draft, onDelete }: DraftCardProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();



  // Delete draft mutation (with toast notification for user-initiated deletions)
  const deleteDraftMutation = useMutation({
    mutationFn: deleteDraft,
    onSuccess: () => {
      toast({
        title: "Draft deleted",
        description: "Your draft has been deleted",
      });
      if (onDelete) {
        onDelete(draft.id);
      }
      // Invalidate drafts query to refresh the list
      queryClient.invalidateQueries({ queryKey: ['/api/drafts'] });
    },
    onError: (error) => {
      toast({
        title: "Error deleting draft",
        description: (error as Error).message,
        variant: "destructive",
      });
    },
  });

  // Format the creation date with error handling
  let timeAgo = '';
  try {
    const createdAt = new Date(draft.created_at);
    if (!isNaN(createdAt.getTime())) {
      timeAgo = formatDistanceToNow(createdAt, { addSuffix: true });
    } else {
      console.warn('Invalid date format:', draft.created_at);
      timeAgo = 'recently';
    }
  } catch (error) {
    console.warn('Error formatting date:', error);
    timeAgo = 'recently';
  }

  const handleDelete = () => {
    if (confirm('Are you sure you want to delete this draft?')) {
      deleteDraftMutation.mutate(draft.id);
    }
  };

  return (
    <Card className="overflow-hidden">
      <div className="relative h-48 bg-gray-100 flex items-center justify-center">
        <div className="flex flex-col items-center justify-center">
          <span className="material-icons text-gray-400 text-4xl mb-2">description</span>
          <p className="text-gray-600 font-medium">Draft Project</p>
        </div>
      </div>

      <CardContent className="p-4">
        <div className="flex justify-between items-start">
          <div>
            <h3 className="font-medium text-gray-900">{draft.title || 'Untitled Project'}</h3>
            <p className="text-gray-500 text-sm">Created {timeAgo}</p>
          </div>
          <Badge variant="secondary" className="bg-blue-100 text-blue-800">
            Draft
          </Badge>
        </div>

        <p className="text-gray-600 text-sm mt-2">
          {draft.description ?
            (draft.description.length > 100 ?
              draft.description.substring(0, 100) + '...' :
              draft.description) :
            'No description'}
        </p>
        {draft.modification_description && (
          <p className="text-gray-500 text-xs mt-1">
            <span className="font-medium">Modification:</span> {draft.modification_description.substring(0, 50)}
            {draft.modification_description.length > 50 ? '...' : ''}
          </p>
        )}

        <div className="mt-4 flex items-center space-x-2">
          <Button
            variant="ghost"
            size="sm"
            className="text-xs"
            onClick={handleDelete}
          >
            <span className="material-icons text-xs mr-1">delete</span>
            Delete
          </Button>

          <Link href={`/create?draftId=${draft.id}`}>
            <Button variant="outline" size="sm" className="text-xs">
              <span className="material-icons text-xs mr-1">edit</span>
              Continue
            </Button>
          </Link>
        </div>
      </CardContent>
    </Card>
  );
}
