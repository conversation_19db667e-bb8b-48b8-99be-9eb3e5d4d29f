import { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Textarea } from "@/components/ui/textarea";
import { ModificationType, uploadReferenceImages } from "@/lib/api";
import { CheckCircle2, Image as ImageIcon, ArrowLeft, ArrowRight } from "lucide-react";
import { Switch } from "@/components/ui/switch";
import { ImageUploader } from "@/components/ui/image-uploader";
import { ReferenceItemSelector } from "@/components/ReferenceItemSelector";
import { useMutation } from "@tanstack/react-query";

// Define the image type for uploaded images
export interface UploadedImageType {
  id: number;
  path: string;
  originalFilename?: string;
  type?: string;
  projectId?: number;
}

// Define the available floor materials
const floorMaterials = [
  { id: "hardwood", name: "Hardwood", description: "Classic, warm, and timeless" },
  { id: "laminate", name: "Laminate", description: "Affordable and durable" },
  { id: "tile", name: "Tile", description: "Great for kitchens and bathrooms" },
  { id: "vinyl", name: "Vinyl", description: "Water-resistant and low maintenance" },
  { id: "carpet", name: "Carpet", description: "Soft and comfortable" },
  { id: "concrete", name: "Concrete", description: "Modern and industrial" },
];

// Define the available floor colors
const floorColors = [
  { id: "natural", name: "Natural" },
  { id: "light", name: "Light" },
  { id: "medium", name: "Medium" },
  { id: "dark", name: "Dark" },
  { id: "white", name: "White" },
  { id: "gray", name: "Gray" },
  { id: "black", name: "Black" },
];

// Define the available wall colors
const wallColors = [
  { id: "white", name: "White", description: "Clean and bright" },
  { id: "off-white", name: "Off-White", description: "Warm and inviting" },
  { id: "beige", name: "Beige", description: "Neutral and versatile" },
  { id: "gray", name: "Gray", description: "Modern and sophisticated" },
  { id: "light-gray", name: "Light Gray", description: "Soft and contemporary" },
  { id: "dark-gray", name: "Dark Gray", description: "Bold and dramatic" },
  { id: "blue", name: "Blue", description: "Calming and serene" },
  { id: "green", name: "Green", description: "Natural and refreshing" },
  { id: "yellow", name: "Yellow", description: "Cheerful and energetic" },
  { id: "pink", name: "Pink", description: "Soft and romantic" },
  { id: "black", name: "Black", description: "Elegant and striking" },
  { id: "custom", name: "Custom Color", description: "Specify your own color" },
];

// Define the available wall finishes
const wallFinishes = [
  { id: "matte", name: "Matte", description: "No shine, hides imperfections" },
  { id: "eggshell", name: "Eggshell", description: "Slight sheen, easy to clean" },
  { id: "satin", name: "Satin", description: "Smooth finish with subtle shine" },
  { id: "semi-gloss", name: "Semi-Gloss", description: "Durable and washable" },
  { id: "gloss", name: "Gloss", description: "High shine, very durable" },
];

// Define the available cabinet materials
const cabinetMaterials = [
  { id: "wood", name: "Wood", description: "Natural and traditional" },
  { id: "laminate", name: "Laminate", description: "Affordable and versatile" },
  { id: "thermofoil", name: "Thermofoil", description: "Smooth and easy to clean" },
  { id: "metal", name: "Metal", description: "Modern and industrial" },
  { id: "glass", name: "Glass", description: "Open and contemporary" },
];

// Define the available cabinet colors
const cabinetColors = [
  { id: "white", name: "White" },
  { id: "off-white", name: "Off-White" },
  { id: "cream", name: "Cream" },
  { id: "gray", name: "Gray" },
  { id: "black", name: "Black" },
  { id: "natural-wood", name: "Natural Wood" },
  { id: "dark-wood", name: "Dark Wood" },
  { id: "light-wood", name: "Light Wood" },
  { id: "navy", name: "Navy" },
  { id: "green", name: "Green" },
];

// Define the available cabinet styles
const cabinetStyles = [
  { id: "shaker", name: "Shaker", description: "Clean lines and simple design" },
  { id: "raised-panel", name: "Raised Panel", description: "Traditional and elegant" },
  { id: "flat-panel", name: "Flat Panel", description: "Modern and minimalist" },
  { id: "beadboard", name: "Beadboard", description: "Cottage and farmhouse style" },
  { id: "glass-front", name: "Glass Front", description: "Open and airy feel" },
  { id: "louvered", name: "Louvered", description: "Tropical and coastal style" },
];

// Kitchen remodel options
const kitchenCabinetStyles = [
  { id: "modern", name: "Modern", description: "Sleek and contemporary" },
  { id: "traditional", name: "Traditional", description: "Classic and timeless" },
  { id: "transitional", name: "Transitional", description: "Blend of modern and traditional" },
  { id: "farmhouse", name: "Farmhouse", description: "Rustic and cozy" },
  { id: "industrial", name: "Industrial", description: "Urban and edgy" },
];

const kitchenCountertopMaterials = [
  { id: "quartz", name: "Quartz", description: "Durable and low maintenance" },
  { id: "granite", name: "Granite", description: "Natural stone with unique patterns" },
  { id: "marble", name: "Marble", description: "Elegant and luxurious" },
  { id: "butcher-block", name: "Butcher Block", description: "Warm wood surface" },
  { id: "concrete", name: "Concrete", description: "Modern industrial look" },
];

const kitchenApplianceStyles = [
  { id: "stainless-steel", name: "Stainless Steel", description: "Professional and modern" },
  { id: "black-stainless", name: "Black Stainless", description: "Sophisticated and sleek" },
  { id: "white", name: "White", description: "Clean and classic" },
  { id: "black", name: "Black", description: "Bold and dramatic" },
  { id: "panel-ready", name: "Panel Ready", description: "Integrated with cabinetry" },
];

const kitchenColorSchemes = [
  { id: "white-and-gray", name: "White & Gray", description: "Clean and timeless" },
  { id: "navy-and-white", name: "Navy & White", description: "Classic and sophisticated" },
  { id: "black-and-white", name: "Black & White", description: "Bold and dramatic" },
  { id: "warm-wood", name: "Warm Wood", description: "Natural and inviting" },
  { id: "earth-tones", name: "Earth Tones", description: "Warm and organic" },
];

// Bathroom remodel options
const bathroomFixtureStyles = [
  { id: "modern", name: "Modern", description: "Clean lines and minimalist" },
  { id: "traditional", name: "Traditional", description: "Classic and elegant" },
  { id: "transitional", name: "Transitional", description: "Blend of styles" },
  { id: "spa-like", name: "Spa-like", description: "Relaxing and luxurious" },
  { id: "industrial", name: "Industrial", description: "Urban and edgy" },
];

const bathroomTileStyles = [
  { id: "subway", name: "Subway Tile", description: "Classic rectangular tiles" },
  { id: "mosaic", name: "Mosaic", description: "Small decorative tiles" },
  { id: "large-format", name: "Large Format", description: "Minimal grout lines" },
  { id: "natural-stone", name: "Natural Stone", description: "Organic textures" },
  { id: "geometric", name: "Geometric", description: "Modern patterns" },
];

const bathroomVanityStyles = [
  { id: "floating", name: "Floating", description: "Wall-mounted for modern look" },
  { id: "freestanding", name: "Freestanding", description: "Traditional cabinet style" },
  { id: "double-vanity", name: "Double Vanity", description: "Two sinks for convenience" },
  { id: "vessel-sink", name: "Vessel Sink", description: "Bowl-style sink on counter" },
  { id: "integrated", name: "Integrated", description: "Seamless sink and counter" },
];

const bathroomColorSchemes = [
  { id: "white-and-gray", name: "White & Gray", description: "Clean and spa-like" },
  { id: "navy-and-white", name: "Navy & White", description: "Nautical and fresh" },
  { id: "earth-tones", name: "Earth Tones", description: "Warm and natural" },
  { id: "black-and-white", name: "Black & White", description: "Classic contrast" },
  { id: "soft-pastels", name: "Soft Pastels", description: "Calming and serene" },
];

// Interior painting options
const interiorWallColors = [
  { id: "warm-white", name: "Warm White", description: "Cozy and inviting" },
  { id: "cool-gray", name: "Cool Gray", description: "Modern and sophisticated" },
  { id: "sage-green", name: "Sage Green", description: "Calming and natural" },
  { id: "navy-blue", name: "Navy Blue", description: "Bold and dramatic" },
  { id: "soft-beige", name: "Soft Beige", description: "Neutral and versatile" },
];

const interiorCeilingColors = [
  { id: "white", name: "White", description: "Classic and bright" },
  { id: "off-white", name: "Off-White", description: "Warm and subtle" },
  { id: "light-gray", name: "Light Gray", description: "Modern and soft" },
  { id: "same-as-walls", name: "Same as Walls", description: "Cohesive look" },
];

const interiorAccentWalls = [
  { id: "feature-wall", name: "Feature Wall", description: "One bold accent wall" },
  { id: "fireplace-wall", name: "Fireplace Wall", description: "Highlight focal point" },
  { id: "bedroom-headboard", name: "Bedroom Headboard", description: "Behind the bed" },
  { id: "dining-room", name: "Dining Room", description: "Create intimate space" },
];

const interiorFinishes = [
  { id: "matte", name: "Matte", description: "No shine, hides imperfections" },
  { id: "eggshell", name: "Eggshell", description: "Slight sheen, easy to clean" },
  { id: "satin", name: "Satin", description: "Smooth with subtle shine" },
  { id: "semi-gloss", name: "Semi-Gloss", description: "Durable and washable" },
];

// Lighting upgrade options
const lightingFixtureStyles = [
  { id: "modern", name: "Modern", description: "Clean and contemporary" },
  { id: "traditional", name: "Traditional", description: "Classic and elegant" },
  { id: "industrial", name: "Industrial", description: "Urban and edgy" },
  { id: "farmhouse", name: "Farmhouse", description: "Rustic and cozy" },
  { id: "mid-century", name: "Mid-Century", description: "Retro and stylish" },
];

const lightingTypes = [
  { id: "recessed", name: "Recessed", description: "Clean ceiling-mounted lights" },
  { id: "pendant", name: "Pendant", description: "Hanging decorative lights" },
  { id: "chandelier", name: "Chandelier", description: "Statement ceiling fixture" },
  { id: "track", name: "Track", description: "Adjustable directional lighting" },
  { id: "under-cabinet", name: "Under Cabinet", description: "Task lighting for counters" },
];

const lightingAmbianceOptions = [
  { id: "bright-energizing", name: "Bright & Energizing", description: "High light levels" },
  { id: "warm-cozy", name: "Warm & Cozy", description: "Soft ambient lighting" },
  { id: "dramatic", name: "Dramatic", description: "Accent and mood lighting" },
  { id: "natural", name: "Natural", description: "Daylight-balanced lighting" },
];

// Exterior repainting options
const exteriorMainColors = [
  { id: "classic-white", name: "Classic White", description: "Timeless and clean" },
  { id: "charcoal-gray", name: "Charcoal Gray", description: "Modern and sophisticated" },
  { id: "navy-blue", name: "Navy Blue", description: "Bold and traditional" },
  { id: "sage-green", name: "Sage Green", description: "Natural and calming" },
  { id: "warm-beige", name: "Warm Beige", description: "Neutral and welcoming" },
];

const exteriorTrimColors = [
  { id: "bright-white", name: "Bright White", description: "Crisp contrast" },
  { id: "cream", name: "Cream", description: "Soft and warm" },
  { id: "black", name: "Black", description: "Bold and dramatic" },
  { id: "dark-gray", name: "Dark Gray", description: "Modern contrast" },
];

const exteriorAccentColors = [
  { id: "red", name: "Red", description: "Bold and welcoming" },
  { id: "blue", name: "Blue", description: "Classic and fresh" },
  { id: "green", name: "Green", description: "Natural and harmonious" },
  { id: "yellow", name: "Yellow", description: "Cheerful and bright" },
];

const exteriorDoorColors = [
  { id: "red", name: "Red", description: "Welcoming and bold" },
  { id: "navy", name: "Navy", description: "Classic and sophisticated" },
  { id: "black", name: "Black", description: "Elegant and timeless" },
  { id: "green", name: "Green", description: "Natural and inviting" },
  { id: "natural-wood", name: "Natural Wood", description: "Warm and organic" },
];

// Define the available modification types
const modificationTypes = [
  {
    id: "replace_floor",
    name: "Replace Floor",
    description: "Change the flooring material and color",
    icon: "layers",
    enabled: true
  },
  {
    id: "change_wall_color",
    name: "Change Wall Color",
    description: "Update the wall paint or wallpaper",
    icon: "format_paint",
    enabled: true
  },
  {
    id: "update_cabinets",
    name: "Update Cabinets",
    description: "Modify kitchen or bathroom cabinets",
    icon: "kitchen",
    enabled: true
  },
  {
    id: "kitchen_remodel",
    name: "Kitchen Remodel",
    description: "Upgrade cabinetry, countertops, appliances, and layout for a modern, functional kitchen",
    icon: "countertops",
    enabled: true
  },
  {
    id: "bathroom_remodel",
    name: "Bathroom Remodel",
    description: "Modernize fixtures, tiles, lighting, and layout to create a spa-like retreat",
    icon: "bathtub",
    enabled: true
  },
  {
    id: "interior_painting",
    name: "Interior Painting",
    description: "Refresh walls and ceilings with new colors to transform the ambiance of your space",
    icon: "brush",
    enabled: true
  },
  {
    id: "lighting_upgrades",
    name: "Lighting Upgrades",
    description: "Enhance your home's ambiance and energy efficiency with updated lighting fixtures and smart controls",
    icon: "lightbulb",
    enabled: true
  },
  {
    id: "exterior_repainting",
    name: "Exterior Repainting",
    description: "Give your home's facade a fresh new look with updated exterior paint",
    icon: "home",
    enabled: true
  },
  {
    id: "custom",
    name: "Custom Modification",
    description: "Describe your own custom changes",
    icon: "edit",
    enabled: true
  }
];

interface CreateVisualizationFlowProps {
  onComplete: (data: {
    modificationType: ModificationType;
    description: string;
    options?: any;
    primaryReferenceImageId?: number;
  }) => void;
  onBeforeImagesUpload: (files: File[]) => void;
  onReferenceImagesUpload: (files: File[]) => void;
  onReferenceItemsSelected: (items: any[]) => void;
  beforeImages: UploadedImageType[];
  referenceImages: UploadedImageType[];
  projectId?: number;
}

export function CreateVisualizationFlow({
  onComplete,
  onBeforeImagesUpload,
  onReferenceImagesUpload,
  onReferenceItemsSelected,
  beforeImages,
  referenceImages,
  projectId
}: CreateVisualizationFlowProps) {
  // Flow state
  const [currentStep, setCurrentStep] = useState<number>(1);
  const [isReferenceLibraryOpen, setIsReferenceLibraryOpen] = useState(false);
  const [activeReferenceTab, setActiveReferenceTab] = useState<string>("upload");

  // Modification selection state
  const [selectedModificationType, setSelectedModificationType] = useState<ModificationType | null>(null);
  const [customDescription, setCustomDescription] = useState<string>("");

  // Floor-specific state
  const [useReferenceImage, setUseReferenceImage] = useState<boolean>(true);
  const [selectedReferenceImageId, setSelectedReferenceImageId] = useState<number | null>(null);
  const [floorMaterial, setFloorMaterial] = useState<string>("");
  const [floorColor, setFloorColor] = useState<string>("");
  const [customFloorDescription, setCustomFloorDescription] = useState<string>("");

  // Wall color state
  const [wallColor, setWallColor] = useState<string>("");
  const [wallFinish, setWallFinish] = useState<string>("");
  const [customWallDescription, setCustomWallDescription] = useState<string>("");
  const [useWallReferenceImage, setUseWallReferenceImage] = useState<boolean>(false);
  const [selectedWallReferenceImageId, setSelectedWallReferenceImageId] = useState<number | null>(null);

  // Cabinet update state
  const [cabinetMaterial, setCabinetMaterial] = useState<string>("");
  const [cabinetColor, setCabinetColor] = useState<string>("");
  const [cabinetStyle, setCabinetStyle] = useState<string>("");
  const [customCabinetDescription, setCustomCabinetDescription] = useState<string>("");
  const [useCabinetReferenceImage, setUseCabinetReferenceImage] = useState<boolean>(false);
  const [selectedCabinetReferenceImageId, setSelectedCabinetReferenceImageId] = useState<number | null>(null);

  // Kitchen remodel state
  const [kitchenCabinetStyle, setKitchenCabinetStyle] = useState<string>("");
  const [kitchenCountertopMaterial, setKitchenCountertopMaterial] = useState<string>("");
  const [kitchenApplianceStyle, setKitchenApplianceStyle] = useState<string>("");
  const [kitchenColorScheme, setKitchenColorScheme] = useState<string>("");
  const [customKitchenDescription, setCustomKitchenDescription] = useState<string>("");
  const [useKitchenReferenceImage, setUseKitchenReferenceImage] = useState<boolean>(false);
  const [selectedKitchenReferenceImageId, setSelectedKitchenReferenceImageId] = useState<number | null>(null);

  // Bathroom remodel state
  const [bathroomFixtureStyle, setBathroomFixtureStyle] = useState<string>("");
  const [bathroomTileStyle, setBathroomTileStyle] = useState<string>("");
  const [bathroomVanityStyle, setBathroomVanityStyle] = useState<string>("");
  const [bathroomColorScheme, setBathroomColorScheme] = useState<string>("");
  const [customBathroomDescription, setCustomBathroomDescription] = useState<string>("");
  const [useBathroomReferenceImage, setUseBathroomReferenceImage] = useState<boolean>(false);
  const [selectedBathroomReferenceImageId, setSelectedBathroomReferenceImageId] = useState<number | null>(null);

  // Interior painting state
  const [interiorWallColor, setInteriorWallColor] = useState<string>("");
  const [interiorCeilingColor, setInteriorCeilingColor] = useState<string>("");
  const [interiorAccentWall, setInteriorAccentWall] = useState<string>("");
  const [interiorFinish, setInteriorFinish] = useState<string>("");
  const [customInteriorDescription, setCustomInteriorDescription] = useState<string>("");
  const [useInteriorReferenceImage, setUseInteriorReferenceImage] = useState<boolean>(false);
  const [selectedInteriorReferenceImageId, setSelectedInteriorReferenceImageId] = useState<number | null>(null);

  // Lighting upgrades state
  const [lightingFixtureStyle, setLightingFixtureStyle] = useState<string>("");
  const [lightingType, setLightingType] = useState<string>("");
  const [lightingAmbiance, setLightingAmbiance] = useState<string>("");
  const [lightingSmartControls, setLightingSmartControls] = useState<boolean>(false);
  const [customLightingDescription, setCustomLightingDescription] = useState<string>("");
  const [useLightingReferenceImage, setUseLightingReferenceImage] = useState<boolean>(false);
  const [selectedLightingReferenceImageId, setSelectedLightingReferenceImageId] = useState<number | null>(null);

  // Exterior repainting state
  const [exteriorMainColor, setExteriorMainColor] = useState<string>("");
  const [exteriorTrimColor, setExteriorTrimColor] = useState<string>("");
  const [exteriorAccentColor, setExteriorAccentColor] = useState<string>("");
  const [exteriorDoorColor, setExteriorDoorColor] = useState<string>("");
  const [customExteriorDescription, setCustomExteriorDescription] = useState<string>("");
  const [useExteriorReferenceImage, setUseExteriorReferenceImage] = useState<boolean>(false);
  const [selectedExteriorReferenceImageId, setSelectedExteriorReferenceImageId] = useState<number | null>(null);

  // Generated description
  const [generatedDescription, setGeneratedDescription] = useState<string>("");

  // Reusable component for reference image toggle
  const ReferenceImageToggle = ({
    id,
    checked,
    onCheckedChange,
    title,
    description
  }: {
    id: string;
    checked: boolean;
    onCheckedChange: (checked: boolean) => void;
    title: string;
    description: string;
  }) => (
    <div className="flex items-center justify-between mb-4 border-b pb-4">
      <div className="flex items-center space-x-2">
        <Label htmlFor={id} className="font-medium text-lg">
          {title}
        </Label>
        <p className="text-sm text-muted-foreground">
          {description}
        </p>
      </div>
      <Switch
        id={id}
        checked={checked}
        onCheckedChange={onCheckedChange}
      />
    </div>
  );

  // Reusable component for option selection
  const OptionSelector = ({
    title,
    options,
    value,
    onChange,
    columns = 2
  }: {
    title: string;
    options: Array<{ id: string; name: string; description: string }>;
    value: string;
    onChange: (value: string) => void;
    columns?: number;
  }) => (
    <div>
      <h3 className="text-lg font-medium mb-4">{title}</h3>
      <RadioGroup value={value} onValueChange={onChange} className={`grid grid-cols-${columns} gap-4`}>
        {options.map((option) => (
          <div key={option.id} className="flex items-start space-x-2">
            <RadioGroupItem value={option.id} id={`${title.toLowerCase().replace(/\s+/g, '-')}-${option.id}`} />
            <div className="grid gap-1.5">
              <Label htmlFor={`${title.toLowerCase().replace(/\s+/g, '-')}-${option.id}`} className="font-medium">
                {option.name}
              </Label>
              <p className="text-sm text-muted-foreground">{option.description}</p>
            </div>
          </div>
        ))}
      </RadioGroup>
    </div>
  );

  // Reusable component for reference image selection
  const ReferenceImageSelector = ({
    selectedImageId,
    onImageSelect,
    title = "Select Reference Image"
  }: {
    selectedImageId: number | null;
    onImageSelect: (imageId: number) => void;
    title?: string;
  }) => (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">{title}</h3>
        {referenceImages.length === 0 && (
          <Button variant="outline" size="sm" onClick={() => {
            setCurrentStep(2);
            setActiveReferenceTab("upload");
          }}>
            <ImageIcon className="h-4 w-4 mr-2" />
            Add Reference Images
          </Button>
        )}
      </div>

      {referenceImages.length === 0 ? (
        <div className="border border-dashed rounded-lg p-8 text-center">
          <ImageIcon className="h-10 w-10 mx-auto mb-2 text-muted-foreground" />
          <p className="text-muted-foreground">No reference images available</p>
          <p className="text-sm text-muted-foreground mt-1">
            Add reference images to use as inspiration
          </p>
          <Button
            variant="outline"
            size="sm"
            className="mt-4"
            onClick={() => {
              setCurrentStep(2);
              setActiveReferenceTab("upload");
            }}
          >
            Go Back to Add Images
          </Button>
        </div>
      ) : (
        <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
          {referenceImages.map((image) => (
            <div
              key={image.id}
              className={`relative border rounded-md overflow-hidden cursor-pointer transition-all ${
                selectedImageId === image.id
                  ? "ring-2 ring-primary border-primary"
                  : "hover:border-muted-foreground"
              }`}
              onClick={() => onImageSelect(image.id)}
            >
              <div className="aspect-square">
                <img
                  src={image.path.startsWith('http') ? image.path : `/uploads/${image.path.split('/').pop()}`}
                  alt={image.originalFilename || "Reference image"}
                  className="w-full h-full object-cover"
                />
              </div>
              {selectedImageId === image.id && (
                <div className="absolute top-2 right-2 bg-primary text-white rounded-full p-1">
                  <CheckCircle2 className="h-4 w-4" />
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );

  // Reusable component for custom description
  const CustomDescriptionField = ({
    value,
    onChange,
    placeholder
  }: {
    value: string;
    onChange: (value: string) => void;
    placeholder: string;
  }) => (
    <div>
      <h3 className="text-lg font-medium mb-2">Additional Details (Optional)</h3>
      <Textarea
        placeholder={placeholder}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className="min-h-20"
      />
    </div>
  );

  // Auto-enable reference image toggle and auto-select first reference image when images are uploaded
  useEffect(() => {
    if (referenceImages && referenceImages.length > 0) {
      const firstImageId = referenceImages[0].id;
      
      // Auto-enable toggle and select first image based on current modification type
      switch (selectedModificationType) {
        case "replace_floor":
          if (!useReferenceImage) {
            setUseReferenceImage(true);
          }
          if (!selectedReferenceImageId) {
            setSelectedReferenceImageId(firstImageId);
          }
          break;
        case "change_wall_color":
          if (!useWallReferenceImage) {
            setUseWallReferenceImage(true);
          }
          if (!selectedWallReferenceImageId) {
            setSelectedWallReferenceImageId(firstImageId);
          }
          break;
        case "update_cabinets":
          if (!useCabinetReferenceImage) {
            setUseCabinetReferenceImage(true);
          }
          if (!selectedCabinetReferenceImageId) {
            setSelectedCabinetReferenceImageId(firstImageId);
          }
          break;
        case "kitchen_remodel":
          if (!useKitchenReferenceImage) {
            setUseKitchenReferenceImage(true);
          }
          if (!selectedKitchenReferenceImageId) {
            setSelectedKitchenReferenceImageId(firstImageId);
          }
          break;
        case "bathroom_remodel":
          if (!useBathroomReferenceImage) {
            setUseBathroomReferenceImage(true);
          }
          if (!selectedBathroomReferenceImageId) {
            setSelectedBathroomReferenceImageId(firstImageId);
          }
          break;
        case "interior_painting":
          if (!useInteriorReferenceImage) {
            setUseInteriorReferenceImage(true);
          }
          if (!selectedInteriorReferenceImageId) {
            setSelectedInteriorReferenceImageId(firstImageId);
          }
          break;
        case "lighting_upgrades":
          if (!useLightingReferenceImage) {
            setUseLightingReferenceImage(true);
          }
          if (!selectedLightingReferenceImageId) {
            setSelectedLightingReferenceImageId(firstImageId);
          }
          break;
        case "exterior_repainting":
          if (!useExteriorReferenceImage) {
            setUseExteriorReferenceImage(true);
          }
          if (!selectedExteriorReferenceImageId) {
            setSelectedExteriorReferenceImageId(firstImageId);
          }
          break;
      }
    }
  }, [
    referenceImages, 
    selectedModificationType,
    useReferenceImage,
    useWallReferenceImage,
    useCabinetReferenceImage,
    useKitchenReferenceImage,
    useBathroomReferenceImage,
    useInteriorReferenceImage,
    useLightingReferenceImage,
    useExteriorReferenceImage
  ]);

  // Additional effect: Auto-select when modification type changes and we have reference images
  useEffect(() => {
    if (selectedModificationType && referenceImages && referenceImages.length > 0) {
      const firstImageId = referenceImages[0].id;
      
      // Force auto-selection when modification type changes
      switch (selectedModificationType) {
        case "replace_floor":
          setUseReferenceImage(true);
          setSelectedReferenceImageId(firstImageId);
          break;
        case "change_wall_color":
          setUseWallReferenceImage(true);
          setSelectedWallReferenceImageId(firstImageId);
          break;
        case "update_cabinets":
          setUseCabinetReferenceImage(true);
          setSelectedCabinetReferenceImageId(firstImageId);
          break;
        case "kitchen_remodel":
          setUseKitchenReferenceImage(true);
          setSelectedKitchenReferenceImageId(firstImageId);
          break;
        case "bathroom_remodel":
          setUseBathroomReferenceImage(true);
          setSelectedBathroomReferenceImageId(firstImageId);
          break;
        case "interior_painting":
          setUseInteriorReferenceImage(true);
          setSelectedInteriorReferenceImageId(firstImageId);
          break;
        case "lighting_upgrades":
          setUseLightingReferenceImage(true);
          setSelectedLightingReferenceImageId(firstImageId);
          break;
        case "exterior_repainting":
          setUseExteriorReferenceImage(true);
          setSelectedExteriorReferenceImageId(firstImageId);
          break;
      }
    }
  }, [selectedModificationType, referenceImages]);

  // Reset reference image selection when toggling switches off for all modification types
  useEffect(() => {
    if (!useReferenceImage) {
      setSelectedReferenceImageId(null);
    }
  }, [useReferenceImage]);

  useEffect(() => {
    if (!useWallReferenceImage) {
      setSelectedWallReferenceImageId(null);
    }
  }, [useWallReferenceImage]);

  useEffect(() => {
    if (!useCabinetReferenceImage) {
      setSelectedCabinetReferenceImageId(null);
    }
  }, [useCabinetReferenceImage]);

  useEffect(() => {
    if (!useKitchenReferenceImage) {
      setSelectedKitchenReferenceImageId(null);
    }
  }, [useKitchenReferenceImage]);

  useEffect(() => {
    if (!useBathroomReferenceImage) {
      setSelectedBathroomReferenceImageId(null);
    }
  }, [useBathroomReferenceImage]);

  useEffect(() => {
    if (!useInteriorReferenceImage) {
      setSelectedInteriorReferenceImageId(null);
    }
  }, [useInteriorReferenceImage]);

  useEffect(() => {
    if (!useLightingReferenceImage) {
      setSelectedLightingReferenceImageId(null);
    }
  }, [useLightingReferenceImage]);

  useEffect(() => {
    if (!useExteriorReferenceImage) {
      setSelectedExteriorReferenceImageId(null);
    }
  }, [useExteriorReferenceImage]);

  // Auto-select reference image when switching modification types (if images are available and reference toggle is on)
  useEffect(() => {
    if (referenceImages && referenceImages.length > 0 && selectedModificationType) {
      const firstImageId = referenceImages[0].id;
      
      // Auto-select when switching to a modification type with reference image enabled by default
      switch (selectedModificationType) {
        case "replace_floor":
          if (useReferenceImage && !selectedReferenceImageId) {
            setSelectedReferenceImageId(firstImageId);
          }
          break;
        case "change_wall_color":
          if (useWallReferenceImage && !selectedWallReferenceImageId) {
            setSelectedWallReferenceImageId(firstImageId);
          }
          break;
        case "update_cabinets":
          if (useCabinetReferenceImage && !selectedCabinetReferenceImageId) {
            setSelectedCabinetReferenceImageId(firstImageId);
          }
          break;
        case "kitchen_remodel":
          if (useKitchenReferenceImage && !selectedKitchenReferenceImageId) {
            setSelectedKitchenReferenceImageId(firstImageId);
          }
          break;
        case "bathroom_remodel":
          if (useBathroomReferenceImage && !selectedBathroomReferenceImageId) {
            setSelectedBathroomReferenceImageId(firstImageId);
          }
          break;
        case "interior_painting":
          if (useInteriorReferenceImage && !selectedInteriorReferenceImageId) {
            setSelectedInteriorReferenceImageId(firstImageId);
          }
          break;
        case "lighting_upgrades":
          if (useLightingReferenceImage && !selectedLightingReferenceImageId) {
            setSelectedLightingReferenceImageId(firstImageId);
          }
          break;
        case "exterior_repainting":
          if (useExteriorReferenceImage && !selectedExteriorReferenceImageId) {
            setSelectedExteriorReferenceImageId(firstImageId);
          }
          break;
      }
    }
  }, [selectedModificationType]);

  // Update the generated description when options change
  useEffect(() => {
    if (selectedModificationType === "replace_floor") {
      if (useReferenceImage && selectedReferenceImageId) {
        // Find the selected reference image
        const selectedImage = referenceImages.find(img => img.id === selectedReferenceImageId);
        const imageName = selectedImage?.originalFilename || "selected reference image";
        const customDesc = customFloorDescription ? customFloorDescription : "";

        const description = `Replace the floor using the reference image "${imageName}" as inspiration. ${customDesc}`.trim();
        setGeneratedDescription(description);
      } else if (floorMaterial && floorColor) {
        // Standard material/color selection
        const material = floorMaterialName(floorMaterial);
        const color = floorColor;
        const customDesc = customFloorDescription ? customFloorDescription : "";

        const description = `Replace the floor with ${color} ${material} flooring. ${customDesc}`.trim();
        setGeneratedDescription(description);
      } else {
        setGeneratedDescription("");
      }
    } else if (selectedModificationType === "change_wall_color") {
      if (useWallReferenceImage && selectedWallReferenceImageId) {
        // Find the selected reference image
        const selectedImage = referenceImages.find(img => img.id === selectedWallReferenceImageId);
        const imageName = selectedImage?.originalFilename || "selected reference image";
        const customDesc = customWallDescription ? customWallDescription : "";

        const description = `Change the wall color using the reference image "${imageName}" as inspiration. ${customDesc}`.trim();
        setGeneratedDescription(description);
      } else if (wallColor) {
        // Standard color/finish selection
        const color = wallColorName(wallColor);
        const finish = wallFinish ? wallFinishName(wallFinish) : "";
        const customDesc = customWallDescription ? customWallDescription : "";

        const description = `Change the walls to ${color}${finish ? ` with ${finish} finish` : ""}. ${customDesc}`.trim();
        setGeneratedDescription(description);
      } else {
        setGeneratedDescription("");
      }
    } else if (selectedModificationType === "update_cabinets") {
      if (useCabinetReferenceImage && selectedCabinetReferenceImageId) {
        // Find the selected reference image
        const selectedImage = referenceImages.find(img => img.id === selectedCabinetReferenceImageId);
        const imageName = selectedImage?.originalFilename || "selected reference image";
        const customDesc = customCabinetDescription ? customCabinetDescription : "";

        const description = `Update the cabinets using the reference image "${imageName}" as inspiration. ${customDesc}`.trim();
        setGeneratedDescription(description);
      } else if (cabinetColor || cabinetMaterial || cabinetStyle) {
        // Standard cabinet options selection
        const color = cabinetColor ? cabinetColorName(cabinetColor) : "";
        const material = cabinetMaterial ? cabinetMaterialName(cabinetMaterial) : "";
        const style = cabinetStyle ? cabinetStyleName(cabinetStyle) : "";
        const customDesc = customCabinetDescription ? customCabinetDescription : "";

        let description = "Update the cabinets";
        const parts = [];
        if (color) parts.push(`${color} color`);
        if (material) parts.push(`${material} material`);
        if (style) parts.push(`${style} style`);

        if (parts.length > 0) {
          description += ` with ${parts.join(", ")}`;
        }
        description += `.`;
        if (customDesc) description += ` ${customDesc}`;

        setGeneratedDescription(description.trim());
      } else {
        setGeneratedDescription("");
      }
    } else if (selectedModificationType === "kitchen_remodel") {
      if (useKitchenReferenceImage && selectedKitchenReferenceImageId) {
        const selectedImage = referenceImages.find(img => img.id === selectedKitchenReferenceImageId);
        const imageName = selectedImage?.originalFilename || "selected reference image";
        const customDesc = customKitchenDescription ? customKitchenDescription : "";
        const description = `Remodel the kitchen using the reference image "${imageName}" as inspiration. ${customDesc}`.trim();
        setGeneratedDescription(description);
      } else if (kitchenCabinetStyle || kitchenCountertopMaterial || kitchenApplianceStyle || kitchenColorScheme) {
        let description = "Remodel the kitchen";
        const parts = [];
        if (kitchenCabinetStyle) parts.push(`${kitchenCabinetStyleName(kitchenCabinetStyle)} cabinets`);
        if (kitchenCountertopMaterial) parts.push(`${kitchenCountertopMaterialName(kitchenCountertopMaterial)} countertops`);
        if (kitchenApplianceStyle) parts.push(`${kitchenApplianceStyleName(kitchenApplianceStyle)} appliances`);
        if (kitchenColorScheme) parts.push(`${kitchenColorSchemeName(kitchenColorScheme)} color scheme`);
        if (parts.length > 0) {
          description += ` with ${parts.join(", ")}`;
        }
        description += `.`;
        if (customKitchenDescription) description += ` ${customKitchenDescription}`;
        setGeneratedDescription(description.trim());
      } else {
        setGeneratedDescription("");
      }
    } else if (selectedModificationType === "bathroom_remodel") {
      if (useBathroomReferenceImage && selectedBathroomReferenceImageId) {
        const selectedImage = referenceImages.find(img => img.id === selectedBathroomReferenceImageId);
        const imageName = selectedImage?.originalFilename || "selected reference image";
        const customDesc = customBathroomDescription ? customBathroomDescription : "";
        const description = `Remodel the bathroom using the reference image "${imageName}" as inspiration. ${customDesc}`.trim();
        setGeneratedDescription(description);
      } else if (bathroomFixtureStyle || bathroomTileStyle || bathroomVanityStyle || bathroomColorScheme) {
        let description = "Remodel the bathroom";
        const parts = [];
        if (bathroomFixtureStyle) parts.push(`${bathroomFixtureStyleName(bathroomFixtureStyle)} fixtures`);
        if (bathroomTileStyle) parts.push(`${bathroomTileStyleName(bathroomTileStyle)} tiles`);
        if (bathroomVanityStyle) parts.push(`${bathroomVanityStyleName(bathroomVanityStyle)} vanity`);
        if (bathroomColorScheme) parts.push(`${bathroomColorSchemeName(bathroomColorScheme)} color scheme`);
        if (parts.length > 0) {
          description += ` with ${parts.join(", ")}`;
        }
        description += `.`;
        if (customBathroomDescription) description += ` ${customBathroomDescription}`;
        setGeneratedDescription(description.trim());
      } else {
        setGeneratedDescription("");
      }
    } else if (selectedModificationType === "interior_painting") {
      if (useInteriorReferenceImage && selectedInteriorReferenceImageId) {
        const selectedImage = referenceImages.find(img => img.id === selectedInteriorReferenceImageId);
        const imageName = selectedImage?.originalFilename || "selected reference image";
        const customDesc = customInteriorDescription ? customInteriorDescription : "";
        const description = `Paint the interior using the reference image "${imageName}" as inspiration. ${customDesc}`.trim();
        setGeneratedDescription(description);
      } else if (interiorWallColor || interiorCeilingColor || interiorAccentWall || interiorFinish) {
        let description = "Paint the interior";
        const parts = [];
        if (interiorWallColor) parts.push(`${interiorWallColorName(interiorWallColor)} walls`);
        if (interiorCeilingColor) parts.push(`${interiorCeilingColorName(interiorCeilingColor)} ceiling`);
        if (interiorAccentWall) parts.push(`${interiorAccentWallName(interiorAccentWall)}`);
        if (interiorFinish) parts.push(`${interiorFinishName(interiorFinish)} finish`);
        if (parts.length > 0) {
          description += ` with ${parts.join(", ")}`;
        }
        description += `.`;
        if (customInteriorDescription) description += ` ${customInteriorDescription}`;
        setGeneratedDescription(description.trim());
      } else {
        setGeneratedDescription("");
      }
    } else if (selectedModificationType === "lighting_upgrades") {
      if (useLightingReferenceImage && selectedLightingReferenceImageId) {
        const selectedImage = referenceImages.find(img => img.id === selectedLightingReferenceImageId);
        const imageName = selectedImage?.originalFilename || "selected reference image";
        const customDesc = customLightingDescription ? customLightingDescription : "";
        const description = `Upgrade the lighting using the reference image "${imageName}" as inspiration. ${customDesc}`.trim();
        setGeneratedDescription(description);
      } else if (lightingFixtureStyle || lightingType || lightingAmbiance || lightingSmartControls) {
        let description = "Upgrade the lighting";
        const parts = [];
        if (lightingFixtureStyle) parts.push(`${lightingFixtureStyleName(lightingFixtureStyle)} fixtures`);
        if (lightingType) parts.push(`${lightingTypeName(lightingType)} lighting`);
        if (lightingAmbiance) parts.push(`${lightingAmbianceName(lightingAmbiance)} ambiance`);
        if (lightingSmartControls) parts.push(`smart controls`);
        if (parts.length > 0) {
          description += ` with ${parts.join(", ")}`;
        }
        description += `.`;
        if (customLightingDescription) description += ` ${customLightingDescription}`;
        setGeneratedDescription(description.trim());
      } else {
        setGeneratedDescription("");
      }
    } else if (selectedModificationType === "exterior_repainting") {
      if (useExteriorReferenceImage && selectedExteriorReferenceImageId) {
        const selectedImage = referenceImages.find(img => img.id === selectedExteriorReferenceImageId);
        const imageName = selectedImage?.originalFilename || "selected reference image";
        const customDesc = customExteriorDescription ? customExteriorDescription : "";
        const description = `Repaint the exterior using the reference image "${imageName}" as inspiration. ${customDesc}`.trim();
        setGeneratedDescription(description);
      } else if (exteriorMainColor || exteriorTrimColor || exteriorAccentColor || exteriorDoorColor) {
        let description = "Repaint the exterior";
        const parts = [];
        if (exteriorMainColor) parts.push(`${exteriorMainColorName(exteriorMainColor)} main color`);
        if (exteriorTrimColor) parts.push(`${exteriorTrimColorName(exteriorTrimColor)} trim`);
        if (exteriorAccentColor) parts.push(`${exteriorAccentColorName(exteriorAccentColor)} accents`);
        if (exteriorDoorColor) parts.push(`${exteriorDoorColorName(exteriorDoorColor)} door`);
        if (parts.length > 0) {
          description += ` with ${parts.join(", ")}`;
        }
        description += `.`;
        if (customExteriorDescription) description += ` ${customExteriorDescription}`;
        setGeneratedDescription(description.trim());
      } else {
        setGeneratedDescription("");
      }
    } else if (selectedModificationType === "custom") {
      setGeneratedDescription(customDescription);
    } else {
      setGeneratedDescription("");
    }
  }, [
    selectedModificationType,
    useReferenceImage,
    selectedReferenceImageId,
    floorMaterial,
    floorColor,
    customFloorDescription,
    wallColor,
    wallFinish,
    customWallDescription,
    useWallReferenceImage,
    selectedWallReferenceImageId,
    cabinetMaterial,
    cabinetColor,
    cabinetStyle,
    customCabinetDescription,
    useCabinetReferenceImage,
    selectedCabinetReferenceImageId,
    // Kitchen remodel dependencies
    kitchenCabinetStyle,
    kitchenCountertopMaterial,
    kitchenApplianceStyle,
    kitchenColorScheme,
    customKitchenDescription,
    useKitchenReferenceImage,
    selectedKitchenReferenceImageId,
    // Bathroom remodel dependencies
    bathroomFixtureStyle,
    bathroomTileStyle,
    bathroomVanityStyle,
    bathroomColorScheme,
    customBathroomDescription,
    useBathroomReferenceImage,
    selectedBathroomReferenceImageId,
    // Interior painting dependencies
    interiorWallColor,
    interiorCeilingColor,
    interiorAccentWall,
    interiorFinish,
    customInteriorDescription,
    useInteriorReferenceImage,
    selectedInteriorReferenceImageId,
    // Lighting upgrades dependencies
    lightingFixtureStyle,
    lightingType,
    lightingAmbiance,
    lightingSmartControls,
    customLightingDescription,
    useLightingReferenceImage,
    selectedLightingReferenceImageId,
    // Exterior repainting dependencies
    exteriorMainColor,
    exteriorTrimColor,
    exteriorAccentColor,
    exteriorDoorColor,
    customExteriorDescription,
    useExteriorReferenceImage,
    selectedExteriorReferenceImageId,
    customDescription,
    referenceImages
  ]);

  // Helper function to get the display name of floor materials
  const floorMaterialName = (materialId: string): string => {
    const materials: Record<string, string> = {
      "hardwood": "hardwood",
      "laminate": "laminate",
      "tile": "tile",
      "vinyl": "vinyl",
      "carpet": "carpet",
      "concrete": "concrete"
    };
    return materials[materialId] || materialId;
  };

  // Helper function to get the display name of wall colors
  const wallColorName = (colorId: string): string => {
    const color = wallColors.find(c => c.id === colorId);
    return color?.name || colorId;
  };

  // Helper function to get the display name of wall finishes
  const wallFinishName = (finishId: string): string => {
    const finish = wallFinishes.find(f => f.id === finishId);
    return finish?.name || finishId;
  };

  // Helper function to get the display name of cabinet materials
  const cabinetMaterialName = (materialId: string): string => {
    const material = cabinetMaterials.find(m => m.id === materialId);
    return material?.name || materialId;
  };

  // Helper function to get the display name of cabinet colors
  const cabinetColorName = (colorId: string): string => {
    const color = cabinetColors.find(c => c.id === colorId);
    return color?.name || colorId;
  };

  // Helper function to get the display name of cabinet styles
  const cabinetStyleName = (styleId: string): string => {
    const style = cabinetStyles.find(s => s.id === styleId);
    return style?.name || styleId;
  };

  // Helper functions for new modification types
  const kitchenCabinetStyleName = (styleId: string): string => {
    const style = kitchenCabinetStyles.find(s => s.id === styleId);
    return style?.name || styleId;
  };

  const kitchenCountertopMaterialName = (materialId: string): string => {
    const material = kitchenCountertopMaterials.find(m => m.id === materialId);
    return material?.name || materialId;
  };

  const kitchenApplianceStyleName = (styleId: string): string => {
    const style = kitchenApplianceStyles.find(s => s.id === styleId);
    return style?.name || styleId;
  };

  const kitchenColorSchemeName = (schemeId: string): string => {
    const scheme = kitchenColorSchemes.find(s => s.id === schemeId);
    return scheme?.name || schemeId;
  };

  const bathroomFixtureStyleName = (styleId: string): string => {
    const style = bathroomFixtureStyles.find(s => s.id === styleId);
    return style?.name || styleId;
  };

  const bathroomTileStyleName = (styleId: string): string => {
    const style = bathroomTileStyles.find(s => s.id === styleId);
    return style?.name || styleId;
  };

  const bathroomVanityStyleName = (styleId: string): string => {
    const style = bathroomVanityStyles.find(s => s.id === styleId);
    return style?.name || styleId;
  };

  const bathroomColorSchemeName = (schemeId: string): string => {
    const scheme = bathroomColorSchemes.find(s => s.id === schemeId);
    return scheme?.name || schemeId;
  };

  const interiorWallColorName = (colorId: string): string => {
    const color = interiorWallColors.find(c => c.id === colorId);
    return color?.name || colorId;
  };

  const interiorCeilingColorName = (colorId: string): string => {
    const color = interiorCeilingColors.find(c => c.id === colorId);
    return color?.name || colorId;
  };

  const interiorAccentWallName = (wallId: string): string => {
    const wall = interiorAccentWalls.find(w => w.id === wallId);
    return wall?.name || wallId;
  };

  const interiorFinishName = (finishId: string): string => {
    const finish = interiorFinishes.find(f => f.id === finishId);
    return finish?.name || finishId;
  };

  const lightingFixtureStyleName = (styleId: string): string => {
    const style = lightingFixtureStyles.find(s => s.id === styleId);
    return style?.name || styleId;
  };

  const lightingTypeName = (typeId: string): string => {
    const type = lightingTypes.find(t => t.id === typeId);
    return type?.name || typeId;
  };

  const lightingAmbianceName = (ambianceId: string): string => {
    const ambiance = lightingAmbianceOptions.find(a => a.id === ambianceId);
    return ambiance?.name || ambianceId;
  };

  const exteriorMainColorName = (colorId: string): string => {
    const color = exteriorMainColors.find(c => c.id === colorId);
    return color?.name || colorId;
  };

  const exteriorTrimColorName = (colorId: string): string => {
    const color = exteriorTrimColors.find(c => c.id === colorId);
    return color?.name || colorId;
  };

  const exteriorAccentColorName = (colorId: string): string => {
    const color = exteriorAccentColors.find(c => c.id === colorId);
    return color?.name || colorId;
  };

  const exteriorDoorColorName = (colorId: string): string => {
    const color = exteriorDoorColors.find(c => c.id === colorId);
    return color?.name || colorId;
  };

  // Handle selecting a modification type
  const handleSelectModification = (type: ModificationType) => {
    setSelectedModificationType(type);

    // Always go to step 2 for image uploads, regardless of modification type
    setCurrentStep(2);
  };

  // Handle selecting a reference image
  const handleSelectReferenceImage = (imageId: number) => {
    setSelectedReferenceImageId(imageId === selectedReferenceImageId ? null : imageId);
  };

  // Check if we can proceed to the next step
  const canProceedToNextStep = () => {
    if (currentStep === 1) {
      // Can proceed if a modification type is selected
      return !!selectedModificationType;
    } else if (currentStep === 2) {
      // Can proceed if at least one before image is uploaded
      return beforeImages.length > 0;
    } else if (currentStep === 3) {
      // Can proceed based on the selected modification type
      if (selectedModificationType === "replace_floor") {
        if (useReferenceImage) {
          return !!selectedReferenceImageId;
        } else {
          return !!floorMaterial && !!floorColor;
        }
      } else if (selectedModificationType === "change_wall_color") {
        if (useWallReferenceImage) {
          return !!selectedWallReferenceImageId;
        } else {
          return !!wallColor;
        }
      } else if (selectedModificationType === "update_cabinets") {
        if (useCabinetReferenceImage) {
          return !!selectedCabinetReferenceImageId;
        } else {
          // At least one cabinet option must be selected
          return !!(cabinetColor || cabinetMaterial || cabinetStyle);
        }
      } else if (selectedModificationType === "kitchen_remodel") {
        if (useKitchenReferenceImage) {
          return !!selectedKitchenReferenceImageId;
        } else {
          // At least one kitchen option must be selected
          return !!(kitchenCabinetStyle || kitchenCountertopMaterial || kitchenApplianceStyle || kitchenColorScheme);
        }
      } else if (selectedModificationType === "bathroom_remodel") {
        if (useBathroomReferenceImage) {
          return !!selectedBathroomReferenceImageId;
        } else {
          // At least one bathroom option must be selected
          return !!(bathroomFixtureStyle || bathroomTileStyle || bathroomVanityStyle || bathroomColorScheme);
        }
      } else if (selectedModificationType === "interior_painting") {
        if (useInteriorReferenceImage) {
          return !!selectedInteriorReferenceImageId;
        } else {
          // At least one painting option must be selected
          return !!(interiorWallColor || interiorCeilingColor || interiorAccentWall || interiorFinish);
        }
      } else if (selectedModificationType === "lighting_upgrades") {
        if (useLightingReferenceImage) {
          return !!selectedLightingReferenceImageId;
        } else {
          // At least one lighting option must be selected
          return !!(lightingFixtureStyle || lightingType || lightingAmbiance || lightingSmartControls);
        }
      } else if (selectedModificationType === "exterior_repainting") {
        if (useExteriorReferenceImage) {
          return !!selectedExteriorReferenceImageId;
        } else {
          // At least one exterior option must be selected
          return !!(exteriorMainColor || exteriorTrimColor || exteriorAccentColor || exteriorDoorColor);
        }
      } else if (selectedModificationType === "custom") {
        // For custom modifications, we need a description of at least 10 characters
        // Reference images are optional but encouraged
        return customDescription.length >= 10;
      }
      return false;
    } else if (currentStep === 4) {
      // Can always proceed from review to completion as long as we have a description
      return !!generatedDescription;
    }
    return true;
  };

  // Handle completing the flow
  const handleComplete = () => {
    if (!selectedModificationType) return;

    if (selectedModificationType === "replace_floor") {
      const options = {
        material: useReferenceImage ? null : floorMaterial,
        color: useReferenceImage ? null : floorColor,
        customDescription: customFloorDescription,
        useReferenceImage: useReferenceImage,
        referenceImageId: selectedReferenceImageId
      };

      // Combine the generated description with custom description for backend processing
      const finalDescription = customDescription ?
        `${generatedDescription} ${customDescription}`.trim() :
        generatedDescription;

      onComplete({
        modificationType: selectedModificationType,
        description: finalDescription,
        options,
        primaryReferenceImageId: useReferenceImage ? selectedReferenceImageId || undefined : undefined
      });
    } else if (selectedModificationType === "change_wall_color") {
      const options = {
        color: useWallReferenceImage ? null : wallColor,
        finish: useWallReferenceImage ? null : wallFinish,
        customDescription: customWallDescription,
        useReferenceImage: useWallReferenceImage,
        referenceImageId: selectedWallReferenceImageId
      };

      // Combine the generated description with custom description for backend processing
      const finalDescription = customDescription ?
        `${generatedDescription} ${customDescription}`.trim() :
        generatedDescription;

      onComplete({
        modificationType: selectedModificationType,
        description: finalDescription,
        options,
        primaryReferenceImageId: useWallReferenceImage ? selectedWallReferenceImageId || undefined : undefined
      });
    } else if (selectedModificationType === "update_cabinets") {
      const options = {
        material: useCabinetReferenceImage ? null : cabinetMaterial,
        color: useCabinetReferenceImage ? null : cabinetColor,
        style: useCabinetReferenceImage ? null : cabinetStyle,
        customDescription: customCabinetDescription,
        useReferenceImage: useCabinetReferenceImage,
        referenceImageId: selectedCabinetReferenceImageId
      };

      // Combine the generated description with custom description for backend processing
      const finalDescription = customDescription ?
        `${generatedDescription} ${customDescription}`.trim() :
        generatedDescription;

      onComplete({
        modificationType: selectedModificationType,
        description: finalDescription,
        options,
        primaryReferenceImageId: useCabinetReferenceImage ? selectedCabinetReferenceImageId || undefined : undefined
      });
    } else if (selectedModificationType === "kitchen_remodel") {
      const options = {
        cabinetStyle: useKitchenReferenceImage ? null : kitchenCabinetStyle,
        countertopMaterial: useKitchenReferenceImage ? null : kitchenCountertopMaterial,
        applianceStyle: useKitchenReferenceImage ? null : kitchenApplianceStyle,
        colorScheme: useKitchenReferenceImage ? null : kitchenColorScheme,
        customDescription: customKitchenDescription,
        useReferenceImage: useKitchenReferenceImage,
        referenceImageId: selectedKitchenReferenceImageId
      };

      // Combine the generated description with custom description for backend processing
      const finalDescription = customDescription ?
        `${generatedDescription} ${customDescription}`.trim() :
        generatedDescription;

      onComplete({
        modificationType: selectedModificationType,
        description: finalDescription,
        options,
        primaryReferenceImageId: useKitchenReferenceImage ? selectedKitchenReferenceImageId || undefined : undefined
      });
    } else if (selectedModificationType === "bathroom_remodel") {
      const options = {
        fixtureStyle: useBathroomReferenceImage ? null : bathroomFixtureStyle,
        tileStyle: useBathroomReferenceImage ? null : bathroomTileStyle,
        vanityStyle: useBathroomReferenceImage ? null : bathroomVanityStyle,
        colorScheme: useBathroomReferenceImage ? null : bathroomColorScheme,
        customDescription: customBathroomDescription,
        useReferenceImage: useBathroomReferenceImage,
        referenceImageId: selectedBathroomReferenceImageId
      };

      // Combine the generated description with custom description for backend processing
      const finalDescription = customDescription ?
        `${generatedDescription} ${customDescription}`.trim() :
        generatedDescription;

      onComplete({
        modificationType: selectedModificationType,
        description: finalDescription,
        options,
        primaryReferenceImageId: useBathroomReferenceImage ? selectedBathroomReferenceImageId || undefined : undefined
      });
    } else if (selectedModificationType === "interior_painting") {
      const options = {
        wallColor: useInteriorReferenceImage ? null : interiorWallColor,
        ceilingColor: useInteriorReferenceImage ? null : interiorCeilingColor,
        accentWall: useInteriorReferenceImage ? null : interiorAccentWall,
        finish: useInteriorReferenceImage ? null : interiorFinish,
        customDescription: customInteriorDescription,
        useReferenceImage: useInteriorReferenceImage,
        referenceImageId: selectedInteriorReferenceImageId
      };

      // Combine the generated description with custom description for backend processing
      const finalDescription = customDescription ?
        `${generatedDescription} ${customDescription}`.trim() :
        generatedDescription;

      onComplete({
        modificationType: selectedModificationType,
        description: finalDescription,
        options,
        primaryReferenceImageId: useInteriorReferenceImage ? selectedInteriorReferenceImageId || undefined : undefined
      });
    } else if (selectedModificationType === "lighting_upgrades") {
      const options = {
        fixtureStyle: useLightingReferenceImage ? null : lightingFixtureStyle,
        lightingType: useLightingReferenceImage ? null : lightingType,
        ambiance: useLightingReferenceImage ? null : lightingAmbiance,
        smartControls: useLightingReferenceImage ? null : lightingSmartControls,
        customDescription: customLightingDescription,
        useReferenceImage: useLightingReferenceImage,
        referenceImageId: selectedLightingReferenceImageId
      };

      // Combine the generated description with custom description for backend processing
      const finalDescription = customDescription ?
        `${generatedDescription} ${customDescription}`.trim() :
        generatedDescription;

      onComplete({
        modificationType: selectedModificationType,
        description: finalDescription,
        options,
        primaryReferenceImageId: useLightingReferenceImage ? selectedLightingReferenceImageId || undefined : undefined
      });
    } else if (selectedModificationType === "exterior_repainting") {
      const options = {
        mainColor: useExteriorReferenceImage ? null : exteriorMainColor,
        trimColor: useExteriorReferenceImage ? null : exteriorTrimColor,
        accentColor: useExteriorAccentColor ? null : exteriorAccentColor,
        doorColor: useExteriorDoorColor ? null : exteriorDoorColor,
        customDescription: customExteriorDescription,
        useReferenceImage: useExteriorReferenceImage,
        referenceImageId: selectedExteriorReferenceImageId
      };

      // Combine the generated description with custom description for backend processing
      const finalDescription = customDescription ?
        `${generatedDescription} ${customDescription}`.trim() :
        generatedDescription;

      onComplete({
        modificationType: selectedModificationType,
        description: finalDescription,
        options,
        primaryReferenceImageId: useExteriorReferenceImage ? selectedExteriorReferenceImageId || undefined : undefined
      });
    } else if (selectedModificationType === "custom") {
      // For custom modifications, check if we have a selected reference image to prioritize
      const primaryReferenceImageId = selectedReferenceImageId ||
        (referenceImages.length > 0 ? referenceImages[0].id : undefined);

      // Combine the generated description with custom description for backend processing
      const finalDescription = customDescription ?
        `${generatedDescription} ${customDescription}`.trim() :
        generatedDescription;

      onComplete({
        modificationType: "custom",
        description: finalDescription,
        // Include any selected reference image as the primary one
        primaryReferenceImageId: primaryReferenceImageId
      });
    }
  };

  // Render the step content
  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return renderModificationSelection();
      case 2:
        return renderImageUpload();
      case 3:
        return renderModificationOptions();
      case 4:
        return renderReview();
      default:
        return null;
    }
  };

  // Render the modification selection step
  const renderModificationSelection = () => {
    return (
      <div className="space-y-6">
        <h3 className="text-lg font-medium text-gray-900">Choose What You Want to Change</h3>
        <p className="text-gray-600 text-sm">Select the type of modification you want to make to your image</p>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
          {modificationTypes.map((type) => (
            <Card
              key={type.id}
              className={`cursor-pointer hover:border-primary-300 transition-colors ${
                !type.enabled ? "opacity-50 cursor-not-allowed" : ""
              } ${selectedModificationType === type.id ? "border-primary-500 ring-2 ring-primary-200" : ""}`}
              onClick={() => type.enabled && handleSelectModification(type.id as ModificationType)}
            >
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center">
                    <span className="material-icons text-primary-600">{type.icon}</span>
                  </div>
                  <div>
                    <h3 className="font-medium text-lg">{type.name}</h3>
                    <p className="text-sm text-gray-600">{type.description}</p>
                    {!type.enabled && <p className="text-xs text-gray-500 mt-1">Coming soon</p>}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  };

  // Render the image upload step
  const renderImageUpload = () => {
    return (
      <div className="space-y-6">
        <div className="mb-6">
          <h3 className="text-lg font-medium text-gray-900 mb-2">Upload Before Images</h3>
          <p className="text-gray-600 text-sm mb-4">Upload images of the current state that you want to modify</p>

          <ImageUploader
            onFilesSelected={onBeforeImagesUpload}
            title="Drag and drop images here, or click to browse"
            description="Supports JPG, PNG, WEBP - Max 10MB"
            imageType="Before"
            uploadedFiles={beforeImages}
          />
        </div>

        <div className="mb-6">
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {selectedModificationType === "replace_floor"
              ? "Floor Reference Images (Optional)"
              : "Reference Images (Optional)"}
          </h3>
          <p className="text-gray-600 text-sm mb-4">
            {selectedModificationType === "replace_floor"
              ? "Upload or select reference images showing the floor style you want"
              : "Upload or select reference images to inspire your modifications"}
          </p>

          <Tabs
            defaultValue="upload"
            className="mb-4"
            value={activeReferenceTab}
            onValueChange={setActiveReferenceTab}
          >
            <TabsList className="mb-2">
              <TabsTrigger value="upload">Upload New</TabsTrigger>
              <TabsTrigger value="library">From My Library</TabsTrigger>
            </TabsList>

            <TabsContent value="upload">
              <ImageUploader
                onFilesSelected={onReferenceImagesUpload}
                title={`Drag and drop ${selectedModificationType === "replace_floor" ? "floor " : ""}reference images here`}
                description={selectedModificationType === "replace_floor"
                  ? "Upload images of floor styles you like"
                  : "Upload images that show the style or elements you want to incorporate"}
                imageType="Reference"
                uploadedFiles={referenceImages}
              />
            </TabsContent>

            <TabsContent value="library">
              <div className="border-2 border-dashed rounded-md p-8 text-center">
                <div className="space-y-4">
                  <h4 className="font-medium">Select from your Reference Library</h4>
                  <p className="text-sm text-muted-foreground">
                    {selectedModificationType === "replace_floor"
                      ? "Choose floor inspiration images from your saved reference library"
                      : "Choose inspiration images from your saved reference library"}
                  </p>
                  <Button
                    onClick={() => setIsReferenceLibraryOpen(true)}
                    size="lg"
                    className="mt-2"
                  >
                    Browse Reference Library
                  </Button>
                </div>

                {referenceImages.length > 0 && (
                  <div className="mt-6">
                    <h5 className="font-medium mb-2 text-sm">Selected References</h5>
                    <div className="grid grid-cols-4 gap-2">
                      {referenceImages.map(img => (
                        <div key={img.id} className="relative aspect-square rounded-md overflow-hidden">
                          <img
                            src={img.path.startsWith('http') ? img.path : `/uploads/${img.path.split('/').pop()}`}
                            alt=""
                            className="w-full h-full object-cover"
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </div>

        {/* Reference Item Selector Dialog */}
        <ReferenceItemSelector
          isOpen={isReferenceLibraryOpen}
          onClose={() => setIsReferenceLibraryOpen(false)}
          onItemsSelected={onReferenceItemsSelected}
          multiSelect={true}
        />
      </div>
    );
  };

  // Render the modification options step
  const renderModificationOptions = () => {
    if (selectedModificationType === "replace_floor") {
      return (
        <div className="space-y-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Floor Replacement Options</h3>

          <div className="flex items-center justify-between mb-4 border-b pb-4">
            <div className="flex items-center space-x-2">
              <Label htmlFor="use-reference-image" className="font-medium text-lg">
                Use Reference Image
              </Label>
              <p className="text-sm text-muted-foreground">
                Replace floor with the exact style, material, and pattern from your reference image
              </p>
            </div>
            <Switch
              id="use-reference-image"
              checked={useReferenceImage}
              onCheckedChange={setUseReferenceImage}
            />
          </div>

          {useReferenceImage ? (
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium">Select Reference Image</h3>
                {referenceImages.length === 0 && (
                  <Button variant="outline" size="sm" onClick={() => {
                    setCurrentStep(2);
                    setActiveReferenceTab("upload");
                  }}>
                    <ImageIcon className="h-4 w-4 mr-2" />
                    Add Reference Images
                  </Button>
                )}
              </div>

              {referenceImages.length === 0 ? (
                <div className="border border-dashed rounded-lg p-8 text-center">
                  <ImageIcon className="h-10 w-10 mx-auto mb-2 text-muted-foreground" />
                  <p className="text-muted-foreground">No reference images available</p>
                  <p className="text-sm text-muted-foreground mt-1">
                    Add reference images to use as floor inspiration
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-4"
                    onClick={() => {
                      setCurrentStep(2);
                      setActiveReferenceTab("upload");
                    }}
                  >
                    Go Back to Add Images
                  </Button>
                </div>
              ) : (
                <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
                  {referenceImages.map((image) => (
                    <div
                      key={image.id}
                      className={`relative border rounded-md overflow-hidden cursor-pointer transition-all ${
                        selectedReferenceImageId === image.id
                          ? "ring-2 ring-primary border-primary"
                          : "hover:border-muted-foreground"
                      }`}
                      onClick={() => handleSelectReferenceImage(image.id)}
                    >
                      <div className="aspect-square">
                        <img
                          src={image.path.startsWith('http') ? image.path : `/uploads/${image.path.split('/').pop()}`}
                          alt={image.originalFilename || "Reference image"}
                          className="w-full h-full object-cover"
                        />
                      </div>
                      {selectedReferenceImageId === image.id && (
                        <div className="absolute top-2 right-2 bg-primary text-white rounded-full p-1">
                          <CheckCircle2 className="h-4 w-4" />
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}

              <div>
                <h3 className="text-lg font-medium mb-2">Additional Details (Optional)</h3>
                <Textarea
                  placeholder="Add any specific details about how to use the reference image (e.g., 'Use the wood pattern but make it slightly darker')"
                  value={customFloorDescription}
                  onChange={(e) => setCustomFloorDescription(e.target.value)}
                  className="min-h-20"
                />
              </div>
            </div>
          ) : (
            <>
              <div>
                <h3 className="text-lg font-medium mb-4">Select Floor Material</h3>
                <RadioGroup value={floorMaterial} onValueChange={setFloorMaterial} className="grid grid-cols-2 gap-4">
                  {floorMaterials.map((material) => (
                    <div key={material.id} className="flex items-start space-x-2">
                      <RadioGroupItem value={material.id} id={`material-${material.id}`} />
                      <div className="grid gap-1.5">
                        <Label htmlFor={`material-${material.id}`} className="font-medium">
                          {material.name}
                        </Label>
                        <p className="text-sm text-muted-foreground">{material.description}</p>
                      </div>
                    </div>
                  ))}
                </RadioGroup>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-4">Select Floor Color</h3>
                <RadioGroup value={floorColor} onValueChange={setFloorColor} className="grid grid-cols-3 gap-4">
                  {floorColors.map((color) => (
                    <div key={color.id} className="flex items-center space-x-2">
                      <RadioGroupItem value={color.id} id={`color-${color.id}`} />
                      <Label htmlFor={`color-${color.id}`}>{color.name}</Label>
                    </div>
                  ))}
                </RadioGroup>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">Additional Details (Optional)</h3>
                <Textarea
                  placeholder="Add any specific details about the floor you want (e.g., wide planks, herringbone pattern, etc.)"
                  value={customFloorDescription}
                  onChange={(e) => setCustomFloorDescription(e.target.value)}
                  className="min-h-20"
                />
              </div>
            </>
          )}
        </div>
      );
    } else if (selectedModificationType === "change_wall_color") {
      return (
        <div className="space-y-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Wall Color Options</h3>

          <div className="flex items-center justify-between mb-4 border-b pb-4">
            <div className="flex items-center space-x-2">
              <Label htmlFor="use-wall-reference-image" className="font-medium text-lg">
                Use Reference Image
              </Label>
              <p className="text-sm text-muted-foreground">
                Change wall color using a reference image
              </p>
            </div>
            <Switch
              id="use-wall-reference-image"
              checked={useWallReferenceImage}
              onCheckedChange={setUseWallReferenceImage}
            />
          </div>

          {useWallReferenceImage ? (
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium">Select Reference Image</h3>
                {referenceImages.length === 0 && (
                  <Button variant="outline" size="sm" onClick={() => {
                    setCurrentStep(2);
                    setActiveReferenceTab("upload");
                  }}>
                    <ImageIcon className="h-4 w-4 mr-2" />
                    Add Reference Images
                  </Button>
                )}
              </div>

              {referenceImages.length === 0 ? (
                <div className="border border-dashed rounded-lg p-8 text-center">
                  <ImageIcon className="h-10 w-10 mx-auto mb-2 text-muted-foreground" />
                  <p className="text-muted-foreground">No reference images available</p>
                  <p className="text-sm text-muted-foreground mt-1">
                    Add reference images to use as wall color inspiration
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-4"
                    onClick={() => {
                      setCurrentStep(2);
                      setActiveReferenceTab("upload");
                    }}
                  >
                    Go Back to Add Images
                  </Button>
                </div>
              ) : (
                <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
                  {referenceImages.map((image) => (
                    <div
                      key={image.id}
                      className={`relative border rounded-md overflow-hidden cursor-pointer transition-all ${
                        selectedWallReferenceImageId === image.id
                          ? "ring-2 ring-primary border-primary"
                          : "hover:border-muted-foreground"
                      }`}
                      onClick={() => setSelectedWallReferenceImageId(image.id === selectedWallReferenceImageId ? null : image.id)}
                    >
                      <div className="aspect-square">
                        <img
                          src={image.path.startsWith('http') ? image.path : `/uploads/${image.path.split('/').pop()}`}
                          alt={image.originalFilename || "Reference image"}
                          className="w-full h-full object-cover"
                        />
                      </div>
                      {selectedWallReferenceImageId === image.id && (
                        <div className="absolute top-2 right-2 bg-primary text-white rounded-full p-1">
                          <CheckCircle2 className="h-4 w-4" />
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}

              <div>
                <h3 className="text-lg font-medium mb-2">Additional Details (Optional)</h3>
                <Textarea
                  placeholder="Add any specific details about how to use the reference image (e.g., 'Use the blue tone but make it lighter')"
                  value={customWallDescription}
                  onChange={(e) => setCustomWallDescription(e.target.value)}
                  className="min-h-20"
                />
              </div>
            </div>
          ) : (
            <>
              <div>
                <h3 className="text-lg font-medium mb-4">Select Wall Color</h3>
                <RadioGroup value={wallColor} onValueChange={setWallColor} className="grid grid-cols-2 gap-4">
                  {wallColors.map((color) => (
                    <div key={color.id} className="flex items-start space-x-2">
                      <RadioGroupItem value={color.id} id={`wall-color-${color.id}`} />
                      <div className="grid gap-1.5">
                        <Label htmlFor={`wall-color-${color.id}`} className="font-medium">
                          {color.name}
                        </Label>
                        <p className="text-sm text-muted-foreground">{color.description}</p>
                      </div>
                    </div>
                  ))}
                </RadioGroup>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-4">Select Wall Finish (Optional)</h3>
                <RadioGroup value={wallFinish} onValueChange={setWallFinish} className="grid grid-cols-2 gap-4">
                  {wallFinishes.map((finish) => (
                    <div key={finish.id} className="flex items-start space-x-2">
                      <RadioGroupItem value={finish.id} id={`wall-finish-${finish.id}`} />
                      <div className="grid gap-1.5">
                        <Label htmlFor={`wall-finish-${finish.id}`} className="font-medium">
                          {finish.name}
                        </Label>
                        <p className="text-sm text-muted-foreground">{finish.description}</p>
                      </div>
                    </div>
                  ))}
                </RadioGroup>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">Additional Details (Optional)</h3>
                <Textarea
                  placeholder="Add any specific details about the wall color you want (e.g., accent wall, specific room, texture, etc.)"
                  value={customWallDescription}
                  onChange={(e) => setCustomWallDescription(e.target.value)}
                  className="min-h-20"
                />
              </div>
            </>
          )}
        </div>
      );
    } else if (selectedModificationType === "update_cabinets") {
      return (
        <div className="space-y-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Cabinet Update Options</h3>

          <div className="flex items-center justify-between mb-4 border-b pb-4">
            <div className="flex items-center space-x-2">
              <Label htmlFor="use-cabinet-reference-image" className="font-medium text-lg">
                Use Reference Image
              </Label>
              <p className="text-sm text-muted-foreground">
                Update cabinets using a reference image
              </p>
            </div>
            <Switch
              id="use-cabinet-reference-image"
              checked={useCabinetReferenceImage}
              onCheckedChange={setUseCabinetReferenceImage}
            />
          </div>

          {useCabinetReferenceImage ? (
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium">Select Reference Image</h3>
                {referenceImages.length === 0 && (
                  <Button variant="outline" size="sm" onClick={() => {
                    setCurrentStep(2);
                    setActiveReferenceTab("upload");
                  }}>
                    <ImageIcon className="h-4 w-4 mr-2" />
                    Add Reference Images
                  </Button>
                )}
              </div>

              {referenceImages.length === 0 ? (
                <div className="border border-dashed rounded-lg p-8 text-center">
                  <ImageIcon className="h-10 w-10 mx-auto mb-2 text-muted-foreground" />
                  <p className="text-muted-foreground">No reference images available</p>
                  <p className="text-sm text-muted-foreground mt-1">
                    Add reference images to use as cabinet inspiration
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-4"
                    onClick={() => {
                      setCurrentStep(2);
                      setActiveReferenceTab("upload");
                    }}
                  >
                    Go Back to Add Images
                  </Button>
                </div>
              ) : (
                <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
                  {referenceImages.map((image) => (
                    <div
                      key={image.id}
                      className={`relative border rounded-md overflow-hidden cursor-pointer transition-all ${
                        selectedCabinetReferenceImageId === image.id
                          ? "ring-2 ring-primary border-primary"
                          : "hover:border-muted-foreground"
                      }`}
                      onClick={() => setSelectedCabinetReferenceImageId(image.id === selectedCabinetReferenceImageId ? null : image.id)}
                    >
                      <div className="aspect-square">
                        <img
                          src={image.path.startsWith('http') ? image.path : `/uploads/${image.path.split('/').pop()}`}
                          alt={image.originalFilename || "Reference image"}
                          className="w-full h-full object-cover"
                        />
                      </div>
                      {selectedCabinetReferenceImageId === image.id && (
                        <div className="absolute top-2 right-2 bg-primary text-white rounded-full p-1">
                          <CheckCircle2 className="h-4 w-4" />
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}

              <div>
                <h3 className="text-lg font-medium mb-2">Additional Details (Optional)</h3>
                <Textarea
                  placeholder="Add any specific details about how to use the reference image (e.g., 'Use the style but change to white color')"
                  value={customCabinetDescription}
                  onChange={(e) => setCustomCabinetDescription(e.target.value)}
                  className="min-h-20"
                />
              </div>
            </div>
          ) : (
            <>
              <div>
                <h3 className="text-lg font-medium mb-4">Select Cabinet Material (Optional)</h3>
                <RadioGroup value={cabinetMaterial} onValueChange={setCabinetMaterial} className="grid grid-cols-2 gap-4">
                  {cabinetMaterials.map((material) => (
                    <div key={material.id} className="flex items-start space-x-2">
                      <RadioGroupItem value={material.id} id={`cabinet-material-${material.id}`} />
                      <div className="grid gap-1.5">
                        <Label htmlFor={`cabinet-material-${material.id}`} className="font-medium">
                          {material.name}
                        </Label>
                        <p className="text-sm text-muted-foreground">{material.description}</p>
                      </div>
                    </div>
                  ))}
                </RadioGroup>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-4">Select Cabinet Color (Optional)</h3>
                <RadioGroup value={cabinetColor} onValueChange={setCabinetColor} className="grid grid-cols-3 gap-4">
                  {cabinetColors.map((color) => (
                    <div key={color.id} className="flex items-center space-x-2">
                      <RadioGroupItem value={color.id} id={`cabinet-color-${color.id}`} />
                      <Label htmlFor={`cabinet-color-${color.id}`}>{color.name}</Label>
                    </div>
                  ))}
                </RadioGroup>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-4">Select Cabinet Style (Optional)</h3>
                <RadioGroup value={cabinetStyle} onValueChange={setCabinetStyle} className="grid grid-cols-2 gap-4">
                  {cabinetStyles.map((style) => (
                    <div key={style.id} className="flex items-start space-x-2">
                      <RadioGroupItem value={style.id} id={`cabinet-style-${style.id}`} />
                      <div className="grid gap-1.5">
                        <Label htmlFor={`cabinet-style-${style.id}`} className="font-medium">
                          {style.name}
                        </Label>
                        <p className="text-sm text-muted-foreground">{style.description}</p>
                      </div>
                    </div>
                  ))}
                </RadioGroup>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">Additional Details (Optional)</h3>
                <Textarea
                  placeholder="Add any specific details about the cabinets you want (e.g., hardware style, specific rooms, etc.)"
                  value={customCabinetDescription}
                  onChange={(e) => setCustomCabinetDescription(e.target.value)}
                  className="min-h-20"
                />
              </div>
            </>
          )}
        </div>
      );
    } else if (selectedModificationType === "kitchen_remodel") {
      return (
        <div className="space-y-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Kitchen Remodel Options</h3>

          <ReferenceImageToggle
            id="use-kitchen-reference-image"
            checked={useKitchenReferenceImage}
            onCheckedChange={setUseKitchenReferenceImage}
            title="Use Reference Image"
            description="Apply the exact cabinet style, countertops, and layout from your reference image"
          />

          {useKitchenReferenceImage ? (
            <div className="space-y-4">
              <ReferenceImageSelector
                selectedImageId={selectedKitchenReferenceImageId}
                onImageSelect={(imageId) => setSelectedKitchenReferenceImageId(imageId === selectedKitchenReferenceImageId ? null : imageId)}
                title="Select Kitchen Reference Image"
              />

              <CustomDescriptionField
                value={customKitchenDescription}
                onChange={setCustomKitchenDescription}
                placeholder="Add any specific details about how to use the reference image (e.g., 'Use the cabinet style but with different countertops')"
              />
            </div>
          ) : (
            <>
              <OptionSelector
                title="Select Cabinet Style"
                options={kitchenCabinetStyles}
                value={kitchenCabinetStyle}
                onChange={setKitchenCabinetStyle}
                columns={2}
              />

              <OptionSelector
                title="Select Countertop Material"
                options={kitchenCountertopMaterials}
                value={kitchenCountertopMaterial}
                onChange={setKitchenCountertopMaterial}
                columns={2}
              />

              <OptionSelector
                title="Select Appliance Style"
                options={kitchenApplianceStyles}
                value={kitchenApplianceStyle}
                onChange={setKitchenApplianceStyle}
                columns={2}
              />

              <OptionSelector
                title="Select Color Scheme"
                options={kitchenColorSchemes}
                value={kitchenColorScheme}
                onChange={setKitchenColorScheme}
                columns={2}
              />

              <CustomDescriptionField
                value={customKitchenDescription}
                onChange={setCustomKitchenDescription}
                placeholder="Add any specific details about the kitchen remodel (e.g., layout changes, specific appliances, etc.)"
              />
            </>
          )}
        </div>
      );
    } else if (selectedModificationType === "bathroom_remodel") {
      return (
        <div className="space-y-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Bathroom Remodel Options</h3>

          <ReferenceImageToggle
            id="use-bathroom-reference-image"
            checked={useBathroomReferenceImage}
            onCheckedChange={setUseBathroomReferenceImage}
            title="Use Reference Image"
            description="Apply the exact fixtures, tiles, and vanity style from your reference image"
          />

          {useBathroomReferenceImage ? (
            <div className="space-y-4">
              <ReferenceImageSelector
                selectedImageId={selectedBathroomReferenceImageId}
                onImageSelect={(imageId) => setSelectedBathroomReferenceImageId(imageId === selectedBathroomReferenceImageId ? null : imageId)}
                title="Select Bathroom Reference Image"
              />

              <CustomDescriptionField
                value={customBathroomDescription}
                onChange={setCustomBathroomDescription}
                placeholder="Add any specific details about how to use the reference image (e.g., 'Use the tile pattern but with different fixtures')"
              />
            </div>
          ) : (
            <>
              <OptionSelector
                title="Select Fixture Style"
                options={bathroomFixtureStyles}
                value={bathroomFixtureStyle}
                onChange={setBathroomFixtureStyle}
                columns={2}
              />

              <OptionSelector
                title="Select Tile Style"
                options={bathroomTileStyles}
                value={bathroomTileStyle}
                onChange={setBathroomTileStyle}
                columns={2}
              />

              <OptionSelector
                title="Select Vanity Style"
                options={bathroomVanityStyles}
                value={bathroomVanityStyle}
                onChange={setBathroomVanityStyle}
                columns={2}
              />

              <OptionSelector
                title="Select Color Scheme"
                options={bathroomColorSchemes}
                value={bathroomColorScheme}
                onChange={setBathroomColorScheme}
                columns={2}
              />

              <CustomDescriptionField
                value={customBathroomDescription}
                onChange={setCustomBathroomDescription}
                placeholder="Add any specific details about the bathroom remodel (e.g., spa features, storage needs, etc.)"
              />
            </>
          )}
        </div>
      );
    } else if (selectedModificationType === "interior_painting") {
      return (
        <div className="space-y-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Interior Painting Options</h3>

          <ReferenceImageToggle
            id="use-interior-reference-image"
            checked={useInteriorReferenceImage}
            onCheckedChange={setUseInteriorReferenceImage}
            title="Use Reference Image"
            description="Apply the exact paint colors and finishes from your reference image"
          />

          {useInteriorReferenceImage ? (
            <div className="space-y-4">
              <ReferenceImageSelector
                selectedImageId={selectedInteriorReferenceImageId}
                onImageSelect={(imageId) => setSelectedInteriorReferenceImageId(imageId === selectedInteriorReferenceImageId ? null : imageId)}
                title="Select Interior Painting Reference Image"
              />

              <CustomDescriptionField
                value={customInteriorDescription}
                onChange={setCustomInteriorDescription}
                placeholder="Add any specific details about how to use the reference image (e.g., 'Use the color palette but apply to different walls')"
              />
            </div>
          ) : (
            <>
              <OptionSelector
                title="Select Wall Color"
                options={interiorWallColors}
                value={interiorWallColor}
                onChange={setInteriorWallColor}
                columns={2}
              />

              <OptionSelector
                title="Select Ceiling Color"
                options={interiorCeilingColors}
                value={interiorCeilingColor}
                onChange={setInteriorCeilingColor}
                columns={2}
              />

              <OptionSelector
                title="Select Accent Wall"
                options={interiorAccentWalls}
                value={interiorAccentWall}
                onChange={setInteriorAccentWall}
                columns={2}
              />

              <OptionSelector
                title="Select Paint Finish"
                options={interiorFinishes}
                value={interiorFinish}
                onChange={setInteriorFinish}
                columns={2}
              />

              <CustomDescriptionField
                value={customInteriorDescription}
                onChange={setCustomInteriorDescription}
                placeholder="Add any specific details about the painting (e.g., specific rooms, accent walls, etc.)"
              />
            </>
          )}
        </div>
      );
    } else if (selectedModificationType === "lighting_upgrades") {
      return (
        <div className="space-y-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Lighting Upgrade Options</h3>

          <ReferenceImageToggle
            id="use-lighting-reference-image"
            checked={useLightingReferenceImage}
            onCheckedChange={setUseLightingReferenceImage}
            title="Use Reference Image"
            description="Apply the exact lighting fixtures and placement from your reference image"
          />

          {useLightingReferenceImage ? (
            <div className="space-y-4">
              <ReferenceImageSelector
                selectedImageId={selectedLightingReferenceImageId}
                onImageSelect={(imageId) => setSelectedLightingReferenceImageId(imageId === selectedLightingReferenceImageId ? null : imageId)}
                title="Select Lighting Reference Image"
              />

              <CustomDescriptionField
                value={customLightingDescription}
                onChange={setCustomLightingDescription}
                placeholder="Add any specific details about how to use the reference image (e.g., 'Use the fixture style but with different placement')"
              />
            </div>
          ) : (
            <>
              <OptionSelector
                title="Select Fixture Style"
                options={lightingFixtureStyles}
                value={lightingFixtureStyle}
                onChange={setLightingFixtureStyle}
                columns={2}
              />

              <OptionSelector
                title="Select Lighting Type"
                options={lightingTypes}
                value={lightingType}
                onChange={setLightingType}
                columns={2}
              />

              <OptionSelector
                title="Select Ambiance"
                options={lightingAmbianceOptions}
                value={lightingAmbiance}
                onChange={setLightingAmbiance}
                columns={2}
              />

              <div>
                <h3 className="text-lg font-medium mb-4">Smart Controls</h3>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="smart-controls"
                    checked={lightingSmartControls}
                    onCheckedChange={setLightingSmartControls}
                  />
                  <Label htmlFor="smart-controls" className="font-medium">
                    Include Smart Lighting Controls
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    Add smart switches and dimming capabilities
                  </p>
                </div>
              </div>

              <CustomDescriptionField
                value={customLightingDescription}
                onChange={setCustomLightingDescription}
                placeholder="Add any specific details about the lighting upgrades (e.g., specific rooms, energy efficiency features, etc.)"
              />
            </>
          )}
        </div>
      );
    } else if (selectedModificationType === "exterior_repainting") {
      return (
        <div className="space-y-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Exterior Repainting Options</h3>

          <ReferenceImageToggle
            id="use-exterior-reference-image"
            checked={useExteriorReferenceImage}
            onCheckedChange={setUseExteriorReferenceImage}
            title="Use Reference Image"
            description="Apply the exact exterior colors and paint scheme from your reference image"
          />

          {useExteriorReferenceImage ? (
            <div className="space-y-4">
              <ReferenceImageSelector
                selectedImageId={selectedExteriorReferenceImageId}
                onImageSelect={(imageId) => setSelectedExteriorReferenceImageId(imageId === selectedExteriorReferenceImageId ? null : imageId)}
                title="Select Exterior Painting Reference Image"
              />

              <CustomDescriptionField
                value={customExteriorDescription}
                onChange={setCustomExteriorDescription}
                placeholder="Add any specific details about how to use the reference image (e.g., 'Use the color scheme but with different trim colors')"
              />
            </div>
          ) : (
            <>
              <OptionSelector
                title="Select Main Color"
                options={exteriorMainColors}
                value={exteriorMainColor}
                onChange={setExteriorMainColor}
                columns={2}
              />

              <OptionSelector
                title="Select Trim Color"
                options={exteriorTrimColors}
                value={exteriorTrimColor}
                onChange={setExteriorTrimColor}
                columns={2}
              />

              <OptionSelector
                title="Select Accent Color"
                options={exteriorAccentColors}
                value={exteriorAccentColor}
                onChange={setExteriorAccentColor}
                columns={2}
              />

              <OptionSelector
                title="Select Door Color"
                options={exteriorDoorColors}
                value={exteriorDoorColor}
                onChange={setExteriorDoorColor}
                columns={2}
              />

              <CustomDescriptionField
                value={customExteriorDescription}
                onChange={setCustomExteriorDescription}
                placeholder="Add any specific details about the exterior painting (e.g., specific architectural features, weatherproofing, etc.)"
              />
            </>
          )}
        </div>
      );
    } else if (selectedModificationType === "custom") {
      return (
        <div className="space-y-6">
          <h3 className="text-lg font-medium text-gray-900 mb-2">Describe Your Changes</h3>
          <p className="text-gray-600 text-sm mb-4">
            Provide a detailed description of all the changes you want to make to your image.
          </p>

          {referenceImages.length > 0 && (
            <div className="mb-6 border rounded-lg p-4 bg-gray-50">
              <h4 className="font-medium text-gray-700 mb-2">Selected Reference Images</h4>
              <p className="text-sm text-gray-600 mb-3">
                Your reference images will be used to inspire the modifications.
              </p>
              <div className="grid grid-cols-2 sm:grid-cols-4 gap-3">
                {referenceImages.map((image) => (
                  <div
                    key={image.id}
                    className={`relative border rounded-md overflow-hidden cursor-pointer transition-all ${
                      selectedReferenceImageId === image.id
                        ? "ring-2 ring-primary border-primary"
                        : "hover:border-muted-foreground"
                    }`}
                    onClick={() => handleSelectReferenceImage(image.id)}
                  >
                    <div className="aspect-square">
                      <img
                        src={image.path.startsWith('http') ? image.path : `/uploads/${image.path.split('/').pop()}`}
                        alt={image.originalFilename || "Reference image"}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    {selectedReferenceImageId === image.id && (
                      <div className="absolute top-2 right-2 bg-primary text-white rounded-full p-1">
                        <CheckCircle2 className="h-4 w-4" />
                      </div>
                    )}
                  </div>
                ))}
              </div>

              {selectedReferenceImageId && (
                <p className="text-sm text-gray-600 mt-2">
                  Selected reference image will be prioritized for your custom modification.
                </p>
              )}

              <div className="mt-3 flex justify-end">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setCurrentStep(2);
                    setActiveReferenceTab("upload");
                  }}
                >
                  <ImageIcon className="h-4 w-4 mr-2" />
                  Manage Reference Images
                </Button>
              </div>
            </div>
          )}

          <Textarea
            placeholder="Describe in detail the modifications you want (e.g., 'Change the kitchen cabinets to white, add a wooden countertop, and replace the floor with dark hardwood')"
            className="min-h-32"
            value={customDescription}
            onChange={(e) => setCustomDescription(e.target.value)}
          />

          {referenceImages.length === 0 && (
            <div className="mt-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setCurrentStep(2);
                  setActiveReferenceTab("upload");
                }}
              >
                <ImageIcon className="h-4 w-4 mr-2" />
                Add Reference Images
              </Button>
              <p className="text-sm text-gray-500 mt-2">
                Adding reference images can help achieve better results
              </p>
            </div>
          )}
        </div>
      );
    }

    return null;
  };

  // Render the review step
  const renderReview = () => {
    return (
      <div className="space-y-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Review Your Visualization Request</h3>

        <div className="border rounded-lg p-6 space-y-4">
          <div>
            <h4 className="font-medium text-gray-700">Modification Type</h4>
            <p className="mt-1">
              {modificationTypes.find(t => t.id === selectedModificationType)?.name || "Custom Modification"}
            </p>
          </div>

          <div>
            <h4 className="font-medium text-gray-700">Before Images</h4>
            <div className="grid grid-cols-4 gap-2 mt-2">
              {beforeImages.map(img => (
                <div key={img.id} className="relative aspect-square rounded-md overflow-hidden">
                  <img
                    src={img.path.startsWith('http') ? img.path : `/uploads/${img.path.split('/').pop()}`}
                    alt=""
                    className="w-full h-full object-cover"
                  />
                </div>
              ))}
            </div>
          </div>

          {referenceImages.length > 0 && (
            <div>
              <h4 className="font-medium text-gray-700">Reference Images</h4>
              <div className="grid grid-cols-4 gap-2 mt-2">
                {referenceImages.map(img => (
                  <div
                    key={img.id}
                    className={`relative aspect-square rounded-md overflow-hidden ${
                      (selectedModificationType === "replace_floor" && selectedReferenceImageId === img.id) ||
                      (selectedModificationType === "change_wall_color" && selectedWallReferenceImageId === img.id) ||
                      (selectedModificationType === "update_cabinets" && selectedCabinetReferenceImageId === img.id) ||
                      (selectedModificationType === "custom" &&
                        (selectedReferenceImageId === img.id ||
                         (!selectedReferenceImageId && img.id === referenceImages[0].id)))
                        ? "ring-2 ring-primary"
                        : ""
                    }`}
                  >
                    <img
                      src={img.path.startsWith('http') ? img.path : `/uploads/${img.path.split('/').pop()}`}
                      alt=""
                      className="w-full h-full object-cover"
                    />
                    {((selectedModificationType === "replace_floor" && selectedReferenceImageId === img.id) ||
                      (selectedModificationType === "change_wall_color" && selectedWallReferenceImageId === img.id) ||
                      (selectedModificationType === "update_cabinets" && selectedCabinetReferenceImageId === img.id) ||
                      (selectedModificationType === "custom" &&
                        (selectedReferenceImageId === img.id ||
                         (!selectedReferenceImageId && img.id === referenceImages[0].id)))) && (
                      <div className="absolute top-2 right-2 bg-primary text-white rounded-full p-1">
                        <CheckCircle2 className="h-4 w-4" />
                      </div>
                    )}
                  </div>
                ))}
              </div>
              {selectedModificationType === "custom" && (
                <p className="text-sm text-gray-600 mt-2">
                  {selectedReferenceImageId
                    ? "The highlighted reference image will be prioritized for your custom modification."
                    : referenceImages.length > 0
                      ? "The first reference image will be prioritized for your custom modification."
                      : ""}
                </p>
              )}
            </div>
          )}

          <div>
            <h4 className="font-medium text-gray-700">Description</h4>
            <p className="mt-1 text-gray-600">{generatedDescription}</p>
          </div>

          {selectedModificationType === "replace_floor" && (
            <div>
              <h4 className="font-medium text-gray-700">Floor Options</h4>
              {useReferenceImage ? (
                <p className="mt-1 text-gray-600">Using reference image for floor replacement</p>
              ) : (
                <p className="mt-1 text-gray-600">
                  {floorColor} {floorMaterialName(floorMaterial)} flooring
                </p>
              )}
              {customFloorDescription && (
                <p className="mt-1 text-gray-600">Additional details: {customFloorDescription}</p>
              )}
            </div>
          )}

          {selectedModificationType === "change_wall_color" && (
            <div>
              <h4 className="font-medium text-gray-700">Wall Color Options</h4>
              {useWallReferenceImage ? (
                <p className="mt-1 text-gray-600">Using reference image for wall color</p>
              ) : (
                <p className="mt-1 text-gray-600">
                  {wallColorName(wallColor)}{wallFinish ? ` with ${wallFinishName(wallFinish)} finish` : ""}
                </p>
              )}
              {customWallDescription && (
                <p className="mt-1 text-gray-600">Additional details: {customWallDescription}</p>
              )}
            </div>
          )}

          {selectedModificationType === "update_cabinets" && (
            <div>
              <h4 className="font-medium text-gray-700">Cabinet Options</h4>
              {useCabinetReferenceImage ? (
                <p className="mt-1 text-gray-600">Using reference image for cabinet updates</p>
              ) : (
                <div className="mt-1 text-gray-600">
                  {cabinetColor && <p>Color: {cabinetColorName(cabinetColor)}</p>}
                  {cabinetMaterial && <p>Material: {cabinetMaterialName(cabinetMaterial)}</p>}
                  {cabinetStyle && <p>Style: {cabinetStyleName(cabinetStyle)}</p>}
                </div>
              )}
              {customCabinetDescription && (
                <p className="mt-1 text-gray-600">Additional details: {customCabinetDescription}</p>
              )}
            </div>
          )}
        </div>

        <div>
          <h4 className="font-medium text-gray-700 mb-2">Additional Details (Optional)</h4>
          <Textarea
            value={customDescription}
            onChange={(e) => setCustomDescription(e.target.value)}
            className="min-h-20"
            placeholder="Add any specific details or preferences for your renovation"
          />
        </div>
      </div>
    );
  };

  // Upload before images mutation
  // const uploadBeforeImagesMutation = useMutation({
  //   mutationFn: ({ projectId, files }: { projectId: number; files: File[] }) =>
  //     uploadBeforeImages(projectId, files),
  //   onSuccess: (data) => {
  //     setBeforeImages(data);
  //     toast({
  //       title: "Before images uploaded successfully",
  //       description: `${data.length} before ${data.length === 1 ? 'image' : 'images'} uploaded`,
  //     });
  //   },
  //   onError: (error) => {
  //     toast({
  //       title: "Error uploading before images",
  //       description: error.message,
  //       variant: "destructive",
  //     });
  //   },
  // });

  // Upload reference images mutation
  const uploadReferenceImagesMutation = useMutation({
    mutationFn: ({ projectId, files }: { projectId: number; files: File[] }) =>
      uploadReferenceImages(projectId, files),
    onSuccess: (data) => {
      setReferenceImages(prev => {
        const newImages = [...prev, ...data];

        // Auto-select the first reference image if none is selected
        if (data.length > 0) {
          if (selectedModificationType === "replace_floor" && !selectedReferenceImageId && useReferenceImage) {
            setSelectedReferenceImageId(data[0].id);
          } else if (selectedModificationType === "change_wall_color" && !selectedWallReferenceImageId && useWallReferenceImage) {
            setSelectedWallReferenceImageId(data[0].id);
          } else if (selectedModificationType === "update_cabinets" && !selectedCabinetReferenceImageId && useCabinetReferenceImage) {
            setSelectedCabinetReferenceImageId(data[0].id);
          } else if (selectedModificationType === "kitchen_remodel" && !selectedKitchenReferenceImageId && useKitchenReferenceImage) {
            setSelectedKitchenReferenceImageId(data[0].id);
          } else if (selectedModificationType === "bathroom_remodel" && !selectedBathroomReferenceImageId && useBathroomReferenceImage) {
            setSelectedBathroomReferenceImageId(data[0].id);
          } else if (selectedModificationType === "interior_painting" && !selectedInteriorReferenceImageId && useInteriorReferenceImage) {
            setSelectedInteriorReferenceImageId(data[0].id);
          } else if (selectedModificationType === "lighting_upgrades" && !selectedLightingReferenceImageId && useLightingReferenceImage) {
            setSelectedLightingReferenceImageId(data[0].id);
          } else if (selectedModificationType === "exterior_repainting" && !selectedExteriorReferenceImageId && useExteriorReferenceImage) {
            setSelectedExteriorReferenceImageId(data[0].id);
          } else if (selectedModificationType === "custom" && !selectedReferenceImageId) {
            setSelectedReferenceImageId(data[0].id);
          }
        }

        return newImages;
      });

      toast({
        title: "Reference images uploaded successfully",
        description: `${data.length} reference ${data.length === 1 ? 'image' : 'images'} uploaded`,
      });
    },
    onError: (error) => {
      toast({
        title: "Error uploading reference images",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Handle selecting items from reference library
  const handleReferenceItemsSelected = async (items: ReferenceItem[]) => {
    if (!projectId || items.length === 0) return;

    try {
      toast({
        title: "Processing reference items",
        description: "Please wait while we process your selected items...",
      });

      console.log("Original selected items:", JSON.stringify(items, null, 2));

      // Create temporary reference objects directly from the selected items
      const tempReferenceImages = items.map(item => {
        const image: UploadedImageType = {
          id: item.id,
          path: item.imagePath,
          originalFilename: item.name,
          type: "reference",
          projectId: projectId
        };
        return image;
      });

      console.log("Mapped reference images:", JSON.stringify(tempReferenceImages, null, 2));

      // Update state with the newly mapped reference images
      setReferenceImages(prev => {
        const newImages = [...prev, ...tempReferenceImages];

        // Auto-select the first reference image if none is selected and we're in reference mode
        if (tempReferenceImages.length > 0) {
          if (selectedModificationType === "replace_floor" && !selectedReferenceImageId && useReferenceImage) {
            setSelectedReferenceImageId(tempReferenceImages[0].id);
          } else if (selectedModificationType === "change_wall_color" && !selectedWallReferenceImageId && useWallReferenceImage) {
            setSelectedWallReferenceImageId(tempReferenceImages[0].id);
          } else if (selectedModificationType === "update_cabinets" && !selectedCabinetReferenceImageId && useCabinetReferenceImage) {
            setSelectedCabinetReferenceImageId(tempReferenceImages[0].id);
          } else if (selectedModificationType === "kitchen_remodel" && !selectedKitchenReferenceImageId && useKitchenReferenceImage) {
            setSelectedKitchenReferenceImageId(tempReferenceImages[0].id);
          } else if (selectedModificationType === "bathroom_remodel" && !selectedBathroomReferenceImageId && useBathroomReferenceImage) {
            setSelectedBathroomReferenceImageId(tempReferenceImages[0].id);
          } else if (selectedModificationType === "interior_painting" && !selectedInteriorReferenceImageId && useInteriorReferenceImage) {
            setSelectedInteriorReferenceImageId(tempReferenceImages[0].id);
          } else if (selectedModificationType === "lighting_upgrades" && !selectedLightingReferenceImageId && useLightingReferenceImage) {
            setSelectedLightingReferenceImageId(tempReferenceImages[0].id);
          } else if (selectedModificationType === "exterior_repainting" && !selectedExteriorReferenceImageId && useExteriorReferenceImage) {
            setSelectedExteriorReferenceImageId(tempReferenceImages[0].id);
          } else if (selectedModificationType === "custom" && !selectedReferenceImageId) {
            setSelectedReferenceImageId(tempReferenceImages[0].id);
          }
        }

        return newImages;
      });

      toast({
        title: "Reference items added",
        description: `${items.length} reference ${items.length === 1 ? 'item' : 'items'} added from your library`,
      });

      // Switch to the library tab
      setActiveTab("library");

      // Close the reference library dialog
      setIsReferenceLibraryOpen(false);
    } catch (error) {
      toast({
        title: "Error adding reference items",
        description: "Could not process the selected reference items.",
        variant: "destructive",
      });
      console.error("Error processing reference items:", error);
    }
  };

  // Handle reference image uploads
  const handleReferenceImageUpload = (files: File[]) => {
    if (!projectId) return;
    // logger.info("Uploading reference images", { projectId, fileCount: files.length });
    try {
      uploadReferenceImagesMutation.mutate({ projectId, files });
    } catch (error) {
      // logger.error("Error in handleReferenceImageUpload", error);
      toast({
        title: "Unexpected error",
        description: (error as Error).message || "An unexpected error occurred.",
        variant: "destructive",
      });
    }
  };

  // Render the navigation buttons
  const renderNavigation = () => {
    return (
      <div className="flex justify-between mt-8">
        {currentStep > 1 ? (
          <Button
            type="button"
            variant="outline"
            onClick={() => setCurrentStep(currentStep - 1)}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
        ) : (
          <div></div> // Empty div to maintain flex spacing
        )}

        {currentStep < 4 ? (
          <Button
            type="button"
            onClick={() => setCurrentStep(currentStep + 1)}
            disabled={!canProceedToNextStep()}
          >
            Next
            <ArrowRight className="h-4 w-4 ml-2" />
          </Button>
        ) : (
          <Button
            type="button"
            onClick={handleComplete}
            disabled={!generatedDescription}
          >
            Create Visualization
          </Button>
        )}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Progress indicator */}
      <div className="mb-8">
        <div className="flex justify-between mb-2">
          {["Choose Modification", "Upload Images", "Customize", "Review"].map((step, index) => (
            <div
              key={index}
              className={`text-sm font-medium ${
                currentStep >= index + 1 ? "text-primary" : "text-gray-400"
              }`}
            >
              {step}
            </div>
          ))}
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-primary h-2 rounded-full transition-all duration-300"
            style={{ width: `${(currentStep / 4) * 100}%` }}
          ></div>
        </div>
      </div>

      {/* Step content */}
      {renderStepContent()}

      {/* Navigation */}
      {renderNavigation()}
    </div>
  );
}