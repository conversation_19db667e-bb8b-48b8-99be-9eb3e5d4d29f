import { Card, CardContent } from "@/components/ui/card";

interface FeatureCardProps {
  icon: string;
  title: string;
  description: string;
}

export default function FeatureCard({ icon, title, description }: FeatureCardProps) {
  return (
    <Card className="shadow-sm">
      <CardContent className="p-6 flex flex-col items-center text-center">
        <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mb-4">
          <span className="material-icons text-primary-600">{icon}</span>
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">{title}</h3>
        <p className="text-gray-600 text-sm">{description}</p>
      </CardContent>
    </Card>
  );
}
