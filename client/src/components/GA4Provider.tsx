/**
 * Google Analytics 4 Provider Component
 * 
 * This component initializes GA4 and provides analytics context to the entire
 * application. It handles automatic page view tracking and user identification.
 */

import React, { createContext, useContext, ReactNode } from 'react';
import { useGA4 } from '@/hooks/use-ga4';
import { logger } from '@/utils/logger';

// GA4 Context Type
interface GA4ContextType {
  trackEvent: (params: {
    action: string;
    category?: string;
    label?: string;
    value?: number;
    custom_parameters?: Record<string, any>;
  }) => void;
  trackAuth: (action: 'sign_up' | 'sign_in' | 'sign_out') => void;
  trackProject: (
    action: 'project_created' | 'project_completed' | 'image_generated' | 'image_generation_failed',
    projectId?: string,
    additionalParams?: Record<string, any>
  ) => void;
  trackPayment: (
    action: 'checkout_started' | 'checkout_completed' | 'subscription_created' | 'subscription_cancelled',
    additionalParams?: Record<string, any>
  ) => void;
  trackError: (
    errorType: string,
    errorMessage: string,
    additionalParams?: Record<string, any>
  ) => void;
  isReady: boolean;
  userId: string | null;
}

// Create GA4 Context
const GA4Context = createContext<GA4ContextType | null>(null);

// GA4 Provider Props
interface GA4ProviderProps {
  children: ReactNode;
}

/**
 * GA4 Provider Component
 * 
 * Wraps the application and provides GA4 functionality through context.
 * Automatically handles initialization and page view tracking.
 */
export const GA4Provider: React.FC<GA4ProviderProps> = ({ children }) => {
  const ga4 = useGA4();

  // Log GA4 initialization status
  React.useEffect(() => {
    if (ga4.isReady) {
      logger.info('GA4 Provider initialized and ready', 'analytics', {
        userId: ga4.userId,
        measurementId: import.meta.env.VITE_GA4_MEASUREMENT_ID,
      });
    } else {
      logger.debug('GA4 Provider initialized but not ready', 'analytics', {
        reason: 'GA4 not enabled or not in production',
        environment: import.meta.env.NODE_ENV,
        measurementId: import.meta.env.VITE_GA4_MEASUREMENT_ID,
      });
    }
  }, [ga4.isReady, ga4.userId]);

  const contextValue: GA4ContextType = {
    trackEvent: ga4.trackEvent,
    trackAuth: ga4.trackAuth,
    trackProject: ga4.trackProject,
    trackPayment: ga4.trackPayment,
    trackError: ga4.trackError,
    isReady: ga4.isReady,
    userId: ga4.userId,
  };

  return (
    <GA4Context.Provider value={contextValue}>
      {children}
    </GA4Context.Provider>
  );
};

/**
 * Hook to use GA4 context
 * 
 * Provides access to GA4 tracking functions from any component
 * within the GA4Provider tree.
 */
export const useGA4Context = (): GA4ContextType => {
  const context = useContext(GA4Context);
  
  if (!context) {
    throw new Error('useGA4Context must be used within a GA4Provider');
  }
  
  return context;
};

/**
 * Higher-Order Component for GA4 tracking
 * 
 * Wraps a component and provides GA4 tracking capabilities.
 * Useful for components that need analytics but don't want to use hooks.
 */
export const withGA4 = <P extends object>(
  Component: React.ComponentType<P>
): React.FC<P & { ga4?: GA4ContextType }> => {
  const WrappedComponent: React.FC<P & { ga4?: GA4ContextType }> = (props) => {
    const ga4 = useGA4Context();
    
    return <Component {...props} ga4={ga4} />;
  };
  
  WrappedComponent.displayName = `withGA4(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
};

/**
 * Component for tracking specific events on mount
 * 
 * Useful for tracking page-specific events or component visibility.
 */
interface GA4EventTrackerProps {
  event: {
    action: string;
    category?: string;
    label?: string;
    value?: number;
    custom_parameters?: Record<string, any>;
  };
  trackOnMount?: boolean;
  trackOnUnmount?: boolean;
  children?: ReactNode;
}

export const GA4EventTracker: React.FC<GA4EventTrackerProps> = ({
  event,
  trackOnMount = true,
  trackOnUnmount = false,
  children,
}) => {
  const { trackEvent, isReady } = useGA4Context();

  React.useEffect(() => {
    if (trackOnMount && isReady) {
      trackEvent(event);
      logger.debug('GA4 event tracked on mount', 'analytics', event);
    }

    return () => {
      if (trackOnUnmount && isReady) {
        trackEvent({
          ...event,
          action: `${event.action}_end`,
        });
        logger.debug('GA4 event tracked on unmount', 'analytics', event);
      }
    };
  }, [trackOnMount, trackOnUnmount, isReady, trackEvent, event]);

  return <>{children}</>;
};

export default GA4Provider;
