import { Link } from "wouter";
import { NewsletterSubscription } from "@/components/NewsletterSubscription";
import { Facebook, Instagram, Twitter } from "lucide-react";
import { useIsMobile } from "@/hooks/use-mobile";
import { LinkWithScroll } from "@/components/ScrollToTop";

const Footer = () => {
  const isMobile = useIsMobile();

  return (
    <footer className="bg-gray-800 text-white mt-auto w-full">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-10">
        {/* Main footer content */}
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6">
          {/* Logo and social links */}
          <div className="col-span-1 sm:col-span-2 md:col-span-1">
            <LinkWithScroll href="/">
              <div className="flex items-center mb-4 cursor-pointer">
                <img
                  src="/images/logo/Renovision_white_transparent.png"
                  alt="Renovision.Studio"
                  className="h-auto w-40 sm:w-48"
                />
              </div>
            </LinkWithScroll>
            <p className="text-gray-400 text-sm">Helping tradespeople visualize renovations with AI technology.</p>
            <div className="mt-4 flex space-x-4">
              <a
                href="https://www.facebook.com/renovision/"
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-400 hover:text-white transition-colors p-2 -m-2"
              >
                <span className="sr-only">Facebook</span>
                <Facebook size={24} />
              </a>
              <a
                href="https://www.instagram.com/renovision_studio/"
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-400 hover:text-white transition-colors p-2 -m-2"
              >
                <span className="sr-only">Instagram</span>
                <Instagram size={24} />
              </a>
            </div>
          </div>

          {/* Product links */}
          <div className="mt-4 sm:mt-0">
            <h3 className="text-sm font-semibold text-gray-400 uppercase tracking-wider mb-4">Product</h3>
            <ul className="space-y-3">
              <li>
                <LinkWithScroll href="/">
                  <span className="text-gray-300 hover:text-white text-sm cursor-pointer inline-block py-1">Features</span>
                </LinkWithScroll>
              </li>
              <li>
                <LinkWithScroll href="/pricing">
                  <span className="text-gray-300 hover:text-white text-sm cursor-pointer inline-block py-1">Pricing</span>
                </LinkWithScroll>
              </li>
              <li>
                <LinkWithScroll href="/gallery">
                  <span className="text-gray-300 hover:text-white text-sm cursor-pointer inline-block py-1">Examples</span>
                </LinkWithScroll>
              </li>
              <li>
                <LinkWithScroll href="/customer-stories">
                  <span className="text-gray-300 hover:text-white text-sm cursor-pointer inline-block py-1">Customer Stories</span>
                </LinkWithScroll>
              </li>
            </ul>
          </div>

          {/* Support links */}
          <div className="mt-4 sm:mt-0">
            <h3 className="text-sm font-semibold text-gray-400 uppercase tracking-wider mb-4">Support</h3>
            <ul className="space-y-3">
              <li>
                <LinkWithScroll href="/documentation">
                  <span className="text-gray-300 hover:text-white text-sm cursor-pointer inline-block py-1">Documentation</span>
                </LinkWithScroll>
              </li>
              <li>
                <LinkWithScroll href="/tutorials">
                  <span className="text-gray-300 hover:text-white text-sm cursor-pointer inline-block py-1">Tutorials</span>
                </LinkWithScroll>
              </li>
              <li>
                <LinkWithScroll href="/faqs">
                  <span className="text-gray-300 hover:text-white text-sm cursor-pointer inline-block py-1">FAQs</span>
                </LinkWithScroll>
              </li>
              <li>
                <LinkWithScroll href="/contact">
                  <span className="text-gray-300 hover:text-white text-sm cursor-pointer inline-block py-1">Contact Us</span>
                </LinkWithScroll>
              </li>
            </ul>
          </div>

          {/* Newsletter */}
          <div className="mt-6 sm:mt-0">
            <h3 className="text-sm font-semibold text-gray-400 uppercase tracking-wider mb-3">Stay Updated</h3>
            <NewsletterSubscription
              variant="inline"
              title=""
              description=""
              className="text-white"
            />
          </div>
        </div>

        {/* Footer bottom */}
        <div className="mt-6 pt-6 border-t border-gray-700 flex flex-col md:flex-row justify-between items-center">
          {/* Copyright */}
          <div className="flex flex-col space-y-1 text-center md:text-left">
            <p className="text-gray-400 text-sm">&copy; {new Date().getFullYear()} Renovision.Studio. All rights reserved.</p>
            <p className="text-gray-400 text-sm">Made with <span className="text-red-500">❤️</span> in Canberra, Australia</p>
          </div>

          {/* Legal links */}
          <div className="mt-4 md:mt-0 flex flex-wrap justify-center md:justify-end gap-4 md:gap-6">
            <LinkWithScroll href="/privacy-policy">
              <span className="text-gray-400 hover:text-white text-sm cursor-pointer py-1">Privacy Policy</span>
            </LinkWithScroll>
            <LinkWithScroll href="/terms-of-service">
              <span className="text-gray-400 hover:text-white text-sm cursor-pointer py-1">Terms of Service</span>
            </LinkWithScroll>
            <LinkWithScroll href="/cookie-policy">
              <span className="text-gray-400 hover:text-white text-sm cursor-pointer py-1">Cookie Policy</span>
            </LinkWithScroll>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
