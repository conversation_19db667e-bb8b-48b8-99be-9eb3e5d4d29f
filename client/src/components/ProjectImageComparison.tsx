import { ImageComparison } from "@/components/ui/image-comparison";
import type { Image as ImageType } from "@shared/schema";
import { normalizeImagePath } from "@/utils/image-utils";

interface ProjectImageComparisonProps {
  beforeImage: string | ImageType;
  afterImage: string | ImageType;
  beforeAlt?: string;
  afterAlt?: string;
  className?: string;
}

/**
 * A standardized component for project image comparisons that handles
 * different image path formats consistently across the application.
 */
export function ProjectImageComparison({
  beforeImage,
  afterImage,
  beforeAlt = "Before image",
  afterAlt = "After image",
  className = "",
}: ProjectImageComparisonProps) {
  // Check if the after image is an error (text file)
  const isAfterImageError =
    typeof afterImage === 'object' &&
    afterImage &&
    'path' in afterImage &&
    afterImage.path.endsWith('.txt');

  // Determine if this is an after image that should go in the generated folder
  const isAfterImageGenerated =
    !isAfterImageError &&
    typeof afterImage === 'object' &&
    afterImage &&
    'path' in afterImage &&
    !afterImage.path.startsWith('http') &&
    !afterImage.path.startsWith('/uploads/generated/');

  // Normalize image paths
  let beforeImagePath = normalizeImagePath(beforeImage);
  let afterImagePath = isAfterImageError
    ? normalizeImagePath(afterImage) // For error text files, use regular path
    : isAfterImageGenerated
      ? `/uploads/generated/${afterImage.path.split('/').pop()}`
      : normalizeImagePath(afterImage);

  // Log paths for debugging
  console.log('ProjectImageComparison - beforeImagePath:', beforeImagePath);
  console.log('ProjectImageComparison - afterImagePath:', afterImagePath);

  return (
    <ImageComparison
      beforeImage={beforeImagePath}
      afterImage={afterImagePath}
      beforeAlt={beforeAlt}
      afterAlt={afterAlt}
      className={`select-none h-full w-full ${className}`}
    />
  );
}
