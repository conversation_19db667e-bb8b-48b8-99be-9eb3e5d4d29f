import { Switch, Route } from "wouter";
import { Toaster } from "@/components/ui/toaster";
import NotFound from "@/pages/not-found";
import Dashboard from "@/pages/Dashboard";
import CreateVisualization from "@/pages/CreateVisualization";
import Project from "@/pages/Project";
import Gallery from "@/pages/Gallery";
import Pricing from "@/pages/Pricing";
import Profile from "@/pages/Profile";
import Settings from "@/pages/Settings";
import ReferenceLibrary from "@/pages/ReferenceLibrary";
import ReferenceCategory from "@/pages/ReferenceCategory";
import RenovationPresets from "@/pages/RenovationPresets";
import SignInPage from "@/pages/SignInPage";  // Import the custom sign-in page
import SignUpPage from "@/pages/SignUpPage";  // Import the custom sign-up page
import LandingPage from "@/pages/LandingPage"; // Import the landing page
import ContactUs from "@/pages/ContactUs"; // Import the contact us page
import Checkout from "@/pages/Checkout"; // Import the checkout page
import PaymentSuccess from "@/pages/PaymentSuccess"; // Import the payment success page
import Account from "@/pages/Account"; // Import the account page
import CustomerStories from "@/pages/CustomerStories"; // Import the customer stories page
import Documentation from "@/pages/Documentation"; // Import the documentation page
import Tutorials from "@/pages/Tutorials"; // Import the tutorials page
import FAQs from "@/pages/FAQs"; // Import the FAQs page
import PrivacyPolicy from "@/pages/PrivacyPolicy"; // Import the privacy policy page
import TermsOfService from "@/pages/TermsOfService"; // Import the terms of service page
import CookiePolicy from "@/pages/CookiePolicy"; // Import the cookie policy page
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import {
  SignedIn,
  SignedOut,
  useClerk
} from "@clerk/clerk-react";
import { Loader2 } from "lucide-react";
import { useEffect, useState, lazy, Suspense } from "react";
import { useClerkAuth } from "@/hooks/use-clerk-auth";
import { PageTransition } from "@/components/ui/page-transition";
import { useLocation } from "wouter";
import { SubscriptionProvider } from "@/contexts/SubscriptionContext";
import { ScrollToTop } from "@/components/ScrollToTop";

// Lazy load the AuthDebugger component to avoid including it in production builds
const AuthDebugger = lazy(() =>
  import('@/components/debug/AuthDebugger').then(module => ({
    default: module.AuthDebugger
  }))
);

// Protected route component that works with Clerk
function ProtectedRoute({
  path,
  component: Component,
}: {
  path: string;
  component: () => React.JSX.Element;
}) {
  const { loaded } = useClerk();
  const { isAuthenticated, isInitialized, authToken } = useClerkAuth();
  const [, setLocation] = useLocation();

  // Track if this is the initial app load or a navigation
  const [isInitialLoad, setIsInitialLoad] = useState(true);

  // After first render, mark as no longer initial load
  useEffect(() => {
    // Use a short timeout to avoid flashing loading screen on fast loads
    const timer = setTimeout(() => {
      setIsInitialLoad(false);
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  // Debug auth state in protected routes
  useEffect(() => {
    console.log(`[PROTECTED-ROUTE ${path}] Auth state:`, {
      isAuthenticated,
      isInitialized,
      hasToken: !!authToken,
      isInitialLoad
    });
  }, [isAuthenticated, isInitialized, authToken, isInitialLoad, path]);

  // Only show loading screen on initial app load, not during navigation
  if (isInitialLoad && (!loaded || !isInitialized)) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-gradient-to-b from-background to-muted">
        <div className="relative mb-4">
          <Loader2 className="h-10 w-10 animate-spin text-primary" />
          <div className="absolute inset-0 h-10 w-10 animate-pulse rounded-full bg-primary/10"></div>
        </div>
        <h2 className="text-xl font-medium text-foreground mb-1">Renovision.Studio</h2>
        <p className="text-sm text-muted-foreground">Loading your experience...</p>
      </div>
    );
  }

  // If we're initialized and not authenticated, redirect to custom sign-in page
  if (isInitialized && !isAuthenticated && !isInitialLoad) {
    // Redirect to custom sign-in page with the current path as redirect URL
    const currentPath = window.location.pathname;
    const redirectUrl = encodeURIComponent(currentPath);
    setLocation(`/sign-in?redirect_url=${redirectUrl}`);

    return (
      <Route path={path}>
        <div className="min-h-screen flex items-center justify-center">
          <div className="flex flex-col items-center">
            <Loader2 className="h-8 w-8 animate-spin text-primary mb-2" />
            <p className="text-sm text-muted-foreground">Redirecting to sign in...</p>
          </div>
        </div>
      </Route>
    );
  }

  // If we're authenticated or still on initial load, render the component
  return (
    <Route path={path}>
      <Component />
    </Route>
  );
}

function Router() {
  const { isAuthenticated, isInitialized, authToken } = useClerkAuth();

  // Debug auth state in router
  useEffect(() => {
    console.log('[ROUTER] Auth state:', {
      isAuthenticated,
      isInitialized,
      hasToken: !!authToken
    });
  }, [isAuthenticated, isInitialized, authToken]);

  return (
    <div className="flex flex-col min-h-screen">
      <Navbar />
      <main className="flex-grow">
        <PageTransition>
          <Switch>
            {/* Show landing page for unauthenticated users, dashboard for authenticated users */}
            <Route path="/">
              {isAuthenticated === true ? <Dashboard /> : <LandingPage />}
            </Route>

            {/* Protected routes that require authentication */}
            <ProtectedRoute path="/create" component={CreateVisualization} />
            <ProtectedRoute path="/projects/:id" component={Project} />
            <ProtectedRoute path="/gallery" component={Gallery} />
            <ProtectedRoute path="/profile" component={Profile} />
            <ProtectedRoute path="/account" component={Account} />
            <ProtectedRoute path="/settings" component={Settings} />
            <ProtectedRoute path="/reference-library" component={ReferenceLibrary} />
            <ProtectedRoute path="/reference-library/:id" component={ReferenceCategory} />
            <ProtectedRoute path="/renovation-presets" component={RenovationPresets} />

            {/* Public routes */}
            <Route path="/pricing" component={Pricing} />
            <Route path="/contact" component={ContactUs} />
            <Route path="/sign-in" component={SignInPage} />
            <Route path="/sign-in/:stage" component={SignInPage} />
            <Route path="/sign-up" component={SignUpPage} />
            <Route path="/sign-up/:stage" component={SignUpPage} />

            {/* Footer pages */}
            <Route path="/customer-stories" component={CustomerStories} />
            <Route path="/documentation" component={Documentation} />
            <Route path="/tutorials" component={Tutorials} />
            <Route path="/faqs" component={FAQs} />
            <Route path="/privacy-policy" component={PrivacyPolicy} />
            <Route path="/terms-of-service" component={TermsOfService} />
            <Route path="/cookie-policy" component={CookiePolicy} />

            {/* Payment routes */}
            <ProtectedRoute path="/checkout" component={Checkout} />
            <ProtectedRoute path="/checkout/:plan" component={Checkout} />
            <ProtectedRoute path="/payment-success" component={PaymentSuccess} />

            <Route component={NotFound} />
          </Switch>
        </PageTransition>
      </main>
      <Footer />
    </div>
  );
}

function App() {
  // Check if we're in development mode
  const isDevelopment = import.meta.env.MODE === 'development';
  // Initialize auth
  const { isAuthenticated, authToken } = useClerkAuth();

  // Check if auth debugging is enabled via environment variable
  const isAuthDebugEnabled = import.meta.env.VITE_ENABLE_AUTH_DEBUG === 'true';

  // Log authentication status
  useEffect(() => {
    if (isDevelopment) {
      console.info('[APP] Authentication status:', isAuthenticated ? 'Authenticated' : 'Not authenticated');
      console.info('[APP] Auth token available:', !!authToken);
    }
  }, [isAuthenticated, authToken, isDevelopment]);

  // Clean up localStorage drafts on app initialization
  useEffect(() => {
    // Clean up old localStorage drafts since we're now using the database
    const allKeys = Object.keys(localStorage);
    const draftKeys = allKeys.filter(key => key.startsWith('project_draft_'));

    if (draftKeys.length > 0) {
      console.info(`[APP] Cleaning up ${draftKeys.length} localStorage drafts`);
      draftKeys.forEach(key => localStorage.removeItem(key));
    }
  }, []);

  return (
    <>
      <Toaster />
      <SubscriptionProvider>
        <ScrollToTop />
        <Router />
      </SubscriptionProvider>

      {/* Only show the AuthDebugger in development mode AND when explicitly enabled */}
      {isDevelopment && isAuthDebugEnabled && (
        <Suspense fallback={null}>
          <AuthDebugger />
        </Suspense>
      )}
    </>
  );
}

export default App;
