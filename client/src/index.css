@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-border;
  }

  html {
    -webkit-text-size-adjust: 100%;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
  }

  /* Improve tap targets on mobile */
  button, a, input, select, textarea {
    touch-action: manipulation;
  }
}

@layer utilities {
  /* Utility class to prevent text selection */
  .no-select {
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    user-select: none !important;
    pointer-events: none !important;
  }

  /* Apply to images to prevent dragging */
  img {
    -webkit-user-drag: none;
    -khtml-user-drag: none;
    -moz-user-drag: none;
    -o-user-drag: none;
    user-drag: none;
  }

  /* Mobile-specific utilities */
  .touch-target {
    @apply min-h-[44px] min-w-[44px];
  }
}