import { createRoot } from "react-dom/client";
import { useState, useEffect } from "react";
import App from "./App";
import "./index.css";
import { ThemeProvider } from "@/components/ui/theme-provider";
import { QueryClientProvider } from "@tanstack/react-query";
import { queryClient } from "./lib/queryClient";
import { TooltipProvider } from "@/components/ui/tooltip";
import { ClerkProvider } from "@clerk/clerk-react";
import { Loader2 } from "lucide-react";
import { GA4Provider } from "@/components/GA4Provider";

// Use the Clerk publishable key from environment variables
const publishableKey = import.meta.env.VITE_CLERK_PUBLISHABLE_KEY ||
                       "pk_test_bGVhZGluZy10cm91dC0xOS5jbGVyay5hY2NvdW50cy5kZXYk";

// Configure client-side logging
const logger = {
  debug: (message: string, ...args: any[]) => {
    if (process.env.NODE_ENV === 'development') {
      console.debug(`[CLERK-PROVIDER] ${message}`, ...args);
    }
  },
  info: (message: string, ...args: any[]) => {
    console.info(`[CLERK-PROVIDER] ${message}`, ...args);
  },
  warn: (message: string, ...args: any[]) => {
    console.warn(`[CLERK-PROVIDER] ${message}`, ...args);
  },
  error: (message: string, ...args: any[]) => {
    console.error(`[CLERK-PROVIDER] ${message}`, ...args);
  }
};

// Global state to track if Clerk has been loaded at least once
let clerkHasLoaded = false;

const ClerkWrapper = ({ children }: { children: React.ReactNode }) => {
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(!clerkHasLoaded);

  useEffect(() => {
    logger.info('Initializing Clerk authentication');

    // Add error handling for Clerk
    const handleClerkError = (event: ErrorEvent) => {
      if (event.error && event.error.toString().includes('ClerkJS')) {
        logger.error('Clerk initialization error:', event.error);
        setError(event.error.toString());
      }
    };

    window.addEventListener('error', handleClerkError);

    // If Clerk has loaded before, we can skip the loading screen
    if (clerkHasLoaded) {
      setLoading(false);
    } else {
      // Set loading to false after a shorter delay
      const timer = setTimeout(() => {
        setLoading(false);
        logger.debug('Clerk initialization timeout completed');
      }, 1000); // Reduced from 2000ms to 1000ms for faster loading

      return () => clearTimeout(timer);
    }

    return () => {
      window.removeEventListener('error', handleClerkError);
    };
  }, []);

  // Handle Clerk token events
  const handleClerkLoaded = async () => {
    logger.info('Clerk loaded successfully');
    clerkHasLoaded = true;

    // Get the token and store it in localStorage
    try {
      // @ts-ignore - Clerk's global object
      if (window.Clerk && window.Clerk.session) {
        // Set up the global getToken function for use by our API client
        window.__clerk_getToken = async (options) => {
          try {
            return await window.Clerk.session.getToken(options);
          } catch (error) {
            logger.error('Error getting token from Clerk:', error);
            return null;
          }
        };

        // Get the initial token
        const token = await window.Clerk.session.getToken();
        if (token) {
          localStorage.setItem('clerk-auth-token', token);

          // Also store in sessionStorage for persistence between page navigations
          try {
            const authCache = sessionStorage.getItem('renovate-ai-auth-cache') || '{}';
            const parsed = JSON.parse(authCache);
            parsed.authToken = token;
            parsed.lastUpdated = Date.now();
            parsed.isAuthenticated = true;
            sessionStorage.setItem('renovate-ai-auth-cache', JSON.stringify(parsed));
            logger.info('Auth token stored in localStorage and sessionStorage');
          } catch (e) {
            logger.warn('Failed to update sessionStorage', e);
          }
        }
      }
    } catch (error) {
      logger.error('Error getting token from Clerk:', error);
    }

    setLoading(false);
  };

  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-gradient-to-b from-background to-muted">
        <div className="relative mb-4">
          <Loader2 className="h-10 w-10 animate-spin text-primary" />
          <div className="absolute inset-0 h-10 w-10 animate-pulse rounded-full bg-primary/10"></div>
        </div>
        <h2 className="text-xl font-medium text-foreground mb-1">Renovision.Studio</h2>
        <p className="text-sm text-muted-foreground">Loading your experience...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4">
        <h1 className="text-xl font-bold mb-4">Authentication Error</h1>
        <p className="text-red-500 mb-4">{error}</p>
        <p>Please try refreshing the page or contact support.</p>
        <button
          className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          onClick={() => window.location.reload()}
        >
          Refresh Page
        </button>
      </div>
    );
  }

  return (
    <ClerkProvider
      publishableKey={publishableKey}
      onLoaded={handleClerkLoaded}
      signInUrl="/sign-in"
      signUpUrl="/sign-up"
      afterSignInUrl="/"
      afterSignUpUrl="/"
    >
      {children}
    </ClerkProvider>
  );
};

// Root component with Clerk authentication and GA4 analytics
const Root = () => (
  <ClerkWrapper>
    <ThemeProvider defaultTheme="light" storageKey="renovision-studio-theme">
      <QueryClientProvider client={queryClient}>
        <TooltipProvider>
          <GA4Provider>
            <App />
          </GA4Provider>
        </TooltipProvider>
      </QueryClientProvider>
    </ThemeProvider>
  </ClerkWrapper>
);

createRoot(document.getElementById("root")!).render(<Root />);
