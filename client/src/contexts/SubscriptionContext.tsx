import React, { createContext, useContext, useState, useEffect, ReactNode, useRef } from 'react';
import { useClerkAuth } from '@/hooks/use-clerk-auth';

// Define the subscription type
export type Subscription = {
  id: number;
  user_id: string;
  stripe_customer_id: string | null;
  stripe_subscription_id: string | null;
  plan_id: string;
  status: string;
  current_period_start: Date | null;
  current_period_end: Date | null;
  cancel_at_period_end: boolean;
  billing_cycle: string;
  created_at: Date;
  updated_at: Date;
};

// Define the usage type
export type Usage = {
  id: number;
  user_id: string;
  projects_count: number;
  images_count: number;
  period_start: Date;
  period_end: Date;
  created_at: Date;
  updated_at: Date;
};

// Define the plan limits type
export type PlanLimits = {
  projects: number;
  imagesPerProject: number;
  price: number;
};

// Define the subscription context type
type SubscriptionContextType = {
  subscription: Subscription | null;
  usage: Usage | null;
  limits: PlanLimits | null;
  isLoading: boolean;
  error: Error | null;
  refetch: () => Promise<void>;
  clearCache: (type?: 'customer' | 'subscription' | 'all') => Promise<void>;
  hasActiveSubscription: boolean;
  hasReachedProjectLimit: boolean;
  hasReachedImageLimit: (projectId: number) => boolean;
  remainingProjects: number;
  remainingImages: number;
};

// Create the subscription context
const SubscriptionContext = createContext<SubscriptionContextType | undefined>(undefined);

// Create the subscription provider
export function SubscriptionProvider({ children }: { children: ReactNode }) {
  const { isAuthenticated, getToken } = useClerkAuth();
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [usage, setUsage] = useState<Usage | null>(null);
  const [limits, setLimits] = useState<PlanLimits | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  // Fetch subscription data
  const fetchSubscription = async (): Promise<void> => {
    if (!isAuthenticated) {
      console.log('[SUBSCRIPTION] Not authenticated, skipping subscription fetch');
      setIsLoading(false);
      setSubscription(null);
      setUsage(null);
      setLimits(null);
      return Promise.resolve();
    }

    // Check if we've had too many errors in a short time
    const errorCount = parseInt(sessionStorage.getItem('subscription-error-count') || '0');
    const lastErrorTime = parseInt(sessionStorage.getItem('subscription-last-error-time') || '0');
    const now = Date.now();

    // If we've had multiple errors in the last minute, back off
    if (errorCount > 3 && (now - lastErrorTime) < 60000) {
      console.warn('[SUBSCRIPTION] Too many recent errors, backing off from subscription fetch');
      setIsLoading(false);
      // Set default free tier values
      setSubscription(null);
      setUsage(null);
      setLimits({
        projects: 1,
        imagesPerProject: 1,
        price: 0
      });
      return Promise.resolve();
    }

    try {
      setIsLoading(true);
      setError(null);

      // Get the auth token
      const token = await getToken();

      if (!token) {
        console.warn('No auth token available, skipping subscription fetch');
        setIsLoading(false);
        return Promise.resolve();
      }

      // Add cache busting parameter to prevent browser caching
      const cacheBuster = `_cb=${Date.now()}`;

      console.log('[SUBSCRIPTION] Fetching subscription data');
      // Only add Authorization header if token exists and is not null
      const headers: Record<string, string> = {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache'
      };

      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const response = await fetch(`/api/subscription?${cacheBuster}`, { headers });

      if (response.ok) {
        const data = await response.json();
        console.log('[SUBSCRIPTION] Received subscription data:', data);
        console.log('[SUBSCRIPTION] Data structure:', {
          hasSubscription: !!data.subscription,
          subscriptionType: typeof data.subscription,
          subscriptionNull: data.subscription === null,
          subscriptionKeys: data.subscription ? Object.keys(data.subscription) : [],
          nestedSubscription: data.subscription?.subscription ? 'exists' : 'missing',
          nestedSubscriptionType: data.subscription?.subscription ? typeof data.subscription.subscription : 'missing',
          nestedStatus: data.subscription?.subscription?.status || 'missing',
          fullPath: JSON.stringify(data?.subscription?.subscription || {})
        });

        // Store the entire response data to ensure we have the complete structure
        setSubscription(data);
        setUsage(data.usage);
        setLimits(data.limits);
      } else if (response.status === 404) {
        // 404 is expected if no subscription exists
        console.log('[SUBSCRIPTION] No subscription found (404)');
        // Set default free tier values
        setSubscription(null);
        setUsage(null);
        setLimits({
          projects: 1,
          imagesPerProject: 1,
          price: 0
        });
      } else {
        throw new Error(`Failed to fetch subscription data: ${response.status}`);
      }
    } catch (err) {
      console.error('[SUBSCRIPTION] Error fetching subscription:', err);
      setError(err instanceof Error ? err : new Error('Unknown error'));

      // Track error for backoff logic
      const errorCount = parseInt(sessionStorage.getItem('subscription-error-count') || '0');
      sessionStorage.setItem('subscription-error-count', (errorCount + 1).toString());
      sessionStorage.setItem('subscription-last-error-time', Date.now().toString());

      // Set default free tier values after error
      setSubscription(null);
      setUsage(null);
      setLimits({
        projects: 1,
        imagesPerProject: 1,
        price: 0
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Track if we're currently fetching to prevent duplicate requests
  const isFetchingRef = useRef(false);
  const lastFetchTimeRef = useRef(0);
  const hasLoadedRef = useRef(false);
  const MIN_FETCH_INTERVAL = 2000; // 2 seconds minimum between fetches

  // Fetch subscription data when authentication state changes
  useEffect(() => {
    // Skip if not authenticated
    if (!isAuthenticated) {
      setIsLoading(false);
      return;
    }

    const now = Date.now();

    // Prevent rapid successive calls
    if (isFetchingRef.current || (now - lastFetchTimeRef.current < MIN_FETCH_INTERVAL)) {
      return;
    }

    // Only fetch if we haven't already loaded data
    if (!hasLoadedRef.current) {
      console.log('[SUBSCRIPTION] Initial fetch on auth change');
      hasLoadedRef.current = true;
      isFetchingRef.current = true;
      lastFetchTimeRef.current = now;

      fetchSubscription().finally(() => {
        isFetchingRef.current = false;
      });
    }
  }, [isAuthenticated]);

  // Reset the hasLoaded flag when auth state changes
  useEffect(() => {
    return () => {
      hasLoadedRef.current = false;
    };
  }, [isAuthenticated]);

  // Check if user has an active subscription
  // The subscription data is nested: subscription.subscription.subscription.status
  const hasActiveSubscription = subscription?.subscription?.subscription?.status === 'active';

  // Debug log for subscription status
  console.log('[SUBSCRIPTION] Active subscription check:', {
    subscriptionExists: !!subscription,
    nestedExists: !!subscription?.subscription,
    doubleNestedExists: !!subscription?.subscription?.subscription,
    status: subscription?.subscription?.subscription?.status,
    hasActiveSubscription
  });

  // Check if user has reached project limit
  const hasReachedProjectLimit = !hasActiveSubscription ||
    (!!usage && !!limits && usage.projects_count >= limits.projects);

  // Check if user has reached image limit for a project
  const hasReachedImageLimit = (projectId: number) => {
    // This is a simplified check - in a real app, you'd check the number of images for this specific project
    return !hasActiveSubscription || (!!usage && !!limits && usage.images_count >= (limits.projects * limits.imagesPerProject));
  };

  // Calculate remaining projects
  const remainingProjects = limits && usage
    ? Math.max(0, limits.projects - usage.projects_count)
    : 0;

  // Calculate remaining images
  const remainingImages = limits && usage
    ? Math.max(0, (limits.projects * limits.imagesPerProject) - usage.images_count)
    : 0;

  // Function to clear the cache
  const clearCache = async (type: 'customer' | 'subscription' | 'all' = 'all'): Promise<void> => {
    if (!isAuthenticated) {
      return Promise.resolve();
    }

    try {
      // Get the auth token
      const token = await getToken();

      if (!token) {
        console.warn('No auth token available, skipping cache clear');
        return Promise.resolve();
      }

      console.log(`[SUBSCRIPTION] Clearing cache: ${type}`);
      // Only add Authorization header if token exists and is not null
      const headers: Record<string, string> = {
        'Content-Type': 'application/json'
      };

      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const response = await fetch('/api/clear-cache', {
        method: 'POST',
        headers,
        body: JSON.stringify({ type })
      });

      if (response.ok) {
        console.log(`[SUBSCRIPTION] Cache cleared: ${type}`);
        // Refetch subscription data
        return fetchSubscription();
      } else {
        throw new Error(`Failed to clear cache: ${response.status}`);
      }
    } catch (err) {
      console.error('[SUBSCRIPTION] Error clearing cache:', err);
      throw err;
    }
  };

  // Create the context value
  const value = {
    subscription,
    usage,
    limits,
    isLoading,
    error,
    refetch: fetchSubscription,
    clearCache,
    hasActiveSubscription,
    hasReachedProjectLimit,
    hasReachedImageLimit,
    remainingProjects,
    remainingImages,
  };

  return (
    <SubscriptionContext.Provider value={value}>
      {children}
    </SubscriptionContext.Provider>
  );
}

// Create a hook to use the subscription context
export function useSubscription() {
  const context = useContext(SubscriptionContext);

  if (context === undefined) {
    throw new Error('useSubscription must be used within a SubscriptionProvider');
  }

  return context;
}
