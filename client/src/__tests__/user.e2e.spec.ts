import { test, expect } from '@playwright/test';

test.describe('User Authentication and Project Flows', () => {
  test('User can sign up, sign in, and sign out', async ({ page }) => {
    await page.goto('/');
    await page.click('text=Sign Up');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'TestPassword123!');
    await page.click('text=Create Account');
    await expect(page).toHaveURL(/dashboard/);

    await page.click('text=Sign Out');
    await expect(page).toHaveURL('/');
  });

  test('User can create, view, and delete a project', async ({ page }) => {
    // Assume user is signed in
    await page.goto('/dashboard');
    await page.click('text=Start New Project');
    await page.fill('input[name="title"]', 'Test Project');
    await page.fill('textarea[name="description"]', 'A test project');
    await page.click('text=Create Project');
    await expect(page.locator('text=Test Project')).toBeVisible();

    // Delete project (assuming a delete button exists)
    await page.click('text=Delete');
    await expect(page.locator('text=Test Project')).not.toBeVisible();
  });

  // Add more tests for image upload, reference categories, etc.
}); 