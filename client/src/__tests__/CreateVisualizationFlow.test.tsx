import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { CreateVisualizationFlow } from '@/components/CreateVisualizationFlow';
import { ModificationType } from '@/lib/api';

// Mock the ReferenceItemSelector component
jest.mock('@/components/ReferenceItemSelector', () => ({
  ReferenceItemSelector: ({ isOpen, onClose, onItemsSelected }: any) => (
    <div data-testid="mock-reference-selector">
      {isOpen && (
        <button onClick={() => onItemsSelected([{ id: 123, path: '/test-path.jpg' }])}>
          Select Items
        </button>
      )}
    </div>
  ),
}));

describe('CreateVisualizationFlow', () => {
  const mockOnComplete = jest.fn();
  const mockOnBeforeImagesUpload = jest.fn();
  const mockOnReferenceImagesUpload = jest.fn();
  const mockOnReferenceItemsSelected = jest.fn();
  
  const beforeImages = [
    { id: 1, path: '/uploads/before1.jpg', originalFilename: 'before1.jpg' }
  ];
  
  const referenceImages = [
    { id: 2, path: '/uploads/ref1.jpg', originalFilename: 'ref1.jpg' },
    { id: 3, path: '/uploads/ref2.jpg', originalFilename: 'ref2.jpg' }
  ];
  
  const defaultProps = {
    onComplete: mockOnComplete,
    onBeforeImagesUpload: mockOnBeforeImagesUpload,
    onReferenceImagesUpload: mockOnReferenceImagesUpload,
    onReferenceItemsSelected: mockOnReferenceItemsSelected,
    beforeImages,
    referenceImages,
    projectId: 123
  };
  
  beforeEach(() => {
    jest.clearAllMocks();
  });
  
  it('renders the initial step with modification options', () => {
    render(<CreateVisualizationFlow {...defaultProps} />);
    
    // Check for step 1 content
    expect(screen.getByText('Choose What You Want to Change')).toBeInTheDocument();
    expect(screen.getByText('Replace Floor')).toBeInTheDocument();
    expect(screen.getByText('Custom Modification')).toBeInTheDocument();
  });
  
  it('navigates to step 2 when a modification type is selected', async () => {
    render(<CreateVisualizationFlow {...defaultProps} />);
    
    // Select "Replace Floor" option
    const replaceFloorCard = screen.getByText('Replace Floor').closest('.cursor-pointer');
    fireEvent.click(replaceFloorCard!);
    
    // Check that we moved to step 2
    await waitFor(() => {
      expect(screen.getByText('Upload Before Images')).toBeInTheDocument();
    });
  });
  
  it('handles before image uploads', async () => {
    render(<CreateVisualizationFlow {...defaultProps} />);
    
    // Select "Replace Floor" option to move to step 2
    const replaceFloorCard = screen.getByText('Replace Floor').closest('.cursor-pointer');
    fireEvent.click(replaceFloorCard!);
    
    // Find the image uploader (this is a simplified test since we can't actually upload files in Jest)
    await waitFor(() => {
      expect(screen.getByText('Upload Before Images')).toBeInTheDocument();
    });
    
    // Verify the before images are displayed by checking for the uploaded images section
    expect(screen.getAllByText('Uploaded Images')).toHaveLength(2); // One for before images, one for reference images
  });
  
  it('handles reference image selection for floor replacement', async () => {
    render(<CreateVisualizationFlow {...defaultProps} />);
    
    // Select "Replace Floor" option
    const replaceFloorCard = screen.getByText('Replace Floor').closest('.cursor-pointer');
    fireEvent.click(replaceFloorCard!);
    
    // Move to step 3 - use getAllByText and select the first enabled Next button
    const nextButtons = screen.getAllByText('Next');
    const enabledNextButton = nextButtons.find(button => !button.hasAttribute('disabled'));
    fireEvent.click(enabledNextButton!);
    
    // Check that we're on the floor options step
    await waitFor(() => {
      expect(screen.getByText('Floor Replacement Options')).toBeInTheDocument();
    });
    
    // Check that the reference images are displayed
    expect(screen.getAllByRole('img')).toHaveLength(2);
    
    // Select the first reference image
    const firstImage = screen.getAllByRole('img')[0];
    fireEvent.click(firstImage);
    
    // Move to review step
    fireEvent.click(screen.getByText('Next'));
    
    // Check that we're on the review step
    await waitFor(() => {
      expect(screen.getByText('Review Your Visualization Request')).toBeInTheDocument();
    });
    
    // Complete the flow
    fireEvent.click(screen.getByText('Create Visualization'));
    
    // Check that onComplete was called with the correct data
    expect(mockOnComplete).toHaveBeenCalledWith(expect.objectContaining({
      modificationType: 'replace_floor',
      options: expect.objectContaining({
        useReferenceImage: true,
        referenceImageId: 2
      }),
      primaryReferenceImageId: 2
    }));
  });
  
  it('handles custom modification with reference images', async () => {
    render(<CreateVisualizationFlow {...defaultProps} />);
    
    // Select "Custom Modification" option
    const customModCard = screen.getByText('Custom Modification').closest('.cursor-pointer');
    fireEvent.click(customModCard!);
    
    // Move to step 3 (skip step 2 for simplicity) - use getAllByText and select the first enabled Next button
    const nextButtons2 = screen.getAllByText('Next');
    const enabledNextButton2 = nextButtons2.find(button => !button.hasAttribute('disabled'));
    fireEvent.click(enabledNextButton2!);
    
    // Check that we're on the custom description step
    await waitFor(() => {
      expect(screen.getByText('Describe Your Changes')).toBeInTheDocument();
    });
    
    // Enter a custom description
    const textarea = screen.getByPlaceholderText(/Describe in detail the modifications/);
    // Use userEvent or direct value setting to avoid duplication
    Object.defineProperty(textarea, 'value', {
      writable: true,
      value: 'This is a test description that is long enough to proceed.'
    });
    fireEvent.change(textarea, { target: { value: 'This is a test description that is long enough to proceed.' } });
    
    // Move to review step
    fireEvent.click(screen.getByText('Next'));
    
    // Check that we're on the review step
    await waitFor(() => {
      expect(screen.getByText('Review Your Visualization Request')).toBeInTheDocument();
    });
    
    // Complete the flow
    fireEvent.click(screen.getByText('Create Visualization'));
    
    // Check that onComplete was called with the correct data
    expect(mockOnComplete).toHaveBeenCalledWith(expect.objectContaining({
      modificationType: 'custom',
      description: expect.stringContaining('This is a test description that is long enough to proceed'),
      primaryReferenceImageId: expect.any(Number)
    }));
  });
  
  it('validates inputs before allowing to proceed', async () => {
    render(<CreateVisualizationFlow {...defaultProps} beforeImages={[]} />);
    
    // Select "Replace Floor" option
    const replaceFloorCard = screen.getByText('Replace Floor').closest('.cursor-pointer');
    fireEvent.click(replaceFloorCard!);
    
    // Try to move to step 3 - use getAllByText and select the first enabled Next button
    const nextButtons3 = screen.getAllByText('Next');
    const enabledNextButton3 = nextButtons3.find(button => !button.hasAttribute('disabled'));
    if (enabledNextButton3) {
      fireEvent.click(enabledNextButton3);
    }
    
    // Should still be on step 2 because we have no before images
    expect(screen.getByText('Upload Before Images')).toBeInTheDocument();
    
    // Now render with before images
    render(<CreateVisualizationFlow {...defaultProps} />);
    
    // Select "Replace Floor" option
    const replaceFloorCard2 = screen.getByText('Replace Floor').closest('.cursor-pointer');
    fireEvent.click(replaceFloorCard2!);
    
    // Move to step 3 - use getAllByText and select the first enabled Next button
    const nextButtons4 = screen.getAllByText('Next');
    const enabledNextButton4 = nextButtons4.find(button => !button.hasAttribute('disabled'));
    fireEvent.click(enabledNextButton4!);
    
    // Check that we're on the floor options step
    await waitFor(() => {
      expect(screen.getByText('Floor Replacement Options')).toBeInTheDocument();
    });
    
    // Try to move to step 4 without selecting a reference image or material/color
    const nextButtons5 = screen.getAllByText('Next');
    const enabledNextButton5 = nextButtons5.find(button => !button.hasAttribute('disabled'));
    if (enabledNextButton5) {
      fireEvent.click(enabledNextButton5);
    }
    
    // Should still be on step 3
    expect(screen.getByText('Floor Replacement Options')).toBeInTheDocument();
  });
});
