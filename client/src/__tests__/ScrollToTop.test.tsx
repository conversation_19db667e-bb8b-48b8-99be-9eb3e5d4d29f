/**
 * Unit tests for ScrollToTop component
 */
import React from 'react';
import { render, fireEvent } from '@testing-library/react';
import { ScrollToTop, LinkWithScroll } from '@/components/ScrollToTop';
import { useLocation } from 'wouter';

// Mock window.scrollTo
const scrollToMock = jest.fn();
window.scrollTo = scrollToMock;

// Mock wouter's useLocation hook
jest.mock('wouter', () => ({
  useLocation: jest.fn()
}));

describe('ScrollToTop', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should scroll to top when location changes', () => {
    // Mock useLocation to return a location and a setter
    const mockUseLocation = useLocation as jest.Mock;
    mockUseLocation.mockReturnValue(['/initial-path', jest.fn()]);

    // Render the component
    const { rerender } = render(<ScrollToTop />);

    // Verify that scrollTo was called
    expect(scrollToMock).toHaveBeenCalledWith(0, 0);
    scrollToMock.mockClear();

    // Change the location
    mockUseLocation.mockReturnValue(['/new-path', jest.fn()]);
    rerender(<ScrollToTop />);

    // Verify that scrollTo was called again
    expect(scrollToMock).toHaveBeenCalledWith(0, 0);
  });

  it('should not scroll to top if location does not change', () => {
    // Mock useLocation to return a location and a setter
    const mockUseLocation = useLocation as jest.Mock;
    mockUseLocation.mockReturnValue(['/same-path', jest.fn()]);

    // Render the component
    const { rerender } = render(<ScrollToTop />);

    // Verify that scrollTo was called
    expect(scrollToMock).toHaveBeenCalledWith(0, 0);
    scrollToMock.mockClear();

    // Rerender with the same location
    rerender(<ScrollToTop />);

    // Verify that scrollTo was not called again
    expect(scrollToMock).not.toHaveBeenCalled();
  });
});

describe('LinkWithScroll', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should scroll to top when clicked', () => {
    // Mock useLocation to return a location and a navigate function
    const mockNavigate = jest.fn();
    const mockUseLocation = useLocation as jest.Mock;
    mockUseLocation.mockReturnValue(['/current-path', mockNavigate]);

    // Render the component
    const { getByText } = render(
      <LinkWithScroll href="/target-path">Test Link</LinkWithScroll>
    );

    // Click the link
    fireEvent.click(getByText('Test Link'));

    // Verify that scrollTo was called
    expect(scrollToMock).toHaveBeenCalledWith(0, 0);

    // Verify that navigate was called with the correct path
    expect(mockNavigate).toHaveBeenCalledWith('/target-path');
  });

  it('should scroll to top but not navigate when clicking a link to the current page', () => {
    // Mock useLocation to return a location and a navigate function
    const mockNavigate = jest.fn();
    const mockUseLocation = useLocation as jest.Mock;
    mockUseLocation.mockReturnValue(['/same-path', mockNavigate]);

    // Render the component
    const { getByText } = render(
      <LinkWithScroll href="/same-path">Test Link</LinkWithScroll>
    );

    // Click the link
    fireEvent.click(getByText('Test Link'));

    // Verify that scrollTo was called
    expect(scrollToMock).toHaveBeenCalledWith(0, 0);

    // Verify that navigate was NOT called
    expect(mockNavigate).not.toHaveBeenCalled();
  });

  it('should pass additional props to the anchor element', () => {
    // Mock useLocation
    const mockUseLocation = useLocation as jest.Mock;
    mockUseLocation.mockReturnValue(['/current-path', jest.fn()]);

    // Render the component with additional props
    const { getByText } = render(
      <LinkWithScroll href="/target-path" className="test-class" data-testid="test-link">
        Test Link
      </LinkWithScroll>
    );

    // Get the link element
    const linkElement = getByText('Test Link');

    // Verify that the additional props were passed
    expect(linkElement).toHaveClass('test-class');
    expect(linkElement).toHaveAttribute('data-testid', 'test-link');
  });
});
