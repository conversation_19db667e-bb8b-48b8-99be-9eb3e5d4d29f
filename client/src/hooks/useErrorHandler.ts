import { useState, useCallback } from 'react';
import { ErrorMessages } from '@/components/ui/error-message';

type ErrorType = 
  | 'VALIDATION_ERROR'
  | 'AUTHENTICATION_ERROR'
  | 'AUTHORIZATION_ERROR'
  | 'NOT_FOUND_ERROR'
  | 'DATABASE_ERROR'
  | 'EXTERNAL_SERVICE_ERROR'
  | 'INTERNAL_ERROR'
  | 'NETWORK_ERROR'
  | 'UNKNOWN_ERROR';

interface ApiError {
  error?: {
    type?: ErrorType;
    message?: string;
    details?: any;
  };
  message?: string;
  status?: number;
}

export function useErrorHandler() {
  const [error, setError] = useState<{ message: string; type?: ErrorType } | null>(null);

  const handleError = useCallback((err: unknown) => {
    console.error('API Error:', err);
    
    // Network error
    if (err instanceof Error && err.message === 'Failed to fetch') {
      setError({ message: ErrorMessages.NETWORK_ERROR, type: 'NETWORK_ERROR' });
      return;
    }
    
    // Handle API errors
    if (err && typeof err === 'object' && 'error' in err) {
      const apiError = err as ApiError;
      
      if (apiError.error?.type) {
        // Handle specific error types
        switch (apiError.error.type) {
          case 'AUTHENTICATION_ERROR':
            setError({ 
              message: apiError.error.message || ErrorMessages.AUTHENTICATION_ERROR,
              type: 'AUTHENTICATION_ERROR'
            });
            break;
          case 'AUTHORIZATION_ERROR':
            setError({ 
              message: apiError.error.message || ErrorMessages.AUTHORIZATION_ERROR,
              type: 'AUTHORIZATION_ERROR'
            });
            break;
          case 'VALIDATION_ERROR':
            setError({ 
              message: apiError.error.message || ErrorMessages.VALIDATION_ERROR,
              type: 'VALIDATION_ERROR'
            });
            break;
          case 'NOT_FOUND_ERROR':
            setError({ 
              message: apiError.error.message || ErrorMessages.NOT_FOUND_ERROR,
              type: 'NOT_FOUND_ERROR'
            });
            break;
          case 'EXTERNAL_SERVICE_ERROR':
            setError({ 
              message: apiError.error.message || ErrorMessages.AI_SERVICE_ERROR,
              type: 'EXTERNAL_SERVICE_ERROR'
            });
            break;
          default:
            setError({ 
              message: apiError.error.message || ErrorMessages.SERVER_ERROR,
              type: apiError.error.type || 'UNKNOWN_ERROR'
            });
        }
        return;
      }
      
      // Handle HTTP status codes
      if (apiError.status) {
        switch (apiError.status) {
          case 401:
            setError({ 
              message: apiError.message || ErrorMessages.AUTHENTICATION_ERROR,
              type: 'AUTHENTICATION_ERROR'
            });
            break;
          case 403:
            setError({ 
              message: apiError.message || ErrorMessages.AUTHORIZATION_ERROR,
              type: 'AUTHORIZATION_ERROR'
            });
            break;
          case 404:
            setError({ 
              message: apiError.message || ErrorMessages.NOT_FOUND_ERROR,
              type: 'NOT_FOUND_ERROR'
            });
            break;
          case 400:
            setError({ 
              message: apiError.message || ErrorMessages.VALIDATION_ERROR,
              type: 'VALIDATION_ERROR'
            });
            break;
          default:
            setError({ 
              message: apiError.message || ErrorMessages.SERVER_ERROR,
              type: 'UNKNOWN_ERROR'
            });
        }
        return;
      }
    }
    
    // Default error handling
    setError({ 
      message: err instanceof Error ? err.message : ErrorMessages.SERVER_ERROR,
      type: 'UNKNOWN_ERROR'
    });
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return { error, handleError, clearError };
}
