import { useAuth as useClerk<PERSON><PERSON>, useUser, useClerk } from "@clerk/clerk-react";
import { User } from "@shared/schema";
import { useEffect, useState } from "react";

// Configure client-side logging
const logger = {
  debug: (message: string, ...args: any[]) => {
    if (process.env.NODE_ENV === 'development') {
      console.debug(`[CLERK-AUTH-CLIENT] ${message}`, ...args);
    }
  },
  info: (message: string, ...args: any[]) => {
    console.info(`[CLERK-AUTH-CLIENT] ${message}`, ...args);
  },
  warn: (message: string, ...args: any[]) => {
    console.warn(`[CLERK-AUTH-CLIENT] ${message}`, ...args);
  },
  error: (message: string, ...args: any[]) => {
    console.error(`[CLERK-AUTH-CLIENT] ${message}`, ...args);
  }
};

// Custom type that matches our schema but with modified types for Clerk integration
type ClerkCompatibleUser = {
  id: string;
  username: string;
  email: string | null;
  firstName: string | null;
  lastName: string | null;
  bio: string | null;
  profileImageUrl: string | null;
  createdAt: Date | null;
  updatedAt: Date | null;
};

// This hook adapts Clerk auth to our existing auth interface
export function useAuth() {
  const { isLoaded, isSignedIn, getToken } = useClerkAuth();
  const { user: clerkUser, isLoaded: isUserLoaded } = useUser();
  const clerk = useClerk();
  const [error, setError] = useState<string | null>(null);
  const [authToken, setAuthToken] = useState<string | null>(null);

  // Get the auth token when the user is signed in
  useEffect(() => {
    if (isSignedIn && isLoaded) {
      const fetchToken = async () => {
        try {
          logger.debug('Fetching auth token');
          const token = await getToken();
          setAuthToken(token);

          // Store token in localStorage for API requests
          if (token) {
            localStorage.setItem('clerk-auth-token', token);
            logger.debug('Auth token stored in localStorage');
          }

          logger.debug('Auth token fetched successfully');
        } catch (err) {
          logger.error('Error fetching auth token:', err);
          setError('Failed to get authentication token');
        }
      };

      fetchToken();

      // Set up a refresh interval to keep the token fresh
      const refreshInterval = setInterval(async () => {
        try {
          const freshToken = await getToken({ skipCache: true });
          if (freshToken) {
            setAuthToken(freshToken);
            localStorage.setItem('clerk-auth-token', freshToken);
            logger.debug('Auth token refreshed');
          }
        } catch (err) {
          logger.error('Error refreshing token:', err);
        }
      }, 5 * 60 * 1000); // Refresh every 5 minutes

      return () => clearInterval(refreshInterval);
    } else if (isLoaded && !isSignedIn) {
      // Clear token when signed out
      setAuthToken(null);
      localStorage.removeItem('clerk-auth-token');
      logger.debug('Auth token removed from localStorage');
    }
  }, [isSignedIn, isLoaded, getToken]);

  // Log authentication state changes
  useEffect(() => {
    logger.info('Auth state changed', {
      isLoaded,
      isSignedIn,
      hasUser: !!clerkUser,
      userId: clerkUser?.id
    });
  }, [isLoaded, isSignedIn, clerkUser]);

  // Map Clerk user to our User type
  let user: ClerkCompatibleUser | null = null;

  if (isSignedIn && clerkUser) {
    try {
      user = {
        id: clerkUser.id,
        username: clerkUser.username || clerkUser.firstName || "User",
        email: clerkUser.primaryEmailAddress?.emailAddress || null,
        firstName: clerkUser.firstName || null,
        lastName: clerkUser.lastName || null,
        bio: null,
        profileImageUrl: clerkUser.imageUrl || null,
        createdAt: clerkUser.createdAt ? new Date(clerkUser.createdAt) : null,
        updatedAt: new Date()
      };
      logger.debug('User mapped successfully', { userId: user.id });
    } catch (err) {
      logger.error('Error mapping user:', err);
      setError('Failed to process user data');
    }
  }

  return {
    user: user as User | null, // Type cast to the expected User type
    isLoading: !isLoaded || !isUserLoaded,
    error,
    isAuthenticated: !!isSignedIn,
    authToken,
    // Expose Clerk methods that might be needed
    signOut: clerk.signOut,
  };
}