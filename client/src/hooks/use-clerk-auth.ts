import { useEffect, useState, useCallback } from 'react';
import { useAuth as useClerkAuthHook, useUser } from '@clerk/clerk-react';

// Create a global cache for auth state to persist between page navigations
interface AuthCache {
  isAuthenticated: boolean;
  authToken: string | null;
  isInitialized: boolean;
  lastUpdated: number;
}

// Initialize with values from localStorage if available
const AUTH_CACHE_KEY = 'renovate-ai-auth-cache';
let globalAuthCache: AuthCache = {
  isAuthenticated: false,
  authToken: null,
  isInitialized: false,
  lastUpdated: 0
};

// Function to check if a token is expired
const isTokenExpiredStatic = (token: string | null): boolean => {
  if (!token) return true;

  try {
    // Decode the JWT to check expiration
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
      return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
    }).join(''));

    const { exp } = JSON.parse(jsonPayload);
    if (!exp) return false;

    // Check if token is expired (with 30 second buffer)
    return Date.now() >= (exp * 1000) - 30000;
  } catch (e) {
    console.error('[CLERK-AUTH] Error checking token expiration:', e);
    return true; // If we can't decode the token, assume it's expired
  }
};

// Get token from localStorage and validate it
const storedToken = localStorage.getItem('clerk-auth-token');
if (storedToken && !isTokenExpiredStatic(storedToken)) {
  globalAuthCache.authToken = storedToken;
} else if (storedToken) {
  // If token exists but is expired, clear it
  console.warn('[CLERK-AUTH] Found expired token in localStorage, clearing it');
  localStorage.removeItem('clerk-auth-token');
}

// Try to load from sessionStorage to persist between page navigations
try {
  const cachedAuth = sessionStorage.getItem(AUTH_CACHE_KEY);
  if (cachedAuth) {
    const parsed = JSON.parse(cachedAuth);

    // Validate the token from sessionStorage
    if (parsed.authToken && !isTokenExpiredStatic(parsed.authToken)) {
      globalAuthCache = {
        ...parsed,
        // Use the validated token from localStorage if available
        authToken: globalAuthCache.authToken || parsed.authToken
      };
    } else if (parsed.authToken) {
      // If token exists but is expired, use the parsed data but clear the token
      console.warn('[CLERK-AUTH] Found expired token in sessionStorage, clearing it');
      globalAuthCache = {
        ...parsed,
        isAuthenticated: false,
        authToken: null
      };
      // Update sessionStorage with cleared token
      sessionStorage.setItem(AUTH_CACHE_KEY, JSON.stringify(globalAuthCache));
    }
  }
} catch (e) {
  console.warn('[CLERK-AUTH] Failed to load auth cache from sessionStorage', e);
}

export function useClerkAuth() {
  const { isLoaded, userId, getToken } = useClerkAuthHook();
  const { user } = useUser();

  // Initialize with default values that won't cause errors
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(globalAuthCache.isAuthenticated || false);
  const [authToken, setAuthToken] = useState<string | null>(globalAuthCache.authToken || null);
  const [isInitialized, setIsInitialized] = useState<boolean>(globalAuthCache.isInitialized || false);

  // Check if a token is expired
  const isTokenExpired = useCallback((token: string | null): boolean => {
    if (!token) return true;

    try {
      // Decode the JWT to check expiration
      const base64Url = token.split('.')[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
        return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
      }).join(''));

      const { exp } = JSON.parse(jsonPayload);
      if (!exp) return false;

      // Check if token is expired (with 30 second buffer)
      return Date.now() >= (exp * 1000) - 30000;
    } catch (e) {
      console.error('[CLERK-AUTH] Error checking token expiration:', e);
      return true; // If we can't decode the token, assume it's expired
    }
  }, []);

  // Memoize the token refresh function to avoid unnecessary re-renders
  const refreshToken = useCallback(async (force = false) => {
    if (!userId) {
      // If no user ID, clear auth state
      if (authToken || isAuthenticated) {
        console.log('[CLERK-AUTH] No user ID but auth state exists, clearing auth state');
        setAuthToken(null);
        setIsAuthenticated(false);
        localStorage.removeItem('clerk-auth-token');
        globalAuthCache.authToken = null;
        globalAuthCache.isAuthenticated = false;
        sessionStorage.setItem(AUTH_CACHE_KEY, JSON.stringify(globalAuthCache));
      }
      return null;
    }

    try {
      const now = Date.now();
      // Check if token is expired
      const isExpired = isTokenExpired(authToken);

      // If token is expired, immediately clear auth state
      if (isExpired && authToken) {
        console.log('[CLERK-AUTH] Token is expired, clearing auth state');
        setAuthToken(null);
        localStorage.removeItem('clerk-auth-token');
        globalAuthCache.authToken = null;
        // Don't set isAuthenticated to false yet, as we'll try to refresh the token
      }

      // Check if we need to refresh the token
      const shouldRefresh = force ||
                           !globalAuthCache.lastUpdated ||
                           (now - globalAuthCache.lastUpdated > 60000) ||
                           isExpired;

      if (shouldRefresh) {
        console.log('[CLERK-AUTH] Refreshing token, reason:',
          force ? 'forced' :
          !globalAuthCache.lastUpdated ? 'no last update' :
          (now - globalAuthCache.lastUpdated > 60000) ? 'time elapsed' :
          'token expired');

        const token = await getToken({ skipCache: true });
        if (token) {
          console.log('[CLERK-AUTH] Token refreshed successfully');
          setAuthToken(token);
          setIsAuthenticated(true);
          localStorage.setItem('clerk-auth-token', token);

          // Update the global cache
          globalAuthCache.authToken = token;
          globalAuthCache.isAuthenticated = true;
          globalAuthCache.lastUpdated = now;

          // Store in sessionStorage for persistence between page navigations
          sessionStorage.setItem(AUTH_CACHE_KEY, JSON.stringify(globalAuthCache));

          return token;
        } else {
          // If we couldn't get a new token, clear auth state
          console.warn('[CLERK-AUTH] Failed to refresh token, clearing auth state');
          setAuthToken(null);
          setIsAuthenticated(false);
          localStorage.removeItem('clerk-auth-token');
          globalAuthCache.authToken = null;
          globalAuthCache.isAuthenticated = false;
          sessionStorage.setItem(AUTH_CACHE_KEY, JSON.stringify(globalAuthCache));
          return null;
        }
      }
      return authToken;
    } catch (error) {
      console.error('[CLERK-AUTH] Error refreshing token:', error);
      // On error, clear auth state to be safe
      setAuthToken(null);
      setIsAuthenticated(false);
      localStorage.removeItem('clerk-auth-token');
      globalAuthCache.authToken = null;
      globalAuthCache.isAuthenticated = false;
      sessionStorage.setItem(AUTH_CACHE_KEY, JSON.stringify(globalAuthCache));
      return null;
    }
  }, [userId, getToken, authToken, isTokenExpired, isAuthenticated]);

  // Update authentication state when Clerk loads
  useEffect(() => {
    if (!isLoaded) return;

    const updateAuthState = async () => {
      try {
        // Check if user is authenticated according to Clerk
        const isAuth = !!userId;

        // Check if token is expired or invalid
        const tokenExpired = isTokenExpired(authToken);

        // Check for invalid token format
        let tokenInvalid = false;
        if (authToken) {
          try {
            // Simple validation - check if token has three parts separated by dots
            const parts = authToken.split('.');
            if (parts.length !== 3) {
              console.error('[CLERK-AUTH] Token has invalid format');
              tokenInvalid = true;
            }

            // Try to decode the payload to verify it's a valid JWT
            try {
              JSON.parse(atob(parts[1]));
            } catch (e) {
              console.error('[CLERK-AUTH] Token payload is not valid base64 JSON');
              tokenInvalid = true;
            }
          } catch (e) {
            console.error('[CLERK-AUTH] Error validating token format:', e);
            tokenInvalid = true;
          }
        }

        // If token is expired or invalid, clear auth state regardless of Clerk's state
        if ((tokenExpired || tokenInvalid) && authToken) {
          console.log('[CLERK-AUTH] Token expired or invalid during auth state update, clearing state');
          localStorage.removeItem('clerk-auth-token');
          setAuthToken(null);
          setIsAuthenticated(false);
          globalAuthCache.authToken = null;
          globalAuthCache.isAuthenticated = false;
          sessionStorage.setItem(AUTH_CACHE_KEY, JSON.stringify(globalAuthCache));
        }

        // If auth state changed, update it
        if (isAuth !== isAuthenticated) {
          console.log(`[CLERK-AUTH] Auth state changed from ${isAuthenticated} to ${isAuth}`);
          setIsAuthenticated(isAuth);
          globalAuthCache.isAuthenticated = isAuth;
        }

        if (isAuth) {
          // Get the token (force refresh if not initialized yet or token expired)
          await refreshToken(!isInitialized || tokenExpired);
        } else {
          // Clear token if not authenticated
          if (authToken) {
            console.log('[CLERK-AUTH] User not authenticated, clearing token');
            localStorage.removeItem('clerk-auth-token');
            setAuthToken(null);
            globalAuthCache.authToken = null;
            sessionStorage.setItem(AUTH_CACHE_KEY, JSON.stringify(globalAuthCache));
          }
        }

        // Mark as initialized if not already
        if (!isInitialized) {
          setIsInitialized(true);
          globalAuthCache.isInitialized = true;
          sessionStorage.setItem(AUTH_CACHE_KEY, JSON.stringify(globalAuthCache));
        }
      } catch (error) {
        console.error('[CLERK-AUTH] Error updating auth state:', error);
        // On error, clear auth state to be safe
        localStorage.removeItem('clerk-auth-token');
        setAuthToken(null);
        setIsAuthenticated(false);
        globalAuthCache.authToken = null;
        globalAuthCache.isAuthenticated = false;

        if (!isInitialized) {
          setIsInitialized(true);
          globalAuthCache.isInitialized = true;
          sessionStorage.setItem(AUTH_CACHE_KEY, JSON.stringify(globalAuthCache));
        }
      }
    };

    // Run immediately
    updateAuthState();

    // Set up interval to refresh token in the background
    const intervalId = setInterval(() => {
      if (userId) {
        refreshToken();
      } else if (authToken || isAuthenticated) {
        // If we have a token but no userId, clear auth state
        console.log('[CLERK-AUTH] No user ID but auth state exists in interval check, clearing auth state');
        setAuthToken(null);
        setIsAuthenticated(false);
        localStorage.removeItem('clerk-auth-token');
        globalAuthCache.authToken = null;
        globalAuthCache.isAuthenticated = false;
        sessionStorage.setItem(AUTH_CACHE_KEY, JSON.stringify(globalAuthCache));
      }
    }, 60 * 1000); // Check every minute instead of 5 minutes for faster response to token expiration

    return () => {
      clearInterval(intervalId);
    };
  }, [isLoaded, userId, isAuthenticated, isInitialized, refreshToken, authToken, isTokenExpired]);

  return {
    // Ensure boolean values are properly typed
    isAuthenticated: isAuthenticated === true,
    isInitialized: isInitialized === true,
    // Ensure token is either a string or null
    authToken: authToken || null,
    user,
    // Ensure userId is either a string or null
    userId: userId || null,
    refreshToken, // Expose the refresh token function
    // Add a helper method to get the token with minimal re-authentication
    getAuthToken: async () => {
      // If we have a token and it's been refreshed in the last 5 minutes and not expired, use it
      if (authToken &&
          globalAuthCache.lastUpdated &&
          (Date.now() - globalAuthCache.lastUpdated < 5 * 60 * 1000) &&
          !isTokenExpired(authToken)) {
        return authToken;
      }
      // Otherwise refresh it
      return await refreshToken();
    },
    // Expose the original getToken function from Clerk for compatibility
    getToken: async (options?: { skipCache?: boolean }) => {
      try {
        if (getToken && typeof getToken === 'function') {
          const token = await getToken(options);

          // Validate token format
          if (token) {
            try {
              // Simple validation - check if token has three parts separated by dots
              const parts = token.split('.');
              if (parts.length !== 3) {
                console.error('[CLERK-AUTH] Token has invalid format');
                throw new Error('Invalid token format');
              }

              // Try to decode the payload to verify it's a valid JWT
              try {
                JSON.parse(atob(parts[1]));
              } catch (e) {
                console.error('[CLERK-AUTH] Token payload is not valid base64 JSON');
                throw new Error('Invalid token payload');
              }

              return token;
            } catch (e) {
              console.error('[CLERK-AUTH] Error validating token:', e);
              // Clear invalid token
              localStorage.removeItem('clerk-auth-token');
              globalAuthCache.authToken = null;
              globalAuthCache.isAuthenticated = false;
              sessionStorage.setItem(AUTH_CACHE_KEY, JSON.stringify(globalAuthCache));
              return null;
            }
          }

          return token;
        } else {
          console.error('[CLERK-AUTH] getToken function not available');
          // Fall back to our cached token
          return await refreshToken(true);
        }
      } catch (error) {
        console.error('[CLERK-AUTH] Error getting token:', error);
        // Clear potentially corrupted auth state
        localStorage.removeItem('clerk-auth-token');
        globalAuthCache.authToken = null;
        globalAuthCache.isAuthenticated = false;
        sessionStorage.setItem(AUTH_CACHE_KEY, JSON.stringify(globalAuthCache));
        return null;
      }
    }
  };
}

// Export useAuth for backward compatibility
export const useAuth = useClerkAuth;
