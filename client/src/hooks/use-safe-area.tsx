import * as React from "react";

// Hook to handle safe area insets for mobile devices
export function useSafeArea() {
  const [safeArea, setSafeArea] = React.useState({
    top: 0,
    right: 0,
    bottom: 0,
    left: 0
  });

  React.useEffect(() => {
    // Check if the CSS environment variables are supported
    const computedStyle = getComputedStyle(document.documentElement);
    
    const getEnvValue = (key: string): number => {
      const value = computedStyle.getPropertyValue(key);
      return value ? parseInt(value, 10) : 0;
    };

    // Update safe area values
    const updateSafeArea = () => {
      setSafeArea({
        top: getEnvValue('--sat') || getEnvValue('env(safe-area-inset-top)') || 0,
        right: getEnvValue('--sar') || getEnvValue('env(safe-area-inset-right)') || 0,
        bottom: getEnvValue('--sab') || getEnvValue('env(safe-area-inset-bottom)') || 0,
        left: getEnvValue('--sal') || getEnvValue('env(safe-area-inset-left)') || 0
      });
    };

    // Set up CSS variables for safe areas if they don't exist
    if (!document.querySelector('#safe-area-css')) {
      const style = document.createElement('style');
      style.id = 'safe-area-css';
      style.innerHTML = `
        :root {
          --sat: env(safe-area-inset-top);
          --sar: env(safe-area-inset-right);
          --sab: env(safe-area-inset-bottom);
          --sal: env(safe-area-inset-left);
        }
      `;
      document.head.appendChild(style);
    }

    // Initial update
    updateSafeArea();

    // Update on resize
    window.addEventListener('resize', updateSafeArea);
    return () => window.removeEventListener('resize', updateSafeArea);
  }, []);

  return safeArea;
}
