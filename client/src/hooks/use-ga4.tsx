/**
 * React hook for Google Analytics 4 integration
 * 
 * This hook provides GA4 functionality for React components with proper
 * integration with W<PERSON>er routing and Clerk authentication.
 */

import { useEffect, useCallback } from 'react';
import { useLocation } from 'wouter';
import { useUser } from '@clerk/clerk-react';
import {
  initializeGA4,
  trackPageView,
  trackEvent,
  setUserId,
  trackAuthEvent,
  trackProjectEvent,
  trackPaymentEvent,
  trackErrorEvent,
  isGA4Ready,
  type GA4PageViewEvent,
  type GA4CustomEvent,
} from '@/lib/ga4';
import { logger } from '@/utils/logger';

/**
 * Hook for GA4 integration with automatic page view tracking
 */
export const useGA4 = () => {
  const [location] = useLocation();
  const { user, isSignedIn } = useUser();

  // Initialize GA4 on mount
  useEffect(() => {
    initializeGA4();
  }, []);

  // Set user ID when authentication state changes
  useEffect(() => {
    if (isGA4Ready()) {
      const userId = isSignedIn && user ? user.id : null;
      setUserId(userId);
      
      logger.debug('GA4 user ID updated', 'analytics', { 
        userId, 
        isSignedIn,
        userEmail: user?.primaryEmailAddress?.emailAddress 
      });
    }
  }, [isSignedIn, user]);

  // Track page views when location changes
  useEffect(() => {
    if (isGA4Ready()) {
      // Small delay to ensure page title is updated
      const timer = setTimeout(() => {
        const pageViewParams: GA4PageViewEvent = {
          page_path: location,
          page_location: window.location.href,
          page_title: document.title,
          user_id: isSignedIn && user ? user.id : undefined,
        };

        trackPageView(pageViewParams);
        
        logger.debug('GA4 page view tracked for route change', 'analytics', {
          location,
          title: document.title,
          userId: user?.id,
        });
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [location, isSignedIn, user]);

  // Return tracking functions for manual use
  const trackCustomEvent = useCallback((params: GA4CustomEvent) => {
    if (isGA4Ready()) {
      const eventParams = {
        ...params,
        user_id: isSignedIn && user ? user.id : params.user_id,
      };
      trackEvent(eventParams);
    }
  }, [isSignedIn, user]);

  const trackAuth = useCallback((action: 'sign_up' | 'sign_in' | 'sign_out') => {
    if (isGA4Ready()) {
      trackAuthEvent(action, user?.id);
    }
  }, [user]);

  const trackProject = useCallback((
    action: 'project_created' | 'project_completed' | 'image_generated' | 'image_generation_failed',
    projectId?: string,
    additionalParams?: Record<string, any>
  ) => {
    if (isGA4Ready()) {
      trackProjectEvent(action, projectId, user?.id, additionalParams);
    }
  }, [user]);

  const trackPayment = useCallback((
    action: 'checkout_started' | 'checkout_completed' | 'subscription_created' | 'subscription_cancelled',
    additionalParams?: Record<string, any>
  ) => {
    if (isGA4Ready()) {
      trackPaymentEvent(action, user?.id, additionalParams);
    }
  }, [user]);

  const trackError = useCallback((
    errorType: string,
    errorMessage: string,
    additionalParams?: Record<string, any>
  ) => {
    if (isGA4Ready()) {
      trackErrorEvent(errorType, errorMessage, user?.id, additionalParams);
    }
  }, [user]);

  return {
    // Manual tracking functions
    trackEvent: trackCustomEvent,
    trackAuth,
    trackProject,
    trackPayment,
    trackError,
    
    // Utility functions
    isReady: isGA4Ready(),
    userId: user?.id || null,
  };
};

/**
 * Hook for tracking specific page views manually
 * Useful for components that need to track virtual page views
 */
export const useGA4PageView = (
  pagePath?: string,
  pageTitle?: string,
  customParams?: Record<string, any>
) => {
  const { user, isSignedIn } = useUser();

  const trackManualPageView = useCallback(() => {
    if (isGA4Ready()) {
      const pageViewParams: GA4PageViewEvent = {
        page_path: pagePath || window.location.pathname,
        page_title: pageTitle || document.title,
        page_location: window.location.href,
        user_id: isSignedIn && user ? user.id : undefined,
        custom_parameters: customParams,
      };

      trackPageView(pageViewParams);
      
      logger.debug('Manual GA4 page view tracked', 'analytics', pageViewParams);
    }
  }, [pagePath, pageTitle, customParams, isSignedIn, user]);

  return { trackPageView: trackManualPageView };
};

/**
 * Hook for tracking form interactions
 */
export const useGA4FormTracking = () => {
  const { user } = useUser();

  const trackFormStart = useCallback((formName: string) => {
    if (isGA4Ready()) {
      trackEvent({
        action: 'form_start',
        category: 'form',
        label: formName,
        user_id: user?.id,
        custom_parameters: {
          form_name: formName,
        },
      });
    }
  }, [user]);

  const trackFormSubmit = useCallback((formName: string, success: boolean = true) => {
    if (isGA4Ready()) {
      trackEvent({
        action: success ? 'form_submit' : 'form_error',
        category: 'form',
        label: formName,
        user_id: user?.id,
        custom_parameters: {
          form_name: formName,
          success,
        },
      });
    }
  }, [user]);

  const trackFormField = useCallback((formName: string, fieldName: string, action: 'focus' | 'blur' | 'change') => {
    if (isGA4Ready()) {
      trackEvent({
        action: `form_field_${action}`,
        category: 'form',
        label: `${formName}_${fieldName}`,
        user_id: user?.id,
        custom_parameters: {
          form_name: formName,
          field_name: fieldName,
          interaction_type: action,
        },
      });
    }
  }, [user]);

  return {
    trackFormStart,
    trackFormSubmit,
    trackFormField,
  };
};
