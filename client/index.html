<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover, user-scalable=no" />
    <meta name="theme-color" content="#ffffff" />
    <link rel="manifest" href="/manifest.json" />
    <title>Renovision.Studio - AI-Powered Renovation Visualization</title>
    <!-- Favicon -->
    <link rel="icon" href="/images/logo/icon.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon/favicon.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon/favicon.png" />
    <link rel="apple-touch-icon" sizes="180x180" href="/favicon/favicon.png" />
    <link rel="shortcut icon" href="/favicon/favicon.png" />
    <link rel="mask-icon" href="/favicon/favicon.png" color="#ffffff" />
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Montserrat:wght@500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">

    <!-- Google Analytics 4 -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-SYV3MV2GT7"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      // Initial configuration will be handled by React component
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
    <!-- This is a replit script which adds a banner on the top of the page when opened in development mode outside the replit environment -->
    <script type="text/javascript" src="https://replit.com/public/js/replit-dev-banner.js"></script>
  </body>
</html>
